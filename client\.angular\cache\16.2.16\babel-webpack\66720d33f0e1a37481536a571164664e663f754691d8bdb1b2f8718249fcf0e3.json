{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Dashboard Module\n * Contains components for the dashboard feature\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { DashboardPageComponent } from './pages/dashboard-page/dashboard-page.component';\nimport { TaskSummaryComponent } from './components/task-summary/task-summary.component';\nimport { RecentTasksComponent } from './components/recent-tasks/recent-tasks.component';\nimport { TaskStatusChartComponent } from './components/task-status-chart/task-status-chart.component';\nimport { TaskPriorityChartComponent } from './components/task-priority-chart/task-priority-chart.component';\n// Dashboard routes\nconst routes = [{\n  path: '',\n  component: DashboardPageComponent\n}];\nexport let DashboardModule = class DashboardModule {};\nDashboardModule = __decorate([NgModule({\n  declarations: [DashboardPageComponent, TaskSummaryComponent, RecentTasksComponent, TaskStatusChartComponent, TaskPriorityChartComponent],\n  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n})], DashboardModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "SharedModule", "DashboardPageComponent", "TaskSummaryComponent", "RecentTasksComponent", "TaskStatusChartComponent", "TaskPriorityChartComponent", "routes", "path", "component", "DashboardModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\dashboard.module.ts"], "sourcesContent": ["/**\n * Dashboard Module\n * Contains components for the dashboard feature\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { DashboardPageComponent } from './pages/dashboard-page/dashboard-page.component';\nimport { TaskSummaryComponent } from './components/task-summary/task-summary.component';\nimport { RecentTasksComponent } from './components/recent-tasks/recent-tasks.component';\nimport { TaskStatusChartComponent } from './components/task-status-chart/task-status-chart.component';\nimport { TaskPriorityChartComponent } from './components/task-priority-chart/task-priority-chart.component';\n\n// Dashboard routes\nconst routes: Routes = [\n  {\n    path: '',\n    component: DashboardPageComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    DashboardPageComponent,\n    TaskSummaryComponent,\n    RecentTasksComponent,\n    TaskStatusChartComponent,\n    TaskPriorityChartComponent\n  ],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class DashboardModule { }\n"], "mappings": ";AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,sBAAsB,QAAQ,iDAAiD;AACxF,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,wBAAwB,QAAQ,4DAA4D;AACrG,SAASC,0BAA0B,QAAQ,gEAAgE;AAE3G;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEP;CACZ,CACF;AAgBM,WAAMQ,eAAe,GAArB,MAAMA,eAAe,GAAI;AAAnBA,eAAe,GAAAC,UAAA,EAd3Bb,QAAQ,CAAC;EACRc,YAAY,EAAE,CACZV,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,wBAAwB,EACxBC,0BAA0B,CAC3B;EACDO,OAAO,EAAE,CACPd,YAAY,EACZE,YAAY,EACZD,YAAY,CAACc,QAAQ,CAACP,MAAM,CAAC;CAEhC,CAAC,C,EACWG,eAAe,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}