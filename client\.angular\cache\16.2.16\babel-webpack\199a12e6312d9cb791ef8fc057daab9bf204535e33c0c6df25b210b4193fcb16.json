{"ast": null, "code": "/**\n * Click Outside Directive\n * Detects clicks outside of an element and emits an event\n */\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class ClickOutsideDirective {\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    /**\n     * Event emitted when a click occurs outside the element\n     */\n    this.appClickOutside = new EventEmitter();\n  }\n  /**\n   * Listen for document click events\n   * @param event - Mouse event\n   */\n  onClick(event) {\n    // Check if the click was outside the element\n    const clickedInside = this.elementRef.nativeElement.contains(event.target);\n    if (!clickedInside) {\n      this.appClickOutside.emit();\n    }\n  }\n  static {\n    this.ɵfac = function ClickOutsideDirective_Factory(t) {\n      return new (t || ClickOutsideDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: ClickOutsideDirective,\n      selectors: [[\"\", \"appClickOutside\", \"\"]],\n      hostBindings: function ClickOutsideDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function ClickOutsideDirective_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      outputs: {\n        appClickOutside: \"appClickOutside\"\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "ClickOutsideDirective", "constructor", "elementRef", "appClickOutside", "onClick", "event", "clickedInside", "nativeElement", "contains", "target", "emit", "i0", "ɵɵdirectiveInject", "ElementRef", "selectors", "hostBindings", "ClickOutsideDirective_HostBindings", "rf", "ctx", "$event", "ɵɵresolveDocument"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\directives\\click-outside.directive.ts"], "sourcesContent": ["/**\n * Click Outside Directive\n * Detects clicks outside of an element and emits an event\n */\nimport { Directive, ElementRef, EventEmitter, HostListener, Output } from '@angular/core';\n\n@Directive({\n  selector: '[appClickOutside]'\n})\nexport class ClickOutsideDirective {\n  /**\n   * Event emitted when a click occurs outside the element\n   */\n  @Output() appClickOutside = new EventEmitter<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(private elementRef: ElementRef) {}\n\n  /**\n   * Listen for document click events\n   * @param event - Mouse event\n   */\n  @HostListener('document:click', ['$event'])\n  onClick(event: MouseEvent): void {\n    // Check if the click was outside the element\n    const clickedInside = this.elementRef.nativeElement.contains(event.target);\n    \n    if (!clickedInside) {\n      this.appClickOutside.emit();\n    }\n  }\n}\n"], "mappings": "AAAA;;;;AAIA,SAAgCA,YAAY,QAA8B,eAAe;;AAKzF,OAAM,MAAOC,qBAAqB;EAMhC;;;;EAIAC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAT9B;;;IAGU,KAAAC,eAAe,GAAG,IAAIJ,YAAY,EAAQ;EAMP;EAE7C;;;;EAKAK,OAAOA,CAACC,KAAiB;IACvB;IACA,MAAMC,aAAa,GAAG,IAAI,CAACJ,UAAU,CAACK,aAAa,CAACC,QAAQ,CAACH,KAAK,CAACI,MAAM,CAAC;IAE1E,IAAI,CAACH,aAAa,EAAE;MAClB,IAAI,CAACH,eAAe,CAACO,IAAI,EAAE;;EAE/B;;;uBAxBWV,qBAAqB,EAAAW,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,UAAA;IAAA;EAAA;;;YAArBb,qBAAqB;MAAAc,SAAA;MAAAC,YAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAArBC,GAAA,CAAAd,OAAA,CAAAe,MAAA,CAAe;UAAA,UAAAR,EAAA,CAAAS,iBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}