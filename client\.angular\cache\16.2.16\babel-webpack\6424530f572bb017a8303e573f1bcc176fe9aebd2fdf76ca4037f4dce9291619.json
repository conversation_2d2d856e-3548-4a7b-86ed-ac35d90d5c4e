{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"../../../../core/services/notification.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"../../components/register-form/register-form.component\";\nexport class RegisterPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, notificationService, router, cdr) {\n    this.authService = authService;\n    this.notificationService = notificationService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed registration attempt\n     */\n    this.error = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit() {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Handle register form submission\n   * @param userData - User registration data\n   */\n  onFormSubmit(userData) {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    this.authService.register(userData.name, userData.email, userData.password).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.loading = false;\n        this.notificationService.success('Registration successful! Please log in.');\n        this.router.navigate(['/auth/login']);\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.message || 'Registration failed. Please try again.';\n        console.error('Registration error:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Navigate to login page\n   */\n  onLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  static {\n    this.ɵfac = function RegisterPageComponent_Factory(t) {\n      return new (t || RegisterPageComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterPageComponent,\n      selectors: [[\"app-register-page\"]],\n      decls: 11,\n      vars: 2,\n      consts: [[1, \"register-page-container\"], [1, \"auth-container\"], [1, \"auth-header\"], [1, \"logo\"], [1, \"app-title\"], [1, \"app-subtitle\"], [3, \"loading\", \"error\", \"formSubmit\", \"login\"]],\n      template: function RegisterPageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"task_alt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"h1\", 4);\n          i0.ɵɵtext(7, \"Task Manager\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 5);\n          i0.ɵɵtext(9, \"Create your account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"app-register-form\", 6);\n          i0.ɵɵlistener(\"formSubmit\", function RegisterPageComponent_Template_app_register_form_formSubmit_10_listener($event) {\n            return ctx.onFormSubmit($event);\n          })(\"login\", function RegisterPageComponent_Template_app_register_form_login_10_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"loading\", ctx.loading)(\"error\", ctx.error);\n        }\n      },\n      dependencies: [i4.MatIcon, i5.RegisterFormComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.register-page-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  background-color: #f5f5f5;\\n  padding: 24px;\\n}\\n\\n\\n\\n.auth-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n\\n\\n.auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n\\n\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 16px;\\n}\\n.logo[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: var(--primary-color);\\n}\\n\\n\\n\\n.app-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 500;\\n  margin: 0 0 8px;\\n  color: #333;\\n}\\n\\n\\n\\n.app-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .register-page-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    background-color: #fff;\\n  }\\n  .auth-header[_ngcontent-%COMP%] {\\n    margin-bottom: 24px;\\n  }\\n  .app-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "RegisterPageComponent", "constructor", "authService", "notificationService", "router", "cdr", "loading", "error", "destroy$", "ngOnInit", "isAuthenticated", "navigate", "ngOnDestroy", "next", "complete", "onFormSubmit", "userData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register", "name", "email", "password", "pipe", "subscribe", "success", "err", "message", "console", "onLogin", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "NotificationService", "i3", "Router", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "RegisterPageComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "RegisterPageComponent_Template_app_register_form_formSubmit_10_listener", "$event", "RegisterPageComponent_Template_app_register_form_login_10_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\register-page\\register-page.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\register-page\\register-page.component.html"], "sourcesContent": ["/**\n * Register Page Component\n * Page for user registration\n */\nimport { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-register-page',\n  templateUrl: './register-page.component.html',\n  styleUrls: ['./register-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class RegisterPageComponent implements OnInit, OnDestroy {\n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message from failed registration attempt\n   */\n  error: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private notificationService: NotificationService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit(): void {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Handle register form submission\n   * @param userData - User registration data\n   */\n  onFormSubmit(userData: { name: string; email: string; password: string }): void {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    \n    this.authService.register(userData.name, userData.email, userData.password)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.loading = false;\n          this.notificationService.success('Registration successful! Please log in.');\n          this.router.navigate(['/auth/login']);\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = err.message || 'Registration failed. Please try again.';\n          console.error('Registration error:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Navigate to login page\n   */\n  onLogin(): void {\n    this.router.navigate(['/auth/login']);\n  }\n}\n", "<!-- Register page container -->\n<div class=\"register-page-container\">\n  <div class=\"auth-container\">\n    <!-- App logo and title -->\n    <div class=\"auth-header\">\n      <div class=\"logo\">\n        <mat-icon>task_alt</mat-icon>\n      </div>\n      <h1 class=\"app-title\">Task Manager</h1>\n      <p class=\"app-subtitle\">Create your account</p>\n    </div>\n    \n    <!-- Register form component -->\n    <app-register-form\n      [loading]=\"loading\"\n      [error]=\"error\"\n      (formSubmit)=\"onFormSubmit($event)\"\n      (login)=\"onLogin()\">\n    </app-register-form>\n  </div>\n</div>\n"], "mappings": "AAMA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;AAU1C,OAAM,MAAOC,qBAAqB;EAgBhC;;;;;;;EAOAC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA1Bb;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIV,OAAO,EAAQ;EAcnC;EAEH;;;;EAIAW,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACP,WAAW,CAACQ,eAAe,EAAE,EAAE;MACtC,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;;EAIAC,YAAYA,CAACC,QAA2D;IACtE,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACF,GAAG,CAACY,YAAY,EAAE;IAEvB,IAAI,CAACf,WAAW,CAACgB,QAAQ,CAACF,QAAQ,CAACG,IAAI,EAAEH,QAAQ,CAACI,KAAK,EAAEJ,QAAQ,CAACK,QAAQ,CAAC,CACxEC,IAAI,CAACvB,SAAS,CAAC,IAAI,CAACS,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTV,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACP,OAAO,GAAG,KAAK;QACpB,IAAI,CAACH,mBAAmB,CAACqB,OAAO,CAAC,yCAAyC,CAAC;QAC3E,IAAI,CAACpB,MAAM,CAACO,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACvC,CAAC;MACDJ,KAAK,EAAGkB,GAAG,IAAI;QACb,IAAI,CAACnB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGkB,GAAG,CAACC,OAAO,IAAI,wCAAwC;QACpEC,OAAO,CAACpB,KAAK,CAAC,qBAAqB,EAAEkB,GAAG,CAAC;QACzC,IAAI,CAACpB,GAAG,CAACY,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAW,OAAOA,CAAA;IACL,IAAI,CAACxB,MAAM,CAACO,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;;;uBAjFWX,qBAAqB,EAAA6B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAQ,iBAAA;IAAA;EAAA;;;YAArBrC,qBAAqB;MAAAsC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBlCf,EAAA,CAAAiB,cAAA,aAAqC;UAKnBjB,EAAA,CAAAkB,MAAA,eAAQ;UAAAlB,EAAA,CAAAmB,YAAA,EAAW;UAE/BnB,EAAA,CAAAiB,cAAA,YAAsB;UAAAjB,EAAA,CAAAkB,MAAA,mBAAY;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACvCnB,EAAA,CAAAiB,cAAA,WAAwB;UAAAjB,EAAA,CAAAkB,MAAA,0BAAmB;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAIjDnB,EAAA,CAAAiB,cAAA,4BAIsB;UADpBjB,EAAA,CAAAoB,UAAA,wBAAAC,wEAAAC,MAAA;YAAA,OAAcN,GAAA,CAAA9B,YAAA,CAAAoC,MAAA,CAAoB;UAAA,EAAC,mBAAAC,mEAAA;YAAA,OAC1BP,GAAA,CAAAjB,OAAA,EAAS;UAAA,EADiB;UAErCC,EAAA,CAAAmB,YAAA,EAAoB;;;UAJlBnB,EAAA,CAAAwB,SAAA,IAAmB;UAAnBxB,EAAA,CAAAyB,UAAA,YAAAT,GAAA,CAAAvC,OAAA,CAAmB,UAAAuC,GAAA,CAAAtC,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}