{"ast": null, "code": "/**\n * Task Detail Component\n * Displays detailed information about a task\n */\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/form-field\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"../../../../shared/components/loading-spinner/loading-spinner.component\";\nfunction TaskDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"app-loading-spinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskDetailComponent_ng_container_2_mat_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r7.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", status_r7.label, \" \");\n  }\n}\nfunction TaskDetailComponent_ng_container_2_mat_icon_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"warning\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TaskDetailComponent_ng_container_2_mat_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r8.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r8.name, \" \");\n  }\n}\nfunction TaskDetailComponent_ng_container_2_div_43_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r10 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(tag_r10);\n  }\n}\nfunction TaskDetailComponent_ng_container_2_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"h2\");\n    i0.ɵɵtext(2, \"Tags\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 26);\n    i0.ɵɵtemplate(4, TaskDetailComponent_ng_container_2_div_43_span_4_Template, 2, 1, \"span\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.task.tags);\n  }\n}\nfunction TaskDetailComponent_ng_container_2_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"span\", 24);\n    i0.ɵɵtext(2, \"Last updated:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r6.task.updatedAt, \"medium\"));\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"overdue\": a0\n  };\n};\nfunction TaskDetailComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"h1\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function TaskDetailComponent_ng_container_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onEdit());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function TaskDetailComponent_ng_container_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onDelete());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"span\", 11);\n    i0.ɵɵtext(14, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-form-field\", 12)(16, \"mat-select\", 13);\n    i0.ɵɵlistener(\"selectionChange\", function TaskDetailComponent_ng_container_2_Template_mat_select_selectionChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onStatusChange($event.value));\n    });\n    i0.ɵɵtemplate(17, TaskDetailComponent_ng_container_2_mat_option_17_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 10)(19, \"span\", 11);\n    i0.ɵɵtext(20, \"Priority\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 15);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 10)(24, \"span\", 11);\n    i0.ɵɵtext(25, \"Due Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 16);\n    i0.ɵɵtemplate(27, TaskDetailComponent_ng_container_2_mat_icon_27_Template, 2, 0, \"mat-icon\", 2);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 10)(31, \"span\", 11);\n    i0.ɵɵtext(32, \"Assignee\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-form-field\", 12)(34, \"mat-select\", 17);\n    i0.ɵɵlistener(\"selectionChange\", function TaskDetailComponent_ng_container_2_Template_mat_select_selectionChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onAssigneeChange($event.value));\n    });\n    i0.ɵɵelementStart(35, \"mat-option\", 18);\n    i0.ɵɵtext(36, \"Unassigned\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, TaskDetailComponent_ng_container_2_mat_option_37_Template, 2, 2, \"mat-option\", 14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(38, \"div\", 19)(39, \"h2\");\n    i0.ɵɵtext(40, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 20);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(43, TaskDetailComponent_ng_container_2_div_43_Template, 5, 1, \"div\", 21);\n    i0.ɵɵelementStart(44, \"div\", 22)(45, \"div\", 23)(46, \"span\", 24);\n    i0.ɵɵtext(47, \"Created by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"span\");\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 23)(51, \"span\", 24);\n    i0.ɵɵtext(52, \"Created:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"span\");\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(56, TaskDetailComponent_ng_container_2_div_56_Template, 6, 4, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.task.title);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"value\", ctx_r1.task.status)(\"ngClass\", ctx_r1.getStatusClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.statuses);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getPriorityClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.task.priority, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c0, ctx_r1.isOverdue()));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isOverdue());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(29, 17, ctx_r1.task.dueDate, \"mediumDate\" || \"No due date\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r1.task.assignee);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.users);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.task.description || \"No description provided.\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.task.tags && ctx_r1.task.tags.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.getCreatorName());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(55, 20, ctx_r1.task.createdAt, \"medium\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.task.updatedAt);\n  }\n}\nexport class TaskDetailComponent {\n  constructor() {\n    /**\n     * Input list of users for assignee selection\n     */\n    this.users = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Event emitted when edit button is clicked\n     */\n    this.edit = new EventEmitter();\n    /**\n     * Event emitted when delete button is clicked\n     */\n    this.delete = new EventEmitter();\n    /**\n     * Event emitted when status is changed\n     */\n    this.statusChange = new EventEmitter();\n    /**\n     * Event emitted when assignee is changed\n     */\n    this.assigneeChange = new EventEmitter();\n    /**\n     * Available task statuses\n     */\n    this.statuses = [{\n      value: 'todo',\n      label: 'To Do'\n    }, {\n      value: 'in_progress',\n      label: 'In Progress'\n    }, {\n      value: 'review',\n      label: 'Review'\n    }, {\n      value: 'done',\n      label: 'Done'\n    }];\n  }\n  /**\n   * Get CSS class for priority\n   * @returns CSS class name\n   */\n  getPriorityClass() {\n    return `priority-${this.task.priority}`;\n  }\n  /**\n   * Get CSS class for status\n   * @returns CSS class name\n   */\n  getStatusClass() {\n    return `status-${this.task.status}`;\n  }\n  /**\n   * Get formatted status label\n   * @returns Status label\n   */\n  getStatusLabel() {\n    const status = this.statuses.find(s => s.value === this.task.status);\n    return status ? status.label : this.task.status;\n  }\n  /**\n   * Handle status change\n   * @param status - New status\n   */\n  onStatusChange(status) {\n    this.statusChange.emit(status);\n  }\n  /**\n   * Handle assignee change\n   * @param assigneeId - New assignee ID\n   */\n  onAssigneeChange(assigneeId) {\n    this.assigneeChange.emit(assigneeId);\n  }\n  /**\n   * Handle edit button click\n   */\n  onEdit() {\n    this.edit.emit();\n  }\n  /**\n   * Handle delete button click\n   */\n  onDelete() {\n    this.delete.emit();\n  }\n  /**\n   * Check if task is overdue\n   * @returns True if task is overdue\n   */\n  isOverdue() {\n    if (!this.task.dueDate || this.task.status === 'done') {\n      return false;\n    }\n    const dueDate = new Date(this.task.dueDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    return dueDate < today;\n  }\n  /**\n   * Get assignee name\n   * @returns Assignee name or 'Unassigned'\n   */\n  getAssigneeName() {\n    if (!this.task.assignee) {\n      return 'Unassigned';\n    }\n    const user = this.users.find(u => u.id === this.task.assignee);\n    return user ? user.name : 'Unknown User';\n  }\n  /**\n   * Get creator name\n   * @returns Creator name or 'Unknown'\n   */\n  getCreatorName() {\n    if (!this.task.creator) {\n      return 'Unknown';\n    }\n    const user = this.users.find(u => u.id === this.task.creator);\n    return user ? user.name : 'Unknown User';\n  }\n  static {\n    this.ɵfac = function TaskDetailComponent_Factory(t) {\n      return new (t || TaskDetailComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskDetailComponent,\n      selectors: [[\"app-task-detail\"]],\n      inputs: {\n        task: \"task\",\n        users: \"users\",\n        loading: \"loading\"\n      },\n      outputs: {\n        edit: \"edit\",\n        delete: \"delete\",\n        statusChange: \"statusChange\",\n        assigneeChange: \"assigneeChange\"\n      },\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"task-detail-container\"], [\"class\", \"task-detail-loading\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"task-detail-loading\"], [1, \"task-detail-header\"], [1, \"task-title\"], [1, \"task-actions\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Edit task\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"aria-label\", \"Delete task\", 3, \"click\"], [1, \"task-metadata\"], [1, \"metadata-item\"], [1, \"metadata-label\"], [\"appearance\", \"outline\"], [3, \"value\", \"ngClass\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"priority-badge\", 3, \"ngClass\"], [1, \"due-date\", 3, \"ngClass\"], [3, \"value\", \"selectionChange\"], [3, \"value\"], [1, \"task-section\"], [1, \"task-description\"], [\"class\", \"task-section\", 4, \"ngIf\"], [1, \"task-section\", \"task-info\"], [1, \"info-item\"], [1, \"info-label\"], [\"class\", \"info-item\", 4, \"ngIf\"], [1, \"task-tags\"], [\"class\", \"task-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"task-tag\"]],\n      template: function TaskDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TaskDetailComponent_div_1_Template, 2, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, TaskDetailComponent_ng_container_2_Template, 57, 25, \"ng-container\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.task);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.MatIconButton, i3.MatFormField, i4.MatIcon, i5.MatSelect, i6.MatOption, i7.LoadingSpinnerComponent, i1.DatePipe],\n      styles: [\"\\n\\n\\n\\n\\n\\n.task-detail-container[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  padding: 24px;\\n}\\n\\n\\n\\n.task-detail-loading[_ngcontent-%COMP%] {\\n  padding: 32px;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n\\n\\n.task-detail-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 24px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n\\n\\n.task-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.8rem;\\n  font-weight: 500;\\n  color: #333;\\n  flex: 1;\\n}\\n\\n\\n\\n.task-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n\\n\\n.task-metadata[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n\\n\\n.metadata-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-width: 150px;\\n}\\n.metadata-item[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n\\n\\n.metadata-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #666;\\n  margin-bottom: 4px;\\n}\\n\\n\\n\\n.priority-badge[_ngcontent-%COMP%] {\\n  padding: 4px 12px;\\n  border-radius: 16px;\\n  font-size: 0.85rem;\\n  text-transform: uppercase;\\n  font-weight: 500;\\n  display: inline-block;\\n}\\n.priority-badge.priority-high[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n}\\n.priority-badge.priority-medium[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #ff8f00;\\n}\\n.priority-badge.priority-low[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  color: #2e7d32;\\n}\\n\\n\\n\\n.due-date[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.95rem;\\n}\\n.due-date.overdue[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n.due-date[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  margin-right: 4px;\\n}\\n\\n\\n\\n.task-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.task-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 500;\\n  margin-bottom: 12px;\\n  color: #444;\\n}\\n\\n\\n\\n.task-description[_ngcontent-%COMP%] {\\n  line-height: 1.6;\\n  color: #333;\\n  white-space: pre-line;\\n}\\n\\n\\n\\n.task-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n\\n\\n\\n.task-tag[_ngcontent-%COMP%] {\\n  background-color: #f0f0f0;\\n  color: #666;\\n  padding: 4px 12px;\\n  border-radius: 16px;\\n  font-size: 0.85rem;\\n}\\n\\n\\n\\n.task-info[_ngcontent-%COMP%] {\\n  background-color: #f9f9f9;\\n  padding: 16px;\\n  border-radius: 4px;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n\\n\\n.info-item[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.info-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n\\n\\n.info-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-right: 8px;\\n}\\n\\n\\n\\n[_nghost-%COMP%]     .status-todo .mat-mdc-select-value {\\n  color: #616161;\\n}\\n[_nghost-%COMP%]     .status-in_progress .mat-mdc-select-value {\\n  color: #1976d2;\\n}\\n[_nghost-%COMP%]     .status-review .mat-mdc-select-value {\\n  color: #e65100;\\n}\\n[_nghost-%COMP%]     .status-done .mat-mdc-select-value {\\n  color: #2e7d32;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .task-detail-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .task-detail-header[_ngcontent-%COMP%]   .task-actions[_ngcontent-%COMP%] {\\n    margin-top: 16px;\\n    align-self: flex-end;\\n  }\\n  .task-metadata[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "status_r7", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "user_r8", "id", "name", "ɵɵtextInterpolate", "tag_r10", "ɵɵtemplate", "TaskDetailComponent_ng_container_2_div_43_span_4_Template", "ctx_r5", "task", "tags", "ɵɵpipeBind2", "ctx_r6", "updatedAt", "ɵɵelementContainerStart", "ɵɵlistener", "TaskDetailComponent_ng_container_2_Template_button_click_5_listener", "ɵɵrestoreView", "_r12", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "onEdit", "TaskDetailComponent_ng_container_2_Template_button_click_8_listener", "ctx_r13", "onDelete", "TaskDetailComponent_ng_container_2_Template_mat_select_selectionChange_16_listener", "$event", "ctx_r14", "onStatusChange", "TaskDetailComponent_ng_container_2_mat_option_17_Template", "TaskDetailComponent_ng_container_2_mat_icon_27_Template", "TaskDetailComponent_ng_container_2_Template_mat_select_selectionChange_34_listener", "ctx_r15", "onAssigneeChange", "TaskDetailComponent_ng_container_2_mat_option_37_Template", "TaskDetailComponent_ng_container_2_div_43_Template", "TaskDetailComponent_ng_container_2_div_56_Template", "ɵɵelementContainerEnd", "ctx_r1", "title", "status", "getStatusClass", "statuses", "getPriorityClass", "priority", "ɵɵpureFunction1", "_c0", "isOverdue", "dueDate", "assignee", "users", "description", "length", "getCreatorName", "createdAt", "TaskDetailComponent", "constructor", "loading", "edit", "delete", "statusChange", "assignee<PERSON><PERSON><PERSON>", "getStatusLabel", "find", "s", "emit", "assigneeId", "Date", "today", "setHours", "getAs<PERSON>eeName", "user", "u", "creator", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "TaskDetailComponent_Template", "rf", "ctx", "TaskDetailComponent_div_1_Template", "TaskDetailComponent_ng_container_2_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\components\\task-detail\\task-detail.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\components\\task-detail\\task-detail.component.html"], "sourcesContent": ["/**\n * Task Detail Component\n * Displays detailed information about a task\n */\nimport { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-task-detail',\n  templateUrl: './task-detail.component.html',\n  styleUrls: ['./task-detail.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskDetailComponent {\n  /**\n   * Input task to display\n   */\n  @Input() task!: Task;\n  \n  /**\n   * Input list of users for assignee selection\n   */\n  @Input() users: User[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Event emitted when edit button is clicked\n   */\n  @Output() edit = new EventEmitter<void>();\n  \n  /**\n   * Event emitted when delete button is clicked\n   */\n  @Output() delete = new EventEmitter<void>();\n  \n  /**\n   * Event emitted when status is changed\n   */\n  @Output() statusChange = new EventEmitter<string>();\n  \n  /**\n   * Event emitted when assignee is changed\n   */\n  @Output() assigneeChange = new EventEmitter<string>();\n  \n  /**\n   * Available task statuses\n   */\n  statuses = [\n    { value: 'todo', label: 'To Do' },\n    { value: 'in_progress', label: 'In Progress' },\n    { value: 'review', label: 'Review' },\n    { value: 'done', label: 'Done' }\n  ];\n\n  /**\n   * Get CSS class for priority\n   * @returns CSS class name\n   */\n  getPriorityClass(): string {\n    return `priority-${this.task.priority}`;\n  }\n\n  /**\n   * Get CSS class for status\n   * @returns CSS class name\n   */\n  getStatusClass(): string {\n    return `status-${this.task.status}`;\n  }\n\n  /**\n   * Get formatted status label\n   * @returns Status label\n   */\n  getStatusLabel(): string {\n    const status = this.statuses.find(s => s.value === this.task.status);\n    return status ? status.label : this.task.status;\n  }\n\n  /**\n   * Handle status change\n   * @param status - New status\n   */\n  onStatusChange(status: string): void {\n    this.statusChange.emit(status);\n  }\n\n  /**\n   * Handle assignee change\n   * @param assigneeId - New assignee ID\n   */\n  onAssigneeChange(assigneeId: string): void {\n    this.assigneeChange.emit(assigneeId);\n  }\n\n  /**\n   * Handle edit button click\n   */\n  onEdit(): void {\n    this.edit.emit();\n  }\n\n  /**\n   * Handle delete button click\n   */\n  onDelete(): void {\n    this.delete.emit();\n  }\n\n  /**\n   * Check if task is overdue\n   * @returns True if task is overdue\n   */\n  isOverdue(): boolean {\n    if (!this.task.dueDate || this.task.status === 'done') {\n      return false;\n    }\n    \n    const dueDate = new Date(this.task.dueDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    \n    return dueDate < today;\n  }\n\n  /**\n   * Get assignee name\n   * @returns Assignee name or 'Unassigned'\n   */\n  getAssigneeName(): string {\n    if (!this.task.assignee) {\n      return 'Unassigned';\n    }\n    \n    const user = this.users.find(u => u.id === this.task.assignee);\n    return user ? user.name : 'Unknown User';\n  }\n\n  /**\n   * Get creator name\n   * @returns Creator name or 'Unknown'\n   */\n  getCreatorName(): string {\n    if (!this.task.creator) {\n      return 'Unknown';\n    }\n    \n    const user = this.users.find(u => u.id === this.task.creator);\n    return user ? user.name : 'Unknown User';\n  }\n}\n", "<!-- Task detail container -->\n<div class=\"task-detail-container\">\n  <!-- Loading state -->\n  <div *ngIf=\"loading\" class=\"task-detail-loading\">\n    <app-loading-spinner></app-loading-spinner>\n  </div>\n\n  <!-- Task content when loaded -->\n  <ng-container *ngIf=\"!loading && task\">\n    <!-- Header with title and actions -->\n    <div class=\"task-detail-header\">\n      <h1 class=\"task-title\">{{ task.title }}</h1>\n      \n      <div class=\"task-actions\">\n        <button mat-icon-button (click)=\"onEdit()\" aria-label=\"Edit task\">\n          <mat-icon>edit</mat-icon>\n        </button>\n        <button mat-icon-button color=\"warn\" (click)=\"onDelete()\" aria-label=\"Delete task\">\n          <mat-icon>delete</mat-icon>\n        </button>\n      </div>\n    </div>\n\n    <!-- Task metadata -->\n    <div class=\"task-metadata\">\n      <!-- Status -->\n      <div class=\"metadata-item\">\n        <span class=\"metadata-label\">Status</span>\n        <mat-form-field appearance=\"outline\">\n          <mat-select \n            [value]=\"task.status\" \n            (selectionChange)=\"onStatusChange($event.value)\"\n            [ngClass]=\"getStatusClass()\">\n            <mat-option *ngFor=\"let status of statuses\" [value]=\"status.value\">\n              {{ status.label }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n      \n      <!-- Priority -->\n      <div class=\"metadata-item\">\n        <span class=\"metadata-label\">Priority</span>\n        <div class=\"priority-badge\" [ngClass]=\"getPriorityClass()\">\n          {{ task.priority }}\n        </div>\n      </div>\n      \n      <!-- Due date -->\n      <div class=\"metadata-item\">\n        <span class=\"metadata-label\">Due Date</span>\n        <div class=\"due-date\" [ngClass]=\"{'overdue': isOverdue()}\">\n          <mat-icon *ngIf=\"isOverdue()\">warning</mat-icon>\n          {{ task.dueDate | date:'mediumDate' || 'No due date' }}\n        </div>\n      </div>\n      \n      <!-- Assignee -->\n      <div class=\"metadata-item\">\n        <span class=\"metadata-label\">Assignee</span>\n        <mat-form-field appearance=\"outline\">\n          <mat-select \n            [value]=\"task.assignee\" \n            (selectionChange)=\"onAssigneeChange($event.value)\">\n            <mat-option [value]=\"''\">Unassigned</mat-option>\n            <mat-option *ngFor=\"let user of users\" [value]=\"user.id\">\n              {{ user.name }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n    </div>\n\n    <!-- Task description -->\n    <div class=\"task-section\">\n      <h2>Description</h2>\n      <div class=\"task-description\">\n        {{ task.description || 'No description provided.' }}\n      </div>\n    </div>\n\n    <!-- Task tags -->\n    <div class=\"task-section\" *ngIf=\"task.tags && task.tags.length > 0\">\n      <h2>Tags</h2>\n      <div class=\"task-tags\">\n        <span class=\"task-tag\" *ngFor=\"let tag of task.tags\">{{ tag }}</span>\n      </div>\n    </div>\n\n    <!-- Task creation info -->\n    <div class=\"task-section task-info\">\n      <div class=\"info-item\">\n        <span class=\"info-label\">Created by:</span>\n        <span>{{ getCreatorName() }}</span>\n      </div>\n      <div class=\"info-item\">\n        <span class=\"info-label\">Created:</span>\n        <span>{{ task.createdAt | date:'medium' }}</span>\n      </div>\n      <div class=\"info-item\" *ngIf=\"task.updatedAt\">\n        <span class=\"info-label\">Last updated:</span>\n        <span>{{ task.updatedAt | date:'medium' }}</span>\n      </div>\n    </div>\n  </ng-container>\n</div>\n"], "mappings": "AAAA;;;;AAIA,SAAmCA,YAAY,QAAiC,eAAe;;;;;;;;;;;ICD7FC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,0BAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;;;;;IA4BIH,EAAA,CAAAC,cAAA,qBAAmE;IACjED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAa;;;;IAF+BH,EAAA,CAAAK,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IAChEP,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAiBFV,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAW;;;;;IAa9CH,EAAA,CAAAC,cAAA,qBAAyD;IACvDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAa;;;;IAF0BH,EAAA,CAAAK,UAAA,UAAAM,OAAA,CAAAC,EAAA,CAAiB;IACtDZ,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAE,OAAA,CAAAE,IAAA,MACF;;;;;IAkBJb,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAI,MAAA,GAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAAhBH,EAAA,CAAAQ,SAAA,GAAS;IAATR,EAAA,CAAAc,iBAAA,CAAAC,OAAA,CAAS;;;;;IAHlEf,EAAA,CAAAC,cAAA,cAAoE;IAC9DD,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAgB,UAAA,IAAAC,yDAAA,mBAAqE;IACvEjB,EAAA,CAAAG,YAAA,EAAM;;;;IADmCH,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAK,UAAA,YAAAa,MAAA,CAAAC,IAAA,CAAAC,IAAA,CAAY;;;;;IAcrDpB,EAAA,CAAAC,cAAA,cAA8C;IACnBD,EAAA,CAAAI,MAAA,oBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAoC;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAA3CH,EAAA,CAAAQ,SAAA,GAAoC;IAApCR,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAqB,WAAA,OAAAC,MAAA,CAAAH,IAAA,CAAAI,SAAA,YAAoC;;;;;;;;;;;IA7FhDvB,EAAA,CAAAwB,uBAAA,GAAuC;IAErCxB,EAAA,CAAAC,cAAA,aAAgC;IACPD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAE5CH,EAAA,CAAAC,cAAA,aAA0B;IACAD,EAAA,CAAAyB,UAAA,mBAAAC,oEAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAF,OAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IACxChC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EAAA,CAAAC,cAAA,gBAAmF;IAA9CD,EAAA,CAAAyB,UAAA,mBAAAQ,oEAAA;MAAAjC,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAlC,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAG,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACvDnC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAMjCH,EAAA,CAAAC,cAAA,cAA2B;IAGMD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,0BAAqC;IAGjCD,EAAA,CAAAyB,UAAA,6BAAAW,mFAAAC,MAAA;MAAArC,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAU,OAAA,GAAAtC,EAAA,CAAA8B,aAAA;MAAA,OAAmB9B,EAAA,CAAA+B,WAAA,CAAAO,OAAA,CAAAC,cAAA,CAAAF,MAAA,CAAA9B,KAAA,CAA4B;IAAA,EAAC;IAEhDP,EAAA,CAAAgB,UAAA,KAAAwB,yDAAA,yBAEa;IACfxC,EAAA,CAAAG,YAAA,EAAa;IAKjBH,EAAA,CAAAC,cAAA,eAA2B;IACID,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAA2B;IACID,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAgB,UAAA,KAAAyB,uDAAA,sBAAgD;IAChDzC,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAA2B;IACID,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,0BAAqC;IAGjCD,EAAA,CAAAyB,UAAA,6BAAAiB,mFAAAL,MAAA;MAAArC,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAe,OAAA,GAAA3C,EAAA,CAAA8B,aAAA;MAAA,OAAmB9B,EAAA,CAAA+B,WAAA,CAAAY,OAAA,CAAAC,gBAAA,CAAAP,MAAA,CAAA9B,KAAA,CAA8B;IAAA,EAAC;IAClDP,EAAA,CAAAC,cAAA,sBAAyB;IAAAD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAa;IAChDH,EAAA,CAAAgB,UAAA,KAAA6B,yDAAA,yBAEa;IACf7C,EAAA,CAAAG,YAAA,EAAa;IAMnBH,EAAA,CAAAC,cAAA,eAA0B;IACpBD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAgB,UAAA,KAAA8B,kDAAA,kBAKM;IAGN9C,EAAA,CAAAC,cAAA,eAAoC;IAEPD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAuB;IACID,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAoC;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEnDH,EAAA,CAAAgB,UAAA,KAAA+B,kDAAA,kBAGM;IACR/C,EAAA,CAAAG,YAAA,EAAM;IACRH,EAAA,CAAAgD,qBAAA,EAAe;;;;IA7FYhD,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAc,iBAAA,CAAAmC,MAAA,CAAA9B,IAAA,CAAA+B,KAAA,CAAgB;IAmBjClD,EAAA,CAAAQ,SAAA,IAAqB;IAArBR,EAAA,CAAAK,UAAA,UAAA4C,MAAA,CAAA9B,IAAA,CAAAgC,MAAA,CAAqB,YAAAF,MAAA,CAAAG,cAAA;IAGUpD,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAK,UAAA,YAAA4C,MAAA,CAAAI,QAAA,CAAW;IAUlBrD,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAK,UAAA,YAAA4C,MAAA,CAAAK,gBAAA,GAA8B;IACxDtD,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAwC,MAAA,CAAA9B,IAAA,CAAAoC,QAAA,MACF;IAMsBvD,EAAA,CAAAQ,SAAA,GAAoC;IAApCR,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAwD,eAAA,KAAAC,GAAA,EAAAR,MAAA,CAAAS,SAAA,IAAoC;IAC7C1D,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAK,UAAA,SAAA4C,MAAA,CAAAS,SAAA,GAAiB;IAC5B1D,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAqB,WAAA,SAAA4B,MAAA,CAAA9B,IAAA,CAAAwC,OAAA,sCACF;IAQI3D,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAK,UAAA,UAAA4C,MAAA,CAAA9B,IAAA,CAAAyC,QAAA,CAAuB;IAEX5D,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAK,UAAA,aAAY;IACKL,EAAA,CAAAQ,SAAA,GAAQ;IAARR,EAAA,CAAAK,UAAA,YAAA4C,MAAA,CAAAY,KAAA,CAAQ;IAYzC7D,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAwC,MAAA,CAAA9B,IAAA,CAAA2C,WAAA,oCACF;IAIyB9D,EAAA,CAAAQ,SAAA,GAAuC;IAAvCR,EAAA,CAAAK,UAAA,SAAA4C,MAAA,CAAA9B,IAAA,CAAAC,IAAA,IAAA6B,MAAA,CAAA9B,IAAA,CAAAC,IAAA,CAAA2C,MAAA,KAAuC;IAWxD/D,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAc,iBAAA,CAAAmC,MAAA,CAAAe,cAAA,GAAsB;IAItBhE,EAAA,CAAAQ,SAAA,GAAoC;IAApCR,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAqB,WAAA,SAAA4B,MAAA,CAAA9B,IAAA,CAAA8C,SAAA,YAAoC;IAEpBjE,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAK,UAAA,SAAA4C,MAAA,CAAA9B,IAAA,CAAAI,SAAA,CAAoB;;;ADrFlD,OAAM,MAAO2C,mBAAmB;EANhCC,YAAA;IAYE;;;IAGS,KAAAN,KAAK,GAAW,EAAE;IAE3B;;;IAGS,KAAAO,OAAO,GAAG,KAAK;IAExB;;;IAGU,KAAAC,IAAI,GAAG,IAAItE,YAAY,EAAQ;IAEzC;;;IAGU,KAAAuE,MAAM,GAAG,IAAIvE,YAAY,EAAQ;IAE3C;;;IAGU,KAAAwE,YAAY,GAAG,IAAIxE,YAAY,EAAU;IAEnD;;;IAGU,KAAAyE,cAAc,GAAG,IAAIzE,YAAY,EAAU;IAErD;;;IAGA,KAAAsD,QAAQ,GAAG,CACT;MAAE9C,KAAK,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAO,CAAE,EACjC;MAAEH,KAAK,EAAE,aAAa;MAAEG,KAAK,EAAE;IAAa,CAAE,EAC9C;MAAEH,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEH,KAAK,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAM,CAAE,CACjC;;EAED;;;;EAIA4C,gBAAgBA,CAAA;IACd,OAAO,YAAY,IAAI,CAACnC,IAAI,CAACoC,QAAQ,EAAE;EACzC;EAEA;;;;EAIAH,cAAcA,CAAA;IACZ,OAAO,UAAU,IAAI,CAACjC,IAAI,CAACgC,MAAM,EAAE;EACrC;EAEA;;;;EAIAsB,cAAcA,CAAA;IACZ,MAAMtB,MAAM,GAAG,IAAI,CAACE,QAAQ,CAACqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpE,KAAK,KAAK,IAAI,CAACY,IAAI,CAACgC,MAAM,CAAC;IACpE,OAAOA,MAAM,GAAGA,MAAM,CAACzC,KAAK,GAAG,IAAI,CAACS,IAAI,CAACgC,MAAM;EACjD;EAEA;;;;EAIAZ,cAAcA,CAACY,MAAc;IAC3B,IAAI,CAACoB,YAAY,CAACK,IAAI,CAACzB,MAAM,CAAC;EAChC;EAEA;;;;EAIAP,gBAAgBA,CAACiC,UAAkB;IACjC,IAAI,CAACL,cAAc,CAACI,IAAI,CAACC,UAAU,CAAC;EACtC;EAEA;;;EAGA7C,MAAMA,CAAA;IACJ,IAAI,CAACqC,IAAI,CAACO,IAAI,EAAE;EAClB;EAEA;;;EAGAzC,QAAQA,CAAA;IACN,IAAI,CAACmC,MAAM,CAACM,IAAI,EAAE;EACpB;EAEA;;;;EAIAlB,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACvC,IAAI,CAACwC,OAAO,IAAI,IAAI,CAACxC,IAAI,CAACgC,MAAM,KAAK,MAAM,EAAE;MACrD,OAAO,KAAK;;IAGd,MAAMQ,OAAO,GAAG,IAAImB,IAAI,CAAC,IAAI,CAAC3D,IAAI,CAACwC,OAAO,CAAC;IAC3C,MAAMoB,KAAK,GAAG,IAAID,IAAI,EAAE;IACxBC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,OAAOrB,OAAO,GAAGoB,KAAK;EACxB;EAEA;;;;EAIAE,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC9D,IAAI,CAACyC,QAAQ,EAAE;MACvB,OAAO,YAAY;;IAGrB,MAAMsB,IAAI,GAAG,IAAI,CAACrB,KAAK,CAACa,IAAI,CAACS,CAAC,IAAIA,CAAC,CAACvE,EAAE,KAAK,IAAI,CAACO,IAAI,CAACyC,QAAQ,CAAC;IAC9D,OAAOsB,IAAI,GAAGA,IAAI,CAACrE,IAAI,GAAG,cAAc;EAC1C;EAEA;;;;EAIAmD,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC7C,IAAI,CAACiE,OAAO,EAAE;MACtB,OAAO,SAAS;;IAGlB,MAAMF,IAAI,GAAG,IAAI,CAACrB,KAAK,CAACa,IAAI,CAACS,CAAC,IAAIA,CAAC,CAACvE,EAAE,KAAK,IAAI,CAACO,IAAI,CAACiE,OAAO,CAAC;IAC7D,OAAOF,IAAI,GAAGA,IAAI,CAACrE,IAAI,GAAG,cAAc;EAC1C;;;uBA7IWqD,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAmB,SAAA;MAAAC,MAAA;QAAAnE,IAAA;QAAA0C,KAAA;QAAAO,OAAA;MAAA;MAAAmB,OAAA;QAAAlB,IAAA;QAAAC,MAAA;QAAAC,YAAA;QAAAC,cAAA;MAAA;MAAAgB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbhC7F,EAAA,CAAAC,cAAA,aAAmC;UAEjCD,EAAA,CAAAgB,UAAA,IAAA+E,kCAAA,iBAEM;UAGN/F,EAAA,CAAAgB,UAAA,IAAAgF,2CAAA,4BAgGe;UACjBhG,EAAA,CAAAG,YAAA,EAAM;;;UAtGEH,EAAA,CAAAQ,SAAA,GAAa;UAAbR,EAAA,CAAAK,UAAA,SAAAyF,GAAA,CAAA1B,OAAA,CAAa;UAKJpE,EAAA,CAAAQ,SAAA,GAAsB;UAAtBR,EAAA,CAAAK,UAAA,UAAAyF,GAAA,CAAA1B,OAAA,IAAA0B,GAAA,CAAA3E,IAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}