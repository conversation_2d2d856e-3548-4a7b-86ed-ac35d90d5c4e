{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Register Form Component\n * Handles user registration with name, email, and password\n */\nimport { Component, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { Validators } from '@angular/forms';\nexport let RegisterFormComponent = class RegisterFormComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed registration attempt\n     */\n    this.error = null;\n    /**\n     * Event emitted when form is submitted\n     */\n    this.formSubmit = new EventEmitter();\n    /**\n     * Event emitted when login link is clicked\n     */\n    this.login = new EventEmitter();\n    /**\n     * Flag to toggle password visibility\n     */\n    this.hidePassword = true;\n    /**\n     * Flag to toggle confirm password visibility\n     */\n    this.hideConfirmPassword = true;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the registration form\n   */\n  ngOnInit() {\n    this.initForm();\n  }\n  /**\n   * Initialize registration form with validation\n   */\n  initForm() {\n    this.registerForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8), this.passwordStrengthValidator]],\n      confirmPassword: ['', Validators.required],\n      termsAccepted: [false, Validators.requiredTrue]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  /**\n   * Custom validator for password strength\n   * @param control - Form control\n   * @returns Validation errors or null\n   */\n  passwordStrengthValidator(control) {\n    const value = control.value || '';\n    if (!value) {\n      return null;\n    }\n    const hasUpperCase = /[A-Z]+/.test(value);\n    const hasLowerCase = /[a-z]+/.test(value);\n    const hasNumeric = /[0-9]+/.test(value);\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+/.test(value);\n    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n    return !passwordValid ? {\n      passwordStrength: true\n    } : null;\n  }\n  /**\n   * Custom validator for password matching\n   * @param group - Form group\n   * @returns Validation errors or null\n   */\n  passwordMatchValidator(group) {\n    const password = group.get('password')?.value;\n    const confirmPassword = group.get('confirmPassword')?.value;\n    return password === confirmPassword ? null : {\n      passwordMismatch: true\n    };\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.registerForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.registerForm.markAllAsTouched();\n      return;\n    }\n    const {\n      name,\n      email,\n      password\n    } = this.registerForm.value;\n    this.formSubmit.emit({\n      name,\n      email,\n      password\n    });\n  }\n  /**\n   * Handle login link click\n   */\n  onLogin() {\n    this.login.emit();\n  }\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName, errorName) {\n    const control = this.registerForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n  /**\n   * Check if form has error\n   * @param errorName - Name of error\n   * @returns True if form has error\n   */\n  hasFormError(errorName) {\n    return this.registerForm.touched && this.registerForm.hasError(errorName);\n  }\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility() {\n    this.hidePassword = !this.hidePassword;\n  }\n  /**\n   * Toggle confirm password visibility\n   */\n  toggleConfirmPasswordVisibility() {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n};\n__decorate([Input()], RegisterFormComponent.prototype, \"loading\", void 0);\n__decorate([Input()], RegisterFormComponent.prototype, \"error\", void 0);\n__decorate([Output()], RegisterFormComponent.prototype, \"formSubmit\", void 0);\n__decorate([Output()], RegisterFormComponent.prototype, \"login\", void 0);\nRegisterFormComponent = __decorate([Component({\n  selector: 'app-register-form',\n  templateUrl: './register-form.component.html',\n  styleUrls: ['./register-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], RegisterFormComponent);", "map": {"version": 3, "names": ["Component", "Output", "EventEmitter", "Input", "ChangeDetectionStrategy", "Validators", "RegisterFormComponent", "constructor", "fb", "loading", "error", "formSubmit", "login", "hidePassword", "hideConfirmPassword", "ngOnInit", "initForm", "registerForm", "group", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "password", "passwordStrengthValidator", "confirmPassword", "termsAccepted", "requiredTrue", "validators", "passwordMatchValidator", "control", "value", "hasUpperCase", "test", "hasLowerCase", "hasNumeric", "hasSpecialChar", "passwordValid", "passwordStrength", "get", "passwordMismatch", "onSubmit", "invalid", "mark<PERSON>llAsTouched", "emit", "onLogin", "<PERSON><PERSON><PERSON><PERSON>", "controlName", "errorName", "touched", "hasFormError", "togglePasswordVisibility", "toggleConfirmPasswordVisibility", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\register-form\\register-form.component.ts"], "sourcesContent": ["/**\n * Register Form Component\n * Handles user registration with name, email, and password\n */\nimport { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';\n\n@Component({\n  selector: 'app-register-form',\n  templateUrl: './register-form.component.html',\n  styleUrls: ['./register-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class RegisterFormComponent implements OnInit {\n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Error message from failed registration attempt\n   */\n  @Input() error: string | null = null;\n  \n  /**\n   * Event emitted when form is submitted\n   */\n  @Output() formSubmit = new EventEmitter<{ name: string; email: string; password: string }>();\n  \n  /**\n   * Event emitted when login link is clicked\n   */\n  @Output() login = new EventEmitter<void>();\n  \n  /**\n   * Registration form group\n   */\n  registerForm!: FormGroup;\n  \n  /**\n   * Flag to toggle password visibility\n   */\n  hidePassword = true;\n  \n  /**\n   * Flag to toggle confirm password visibility\n   */\n  hideConfirmPassword = true;\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the registration form\n   */\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  /**\n   * Initialize registration form with validation\n   */\n  private initForm(): void {\n    this.registerForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [\n        Validators.required, \n        Validators.minLength(8),\n        this.passwordStrengthValidator\n      ]],\n      confirmPassword: ['', Validators.required],\n      termsAccepted: [false, Validators.requiredTrue]\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  /**\n   * Custom validator for password strength\n   * @param control - Form control\n   * @returns Validation errors or null\n   */\n  private passwordStrengthValidator(control: AbstractControl): ValidationErrors | null {\n    const value: string = control.value || '';\n    \n    if (!value) {\n      return null;\n    }\n    \n    const hasUpperCase = /[A-Z]+/.test(value);\n    const hasLowerCase = /[a-z]+/.test(value);\n    const hasNumeric = /[0-9]+/.test(value);\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+/.test(value);\n    \n    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n    \n    return !passwordValid ? { passwordStrength: true } : null;\n  }\n\n  /**\n   * Custom validator for password matching\n   * @param group - Form group\n   * @returns Validation errors or null\n   */\n  private passwordMatchValidator(group: AbstractControl): ValidationErrors | null {\n    const password = group.get('password')?.value;\n    const confirmPassword = group.get('confirmPassword')?.value;\n    \n    return password === confirmPassword ? null : { passwordMismatch: true };\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.registerForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.registerForm.markAllAsTouched();\n      return;\n    }\n    \n    const { name, email, password } = this.registerForm.value;\n    this.formSubmit.emit({ name, email, password });\n  }\n\n  /**\n   * Handle login link click\n   */\n  onLogin(): void {\n    this.login.emit();\n  }\n\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName: string, errorName: string): boolean {\n    const control = this.registerForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n\n  /**\n   * Check if form has error\n   * @param errorName - Name of error\n   * @returns True if form has error\n   */\n  hasFormError(errorName: string): boolean {\n    return this.registerForm.touched && this.registerForm.hasError(errorName);\n  }\n\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility(): void {\n    this.hidePassword = !this.hidePassword;\n  }\n\n  /**\n   * Toggle confirm password visibility\n   */\n  toggleConfirmPasswordVisibility(): void {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAUC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,eAAe;AACvG,SAAiCC,UAAU,QAA2C,gBAAgB;AAQ/F,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAoChC;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAvCtB;;;IAGS,KAAAC,OAAO,GAAG,KAAK;IAExB;;;IAGS,KAAAC,KAAK,GAAkB,IAAI;IAEpC;;;IAGU,KAAAC,UAAU,GAAG,IAAIT,YAAY,EAAqD;IAE5F;;;IAGU,KAAAU,KAAK,GAAG,IAAIV,YAAY,EAAQ;IAO1C;;;IAGA,KAAAW,YAAY,GAAG,IAAI;IAEnB;;;IAGA,KAAAC,mBAAmB,GAAG,IAAI;EAMY;EAEtC;;;;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEA;;;EAGQA,QAAQA,CAAA;IACd,IAAI,CAACC,YAAY,GAAG,IAAI,CAACT,EAAE,CAACU,KAAK,CAAC;MAChCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACd,UAAU,CAACe,QAAQ,EAAEf,UAAU,CAACgB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjB,UAAU,CAACe,QAAQ,EAAEf,UAAU,CAACiB,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACblB,UAAU,CAACe,QAAQ,EACnBf,UAAU,CAACgB,SAAS,CAAC,CAAC,CAAC,EACvB,IAAI,CAACG,yBAAyB,CAC/B,CAAC;MACFC,eAAe,EAAE,CAAC,EAAE,EAAEpB,UAAU,CAACe,QAAQ,CAAC;MAC1CM,aAAa,EAAE,CAAC,KAAK,EAAErB,UAAU,CAACsB,YAAY;KAC/C,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEA;;;;;EAKQL,yBAAyBA,CAACM,OAAwB;IACxD,MAAMC,KAAK,GAAWD,OAAO,CAACC,KAAK,IAAI,EAAE;IAEzC,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI;;IAGb,MAAMC,YAAY,GAAG,QAAQ,CAACC,IAAI,CAACF,KAAK,CAAC;IACzC,MAAMG,YAAY,GAAG,QAAQ,CAACD,IAAI,CAACF,KAAK,CAAC;IACzC,MAAMI,UAAU,GAAG,QAAQ,CAACF,IAAI,CAACF,KAAK,CAAC;IACvC,MAAMK,cAAc,GAAG,wCAAwC,CAACH,IAAI,CAACF,KAAK,CAAC;IAE3E,MAAMM,aAAa,GAAGL,YAAY,IAAIE,YAAY,IAAIC,UAAU,IAAIC,cAAc;IAElF,OAAO,CAACC,aAAa,GAAG;MAAEC,gBAAgB,EAAE;IAAI,CAAE,GAAG,IAAI;EAC3D;EAEA;;;;;EAKQT,sBAAsBA,CAACX,KAAsB;IACnD,MAAMK,QAAQ,GAAGL,KAAK,CAACqB,GAAG,CAAC,UAAU,CAAC,EAAER,KAAK;IAC7C,MAAMN,eAAe,GAAGP,KAAK,CAACqB,GAAG,CAAC,iBAAiB,CAAC,EAAER,KAAK;IAE3D,OAAOR,QAAQ,KAAKE,eAAe,GAAG,IAAI,GAAG;MAAEe,gBAAgB,EAAE;IAAI,CAAE;EACzE;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACxB,YAAY,CAACyB,OAAO,IAAI,IAAI,CAACjC,OAAO,EAAE;MAC7C;MACA,IAAI,CAACQ,YAAY,CAAC0B,gBAAgB,EAAE;MACpC;;IAGF,MAAM;MAAExB,IAAI;MAAEG,KAAK;MAAEC;IAAQ,CAAE,GAAG,IAAI,CAACN,YAAY,CAACc,KAAK;IACzD,IAAI,CAACpB,UAAU,CAACiC,IAAI,CAAC;MAAEzB,IAAI;MAAEG,KAAK;MAAEC;IAAQ,CAAE,CAAC;EACjD;EAEA;;;EAGAsB,OAAOA,CAAA;IACL,IAAI,CAACjC,KAAK,CAACgC,IAAI,EAAE;EACnB;EAEA;;;;;;EAMAE,QAAQA,CAACC,WAAmB,EAAEC,SAAiB;IAC7C,MAAMlB,OAAO,GAAG,IAAI,CAACb,YAAY,CAACsB,GAAG,CAACQ,WAAW,CAAC;IAClD,OAAO,CAAC,EAAEjB,OAAO,IAAIA,OAAO,CAACmB,OAAO,IAAInB,OAAO,CAACgB,QAAQ,CAACE,SAAS,CAAC,CAAC;EACtE;EAEA;;;;;EAKAE,YAAYA,CAACF,SAAiB;IAC5B,OAAO,IAAI,CAAC/B,YAAY,CAACgC,OAAO,IAAI,IAAI,CAAChC,YAAY,CAAC6B,QAAQ,CAACE,SAAS,CAAC;EAC3E;EAEA;;;EAGAG,wBAAwBA,CAAA;IACtB,IAAI,CAACtC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGAuC,+BAA+BA,CAAA;IAC7B,IAAI,CAACtC,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;CACD;AAvJUuC,UAAA,EAARlD,KAAK,EAAE,C,qDAAiB;AAKhBkD,UAAA,EAARlD,KAAK,EAAE,C,mDAA6B;AAK3BkD,UAAA,EAATpD,MAAM,EAAE,C,wDAAoF;AAKnFoD,UAAA,EAATpD,MAAM,EAAE,C,mDAAkC;AAnBhCK,qBAAqB,GAAA+C,UAAA,EANjCrD,SAAS,CAAC;EACTsD,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC,CAAC;EAC7CC,eAAe,EAAErD,uBAAuB,CAACsD;CAC1C,CAAC,C,EACWpD,qBAAqB,CA2JjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}