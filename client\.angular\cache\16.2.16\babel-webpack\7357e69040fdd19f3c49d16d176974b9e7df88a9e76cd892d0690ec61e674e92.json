{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { MatCommonModule } from '@angular/material/core';\nclass MatDivider {\n  constructor() {\n    this._vertical = false;\n    this._inset = false;\n  }\n  /** Whether the divider is vertically aligned. */\n  get vertical() {\n    return this._vertical;\n  }\n  set vertical(value) {\n    this._vertical = coerceBooleanProperty(value);\n  }\n  /** Whether the divider is an inset divider. */\n  get inset() {\n    return this._inset;\n  }\n  set inset(value) {\n    this._inset = coerceBooleanProperty(value);\n  }\n  static {\n    this.ɵfac = function MatDivider_Factory(t) {\n      return new (t || MatDivider)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatDivider,\n      selectors: [[\"mat-divider\"]],\n      hostAttrs: [\"role\", \"separator\", 1, \"mat-divider\"],\n      hostVars: 7,\n      hostBindings: function MatDivider_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-orientation\", ctx.vertical ? \"vertical\" : \"horizontal\");\n          i0.ɵɵclassProp(\"mat-divider-vertical\", ctx.vertical)(\"mat-divider-horizontal\", !ctx.vertical)(\"mat-divider-inset\", ctx.inset);\n        }\n      },\n      inputs: {\n        vertical: \"vertical\",\n        inset: \"inset\"\n      },\n      decls: 0,\n      vars: 0,\n      template: function MatDivider_Template(rf, ctx) {},\n      styles: [\".mat-divider{--mat-divider-width:1px;display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color);border-top-width:var(--mat-divider-width)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color);border-right-width:var(--mat-divider-width)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDivider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-divider',\n      host: {\n        'role': 'separator',\n        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n        '[class.mat-divider-vertical]': 'vertical',\n        '[class.mat-divider-horizontal]': '!vertical',\n        '[class.mat-divider-inset]': 'inset',\n        'class': 'mat-divider'\n      },\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-divider{--mat-divider-width:1px;display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color);border-top-width:var(--mat-divider-width)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color);border-right-width:var(--mat-divider-width)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"]\n    }]\n  }], null, {\n    vertical: [{\n      type: Input\n    }],\n    inset: [{\n      type: Input\n    }]\n  });\n})();\nclass MatDividerModule {\n  static {\n    this.ɵfac = function MatDividerModule_Factory(t) {\n      return new (t || MatDividerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatDividerModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatDivider, MatCommonModule],\n      declarations: [MatDivider]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatDivider, MatDividerModule };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "NgModule", "coerceBooleanProperty", "MatCommonModule", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "_vertical", "_inset", "vertical", "value", "inset", "ɵfac", "MatDivider_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatDivider_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵclassProp", "inputs", "decls", "vars", "template", "MatDivider_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "MatDividerModule", "MatDividerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/@angular+material@16.2.14_4056c724f738b156ccd72c3e8383c8cb/node_modules/@angular/material/fesm2022/divider.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { MatCommonModule } from '@angular/material/core';\n\nclass MatDivider {\n    constructor() {\n        this._vertical = false;\n        this._inset = false;\n    }\n    /** Whether the divider is vertically aligned. */\n    get vertical() {\n        return this._vertical;\n    }\n    set vertical(value) {\n        this._vertical = coerceBooleanProperty(value);\n    }\n    /** Whether the divider is an inset divider. */\n    get inset() {\n        return this._inset;\n    }\n    set inset(value) {\n        this._inset = coerceBooleanProperty(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDivider, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: <PERSON><PERSON><PERSON><PERSON>, selector: \"mat-divider\", inputs: { vertical: \"vertical\", inset: \"inset\" }, host: { attributes: { \"role\": \"separator\" }, properties: { \"attr.aria-orientation\": \"vertical ? \\\"vertical\\\" : \\\"horizontal\\\"\", \"class.mat-divider-vertical\": \"vertical\", \"class.mat-divider-horizontal\": \"!vertical\", \"class.mat-divider-inset\": \"inset\" }, classAttribute: \"mat-divider\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-divider{--mat-divider-width:1px;display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color);border-top-width:var(--mat-divider-width)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color);border-right-width:var(--mat-divider-width)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDivider, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-divider', host: {\n                        'role': 'separator',\n                        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n                        '[class.mat-divider-vertical]': 'vertical',\n                        '[class.mat-divider-horizontal]': '!vertical',\n                        '[class.mat-divider-inset]': 'inset',\n                        'class': 'mat-divider',\n                    }, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-divider{--mat-divider-width:1px;display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color);border-top-width:var(--mat-divider-width)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color);border-right-width:var(--mat-divider-width)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"] }]\n        }], propDecorators: { vertical: [{\n                type: Input\n            }], inset: [{\n                type: Input\n            }] } });\n\nclass MatDividerModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDividerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDividerModule, declarations: [MatDivider], imports: [MatCommonModule], exports: [MatDivider, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDividerModule, imports: [MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatDividerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatDivider, MatCommonModule],\n                    declarations: [MatDivider],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatDivider, MatDividerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACtG,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,eAAe,QAAQ,wBAAwB;AAExD,MAAMC,UAAU,CAAC;EACbC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,MAAM,GAAG,KAAK;EACvB;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,SAAS;EACzB;EACA,IAAIE,QAAQA,CAACC,KAAK,EAAE;IAChB,IAAI,CAACH,SAAS,GAAGJ,qBAAqB,CAACO,KAAK,CAAC;EACjD;EACA;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACH,MAAM;EACtB;EACA,IAAIG,KAAKA,CAACD,KAAK,EAAE;IACb,IAAI,CAACF,MAAM,GAAGL,qBAAqB,CAACO,KAAK,CAAC;EAC9C;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,mBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFT,UAAU;IAAA,CAAmD;EAAE;EACzK;IAAS,IAAI,CAACU,IAAI,kBAD8ElB,EAAE,CAAAmB,iBAAA;MAAAC,IAAA,EACJZ,UAAU;MAAAa,SAAA;MAAAC,SAAA,WAA2G,WAAW;MAAAC,QAAA;MAAAC,YAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD9H1B,EAAE,CAAA4B,WAAA,qBAAAD,GAAA,CAAAf,QAAA;UAAFZ,EAAE,CAAA6B,WAAA,yBAAAF,GAAA,CAAAf,QAAA,6BAAAe,GAAA,CAAAf,QAAA,uBAAAe,GAAA,CAAAb,KAAA;QAAA;MAAA;MAAAgB,MAAA;QAAAlB,QAAA;QAAAE,KAAA;MAAA;MAAAiB,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,oBAAAR,EAAA,EAAAC,GAAA;MAAAQ,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAC68B;EAAE;AACrjC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGtC,EAAE,CAAAuC,iBAAA,CAGX/B,UAAU,EAAc,CAAC;IACxGY,IAAI,EAAEnB,SAAS;IACfuC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAa;MAAEC,IAAI,EAAE;QAC5B,MAAM,EAAE,WAAW;QACnB,yBAAyB,EAAE,sCAAsC;QACjE,8BAA8B,EAAE,UAAU;QAC1C,gCAAgC,EAAE,WAAW;QAC7C,2BAA2B,EAAE,OAAO;QACpC,OAAO,EAAE;MACb,CAAC;MAAET,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAElC,iBAAiB,CAACyC,IAAI;MAAEN,eAAe,EAAElC,uBAAuB,CAACyC,MAAM;MAAET,MAAM,EAAE,CAAC,qcAAqc;IAAE,CAAC;EACtkB,CAAC,CAAC,QAAkB;IAAEvB,QAAQ,EAAE,CAAC;MACzBQ,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAEU,KAAK,EAAE,CAAC;MACRM,IAAI,EAAEhB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyC,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAAC9B,IAAI,YAAA+B,yBAAA7B,CAAA;MAAA,YAAAA,CAAA,IAAwF4B,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACE,IAAI,kBArB8E/C,EAAE,CAAAgD,gBAAA;MAAA5B,IAAA,EAqBSyB;IAAgB,EAAmG;EAAE;EAChO;IAAS,IAAI,CAACI,IAAI,kBAtB8EjD,EAAE,CAAAkD,gBAAA;MAAAC,OAAA,GAsBqC5C,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AACjL;AACA;EAAA,QAAA+B,SAAA,oBAAAA,SAAA,KAxBoGtC,EAAE,CAAAuC,iBAAA,CAwBXM,gBAAgB,EAAc,CAAC;IAC9GzB,IAAI,EAAEf,QAAQ;IACdmC,IAAI,EAAE,CAAC;MACCW,OAAO,EAAE,CAAC5C,eAAe,CAAC;MAC1B6C,OAAO,EAAE,CAAC5C,UAAU,EAAED,eAAe,CAAC;MACtC8C,YAAY,EAAE,CAAC7C,UAAU;IAC7B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,UAAU,EAAEqC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}