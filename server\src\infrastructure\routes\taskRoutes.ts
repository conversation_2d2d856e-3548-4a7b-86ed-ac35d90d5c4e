/**
 * Task Routes
 * Defines API endpoints for task operations
 */
import { Router } from 'express';
import {
  createTask,
  getTask,
  getAllTasks,
  updateTask,
  deleteTask,
} from '../controllers/taskController';
import { protect, restrictTo } from '../middleware/authMiddleware';
import { UserRole } from '../../domain/entities/User';

// Create router instance
const router = Router();

// Apply authentication middleware to all routes
router.use(protect);

// Define routes
router
  .route('/')
  .get(getAllTasks)
  .post(createTask);

router
  .route('/:id')
  .get(getTask)
  .put(updateTask)
  .delete(restrictTo(UserRole.ADMIN), deleteTask);

export default router;
