/**
 * Header Component
 * Main navigation header for the application
 */
import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { User } from '../../models/user.model';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class HeaderComponent implements OnInit {
  /**
   * Current authenticated user
   */
  currentUser: User | null = null;
  
  /**
   * Flag to show/hide the user menu
   */
  isUserMenuOpen = false;

  /**
   * Constructor with dependency injection
   * @param authService - Authentication service
   * @param router - Router for navigation
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(
    private authService: AuthService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Sets up subscription to current user
   */
  ngOnInit(): void {
    // Subscribe to current user changes
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.cdr.markForCheck();
    });
  }

  /**
   * Toggle the user menu dropdown
   */
  toggleUserMenu(): void {
    this.isUserMenuOpen = !this.isUserMenuOpen;
    this.cdr.markForCheck();
  }

  /**
   * Close the user menu dropdown
   */
  closeUserMenu(): void {
    this.isUserMenuOpen = false;
    this.cdr.markForCheck();
  }

  /**
   * Navigate to user profile page
   */
  goToProfile(): void {
    this.closeUserMenu();
    this.router.navigate(['/profile']);
  }

  /**
   * Log out the current user
   */
  logout(): void {
    this.closeUserMenu();
    this.authService.logout();
  }
}
