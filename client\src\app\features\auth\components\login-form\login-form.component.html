<!-- Login form container -->
<div class="login-form-container">
  <!-- Error message -->
  <app-error-message 
    *ngIf="error" 
    [message]="error">
  </app-error-message>

  <!-- Login form -->
  <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
    <!-- Email field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Email</mat-label>
      <input 
        matInput 
        type="email" 
        formControlName="email" 
        placeholder="Enter your email"
        autocomplete="email">
      <mat-icon matPrefix>email</mat-icon>
      <mat-error *ngIf="hasError('email', 'required')">
        Email is required
      </mat-error>
      <mat-error *ngIf="hasError('email', 'email')">
        Please enter a valid email address
      </mat-error>
    </mat-form-field>

    <!-- Password field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Password</mat-label>
      <input 
        matInput 
        [type]="hidePassword ? 'password' : 'text'" 
        formControlName="password"
        placeholder="Enter your password"
        autocomplete="current-password">
      <mat-icon matPrefix>lock</mat-icon>
      <button 
        mat-icon-button 
        matSuffix 
        type="button"
        (click)="togglePasswordVisibility()" 
        [attr.aria-label]="'Hide password'" 
        [attr.aria-pressed]="hidePassword">
        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
      </button>
      <mat-error *ngIf="hasError('password', 'required')">
        Password is required
      </mat-error>
      <mat-error *ngIf="hasError('password', 'minlength')">
        Password must be at least 8 characters
      </mat-error>
    </mat-form-field>

    <!-- Remember me checkbox -->
    <div class="remember-forgot">
      <mat-checkbox formControlName="rememberMe" color="primary">
        Remember me
      </mat-checkbox>
      <a href="javascript:void(0)" (click)="onForgotPassword()" (keydown.enter)="onForgotPassword()" (keydown.space)="onForgotPassword()" tabindex="0" role="button" class="forgot-link">Forgot password?</a>
    </div>

    <!-- Submit button -->
    <button 
      mat-raised-button 
      color="primary" 
      type="submit" 
      class="submit-button"
      [disabled]="loading">
      <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
      <span *ngIf="!loading">Login</span>
    </button>

    <!-- Register link -->
    <div class="register-link">
      Don't have an account?
      <a href="javascript:void(0)" (click)="onRegister()" (keydown.enter)="onRegister()" (keydown.space)="onRegister()" tabindex="0" role="button">Register</a>
    </div>
  </form>
</div>
