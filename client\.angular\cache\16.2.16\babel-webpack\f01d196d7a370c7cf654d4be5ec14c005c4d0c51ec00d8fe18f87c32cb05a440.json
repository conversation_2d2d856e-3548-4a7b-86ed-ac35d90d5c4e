{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"./core/components/header/header.component\";\nimport * as i5 from \"./core/components/footer/footer.component\";\nimport * as i6 from \"./core/components/sidenav/sidenav.component\";\nfunction AppComponent_app_header_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-header\");\n  }\n}\nfunction AppComponent_app_sidenav_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-sidenav\");\n  }\n}\nexport class AppComponent {\n  /**\n   * Constructor with dependency injection\n   * @param router - Angular Router service\n   * @param authService - Authentication service\n   */\n  constructor(router, authService) {\n    this.router = router;\n    this.authService = authService;\n    /**\n     * Title of the application\n     */\n    this.title = 'Task Management';\n    /**\n     * Flag to determine if the user is authenticated\n     */\n    this.isAuthenticated = false;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up route change tracking and authentication status\n   */\n  ngOnInit() {\n    // Track route changes for analytics or other purposes\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n      // Measure performance metrics\n      this.measurePerformanceMetrics();\n    });\n    // Subscribe to authentication status changes\n    this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n      this.isAuthenticated = isAuthenticated;\n    });\n  }\n  /**\n   * Measures key performance metrics for the application\n   * Focuses on LCP (Largest Contentful Paint) and CLS (Cumulative Layout Shift)\n   */\n  measurePerformanceMetrics() {\n    // Use Performance Observer to measure LCP\n    if ('PerformanceObserver' in window) {\n      // LCP measurement\n      const lcpObserver = new PerformanceObserver(entryList => {\n        const entries = entryList.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        console.log('LCP:', lastEntry.startTime);\n        console.log('LCP Element:', lastEntry.element);\n      });\n      lcpObserver.observe({\n        type: 'largest-contentful-paint',\n        buffered: true\n      });\n      // CLS measurement\n      const clsObserver = new PerformanceObserver(entryList => {\n        const entries = entryList.getEntries();\n        let clsValue = 0;\n        entries.forEach(entry => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        console.log('CLS:', clsValue);\n      });\n      clsObserver.observe({\n        type: 'layout-shift',\n        buffered: true\n      });\n    }\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 7,\n      vars: 2,\n      consts: [[1, \"app-container\"], [4, \"ngIf\"], [1, \"main-content\"], [1, \"content-container\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AppComponent_app_header_1_Template, 1, 0, \"app-header\", 1);\n          i0.ɵɵelementStart(2, \"main\", 2);\n          i0.ɵɵtemplate(3, AppComponent_app_sidenav_3_Template, 1, 0, \"app-sidenav\", 1);\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵelement(5, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(6, \"app-footer\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        }\n      },\n      dependencies: [i3.NgIf, i1.RouterOutlet, i4.HeaderComponent, i5.FooterComponent, i6.SidenavComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.app-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100vh;\\n  background-color: #f5f5f5;\\n}\\n\\n\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n  position: relative;\\n  margin-top: 64px; \\n\\n}\\n\\n\\n\\n.content-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 20px;\\n  overflow-y: auto;\\n  max-width: 100%;\\n  transition: padding 0.3s ease;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .content-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n}\\n\\n\\n*[_ngcontent-%COMP%] {\\n  will-change: transform, opacity;\\n  contain: layout style paint;\\n}\\n\\n\\n\\n[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #1976d2;\\n  outline-offset: 2px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOztFQUFBO0FBSUEseUNBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGlCQUFBO0VBQ0EseUJBQUE7QUFBRjs7QUFHQSx1Q0FBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLE9BQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBLEVBQUEseUJBQUE7QUFBRjs7QUFHQSx3Q0FBQTtBQUNBO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSw2QkFBQTtBQUFGOztBQUdBLDJCQUFBO0FBQ0E7RUFDRTtJQUNFLHNCQUFBO0VBQUY7RUFHQTtJQUNFLGFBQUE7RUFERjtBQUNGO0FBSUEsOEJBQUE7QUFDQTtFQUNFLCtCQUFBO0VBQ0EsMkJBQUE7QUFGRjs7QUFLQSwrQkFBQTtBQUNBO0VBQ0UsMEJBQUE7RUFDQSxtQkFBQTtBQUZGIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNYWluIGFwcGxpY2F0aW9uIHN0eWxlc1xuICovXG5cbi8qIENvbnRhaW5lciBmb3IgdGhlIGVudGlyZSBhcHBsaWNhdGlvbiAqL1xuLmFwcC1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbn1cblxuLyogTWFpbiBjb250ZW50IGFyZWEgd2l0aCBmbGV4IGxheW91dCAqL1xuLm1haW4tY29udGVudCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXg6IDE7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgbWFyZ2luLXRvcDogNjRweDsgLyogSGVpZ2h0IG9mIHRoZSBoZWFkZXIgKi9cbn1cblxuLyogQ29udGFpbmVyIGZvciByb3V0ZXIgb3V0bGV0IGNvbnRlbnQgKi9cbi5jb250ZW50LWNvbnRhaW5lciB7XG4gIGZsZXg6IDE7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIG92ZXJmbG93LXk6IGF1dG87XG4gIG1heC13aWR0aDogMTAwJTtcbiAgdHJhbnNpdGlvbjogcGFkZGluZyAwLjNzIGVhc2U7XG59XG5cbi8qIFJlc3BvbnNpdmUgYWRqdXN0bWVudHMgKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAubWFpbi1jb250ZW50IHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICB9XG4gIFxuICAuY29udGVudC1jb250YWluZXIge1xuICAgIHBhZGRpbmc6IDE2cHg7XG4gIH1cbn1cblxuLyogUGVyZm9ybWFuY2Ugb3B0aW1pemF0aW9ucyAqL1xuKiB7XG4gIHdpbGwtY2hhbmdlOiB0cmFuc2Zvcm0sIG9wYWNpdHk7XG4gIGNvbnRhaW46IGxheW91dCBzdHlsZSBwYWludDtcbn1cblxuLyogQWNjZXNzaWJpbGl0eSBpbXByb3ZlbWVudHMgKi9cbjpmb2N1cyB7XG4gIG91dGxpbmU6IDJweCBzb2xpZCAjMTk3NmQyO1xuICBvdXRsaW5lLW9mZnNldDogMnB4O1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationEnd", "filter", "i0", "ɵɵelement", "AppComponent", "constructor", "router", "authService", "title", "isAuthenticated", "ngOnInit", "events", "pipe", "event", "subscribe", "measurePerformanceMetrics", "isAuthenticated$", "window", "lcpObserver", "PerformanceObserver", "entryList", "entries", "getEntries", "lastEntry", "length", "console", "log", "startTime", "element", "observe", "type", "buffered", "clsObserver", "clsValue", "for<PERSON>ach", "entry", "hadRecentInput", "value", "ɵɵdirectiveInject", "i1", "Router", "i2", "AuthService", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "AppComponent_app_header_1_Template", "AppComponent_app_sidenav_3_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\app.component.html"], "sourcesContent": ["/**\n * Root component of the application\n * Serves as the main container for all other components\n */\nimport { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { AuthService } from './core/services/auth.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class AppComponent implements OnInit {\n  /**\n   * Title of the application\n   */\n  title = 'Task Management';\n  \n  /**\n   * Flag to determine if the user is authenticated\n   */\n  isAuthenticated = false;\n\n  /**\n   * Constructor with dependency injection\n   * @param router - Angular Router service\n   * @param authService - Authentication service\n   */\n  constructor(\n    private router: Router,\n    private authService: AuthService\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up route change tracking and authentication status\n   */\n  ngOnInit(): void {\n    // Track route changes for analytics or other purposes\n    this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd)\n    ).subscribe(() => {\n      // Measure performance metrics\n      this.measurePerformanceMetrics();\n    });\n\n    // Subscribe to authentication status changes\n    this.authService.isAuthenticated$.subscribe(\n      isAuthenticated => {\n        this.isAuthenticated = isAuthenticated;\n      }\n    );\n  }\n\n  /**\n   * Measures key performance metrics for the application\n   * Focuses on LCP (Largest Contentful Paint) and CLS (Cumulative Layout Shift)\n   */\n  private measurePerformanceMetrics(): void {\n    // Use Performance Observer to measure LCP\n    if ('PerformanceObserver' in window) {\n      // LCP measurement\n      const lcpObserver = new PerformanceObserver((entryList) => {\n        const entries = entryList.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        console.log('LCP:', lastEntry.startTime);\n        console.log('LCP Element:', lastEntry.element);\n      });\n      \n      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });\n      \n      // CLS measurement\n      const clsObserver = new PerformanceObserver((entryList) => {\n        const entries = entryList.getEntries();\n        let clsValue = 0;\n        \n        entries.forEach(entry => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        \n        console.log('CLS:', clsValue);\n      });\n      \n      clsObserver.observe({ type: 'layout-shift', buffered: true });\n    }\n  }\n}\n", "<!-- Main application layout -->\n<div class=\"app-container\">\n  <!-- Header component with navigation -->\n  <app-header *ngIf=\"isAuthenticated\"></app-header>\n\n  <!-- Main content area -->\n  <main class=\"main-content\">\n    <!-- Side navigation for authenticated users -->\n    <app-sidenav *ngIf=\"isAuthenticated\"></app-sidenav>\n    \n    <!-- Router outlet for displaying page content -->\n    <div class=\"content-container\">\n      <router-outlet></router-outlet>\n    </div>\n  </main>\n\n  <!-- Footer component -->\n  <app-footer></app-footer>\n</div>\n"], "mappings": "AAKA,SAAiBA,aAAa,QAAQ,iBAAiB;AACvD,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;;ICHrCC,EAAA,CAAAC,SAAA,iBAAiD;;;;;IAK/CD,EAAA,CAAAC,SAAA,kBAAmD;;;ADOvD,OAAM,MAAOC,YAAY;EAWvB;;;;;EAKAC,YACUC,MAAc,EACdC,WAAwB;IADxB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAjBrB;;;IAGA,KAAAC,KAAK,GAAG,iBAAiB;IAEzB;;;IAGA,KAAAC,eAAe,GAAG,KAAK;EAUpB;EAEH;;;;EAIAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,MAAM,CAACK,MAAM,CAACC,IAAI,CACrBX,MAAM,CAACY,KAAK,IAAIA,KAAK,YAAYb,aAAa,CAAC,CAChD,CAACc,SAAS,CAAC,MAAK;MACf;MACA,IAAI,CAACC,yBAAyB,EAAE;IAClC,CAAC,CAAC;IAEF;IACA,IAAI,CAACR,WAAW,CAACS,gBAAgB,CAACF,SAAS,CACzCL,eAAe,IAAG;MAChB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACxC,CAAC,CACF;EACH;EAEA;;;;EAIQM,yBAAyBA,CAAA;IAC/B;IACA,IAAI,qBAAqB,IAAIE,MAAM,EAAE;MACnC;MACA,MAAMC,WAAW,GAAG,IAAIC,mBAAmB,CAAEC,SAAS,IAAI;QACxD,MAAMC,OAAO,GAAGD,SAAS,CAACE,UAAU,EAAE;QACtC,MAAMC,SAAS,GAAGF,OAAO,CAACA,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC;QAC7CC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEH,SAAS,CAACI,SAAS,CAAC;QACxCF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEH,SAAS,CAACK,OAAO,CAAC;MAChD,CAAC,CAAC;MAEFV,WAAW,CAACW,OAAO,CAAC;QAAEC,IAAI,EAAE,0BAA0B;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAEzE;MACA,MAAMC,WAAW,GAAG,IAAIb,mBAAmB,CAAEC,SAAS,IAAI;QACxD,MAAMC,OAAO,GAAGD,SAAS,CAACE,UAAU,EAAE;QACtC,IAAIW,QAAQ,GAAG,CAAC;QAEhBZ,OAAO,CAACa,OAAO,CAACC,KAAK,IAAG;UACtB,IAAI,CAACA,KAAK,CAACC,cAAc,EAAE;YACzBH,QAAQ,IAAIE,KAAK,CAACE,KAAK;;QAE3B,CAAC,CAAC;QAEFZ,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEO,QAAQ,CAAC;MAC/B,CAAC,CAAC;MAEFD,WAAW,CAACH,OAAO,CAAC;QAAEC,IAAI,EAAE,cAAc;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;;EAEjE;;;uBA3EW3B,YAAY,EAAAF,EAAA,CAAAoC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAtC,EAAA,CAAAoC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAZtC,YAAY;MAAAuC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdzB/C,EAAA,CAAAiD,cAAA,aAA2B;UAEzBjD,EAAA,CAAAkD,UAAA,IAAAC,kCAAA,wBAAiD;UAGjDnD,EAAA,CAAAiD,cAAA,cAA2B;UAEzBjD,EAAA,CAAAkD,UAAA,IAAAE,mCAAA,yBAAmD;UAGnDpD,EAAA,CAAAiD,cAAA,aAA+B;UAC7BjD,EAAA,CAAAC,SAAA,oBAA+B;UACjCD,EAAA,CAAAqD,YAAA,EAAM;UAIRrD,EAAA,CAAAC,SAAA,iBAAyB;UAC3BD,EAAA,CAAAqD,YAAA,EAAM;;;UAfSrD,EAAA,CAAAsD,SAAA,GAAqB;UAArBtD,EAAA,CAAAuD,UAAA,SAAAP,GAAA,CAAAzC,eAAA,CAAqB;UAKlBP,EAAA,CAAAsD,SAAA,GAAqB;UAArBtD,EAAA,CAAAuD,UAAA,SAAAP,GAAA,CAAAzC,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}