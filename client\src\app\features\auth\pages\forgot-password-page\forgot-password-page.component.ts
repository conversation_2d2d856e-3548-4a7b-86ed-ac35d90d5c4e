/**
 * Forgot Password Page Component
 * Page for requesting password reset
 */
import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AuthService } from '../../../../core/services/auth.service';
import { NotificationService } from '../../../../core/services/notification.service';

@Component({
  selector: 'app-forgot-password-page',
  templateUrl: './forgot-password-page.component.html',
  styleUrls: ['./forgot-password-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ForgotPasswordPageComponent implements OnInit, OnD<PERSON>roy {
  /**
   * Loading state indicator
   */
  loading = false;
  
  /**
   * Error message from failed request
   */
  error: string | null = null;
  
  /**
   * Success message after successful request
   */
  success: string | null = null;
  
  /**
   * Subject for unsubscribing from observables
   */
  private destroy$ = new Subject<void>();

  /**
   * Constructor with dependency injection
   * @param authService - Authentication service
   * @param notificationService - Notification service for displaying messages
   * @param router - Router for navigation
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(
    private authService: AuthService,
    private notificationService: NotificationService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Checks if user is already authenticated
   */
  ngOnInit(): void {
    // Redirect to dashboard if already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
    }
  }

  /**
   * Lifecycle hook that is called before component destruction
   * Unsubscribes from observables
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Handle forgot password form submission
   * @param data - Form data with email
   */
  onFormSubmit(data: { email: string }): void {
    this.loading = true;
    this.error = null;
    this.success = null;
    this.cdr.markForCheck();
    
    this.authService.forgotPassword(data.email)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.loading = false;
          this.success = 'Password reset instructions have been sent to your email.';
          this.cdr.markForCheck();
        },
        error: (err) => {
          this.loading = false;
          this.error = err.message || 'Failed to send password reset email. Please try again.';
          console.error('Forgot password error:', err);
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Navigate to login page
   */
  onLogin(): void {
    this.router.navigate(['/auth/login']);
  }
}
