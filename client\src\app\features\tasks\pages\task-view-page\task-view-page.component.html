<!-- Task view page container -->
<div class="task-view-page-container">
  <!-- Back button -->
  <div class="page-navigation">
    <button mat-button (click)="goBack()" class="back-button">
      <mat-icon>arrow_back</mat-icon>
      Back to Tasks
    </button>
  </div>
  
  <!-- Error message -->
  <app-error-message 
    *ngIf="error" 
    [message]="error" 
    [showRetry]="true"
    [onRetry]="loadTask.bind(this)">
  </app-error-message>
  
  <!-- Task detail component -->
  <app-task-detail
    *ngIf="!error"
    [task]="task"
    [users]="users"
    [loading]="loading"
    (edit)="onEdit()"
    (delete)="onDelete()"
    (statusChange)="onStatusChange($event)"
    (assigneeChange)="onAssigneeChange($event)">
  </app-task-detail>
</div>
