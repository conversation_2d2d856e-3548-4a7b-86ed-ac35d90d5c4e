{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction SidenavComponent_li_7_a_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r3.label);\n  }\n}\nfunction SidenavComponent_li_7_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 12)(1, \"mat-icon\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SidenavComponent_li_7_a_1_span_3_Template, 2, 1, \"span\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r4.isActive(item_r3.route));\n    i0.ɵɵproperty(\"routerLink\", item_r3.route);\n    i0.ɵɵattribute(\"title\", item_r3.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isExpanded);\n  }\n}\nfunction SidenavComponent_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, SidenavComponent_li_7_a_1_Template, 4, 6, \"a\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r3.requiresAdmin || ctx_r0.isAdmin());\n  }\n}\nfunction SidenavComponent_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser == null ? null : ctx_r1.currentUser.name == null ? null : ctx_r1.currentUser.name.charAt(0));\n  }\n}\nfunction SidenavComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"span\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 18);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.currentUser == null ? null : ctx_r2.currentUser.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.currentUser == null ? null : ctx_r2.currentUser.role);\n  }\n}\nconst _c0 = function () {\n  return [\"/profile\"];\n};\nexport class SidenavComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, router, cdr) {\n    this.authService = authService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Current authenticated user\n     */\n    this.currentUser = null;\n    /**\n     * Flag to control sidenav expanded state\n     */\n    this.isExpanded = true;\n    /**\n     * Current active route\n     */\n    this.activeRoute = '';\n    /**\n     * Navigation menu items\n     */\n    this.navItems = [{\n      label: 'Dashboard',\n      icon: 'dashboard',\n      route: '/dashboard',\n      requiresAdmin: false\n    }, {\n      label: 'My Tasks',\n      icon: 'assignment',\n      route: '/tasks',\n      requiresAdmin: false\n    }, {\n      label: 'Create Task',\n      icon: 'add_task',\n      route: '/tasks/create',\n      requiresAdmin: false\n    }, {\n      label: 'Team',\n      icon: 'people',\n      route: '/team',\n      requiresAdmin: false\n    }, {\n      label: 'Reports',\n      icon: 'bar_chart',\n      route: '/reports',\n      requiresAdmin: true\n    }, {\n      label: 'Settings',\n      icon: 'settings',\n      route: '/settings',\n      requiresAdmin: true\n    }];\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up subscriptions to current user and route changes\n   */\n  ngOnInit() {\n    // Subscribe to current user changes\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.cdr.markForCheck();\n    });\n    // Track active route\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      this.activeRoute = event.urlAfterRedirects;\n      this.cdr.markForCheck();\n    });\n  }\n  /**\n   * Toggle the expanded state of the sidenav\n   */\n  toggleSidenav() {\n    this.isExpanded = !this.isExpanded;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Check if a route is currently active\n   * @param route - Route to check\n   * @returns Boolean indicating if route is active\n   */\n  isActive(route) {\n    return this.activeRoute.startsWith(route);\n  }\n  /**\n   * Check if the current user has admin privileges\n   * @returns Boolean indicating if user is admin\n   */\n  isAdmin() {\n    return this.currentUser?.role === 'admin';\n  }\n  static {\n    this.ɵfac = function SidenavComponent_Factory(t) {\n      return new (t || SidenavComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidenavComponent,\n      selectors: [[\"app-sidenav\"]],\n      decls: 13,\n      vars: 10,\n      consts: [[1, \"sidenav\"], [1, \"sidenav-toggle\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Toggle sidenav\", 3, \"click\"], [1, \"sidenav-nav\"], [1, \"nav-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"sidenav-footer\"], [1, \"profile-link\", 3, \"routerLink\"], [1, \"user-avatar\"], [4, \"ngIf\"], [\"class\", \"user-info\", 4, \"ngIf\"], [\"class\", \"nav-item\", 3, \"routerLink\", \"active\", 4, \"ngIf\"], [1, \"nav-item\", 3, \"routerLink\"], [1, \"nav-icon\"], [\"class\", \"nav-label\", 4, \"ngIf\"], [1, \"nav-label\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-role\"]],\n      template: function SidenavComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function SidenavComponent_Template_button_click_2_listener() {\n            return ctx.toggleSidenav();\n          });\n          i0.ɵɵelementStart(3, \"mat-icon\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"nav\", 3)(6, \"ul\", 4);\n          i0.ɵɵtemplate(7, SidenavComponent_li_7_Template, 2, 1, \"li\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"a\", 7)(10, \"div\", 8);\n          i0.ɵɵtemplate(11, SidenavComponent_span_11_Template, 2, 1, \"span\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, SidenavComponent_div_12_Template, 5, 2, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"expanded\", ctx.isExpanded);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.isExpanded ? \"chevron_left\" : \"chevron_right\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.navItems);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(9, _c0));\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background-image\", (ctx.currentUser == null ? null : ctx.currentUser.avatarUrl) ? \"url(\" + (ctx.currentUser == null ? null : ctx.currentUser.avatarUrl) + \")\" : \"\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.currentUser == null ? null : ctx.currentUser.avatarUrl));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExpanded);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i2.RouterLink],\n      styles: [\"\\n\\n\\n\\n\\n\\n.sidenav[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 100%;\\n  background-color: #fff;\\n  box-shadow: 1px 0 5px rgba(0, 0, 0, 0.1);\\n  transition: width 0.3s ease;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  \\n\\n}\\n.sidenav.expanded[_ngcontent-%COMP%] {\\n  width: 240px;\\n}\\n\\n\\n\\n.sidenav-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 8px;\\n}\\n.sidenav-toggle[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n\\n\\n.sidenav-nav[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  \\n\\n  \\n\\n  -ms-overflow-style: none;\\n  scrollbar-width: none;\\n}\\n.sidenav-nav[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n\\n\\n.nav-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 16px;\\n  color: #333;\\n  text-decoration: none;\\n  transition: background-color 0.2s ease;\\n}\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.nav-item.active[_ngcontent-%COMP%] {\\n  background-color: rgba(63, 81, 181, 0.1);\\n  color: #3f51b5;\\n  border-left: 3px solid #3f51b5;\\n}\\n\\n\\n\\n.nav-icon[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n}\\n\\n\\n\\n.nav-label[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n\\n\\n.sidenav-footer[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.profile-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  text-decoration: none;\\n  color: #333;\\n}\\n.profile-link[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: #3f51b5;\\n  color: #fff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 500;\\n  background-size: cover;\\n  background-position: center;\\n  margin-right: 12px;\\n}\\n.profile-link[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.profile-link[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.profile-link[_ngcontent-%COMP%]   .user-role[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n  text-transform: capitalize;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationEnd", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "item_r3", "label", "ɵɵtemplate", "SidenavComponent_li_7_a_1_span_3_Template", "ɵɵclassProp", "ctx_r4", "isActive", "route", "ɵɵproperty", "ɵɵattribute", "icon", "isExpanded", "SidenavComponent_li_7_a_1_Template", "requiresAdmin", "ctx_r0", "isAdmin", "ctx_r1", "currentUser", "name", "char<PERSON>t", "ctx_r2", "role", "SidenavComponent", "constructor", "authService", "router", "cdr", "activeRoute", "navItems", "ngOnInit", "currentUser$", "subscribe", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "events", "pipe", "event", "urlAfterRedirects", "toggle<PERSON><PERSON><PERSON>", "startsWith", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "SidenavComponent_Template", "rf", "ctx", "ɵɵlistener", "SidenavComponent_Template_button_click_2_listener", "SidenavComponent_li_7_Template", "SidenavComponent_span_11_Template", "SidenavComponent_div_12_Template", "ɵɵpureFunction0", "_c0", "ɵɵstyleProp", "avatarUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\components\\sidenav\\sidenav.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\components\\sidenav\\sidenav.component.html"], "sourcesContent": ["/**\n * Sidenav Component\n * Side navigation menu for the application\n */\nimport { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { AuthService } from '../../services/auth.service';\nimport { User } from '../../models/user.model';\n\n@Component({\n  selector: 'app-sidenav',\n  templateUrl: './sidenav.component.html',\n  styleUrls: ['./sidenav.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class SidenavComponent implements OnInit {\n  /**\n   * Current authenticated user\n   */\n  currentUser: User | null = null;\n  \n  /**\n   * Flag to control sidenav expanded state\n   */\n  isExpanded = true;\n  \n  /**\n   * Current active route\n   */\n  activeRoute = '';\n\n  /**\n   * Navigation menu items\n   */\n  navItems = [\n    { \n      label: 'Dashboard', \n      icon: 'dashboard', \n      route: '/dashboard',\n      requiresAdmin: false\n    },\n    { \n      label: 'My Tasks', \n      icon: 'assignment', \n      route: '/tasks',\n      requiresAdmin: false\n    },\n    { \n      label: 'Create Task', \n      icon: 'add_task', \n      route: '/tasks/create',\n      requiresAdmin: false\n    },\n    { \n      label: 'Team', \n      icon: 'people', \n      route: '/team',\n      requiresAdmin: false\n    },\n    { \n      label: 'Reports', \n      icon: 'bar_chart', \n      route: '/reports',\n      requiresAdmin: true\n    },\n    { \n      label: 'Settings', \n      icon: 'settings', \n      route: '/settings',\n      requiresAdmin: true\n    }\n  ];\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up subscriptions to current user and route changes\n   */\n  ngOnInit(): void {\n    // Subscribe to current user changes\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.cdr.markForCheck();\n    });\n    \n    // Track active route\n    this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd)\n    ).subscribe((event: NavigationEnd) => {\n      this.activeRoute = event.urlAfterRedirects;\n      this.cdr.markForCheck();\n    });\n  }\n\n  /**\n   * Toggle the expanded state of the sidenav\n   */\n  toggleSidenav(): void {\n    this.isExpanded = !this.isExpanded;\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Check if a route is currently active\n   * @param route - Route to check\n   * @returns Boolean indicating if route is active\n   */\n  isActive(route: string): boolean {\n    return this.activeRoute.startsWith(route);\n  }\n\n  /**\n   * Check if the current user has admin privileges\n   * @returns Boolean indicating if user is admin\n   */\n  isAdmin(): boolean {\n    return this.currentUser?.role === 'admin';\n  }\n}\n", "<!-- Side navigation menu -->\n<div class=\"sidenav\" [class.expanded]=\"isExpanded\">\n  <!-- Toggle button for expanding/collapsing sidenav -->\n  <div class=\"sidenav-toggle\">\n    <button mat-icon-button (click)=\"toggleSidenav()\" aria-label=\"Toggle sidenav\">\n      <mat-icon>{{ isExpanded ? 'chevron_left' : 'chevron_right' }}</mat-icon>\n    </button>\n  </div>\n\n  <!-- Navigation items -->\n  <nav class=\"sidenav-nav\">\n    <ul class=\"nav-list\">\n      <!-- Loop through navigation items -->\n      <li *ngFor=\"let item of navItems\">\n        <!-- Only show admin items to admin users -->\n        <a *ngIf=\"!item.requiresAdmin || isAdmin()\"\n           [routerLink]=\"item.route\"\n           [class.active]=\"isActive(item.route)\"\n           class=\"nav-item\"\n           [attr.title]=\"item.label\">\n          <mat-icon class=\"nav-icon\">{{ item.icon }}</mat-icon>\n          <span class=\"nav-label\" *ngIf=\"isExpanded\">{{ item.label }}</span>\n        </a>\n      </li>\n    </ul>\n  </nav>\n\n  <!-- User profile section at bottom -->\n  <div class=\"sidenav-footer\">\n    <a [routerLink]=\"['/profile']\" class=\"profile-link\">\n      <div class=\"user-avatar\" [style.backgroundImage]=\"currentUser?.avatarUrl ? 'url(' + currentUser?.avatarUrl + ')' : ''\">\n        <span *ngIf=\"!currentUser?.avatarUrl\">{{ currentUser?.name?.charAt(0) }}</span>\n      </div>\n      <div class=\"user-info\" *ngIf=\"isExpanded\">\n        <span class=\"user-name\">{{ currentUser?.name }}</span>\n        <span class=\"user-role\">{{ currentUser?.role }}</span>\n      </div>\n    </a>\n  </div>\n</div>\n"], "mappings": "AAKA,SAAiBA,aAAa,QAAQ,iBAAiB;AACvD,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;ICe7BC,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,KAAA,CAAgB;;;;;IAN7DP,EAAA,CAAAC,cAAA,YAI6B;IACAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAQ,UAAA,IAAAC,yCAAA,mBAAkE;IACpET,EAAA,CAAAG,YAAA,EAAI;;;;;IALDH,EAAA,CAAAU,WAAA,WAAAC,MAAA,CAAAC,QAAA,CAAAN,OAAA,CAAAO,KAAA,EAAqC;IADrCb,EAAA,CAAAc,UAAA,eAAAR,OAAA,CAAAO,KAAA,CAAyB;IAGzBb,EAAA,CAAAe,WAAA,UAAAT,OAAA,CAAAC,KAAA,CAAyB;IACCP,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAU,IAAA,CAAe;IACjBhB,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAc,UAAA,SAAAH,MAAA,CAAAM,UAAA,CAAgB;;;;;IAR7CjB,EAAA,CAAAC,cAAA,SAAkC;IAEhCD,EAAA,CAAAQ,UAAA,IAAAU,kCAAA,gBAOI;IACNlB,EAAA,CAAAG,YAAA,EAAK;;;;;IARCH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAc,UAAA,UAAAR,OAAA,CAAAa,aAAA,IAAAC,MAAA,CAAAC,OAAA,GAAsC;;;;;IAgB1CrB,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzCH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,iBAAA,CAAAiB,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,IAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,IAAA,CAAAC,MAAA,IAAkC;;;;;IAE1EzB,EAAA,CAAAC,cAAA,cAA0C;IAChBD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAD9BH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAqB,MAAA,CAAAH,WAAA,kBAAAG,MAAA,CAAAH,WAAA,CAAAC,IAAA,CAAuB;IACvBxB,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAqB,MAAA,CAAAH,WAAA,kBAAAG,MAAA,CAAAH,WAAA,CAAAI,IAAA,CAAuB;;;;;;ADnBvD,OAAM,MAAOC,gBAAgB;EA0D3B;;;;;;EAMAC,YACUC,WAAwB,EACxBC,MAAc,EACdC,GAAsB;IAFtB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IAlEb;;;IAGA,KAAAT,WAAW,GAAgB,IAAI;IAE/B;;;IAGA,KAAAN,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAgB,WAAW,GAAG,EAAE;IAEhB;;;IAGA,KAAAC,QAAQ,GAAG,CACT;MACE3B,KAAK,EAAE,WAAW;MAClBS,IAAI,EAAE,WAAW;MACjBH,KAAK,EAAE,YAAY;MACnBM,aAAa,EAAE;KAChB,EACD;MACEZ,KAAK,EAAE,UAAU;MACjBS,IAAI,EAAE,YAAY;MAClBH,KAAK,EAAE,QAAQ;MACfM,aAAa,EAAE;KAChB,EACD;MACEZ,KAAK,EAAE,aAAa;MACpBS,IAAI,EAAE,UAAU;MAChBH,KAAK,EAAE,eAAe;MACtBM,aAAa,EAAE;KAChB,EACD;MACEZ,KAAK,EAAE,MAAM;MACbS,IAAI,EAAE,QAAQ;MACdH,KAAK,EAAE,OAAO;MACdM,aAAa,EAAE;KAChB,EACD;MACEZ,KAAK,EAAE,SAAS;MAChBS,IAAI,EAAE,WAAW;MACjBH,KAAK,EAAE,UAAU;MACjBM,aAAa,EAAE;KAChB,EACD;MACEZ,KAAK,EAAE,UAAU;MACjBS,IAAI,EAAE,UAAU;MAChBH,KAAK,EAAE,WAAW;MAClBM,aAAa,EAAE;KAChB,CACF;EAYE;EAEH;;;;EAIAgB,QAAQA,CAAA;IACN;IACA,IAAI,CAACL,WAAW,CAACM,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACf,WAAW,GAAGe,IAAI;MACvB,IAAI,CAACN,GAAG,CAACO,YAAY,EAAE;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAACR,MAAM,CAACS,MAAM,CAACC,IAAI,CACrB1C,MAAM,CAAC2C,KAAK,IAAIA,KAAK,YAAY5C,aAAa,CAAC,CAChD,CAACuC,SAAS,CAAEK,KAAoB,IAAI;MACnC,IAAI,CAACT,WAAW,GAAGS,KAAK,CAACC,iBAAiB;MAC1C,IAAI,CAACX,GAAG,CAACO,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEA;;;EAGAK,aAAaA,CAAA;IACX,IAAI,CAAC3B,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACe,GAAG,CAACO,YAAY,EAAE;EACzB;EAEA;;;;;EAKA3B,QAAQA,CAACC,KAAa;IACpB,OAAO,IAAI,CAACoB,WAAW,CAACY,UAAU,CAAChC,KAAK,CAAC;EAC3C;EAEA;;;;EAIAQ,OAAOA,CAAA;IACL,OAAO,IAAI,CAACE,WAAW,EAAEI,IAAI,KAAK,OAAO;EAC3C;;;uBAjHWC,gBAAgB,EAAA5B,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhD,EAAA,CAAA8C,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAlD,EAAA,CAAA8C,iBAAA,CAAA9C,EAAA,CAAAmD,iBAAA;IAAA;EAAA;;;YAAhBvB,gBAAgB;MAAAwB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf7B1D,EAAA,CAAAC,cAAA,aAAmD;UAGvBD,EAAA,CAAA4D,UAAA,mBAAAC,kDAAA;YAAA,OAASF,GAAA,CAAAf,aAAA,EAAe;UAAA,EAAC;UAC/C5C,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,GAAmD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAK5EH,EAAA,CAAAC,cAAA,aAAyB;UAGrBD,EAAA,CAAAQ,UAAA,IAAAsD,8BAAA,gBAUK;UACP9D,EAAA,CAAAG,YAAA,EAAK;UAIPH,EAAA,CAAAC,cAAA,aAA4B;UAGtBD,EAAA,CAAAQ,UAAA,KAAAuD,iCAAA,kBAA+E;UACjF/D,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAQ,UAAA,KAAAwD,gCAAA,kBAGM;UACRhE,EAAA,CAAAG,YAAA,EAAI;;;UApCaH,EAAA,CAAAU,WAAA,aAAAiD,GAAA,CAAA1C,UAAA,CAA6B;UAIlCjB,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAK,iBAAA,CAAAsD,GAAA,CAAA1C,UAAA,oCAAmD;UAQxCjB,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAc,UAAA,YAAA6C,GAAA,CAAAzB,QAAA,CAAW;UAgB/BlC,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAc,UAAA,eAAAd,EAAA,CAAAiE,eAAA,IAAAC,GAAA,EAA2B;UACHlE,EAAA,CAAAI,SAAA,GAA6F;UAA7FJ,EAAA,CAAAmE,WAAA,sBAAAR,GAAA,CAAApC,WAAA,kBAAAoC,GAAA,CAAApC,WAAA,CAAA6C,SAAA,cAAAT,GAAA,CAAApC,WAAA,kBAAAoC,GAAA,CAAApC,WAAA,CAAA6C,SAAA,aAA6F;UAC7GpE,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAc,UAAA,WAAA6C,GAAA,CAAApC,WAAA,kBAAAoC,GAAA,CAAApC,WAAA,CAAA6C,SAAA,EAA6B;UAEdpE,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAc,UAAA,SAAA6C,GAAA,CAAA1C,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}