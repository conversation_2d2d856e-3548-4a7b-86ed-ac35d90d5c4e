{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Task Status Chart Component\n * Displays a chart showing task distribution by status\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\nexport let TaskStatusChartComponent = class TaskStatusChartComponent {\n  constructor() {\n    /**\n     * Number of tasks with 'todo' status\n     */\n    this.todoCount = 0;\n    /**\n     * Number of tasks with 'in_progress' status\n     */\n    this.inProgressCount = 0;\n    /**\n     * Number of tasks with 'done' status\n     */\n    this.doneCount = 0;\n    /**\n     * Chart data for rendering\n     */\n    this.chartData = [];\n    /**\n     * Chart view dimensions\n     */\n    this.view = [300, 200];\n    /**\n     * Chart color scheme\n     */\n    this.colorScheme = {\n      domain: ['#ff9800', '#2196f3', '#4caf50']\n    };\n    /**\n     * Flag to show/hide chart labels\n     */\n    this.showLabels = true;\n    /**\n     * Flag to enable/disable chart animations\n     */\n    this.animations = true;\n    /**\n     * Flag to show/hide legend\n     */\n    this.showLegend = true;\n    /**\n     * Flag to enable/disable gradient fills\n     */\n    this.gradient = false;\n  }\n  /**\n   * Lifecycle hook that is called when input properties change\n   * Updates chart data when counts change\n   * @param changes - Input changes\n   */\n  ngOnChanges(changes) {\n    this.updateChartData();\n  }\n  /**\n   * Update chart data based on current counts\n   */\n  updateChartData() {\n    this.chartData = [{\n      name: 'To Do',\n      value: this.todoCount\n    }, {\n      name: 'In Progress',\n      value: this.inProgressCount\n    }, {\n      name: 'Done',\n      value: this.doneCount\n    }];\n  }\n  /**\n   * Calculate the height percentage for a bar based on its count\n   * @param count - The count value for the bar\n   * @returns Percentage height (0-100)\n   */\n  getBarHeight(count) {\n    const total = this.todoCount + this.inProgressCount + this.doneCount;\n    if (total === 0) return 0;\n    // Calculate percentage with a minimum height of 5% for visibility\n    const percentage = count / total * 100;\n    return Math.max(percentage, 5);\n  }\n};\n__decorate([Input()], TaskStatusChartComponent.prototype, \"todoCount\", void 0);\n__decorate([Input()], TaskStatusChartComponent.prototype, \"inProgressCount\", void 0);\n__decorate([Input()], TaskStatusChartComponent.prototype, \"doneCount\", void 0);\nTaskStatusChartComponent = __decorate([Component({\n  selector: 'app-task-status-chart',\n  templateUrl: './task-status-chart.component.html',\n  styleUrls: ['./task-status-chart.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskStatusChartComponent);", "map": {"version": 3, "names": ["Component", "Input", "ChangeDetectionStrategy", "TaskStatusChartComponent", "constructor", "todoCount", "inProgressCount", "doneCount", "chartData", "view", "colorScheme", "domain", "showLabels", "animations", "showLegend", "gradient", "ngOnChanges", "changes", "updateChartData", "name", "value", "getBarHeight", "count", "total", "percentage", "Math", "max", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\task-status-chart\\task-status-chart.component.ts"], "sourcesContent": ["/**\n * Task Status Chart Component\n * Displays a chart showing task distribution by status\n */\nimport { Component, Input, OnChanges, SimpleChanges, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-task-status-chart',\n  templateUrl: './task-status-chart.component.html',\n  styleUrls: ['./task-status-chart.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskStatusChartComponent implements OnChanges {\n  /**\n   * Number of tasks with 'todo' status\n   */\n  @Input() todoCount = 0;\n  \n  /**\n   * Number of tasks with 'in_progress' status\n   */\n  @Input() inProgressCount = 0;\n  \n  /**\n   * Number of tasks with 'done' status\n   */\n  @Input() doneCount = 0;\n  \n  /**\n   * Chart data for rendering\n   */\n  chartData: any[] = [];\n  \n  /**\n   * Chart view dimensions\n   */\n  view: [number, number] = [300, 200];\n  \n  /**\n   * Chart color scheme\n   */\n  colorScheme = {\n    domain: ['#ff9800', '#2196f3', '#4caf50']\n  };\n  \n  /**\n   * Flag to show/hide chart labels\n   */\n  showLabels = true;\n  \n  /**\n   * Flag to enable/disable chart animations\n   */\n  animations = true;\n  \n  /**\n   * Flag to show/hide legend\n   */\n  showLegend = true;\n  \n  /**\n   * Flag to enable/disable gradient fills\n   */\n  gradient = false;\n  \n  /**\n   * Lifecycle hook that is called when input properties change\n   * Updates chart data when counts change\n   * @param changes - Input changes\n   */\n  ngOnChanges(changes: SimpleChanges): void {\n    this.updateChartData();\n  }\n  \n  /**\n   * Update chart data based on current counts\n   */\n  private updateChartData(): void {\n    this.chartData = [\n      {\n        name: 'To Do',\n        value: this.todoCount\n      },\n      {\n        name: 'In Progress',\n        value: this.inProgressCount\n      },\n      {\n        name: 'Done',\n        value: this.doneCount\n      }\n    ];\n  }\n  \n  /**\n   * Calculate the height percentage for a bar based on its count\n   * @param count - The count value for the bar\n   * @returns Percentage height (0-100)\n   */\n  getBarHeight(count: number): number {\n    const total = this.todoCount + this.inProgressCount + this.doneCount;\n    if (total === 0) return 0;\n    \n    // Calculate percentage with a minimum height of 5% for visibility\n    const percentage = (count / total) * 100;\n    return Math.max(percentage, 5);\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAA4BC,uBAAuB,QAAQ,eAAe;AAQ5F,WAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAA9BC,YAAA;IACL;;;IAGS,KAAAC,SAAS,GAAG,CAAC;IAEtB;;;IAGS,KAAAC,eAAe,GAAG,CAAC;IAE5B;;;IAGS,KAAAC,SAAS,GAAG,CAAC;IAEtB;;;IAGA,KAAAC,SAAS,GAAU,EAAE;IAErB;;;IAGA,KAAAC,IAAI,GAAqB,CAAC,GAAG,EAAE,GAAG,CAAC;IAEnC;;;IAGA,KAAAC,WAAW,GAAG;MACZC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;KACzC;IAED;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,QAAQ,GAAG,KAAK;EA4ClB;EA1CE;;;;;EAKAC,WAAWA,CAACC,OAAsB;IAChC,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGQA,eAAeA,CAAA;IACrB,IAAI,CAACV,SAAS,GAAG,CACf;MACEW,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,IAAI,CAACf;KACb,EACD;MACEc,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,IAAI,CAACd;KACb,EACD;MACEa,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI,CAACb;KACb,CACF;EACH;EAEA;;;;;EAKAc,YAAYA,CAACC,KAAa;IACxB,MAAMC,KAAK,GAAG,IAAI,CAAClB,SAAS,GAAG,IAAI,CAACC,eAAe,GAAG,IAAI,CAACC,SAAS;IACpE,IAAIgB,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzB;IACA,MAAMC,UAAU,GAAIF,KAAK,GAAGC,KAAK,GAAI,GAAG;IACxC,OAAOE,IAAI,CAACC,GAAG,CAACF,UAAU,EAAE,CAAC,CAAC;EAChC;CACD;AA3FUG,UAAA,EAAR1B,KAAK,EAAE,C,0DAAe;AAKd0B,UAAA,EAAR1B,KAAK,EAAE,C,gEAAqB;AAKpB0B,UAAA,EAAR1B,KAAK,EAAE,C,0DAAe;AAdZE,wBAAwB,GAAAwB,UAAA,EANpC3B,SAAS,CAAC;EACT4B,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,eAAe,EAAE7B,uBAAuB,CAAC8B;CAC1C,CAAC,C,EACW7B,wBAAwB,CA+FpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}