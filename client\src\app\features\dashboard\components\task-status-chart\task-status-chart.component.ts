/**
 * Task Status Chart Component
 * Displays a chart showing task distribution by status
 */
import { Component, Input, ChangeDetectionStrategy } from '@angular/core';

/**
 * Interface for OnChanges lifecycle hook
 */
interface OnChangesInterface {
  ngOnChanges(changes: Record<string, any>): void;
}

@Component({
  selector: 'app-task-status-chart',
  templateUrl: './task-status-chart.component.html',
  styleUrls: ['./task-status-chart.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskStatusChartComponent implements OnChangesInterface {
  /**
   * Number of tasks with 'todo' status
   */
  @Input() todoCount = 0;
  
  /**
   * Number of tasks with 'in_progress' status
   */
  @Input() inProgressCount = 0;
  
  /**
   * Number of tasks with 'done' status
   */
  @Input() doneCount = 0;
  
  /**
   * Chart data for rendering
   */
  chartData: any[] = [];
  
  /**
   * Chart view dimensions
   */
  view: [number, number] = [300, 200];
  
  /**
   * Chart color scheme
   */
  colorScheme = {
    domain: ['#ff9800', '#2196f3', '#4caf50']
  };
  
  /**
   * Flag to show/hide chart labels
   */
  showLabels = true;
  
  /**
   * Flag to enable/disable chart animations
   */
  animations = true;
  
  /**
   * Flag to show/hide legend
   */
  showLegend = true;
  
  /**
   * Flag to enable/disable gradient fills
   */
  gradient = false;
  
  /**
   * Lifecycle hook that is called when input properties change
   * Updates chart data when counts change
   * @param changes - Input changes
   */
  ngOnChanges(changes: Record<string, any>): void {
    this.updateChartData();
  }
  
  /**
   * Update chart data based on current counts
   */
  private updateChartData(): void {
    this.chartData = [
      {
        name: 'To Do',
        value: this.todoCount
      },
      {
        name: 'In Progress',
        value: this.inProgressCount
      },
      {
        name: 'Done',
        value: this.doneCount
      }
    ];
  }
  
  /**
   * Calculate the height percentage for a bar based on its count
   * @param count - The count value for the bar
   * @returns Percentage height (0-100)
   */
  getBarHeight(count: number): number {
    const total = this.todoCount + this.inProgressCount + this.doneCount;
    if (total === 0) return 0;
    
    // Calculate percentage with a minimum height of 5% for visibility
    const percentage = (count / total) * 100;
    return Math.max(percentage, 5);
  }
}
