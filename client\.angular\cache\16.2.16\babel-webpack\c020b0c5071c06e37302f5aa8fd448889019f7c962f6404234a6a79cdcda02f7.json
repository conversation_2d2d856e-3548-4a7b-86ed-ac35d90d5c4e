{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/snack-bar\";\nexport class NotificationService {\n  /**\n   * Constructor with dependency injection\n   * @param snackBar - Angular Material SnackBar service\n   */\n  constructor(snackBar) {\n    this.snackBar = snackBar;\n    /**\n     * Default duration for notifications in milliseconds\n     */\n    this.defaultDuration = 5000;\n  }\n  /**\n   * Display a success notification\n   * @param message - Message to display\n   * @param duration - Optional duration in milliseconds\n   */\n  success(message, duration) {\n    this.snackBar.open(message, 'Close', {\n      duration: duration || this.defaultDuration,\n      panelClass: ['notification-success'],\n      horizontalPosition: 'end',\n      verticalPosition: 'top'\n    });\n  }\n  /**\n   * Display an error notification\n   * @param message - Error message to display\n   * @param duration - Optional duration in milliseconds\n   */\n  error(message, duration) {\n    this.snackBar.open(message, 'Close', {\n      duration: duration || this.defaultDuration,\n      panelClass: ['notification-error'],\n      horizontalPosition: 'end',\n      verticalPosition: 'top'\n    });\n  }\n  /**\n   * Display an info notification\n   * @param message - Message to display\n   * @param duration - Optional duration in milliseconds\n   */\n  info(message, duration) {\n    this.snackBar.open(message, 'Close', {\n      duration: duration || this.defaultDuration,\n      panelClass: ['notification-info'],\n      horizontalPosition: 'end',\n      verticalPosition: 'top'\n    });\n  }\n  /**\n   * Display a warning notification\n   * @param message - Warning message to display\n   * @param duration - Optional duration in milliseconds\n   */\n  warning(message, duration) {\n    this.snackBar.open(message, 'Close', {\n      duration: duration || this.defaultDuration,\n      panelClass: ['notification-warning'],\n      horizontalPosition: 'end',\n      verticalPosition: 'top'\n    });\n  }\n  static {\n    this.ɵfac = function NotificationService_Factory(t) {\n      return new (t || NotificationService)(i0.ɵɵinject(i1.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: NotificationService,\n      factory: NotificationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["NotificationService", "constructor", "snackBar", "defaultDuration", "success", "message", "duration", "open", "panelClass", "horizontalPosition", "verticalPosition", "error", "info", "warning", "i0", "ɵɵinject", "i1", "MatSnackBar", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\services\\notification.service.ts"], "sourcesContent": ["/**\n * Notification Service\n * Provides methods for displaying notifications to the user\n */\nimport { Injectable } from '@angular/core';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class NotificationService {\n  /**\n   * Default duration for notifications in milliseconds\n   */\n  private defaultDuration = 5000;\n\n  /**\n   * Constructor with dependency injection\n   * @param snackBar - Angular Material SnackBar service\n   */\n  constructor(private snackBar: MatSnackBar) {}\n\n  /**\n   * Display a success notification\n   * @param message - Message to display\n   * @param duration - Optional duration in milliseconds\n   */\n  success(message: string, duration?: number): void {\n    this.snackBar.open(message, 'Close', {\n      duration: duration || this.defaultDuration,\n      panelClass: ['notification-success'],\n      horizontalPosition: 'end',\n      verticalPosition: 'top'\n    });\n  }\n\n  /**\n   * Display an error notification\n   * @param message - Error message to display\n   * @param duration - Optional duration in milliseconds\n   */\n  error(message: string, duration?: number): void {\n    this.snackBar.open(message, 'Close', {\n      duration: duration || this.defaultDuration,\n      panelClass: ['notification-error'],\n      horizontalPosition: 'end',\n      verticalPosition: 'top'\n    });\n  }\n\n  /**\n   * Display an info notification\n   * @param message - Message to display\n   * @param duration - Optional duration in milliseconds\n   */\n  info(message: string, duration?: number): void {\n    this.snackBar.open(message, 'Close', {\n      duration: duration || this.defaultDuration,\n      panelClass: ['notification-info'],\n      horizontalPosition: 'end',\n      verticalPosition: 'top'\n    });\n  }\n\n  /**\n   * Display a warning notification\n   * @param message - Warning message to display\n   * @param duration - Optional duration in milliseconds\n   */\n  warning(message: string, duration?: number): void {\n    this.snackBar.open(message, 'Close', {\n      duration: duration || this.defaultDuration,\n      panelClass: ['notification-warning'],\n      horizontalPosition: 'end',\n      verticalPosition: 'top'\n    });\n  }\n}\n"], "mappings": ";;AAUA,OAAM,MAAOA,mBAAmB;EAM9B;;;;EAIAC,YAAoBC,QAAqB;IAArB,KAAAA,QAAQ,GAARA,QAAQ;IAT5B;;;IAGQ,KAAAC,eAAe,GAAG,IAAI;EAMc;EAE5C;;;;;EAKAC,OAAOA,CAACC,OAAe,EAAEC,QAAiB;IACxC,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAACF,OAAO,EAAE,OAAO,EAAE;MACnCC,QAAQ,EAAEA,QAAQ,IAAI,IAAI,CAACH,eAAe;MAC1CK,UAAU,EAAE,CAAC,sBAAsB,CAAC;MACpCC,kBAAkB,EAAE,KAAK;MACzBC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEA;;;;;EAKAC,KAAKA,CAACN,OAAe,EAAEC,QAAiB;IACtC,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAACF,OAAO,EAAE,OAAO,EAAE;MACnCC,QAAQ,EAAEA,QAAQ,IAAI,IAAI,CAACH,eAAe;MAC1CK,UAAU,EAAE,CAAC,oBAAoB,CAAC;MAClCC,kBAAkB,EAAE,KAAK;MACzBC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEA;;;;;EAKAE,IAAIA,CAACP,OAAe,EAAEC,QAAiB;IACrC,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAACF,OAAO,EAAE,OAAO,EAAE;MACnCC,QAAQ,EAAEA,QAAQ,IAAI,IAAI,CAACH,eAAe;MAC1CK,UAAU,EAAE,CAAC,mBAAmB,CAAC;MACjCC,kBAAkB,EAAE,KAAK;MACzBC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEA;;;;;EAKAG,OAAOA,CAACR,OAAe,EAAEC,QAAiB;IACxC,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAACF,OAAO,EAAE,OAAO,EAAE;MACnCC,QAAQ,EAAEA,QAAQ,IAAI,IAAI,CAACH,eAAe;MAC1CK,UAAU,EAAE,CAAC,sBAAsB,CAAC;MACpCC,kBAAkB,EAAE,KAAK;MACzBC,gBAAgB,EAAE;KACnB,CAAC;EACJ;;;uBAlEWV,mBAAmB,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAnBjB,mBAAmB;MAAAkB,OAAA,EAAnBlB,mBAAmB,CAAAmB,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}