/**
 * Core module for the application
 * Contains singleton services, universal components, and other features
 * that are used throughout the application and should be loaded only once
 */
import { NgModule, Optional, SkipSelf } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';

// Import SharedModule for Angular Material components
import { SharedModule } from '../shared/shared.module';

// Guards
import { AuthGuard } from './guards/auth.guard';

// Services
import { AuthService } from './services/auth.service';
import { TaskService } from './services/task.service';
import { UserService } from './services/user.service';
import { NotificationService } from './services/notification.service';

// Components
import { HeaderComponent } from './components/header/header.component';
import { FooterComponent } from './components/footer/footer.component';
import { SidenavComponent } from './components/sidenav/sidenav.component';

// Interceptors are provided in app.module.ts

/**
 * Prevents importing CoreModule in any module other than AppModule
 * @param parentModule - The parent module
 */
export function throwIfAlreadyLoaded(parentModule: unknown, moduleName: string): void {
  if (parentModule) {
    throw new Error(
      `${moduleName} has already been loaded. Import Core modules in the AppModule only.`
    );
  }
}

@NgModule({
  declarations: [
    HeaderComponent,
    FooterComponent,
    SidenavComponent
  ],
  imports: [
    CommonModule,
    HttpClientModule,
    RouterModule,
    SharedModule
  ],
  exports: [
    HeaderComponent,
    FooterComponent,
    SidenavComponent
  ],
  providers: [
    AuthGuard,
    AuthService,
    TaskService,
    UserService,
    NotificationService
  ]
})
export class CoreModule {
  /**
   * Constructor to prevent reimporting of the CoreModule
   * @param parentModule - The parent module
   */
  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {
    throwIfAlreadyLoaded(parentModule, 'CoreModule');
  }
}
