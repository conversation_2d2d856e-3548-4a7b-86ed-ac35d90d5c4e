/**
 * Authentication Guard
 * Protects routes that require authentication
 */
import { Injectable } from '@angular/core';
import { 
  ActivatedRouteSnapshot, 
  RouterStateSnapshot, 
  Router,
  UrlTree
} from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard {
  /**
   * Constructor with dependency injection
   * @param authService - Authentication service
   * @param router - Router for navigation
   */
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  /**
   * Determines if a route can be activated
   * @param route - The route being activated
   * @param state - Router state
   * @returns Boolean or UrlTree indicating if route can be activated
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.authService.isAuthenticated$.pipe(
      take(1),
      map(isAuthenticated => {
        // If authenticated, allow access
        if (isAuthenticated) {
          return true;
        }
        
        // Store attempted URL for redirecting after login
        const returnUrl = state.url;
        
        // Navigate to login page with return URL
        return this.router.createUrlTree(['/auth/login'], { 
          queryParams: { returnUrl } 
        });
      })
    );
  }
}
