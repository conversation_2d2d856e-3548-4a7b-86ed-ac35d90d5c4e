{"ast": null, "code": "/**\n * Login Form Component\n * Handles user authentication with email and password\n */\nimport { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/checkbox\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/progress-spinner\";\nimport * as i9 from \"../../../../shared/components/error-message/error-message.component\";\nfunction LoginFormComponent_app_error_message_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-error-message\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"message\", ctx_r0.error);\n  }\n}\nfunction LoginFormComponent_mat_error_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginFormComponent_mat_error_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginFormComponent_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginFormComponent_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 8 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginFormComponent_mat_spinner_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 17);\n  }\n}\nfunction LoginFormComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Login\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginFormComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed login attempt\n     */\n    this.error = null;\n    /**\n     * Event emitted when form is submitted\n     */\n    this.formSubmit = new EventEmitter();\n    /**\n     * Event emitted when forgot password link is clicked\n     */\n    this.forgotPassword = new EventEmitter();\n    /**\n     * Event emitted when register link is clicked\n     */\n    this.register = new EventEmitter();\n    /**\n     * Flag to toggle password visibility\n     */\n    this.hidePassword = true;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the login form\n   */\n  ngOnInit() {\n    this.initForm();\n  }\n  /**\n   * Initialize login form with validation\n   */\n  initForm() {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      rememberMe: [false]\n    });\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.loginForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.loginForm.markAllAsTouched();\n      return;\n    }\n    const {\n      email,\n      password\n    } = this.loginForm.value;\n    this.formSubmit.emit({\n      email,\n      password\n    });\n  }\n  /**\n   * Handle forgot password link click\n   */\n  onForgotPassword() {\n    this.forgotPassword.emit();\n  }\n  /**\n   * Handle register link click\n   */\n  onRegister() {\n    this.register.emit();\n  }\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName, errorName) {\n    const control = this.loginForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility() {\n    this.hidePassword = !this.hidePassword;\n  }\n  static {\n    this.ɵfac = function LoginFormComponent_Factory(t) {\n      return new (t || LoginFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginFormComponent,\n      selectors: [[\"app-login-form\"]],\n      inputs: {\n        loading: \"loading\",\n        error: \"error\"\n      },\n      outputs: {\n        formSubmit: \"formSubmit\",\n        forgotPassword: \"forgotPassword\",\n        register: \"register\"\n      },\n      decls: 34,\n      vars: 13,\n      consts: [[1, \"login-form-container\"], [3, \"message\", 4, \"ngIf\"], [1, \"login-form\", 3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\"], [\"matPrefix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [1, \"remember-forgot\"], [\"formControlName\", \"rememberMe\", \"color\", \"primary\"], [1, \"forgot-link\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"register-link\"], [3, \"click\"], [3, \"message\"], [\"diameter\", \"20\"]],\n      template: function LoginFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, LoginFormComponent_app_error_message_1_Template, 1, 1, \"app-error-message\", 1);\n          i0.ɵɵelementStart(2, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginFormComponent_Template_form_ngSubmit_2_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(3, \"mat-form-field\", 3)(4, \"mat-label\");\n          i0.ɵɵtext(5, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"input\", 4);\n          i0.ɵɵelementStart(7, \"mat-icon\", 5);\n          i0.ɵɵtext(8, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, LoginFormComponent_mat_error_9_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵtemplate(10, LoginFormComponent_mat_error_10_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"mat-form-field\", 3)(12, \"mat-label\");\n          i0.ɵɵtext(13, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 7);\n          i0.ɵɵelementStart(15, \"mat-icon\", 5);\n          i0.ɵɵtext(16, \"lock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function LoginFormComponent_Template_button_click_17_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelementStart(18, \"mat-icon\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(20, LoginFormComponent_mat_error_20_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵtemplate(21, LoginFormComponent_mat_error_21_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 9)(23, \"mat-checkbox\", 10);\n          i0.ɵɵtext(24, \" Remember me \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"a\", 11);\n          i0.ɵɵlistener(\"click\", function LoginFormComponent_Template_a_click_25_listener() {\n            return ctx.onForgotPassword();\n          });\n          i0.ɵɵtext(26, \"Forgot password?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"button\", 12);\n          i0.ɵɵtemplate(28, LoginFormComponent_mat_spinner_28_Template, 1, 0, \"mat-spinner\", 13);\n          i0.ɵɵtemplate(29, LoginFormComponent_span_29_Template, 2, 0, \"span\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 14);\n          i0.ɵɵtext(31, \" Don't have an account? \");\n          i0.ɵɵelementStart(32, \"a\", 15);\n          i0.ɵɵlistener(\"click\", function LoginFormComponent_Template_a_click_32_listener() {\n            return ctx.onRegister();\n          });\n          i0.ɵɵtext(33, \"Register\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"email\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"email\", \"email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"password\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"password\", \"minlength\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.MatButton, i3.MatIconButton, i4.MatCheckbox, i5.MatFormField, i5.MatLabel, i5.MatError, i5.MatPrefix, i5.MatSuffix, i6.MatIcon, i7.MatInput, i8.MatProgressSpinner, i9.ErrorMessageComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.login-form-container[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  padding: 24px;\\n  max-width: 400px;\\n  width: 100%;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n\\n\\n.remember-forgot[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n\\n\\n.forgot-link[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  cursor: pointer;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n}\\n.forgot-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.submit-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1rem;\\n  margin-top: 8px;\\n}\\n.submit-button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.register-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 16px;\\n  font-size: 0.9rem;\\n}\\n.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  cursor: pointer;\\n  margin-left: 4px;\\n}\\n.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .login-form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    box-shadow: none;\\n  }\\n  .remember-forgot[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "error", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "LoginFormComponent", "constructor", "fb", "loading", "formSubmit", "forgotPassword", "register", "hidePassword", "ngOnInit", "initForm", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "rememberMe", "onSubmit", "invalid", "mark<PERSON>llAsTouched", "value", "emit", "onForgotPassword", "onRegister", "<PERSON><PERSON><PERSON><PERSON>", "controlName", "errorName", "control", "get", "touched", "togglePasswordVisibility", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "LoginFormComponent_Template", "rf", "ctx", "ɵɵtemplate", "LoginFormComponent_app_error_message_1_Template", "ɵɵlistener", "LoginFormComponent_Template_form_ngSubmit_2_listener", "LoginFormComponent_mat_error_9_Template", "LoginFormComponent_mat_error_10_Template", "LoginFormComponent_Template_button_click_17_listener", "LoginFormComponent_mat_error_20_Template", "LoginFormComponent_mat_error_21_Template", "LoginFormComponent_Template_a_click_25_listener", "LoginFormComponent_mat_spinner_28_Template", "LoginFormComponent_span_29_Template", "LoginFormComponent_Template_a_click_32_listener", "ɵɵadvance", "ɵɵattribute", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\login-form\\login-form.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\login-form\\login-form.component.html"], "sourcesContent": ["/**\n * Login Form Component\n * Handles user authentication with email and password\n */\nimport { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\n\n@Component({\n  selector: 'app-login-form',\n  templateUrl: './login-form.component.html',\n  styleUrls: ['./login-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class LoginFormComponent implements OnInit {\n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Error message from failed login attempt\n   */\n  @Input() error: string | null = null;\n  \n  /**\n   * Event emitted when form is submitted\n   */\n  @Output() formSubmit = new EventEmitter<{ email: string; password: string }>();\n  \n  /**\n   * Event emitted when forgot password link is clicked\n   */\n  @Output() forgotPassword = new EventEmitter<void>();\n  \n  /**\n   * Event emitted when register link is clicked\n   */\n  @Output() register = new EventEmitter<void>();\n  \n  /**\n   * Login form group\n   */\n  loginForm!: FormGroup;\n  \n  /**\n   * Flag to toggle password visibility\n   */\n  hidePassword = true;\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the login form\n   */\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  /**\n   * Initialize login form with validation\n   */\n  private initForm(): void {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      rememberMe: [false]\n    });\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.loginForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.loginForm.markAllAsTouched();\n      return;\n    }\n    \n    const { email, password } = this.loginForm.value;\n    this.formSubmit.emit({ email, password });\n  }\n\n  /**\n   * Handle forgot password link click\n   */\n  onForgotPassword(): void {\n    this.forgotPassword.emit();\n  }\n\n  /**\n   * Handle register link click\n   */\n  onRegister(): void {\n    this.register.emit();\n  }\n\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName: string, errorName: string): boolean {\n    const control = this.loginForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility(): void {\n    this.hidePassword = !this.hidePassword;\n  }\n}\n", "<!-- Login form container -->\n<div class=\"login-form-container\">\n  <!-- Error message -->\n  <app-error-message \n    *ngIf=\"error\" \n    [message]=\"error\">\n  </app-error-message>\n\n  <!-- Login form -->\n  <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\n    <!-- Email field -->\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\n      <mat-label>Email</mat-label>\n      <input \n        matInput \n        type=\"email\" \n        formControlName=\"email\" \n        placeholder=\"Enter your email\"\n        autocomplete=\"email\">\n      <mat-icon matPrefix>email</mat-icon>\n      <mat-error *ngIf=\"hasError('email', 'required')\">\n        Email is required\n      </mat-error>\n      <mat-error *ngIf=\"hasError('email', 'email')\">\n        Please enter a valid email address\n      </mat-error>\n    </mat-form-field>\n\n    <!-- Password field -->\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\n      <mat-label>Password</mat-label>\n      <input \n        matInput \n        [type]=\"hidePassword ? 'password' : 'text'\" \n        formControlName=\"password\"\n        placeholder=\"Enter your password\"\n        autocomplete=\"current-password\">\n      <mat-icon matPrefix>lock</mat-icon>\n      <button \n        mat-icon-button \n        matSuffix \n        type=\"button\"\n        (click)=\"togglePasswordVisibility()\" \n        [attr.aria-label]=\"'Hide password'\" \n        [attr.aria-pressed]=\"hidePassword\">\n        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n      </button>\n      <mat-error *ngIf=\"hasError('password', 'required')\">\n        Password is required\n      </mat-error>\n      <mat-error *ngIf=\"hasError('password', 'minlength')\">\n        Password must be at least 8 characters\n      </mat-error>\n    </mat-form-field>\n\n    <!-- Remember me checkbox -->\n    <div class=\"remember-forgot\">\n      <mat-checkbox formControlName=\"rememberMe\" color=\"primary\">\n        Remember me\n      </mat-checkbox>\n      <a (click)=\"onForgotPassword()\" class=\"forgot-link\">Forgot password?</a>\n    </div>\n\n    <!-- Submit button -->\n    <button \n      mat-raised-button \n      color=\"primary\" \n      type=\"submit\" \n      class=\"submit-button\"\n      [disabled]=\"loading\">\n      <mat-spinner diameter=\"20\" *ngIf=\"loading\"></mat-spinner>\n      <span *ngIf=\"!loading\">Login</span>\n    </button>\n\n    <!-- Register link -->\n    <div class=\"register-link\">\n      Don't have an account?\n      <a (click)=\"onRegister()\">Register</a>\n    </div>\n  </form>\n</div>\n"], "mappings": "AAAA;;;;AAIA,SAAoCA,YAAY,QAAwC,eAAe;AACvG,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;ICFjEC,EAAA,CAAAC,SAAA,4BAGoB;;;;IADlBD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,KAAA,CAAiB;;;;;IAefJ,EAAA,CAAAK,cAAA,gBAAiD;IAC/CL,EAAA,CAAAM,MAAA,0BACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAA8C;IAC5CL,EAAA,CAAAM,MAAA,2CACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAsBZP,EAAA,CAAAK,cAAA,gBAAoD;IAClDL,EAAA,CAAAM,MAAA,6BACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAAqD;IACnDL,EAAA,CAAAM,MAAA,+CACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAkBZP,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAK,cAAA,WAAuB;IAAAL,EAAA,CAAAM,MAAA,YAAK;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;AD1DzC,OAAM,MAAOC,kBAAkB;EAoC7B;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAvCtB;;;IAGS,KAAAC,OAAO,GAAG,KAAK;IAExB;;;IAGS,KAAAP,KAAK,GAAkB,IAAI;IAEpC;;;IAGU,KAAAQ,UAAU,GAAG,IAAId,YAAY,EAAuC;IAE9E;;;IAGU,KAAAe,cAAc,GAAG,IAAIf,YAAY,EAAQ;IAEnD;;;IAGU,KAAAgB,QAAQ,GAAG,IAAIhB,YAAY,EAAQ;IAO7C;;;IAGA,KAAAiB,YAAY,GAAG,IAAI;EAMmB;EAEtC;;;;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEA;;;EAGQA,QAAQA,CAAA;IACd,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACqB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACwB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACP,SAAS,CAACQ,OAAO,IAAI,IAAI,CAACf,OAAO,EAAE;MAC1C;MACA,IAAI,CAACO,SAAS,CAACS,gBAAgB,EAAE;MACjC;;IAGF,MAAM;MAAEP,KAAK;MAAEE;IAAQ,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACU,KAAK;IAChD,IAAI,CAAChB,UAAU,CAACiB,IAAI,CAAC;MAAET,KAAK;MAAEE;IAAQ,CAAE,CAAC;EAC3C;EAEA;;;EAGAQ,gBAAgBA,CAAA;IACd,IAAI,CAACjB,cAAc,CAACgB,IAAI,EAAE;EAC5B;EAEA;;;EAGAE,UAAUA,CAAA;IACR,IAAI,CAACjB,QAAQ,CAACe,IAAI,EAAE;EACtB;EAEA;;;;;;EAMAG,QAAQA,CAACC,WAAmB,EAAEC,SAAiB;IAC7C,MAAMC,OAAO,GAAG,IAAI,CAACjB,SAAS,CAACkB,GAAG,CAACH,WAAW,CAAC;IAC/C,OAAO,CAAC,EAAEE,OAAO,IAAIA,OAAO,CAACE,OAAO,IAAIF,OAAO,CAACH,QAAQ,CAACE,SAAS,CAAC,CAAC;EACtE;EAEA;;;EAGAI,wBAAwBA,CAAA;IACtB,IAAI,CAACvB,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;;;uBAzGWP,kBAAkB,EAAAR,EAAA,CAAAuC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlBjC,kBAAkB;MAAAkC,SAAA;MAAAC,MAAA;QAAAhC,OAAA;QAAAP,KAAA;MAAA;MAAAwC,OAAA;QAAAhC,UAAA;QAAAC,cAAA;QAAAC,QAAA;MAAA;MAAA+B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ/BlD,EAAA,CAAAK,cAAA,aAAkC;UAEhCL,EAAA,CAAAoD,UAAA,IAAAC,+CAAA,+BAGoB;UAGpBrD,EAAA,CAAAK,cAAA,cAAyE;UAA3CL,EAAA,CAAAsD,UAAA,sBAAAC,qDAAA;YAAA,OAAYJ,GAAA,CAAA1B,QAAA,EAAU;UAAA,EAAC;UAEnDzB,EAAA,CAAAK,cAAA,wBAAwD;UAC3CL,EAAA,CAAAM,MAAA,YAAK;UAAAN,EAAA,CAAAO,YAAA,EAAY;UAC5BP,EAAA,CAAAC,SAAA,eAKuB;UACvBD,EAAA,CAAAK,cAAA,kBAAoB;UAAAL,EAAA,CAAAM,MAAA,YAAK;UAAAN,EAAA,CAAAO,YAAA,EAAW;UACpCP,EAAA,CAAAoD,UAAA,IAAAI,uCAAA,uBAEY;UACZxD,EAAA,CAAAoD,UAAA,KAAAK,wCAAA,uBAEY;UACdzD,EAAA,CAAAO,YAAA,EAAiB;UAGjBP,EAAA,CAAAK,cAAA,yBAAwD;UAC3CL,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAO,YAAA,EAAY;UAC/BP,EAAA,CAAAC,SAAA,gBAKkC;UAClCD,EAAA,CAAAK,cAAA,mBAAoB;UAAAL,EAAA,CAAAM,MAAA,YAAI;UAAAN,EAAA,CAAAO,YAAA,EAAW;UACnCP,EAAA,CAAAK,cAAA,iBAMqC;UAFnCL,EAAA,CAAAsD,UAAA,mBAAAI,qDAAA;YAAA,OAASP,GAAA,CAAAb,wBAAA,EAA0B;UAAA,EAAC;UAGpCtC,EAAA,CAAAK,cAAA,gBAAU;UAAAL,EAAA,CAAAM,MAAA,IAAkD;UAAAN,EAAA,CAAAO,YAAA,EAAW;UAEzEP,EAAA,CAAAoD,UAAA,KAAAO,wCAAA,uBAEY;UACZ3D,EAAA,CAAAoD,UAAA,KAAAQ,wCAAA,uBAEY;UACd5D,EAAA,CAAAO,YAAA,EAAiB;UAGjBP,EAAA,CAAAK,cAAA,cAA6B;UAEzBL,EAAA,CAAAM,MAAA,qBACF;UAAAN,EAAA,CAAAO,YAAA,EAAe;UACfP,EAAA,CAAAK,cAAA,aAAoD;UAAjDL,EAAA,CAAAsD,UAAA,mBAAAO,gDAAA;YAAA,OAASV,GAAA,CAAArB,gBAAA,EAAkB;UAAA,EAAC;UAAqB9B,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAO,YAAA,EAAI;UAI1EP,EAAA,CAAAK,cAAA,kBAKuB;UACrBL,EAAA,CAAAoD,UAAA,KAAAU,0CAAA,0BAAyD;UACzD9D,EAAA,CAAAoD,UAAA,KAAAW,mCAAA,kBAAmC;UACrC/D,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,eAA2B;UACzBL,EAAA,CAAAM,MAAA,gCACA;UAAAN,EAAA,CAAAK,cAAA,aAA0B;UAAvBL,EAAA,CAAAsD,UAAA,mBAAAU,gDAAA;YAAA,OAASb,GAAA,CAAApB,UAAA,EAAY;UAAA,EAAC;UAAC/B,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAO,YAAA,EAAI;;;UAzEvCP,EAAA,CAAAiE,SAAA,GAAW;UAAXjE,EAAA,CAAAE,UAAA,SAAAiD,GAAA,CAAA/C,KAAA,CAAW;UAKRJ,EAAA,CAAAiE,SAAA,GAAuB;UAAvBjE,EAAA,CAAAE,UAAA,cAAAiD,GAAA,CAAAjC,SAAA,CAAuB;UAWblB,EAAA,CAAAiE,SAAA,GAAmC;UAAnCjE,EAAA,CAAAE,UAAA,SAAAiD,GAAA,CAAAnB,QAAA,sBAAmC;UAGnChC,EAAA,CAAAiE,SAAA,GAAgC;UAAhCjE,EAAA,CAAAE,UAAA,SAAAiD,GAAA,CAAAnB,QAAA,mBAAgC;UAU1ChC,EAAA,CAAAiE,SAAA,GAA2C;UAA3CjE,EAAA,CAAAE,UAAA,SAAAiD,GAAA,CAAApC,YAAA,uBAA2C;UAU3Cf,EAAA,CAAAiE,SAAA,GAAmC;UAAnCjE,EAAA,CAAAkE,WAAA,+BAAmC,iBAAAf,GAAA,CAAApC,YAAA;UAEzBf,EAAA,CAAAiE,SAAA,GAAkD;UAAlDjE,EAAA,CAAAmE,iBAAA,CAAAhB,GAAA,CAAApC,YAAA,mCAAkD;UAElDf,EAAA,CAAAiE,SAAA,GAAsC;UAAtCjE,EAAA,CAAAE,UAAA,SAAAiD,GAAA,CAAAnB,QAAA,yBAAsC;UAGtChC,EAAA,CAAAiE,SAAA,GAAuC;UAAvCjE,EAAA,CAAAE,UAAA,SAAAiD,GAAA,CAAAnB,QAAA,0BAAuC;UAmBnDhC,EAAA,CAAAiE,SAAA,GAAoB;UAApBjE,EAAA,CAAAE,UAAA,aAAAiD,GAAA,CAAAxC,OAAA,CAAoB;UACQX,EAAA,CAAAiE,SAAA,GAAa;UAAbjE,EAAA,CAAAE,UAAA,SAAAiD,GAAA,CAAAxC,OAAA,CAAa;UAClCX,EAAA,CAAAiE,SAAA,GAAc;UAAdjE,EAAA,CAAAE,UAAA,UAAAiD,GAAA,CAAAxC,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}