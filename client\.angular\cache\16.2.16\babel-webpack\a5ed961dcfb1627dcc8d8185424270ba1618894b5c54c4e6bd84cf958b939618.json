{"ast": null, "code": "import { __decorate, __param } from \"tslib\";\n/**\n * Confirm Dialog Component\n * Reusable dialog for confirming user actions\n */\nimport { Component, Inject, ChangeDetectionStrategy } from '@angular/core';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nexport let ConfirmDialogComponent = class ConfirmDialogComponent {\n  /**\n   * Constructor with dependency injection\n   * @param dialogRef - Reference to the dialog\n   * @param data - Data passed to the dialog\n   */\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  /**\n   * Close the dialog with false result (cancel)\n   */\n  onCancel() {\n    this.dialogRef.close(false);\n  }\n  /**\n   * Close the dialog with true result (confirm)\n   */\n  onConfirm() {\n    this.dialogRef.close(true);\n  }\n};\nConfirmDialogComponent = __decorate([Component({\n  selector: 'app-confirm-dialog',\n  templateUrl: './confirm-dialog.component.html',\n  styleUrls: ['./confirm-dialog.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n}), __param(1, Inject(MAT_DIALOG_DATA))], ConfirmDialogComponent);", "map": {"version": 3, "names": ["Component", "Inject", "ChangeDetectionStrategy", "MAT_DIALOG_DATA", "ConfirmDialogComponent", "constructor", "dialogRef", "data", "onCancel", "close", "onConfirm", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush", "__param"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\components\\confirm-dialog\\confirm-dialog.component.ts"], "sourcesContent": ["/**\n * Confirm Dialog Component\n * Reusable dialog for confirming user actions\n */\nimport { Component, Inject, ChangeDetectionStrategy } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\n\n/**\n * Interface for dialog data\n */\nexport interface ConfirmDialogData {\n  /**\n   * Dialog title\n   */\n  title: string;\n  \n  /**\n   * Dialog message\n   */\n  message: string;\n  \n  /**\n   * Confirm button text\n   */\n  confirmText?: string;\n  \n  /**\n   * Cancel button text\n   */\n  cancelText?: string;\n  \n  /**\n   * Confirm button color\n   */\n  confirmColor?: 'primary' | 'accent' | 'warn';\n}\n\n@Component({\n  selector: 'app-confirm-dialog',\n  templateUrl: './confirm-dialog.component.html',\n  styleUrls: ['./confirm-dialog.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ConfirmDialogComponent {\n  /**\n   * Constructor with dependency injection\n   * @param dialogRef - Reference to the dialog\n   * @param data - Data passed to the dialog\n   */\n  constructor(\n    public dialogRef: MatDialogRef<ConfirmDialogComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: ConfirmDialogData\n  ) {}\n\n  /**\n   * Close the dialog with false result (cancel)\n   */\n  onCancel(): void {\n    this.dialogRef.close(false);\n  }\n\n  /**\n   * Close the dialog with true result (confirm)\n   */\n  onConfirm(): void {\n    this.dialogRef.close(true);\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAEC,MAAM,EAAEC,uBAAuB,QAAQ,eAAe;AAC1E,SAASC,eAAe,QAAsB,0BAA0B;AAsCjE,WAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EACjC;;;;;EAKAC,YACSC,SAA+C,EACtBC,IAAuB;IADhD,KAAAD,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;EACnC;EAEH;;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACF,SAAS,CAACG,KAAK,CAAC,KAAK,CAAC;EAC7B;EAEA;;;EAGAC,SAASA,CAAA;IACP,IAAI,CAACJ,SAAS,CAACG,KAAK,CAAC,IAAI,CAAC;EAC5B;CACD;AAxBYL,sBAAsB,GAAAO,UAAA,EANlCX,SAAS,CAAC;EACTY,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC,CAAC;EAC9CC,eAAe,EAAEb,uBAAuB,CAACc;CAC1C,CAAC,EASGC,OAAA,IAAAhB,MAAM,CAACE,eAAe,CAAC,E,EARfC,sBAAsB,CAwBlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}