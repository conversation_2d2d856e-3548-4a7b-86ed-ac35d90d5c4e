/**
 * Header component styles
 */

/* Fixed header at the top of the page */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  height: 64px;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Brand section with logo and title */
.header-brand {
  display: flex;
  align-items: center;
  
  .brand-link {
    text-decoration: none;
    color: white;
    display: flex;
    align-items: center;
  }
  
  .brand-title {
    font-size: 1.2rem;
    font-weight: 500;
    margin-left: 8px;
  }
}

/* Flexible spacer */
.spacer {
  flex: 1 1 auto;
}

/* Navigation links */
.nav-links {
  display: flex;
  align-items: center;
  
  a {
    display: flex;
    align-items: center;
    margin: 0 4px;
    
    mat-icon {
      margin-right: 4px;
    }
    
    &.active {
      background-color: rgba(255, 255, 255, 0.15);
    }
  }
}

/* User menu section */
.user-menu {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

/* User dropdown styling */
.user-dropdown {
  position: relative;
  
  .user-button {
    display: flex;
    align-items: center;
    padding: 0 8px;
    height: 36px;
    border-radius: 18px;
    background-color: rgba(255, 255, 255, 0.1);
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #fff;
    color: #3f51b5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    background-size: cover;
    background-position: center;
  }
  
  .user-name {
    margin: 0 8px;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .nav-links span {
    display: none;
  }
  
  .user-name {
    display: none;
  }
  
  .header-brand .brand-title {
    font-size: 1rem;
  }
}
