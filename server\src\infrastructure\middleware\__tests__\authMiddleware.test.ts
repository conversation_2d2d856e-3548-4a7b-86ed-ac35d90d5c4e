/**
 * Tests for Authentication Middleware
 * Verifies the middleware functions for protecting routes and restricting access
 */
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { protect, restrictTo } from '../authMiddleware';
import { MongoUserRepository } from '../../repositories/MongoUserRepository';
import { UserRole } from '../../../domain/entities/User';
import { AppError } from '../../../domain/common/AppError';

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('../../repositories/MongoUserRepository');

// Mock request, response, and next function
const mockRequest = () => {
  const req: Partial<Request> = {
    headers: {},
    user: undefined,
  };
  return req as Request;
};

const mockResponse = () => {
  const res: Partial<Response> = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn(),
  };
  return res as Response;
};

const mockNext: NextFunction = jest.fn();

// Mock user repository
const mockUserRepository = new MongoUserRepository() as jest.Mocked<MongoUserRepository>;

// Mock user
const mockUser = {
  id: '123456789012',
  name: 'Test User',
  email: '<EMAIL>',
  role: UserRole.USER,
};

describe('Authentication Middleware', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.JWT_SECRET = 'test-secret';
  });

  describe('protect middleware', () => {
    it('should throw an error if no token is provided', async () => {
      // Arrange
      const req = mockRequest();
      req.headers.authorization = undefined;

      // Act
      await protect(req, mockResponse(), mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'You are not logged in. Please log in to get access',
          statusCode: 401,
        })
      );
    });

    it('should throw an error if token format is invalid', async () => {
      // Arrange
      const req = mockRequest();
      req.headers.authorization = 'Invalid-token-format';

      // Act
      await protect(req, mockResponse(), mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'You are not logged in. Please log in to get access',
          statusCode: 401,
        })
      );
    });

    it('should throw an error if token is invalid', async () => {
      // Arrange
      const req = mockRequest();
      req.headers.authorization = 'Bearer invalid-token';
      (jwt.verify as jest.Mock).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act
      await protect(req, mockResponse(), mockNext);

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith('invalid-token', 'test-secret');
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid token or user no longer exists',
          statusCode: 401,
        })
      );
    });

    it('should throw an error if user no longer exists', async () => {
      // Arrange
      const req = mockRequest();
      req.headers.authorization = 'Bearer valid-token';
      (jwt.verify as jest.Mock).mockReturnValue({ id: '123456789012' });
      mockUserRepository.findById.mockResolvedValue(null);

      // Act
      await protect(req, mockResponse(), mockNext);

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith('valid-token', 'test-secret');
      expect(mockUserRepository.findById).toHaveBeenCalledWith('123456789012');
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'The user belonging to this token no longer exists',
          statusCode: 401,
        })
      );
    });

    it('should set user on request and call next if token is valid', async () => {
      // Arrange
      const req = mockRequest();
      req.headers.authorization = 'Bearer valid-token';
      (jwt.verify as jest.Mock).mockReturnValue({ id: '123456789012' });
      mockUserRepository.findById.mockResolvedValue(mockUser as any);

      // Act
      await protect(req, mockResponse(), mockNext);

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith('valid-token', 'test-secret');
      expect(mockUserRepository.findById).toHaveBeenCalledWith('123456789012');
      expect(req.user).toEqual(mockUser);
      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('restrictTo middleware', () => {
    it('should throw an error if user is not in allowed roles', () => {
      // Arrange
      const req = mockRequest();
      req.user = { ...mockUser, role: UserRole.USER };
      const restrictToAdmin = restrictTo(UserRole.ADMIN);

      // Act
      restrictToAdmin(req, mockResponse(), mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'You do not have permission to perform this action',
          statusCode: 403,
        })
      );
    });

    it('should call next if user is in allowed roles', () => {
      // Arrange
      const req = mockRequest();
      req.user = { ...mockUser, role: UserRole.ADMIN };
      const restrictToAdmin = restrictTo(UserRole.ADMIN);

      // Act
      restrictToAdmin(req, mockResponse(), mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should accept multiple roles and allow if user has any of them', () => {
      // Arrange
      const req = mockRequest();
      req.user = { ...mockUser, role: UserRole.ADMIN };
      const restrictToAdminOrUser = restrictTo(UserRole.USER, UserRole.ADMIN);

      // Act
      restrictToAdminOrUser(req, mockResponse(), mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalledWith();
    });
  });
});
