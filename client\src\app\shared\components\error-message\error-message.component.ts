/**
 * Error Message Component
 * Displays error messages with consistent styling
 */
import { Component, Input, ChangeDetectionStrategy } from '@angular/core';

@Component({
  selector: 'app-error-message',
  templateUrl: './error-message.component.html',
  styleUrls: ['./error-message.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ErrorMessageComponent {
  /**
   * Error message to display
   */
  @Input() message = 'An error occurred';
  
  /**
   * Whether to show an icon with the message
   */
  @Input() showIcon = true;
  
  /**
   * Whether to show a retry button
   */
  @Input() showRetry = false;
  
  /**
   * Event handler for retry button click
   */
  @Input() onRetry: () => void = () => {};
}
