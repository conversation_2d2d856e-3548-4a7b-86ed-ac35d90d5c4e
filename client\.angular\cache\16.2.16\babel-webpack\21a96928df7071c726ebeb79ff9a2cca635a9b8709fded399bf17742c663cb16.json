{"ast": null, "code": "import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function distinctUntilChanged(comparator, keySelector = identity) {\n  comparator = comparator !== null && comparator !== void 0 ? comparator : defaultCompare;\n  return operate((source, subscriber) => {\n    let previousKey;\n    let first = true;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const currentKey = keySelector(value);\n      if (first || !comparator(previousKey, currentKey)) {\n        first = false;\n        previousKey = currentKey;\n        subscriber.next(value);\n      }\n    }));\n  });\n}\nfunction defaultCompare(a, b) {\n  return a === b;\n}", "map": {"version": 3, "names": ["identity", "operate", "createOperatorSubscriber", "distinctUntilChanged", "comparator", "keySelector", "defaultCompare", "source", "subscriber", "previousKey", "first", "subscribe", "value", "current<PERSON><PERSON>", "next", "a", "b"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/esm/internal/operators/distinctUntilChanged.js"], "sourcesContent": ["import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function distinctUntilChanged(comparator, keySelector = identity) {\n    comparator = comparator !== null && comparator !== void 0 ? comparator : defaultCompare;\n    return operate((source, subscriber) => {\n        let previousKey;\n        let first = true;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const currentKey = keySelector(value);\n            if (first || !comparator(previousKey, currentKey)) {\n                first = false;\n                previousKey = currentKey;\n                subscriber.next(value);\n            }\n        }));\n    });\n}\nfunction defaultCompare(a, b) {\n    return a === b;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,oBAAoBA,CAACC,UAAU,EAAEC,WAAW,GAAGL,QAAQ,EAAE;EACrEI,UAAU,GAAGA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGE,cAAc;EACvF,OAAOL,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,WAAW;IACf,IAAIC,KAAK,GAAG,IAAI;IAChBH,MAAM,CAACI,SAAS,CAACT,wBAAwB,CAACM,UAAU,EAAGI,KAAK,IAAK;MAC7D,MAAMC,UAAU,GAAGR,WAAW,CAACO,KAAK,CAAC;MACrC,IAAIF,KAAK,IAAI,CAACN,UAAU,CAACK,WAAW,EAAEI,UAAU,CAAC,EAAE;QAC/CH,KAAK,GAAG,KAAK;QACbD,WAAW,GAAGI,UAAU;QACxBL,UAAU,CAACM,IAAI,CAACF,KAAK,CAAC;MAC1B;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACA,SAASN,cAAcA,CAACS,CAAC,EAAEC,CAAC,EAAE;EAC1B,OAAOD,CAAC,KAAKC,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}