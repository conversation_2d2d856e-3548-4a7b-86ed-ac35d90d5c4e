{"ast": null, "code": "import * as i1$2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, Directive, Inject, EventEmitter, Optional, Output, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, ContentChild, ContentChildren, QueryList, Attribute, NgModule } from '@angular/core';\nimport * as i5 from '@angular/material/core';\nimport { mixinDisabled, mixinColor, mixinDisableRipple, mixinTabIndex, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/portal';\nimport { CdkPortalOutlet, CdkPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i5$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i4 from '@angular/cdk/a11y';\nimport { FocusKeyManager, A11yModule } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/bidi';\nimport { Subscription, Subject, fromEvent, of, merge, EMPTY, Observable, timer, BehaviorSubject } from 'rxjs';\nimport { startWith, distinctUntilChanged, takeUntil, take, switchMap, skip, filter } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nfunction MatTabBody_ng_template_2_Template(rf, ctx) {}\nconst _c0 = function (a0) {\n  return {\n    animationDuration: a0\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    value: a0,\n    params: a1\n  };\n};\nfunction MatTab_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c2 = [\"*\"];\nconst _c3 = [\"tabListContainer\"];\nconst _c4 = [\"tabList\"];\nconst _c5 = [\"tabListInner\"];\nconst _c6 = [\"nextPaginator\"];\nconst _c7 = [\"previousPaginator\"];\nconst _c8 = [\"tabBodyWrapper\"];\nconst _c9 = [\"tabHeader\"];\nfunction MatTabGroup_div_2_ng_template_6_ng_template_0_Template(rf, ctx) {}\nfunction MatTabGroup_div_2_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatTabGroup_div_2_ng_template_6_ng_template_0_Template, 0, 0, \"ng-template\", 14);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"cdkPortalOutlet\", tab_r4.templateLabel);\n  }\n}\nfunction MatTabGroup_div_2_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(tab_r4.textLabel);\n  }\n}\nfunction MatTabGroup_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6, 7);\n    i0.ɵɵlistener(\"click\", function MatTabGroup_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const tab_r4 = restoredCtx.$implicit;\n      const i_r5 = restoredCtx.index;\n      const ctx_r13 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(ctx_r13._handleClick(tab_r4, _r0, i_r5));\n    })(\"cdkFocusChange\", function MatTabGroup_div_2_Template_div_cdkFocusChange_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const i_r5 = restoredCtx.index;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15._tabFocusChanged($event, i_r5));\n    });\n    i0.ɵɵelement(2, \"span\", 8)(3, \"div\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10)(5, \"span\", 11);\n    i0.ɵɵtemplate(6, MatTabGroup_div_2_ng_template_6_Template, 1, 1, \"ng-template\", 12);\n    i0.ɵɵtemplate(7, MatTabGroup_div_2_ng_template_7_Template, 1, 1, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const _r6 = i0.ɵɵreference(1);\n    const _r8 = i0.ɵɵreference(8);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mdc-tab--active\", ctx_r1.selectedIndex === i_r5);\n    i0.ɵɵproperty(\"id\", ctx_r1._getTabLabelId(i_r5))(\"ngClass\", tab_r4.labelClass)(\"disabled\", tab_r4.disabled)(\"fitInkBarToContent\", ctx_r1.fitInkBarToContent);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r1._getTabIndex(i_r5))(\"aria-posinset\", i_r5 + 1)(\"aria-setsize\", ctx_r1._tabs.length)(\"aria-controls\", ctx_r1._getTabContentId(i_r5))(\"aria-selected\", ctx_r1.selectedIndex === i_r5)(\"aria-label\", tab_r4.ariaLabel || null)(\"aria-labelledby\", !tab_r4.ariaLabel && tab_r4.ariaLabelledby ? tab_r4.ariaLabelledby : null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matRippleTrigger\", _r6)(\"matRippleDisabled\", tab_r4.disabled || ctx_r1.disableRipple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", tab_r4.templateLabel)(\"ngIfElse\", _r8);\n  }\n}\nfunction MatTabGroup_mat_tab_body_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab-body\", 15);\n    i0.ɵɵlistener(\"_onCentered\", function MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentered_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18._removeTabBodyWrapperHeight());\n    })(\"_onCentering\", function MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20._setTabBodyWrapperHeight($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r16 = ctx.$implicit;\n    const i_r17 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-mdc-tab-body-active\", ctx_r3.selectedIndex === i_r17);\n    i0.ɵɵproperty(\"id\", ctx_r3._getTabContentId(i_r17))(\"ngClass\", tab_r16.bodyClass)(\"content\", tab_r16.content)(\"position\", tab_r16.position)(\"origin\", tab_r16.origin)(\"animationDuration\", ctx_r3.animationDuration)(\"preserveContent\", ctx_r3.preserveContent);\n    i0.ɵɵattribute(\"tabindex\", ctx_r3.contentTabIndex != null && ctx_r3.selectedIndex === i_r17 ? ctx_r3.contentTabIndex : null)(\"aria-labelledby\", ctx_r3._getTabLabelId(i_r17))(\"aria-hidden\", ctx_r3.selectedIndex !== i_r17);\n  }\n}\nconst _c10 = [\"mat-tab-nav-bar\", \"\"];\nconst _c11 = [\"mat-tab-link\", \"\"];\nconst matTabsAnimations = {\n  /** Animation translates a tab along the X axis. */\n  translateTab: trigger('translateTab', [\n  // Transitions to `none` instead of 0, because some browsers might blur the content.\n  state('center, void, left-origin-center, right-origin-center', style({\n    transform: 'none'\n  })),\n  // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  // in order to ensure that the element has a height before its state changes. This is\n  // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n  // not have a static height and is not rendered. See related issue: #9465\n  state('left', style({\n    transform: 'translate3d(-100%, 0, 0)',\n    minHeight: '1px',\n    // Normally this is redundant since we detach the content from the DOM, but if the user\n    // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n    visibility: 'hidden'\n  })), state('right', style({\n    transform: 'translate3d(100%, 0, 0)',\n    minHeight: '1px',\n    visibility: 'hidden'\n  })), transition('* => left, * => right, left => center, right => center', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')), transition('void => left-origin-center', [style({\n    transform: 'translate3d(-100%, 0, 0)',\n    visibility: 'hidden'\n  }), animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')]), transition('void => right-origin-center', [style({\n    transform: 'translate3d(100%, 0, 0)',\n    visibility: 'hidden'\n  }), animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')])])\n};\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n  constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n    super(componentFactoryResolver, viewContainerRef, _document);\n    this._host = _host;\n    /** Subscription to events for when the tab body begins centering. */\n    this._centeringSub = Subscription.EMPTY;\n    /** Subscription to events for when the tab body finishes leaving from center position. */\n    this._leavingSub = Subscription.EMPTY;\n  }\n  /** Set initial visibility or set up subscription for changing visibility. */\n  ngOnInit() {\n    super.ngOnInit();\n    this._centeringSub = this._host._beforeCentering.pipe(startWith(this._host._isCenterPosition(this._host._position))).subscribe(isCentering => {\n      if (isCentering && !this.hasAttached()) {\n        this.attach(this._host._content);\n      }\n    });\n    this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n      if (!this._host.preserveContent) {\n        this.detach();\n      }\n    });\n  }\n  /** Clean up centering subscription. */\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._centeringSub.unsubscribe();\n    this._leavingSub.unsubscribe();\n  }\n  static {\n    this.ɵfac = function MatTabBodyPortal_Factory(t) {\n      return new (t || MatTabBodyPortal)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(forwardRef(() => MatTabBody)), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTabBodyPortal,\n      selectors: [[\"\", \"matTabBodyHost\", \"\"]],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBodyPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabBodyHost]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: MatTabBody,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatTabBody)]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Base class with all of the `MatTabBody` functionality.\n * @docs-private\n */\nclass _MatTabBodyBase {\n  /** The shifted index position of the tab body, where zero represents the active center tab. */\n  set position(position) {\n    this._positionIndex = position;\n    this._computePositionAnimationState();\n  }\n  constructor(_elementRef, _dir, changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._dir = _dir;\n    /** Subscription to the directionality change observable. */\n    this._dirChangeSubscription = Subscription.EMPTY;\n    /** Emits when an animation on the tab is complete. */\n    this._translateTabComplete = new Subject();\n    /** Event emitted when the tab begins to animate towards the center as the active tab. */\n    this._onCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    this._beforeCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    this._afterLeavingCenter = new EventEmitter();\n    /** Event emitted when the tab completes its animation towards the center. */\n    this._onCentered = new EventEmitter(true);\n    // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n    // anyway to prevent the animations module from throwing an error if the body is used on its own.\n    /** Duration for the tab's animation. */\n    this.animationDuration = '500ms';\n    /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n    this.preserveContent = false;\n    if (_dir) {\n      this._dirChangeSubscription = _dir.change.subscribe(dir => {\n        this._computePositionAnimationState(dir);\n        changeDetectorRef.markForCheck();\n      });\n    }\n    // Ensure that we get unique animation events, because the `.done` callback can get\n    // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n    this._translateTabComplete.pipe(distinctUntilChanged((x, y) => {\n      return x.fromState === y.fromState && x.toState === y.toState;\n    })).subscribe(event => {\n      // If the transition to the center is complete, emit an event.\n      if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n        this._onCentered.emit();\n      }\n      if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n        this._afterLeavingCenter.emit();\n      }\n    });\n  }\n  /**\n   * After initialized, check if the content is centered and has an origin. If so, set the\n   * special position states that transition the tab from the left or right before centering.\n   */\n  ngOnInit() {\n    if (this._position == 'center' && this.origin != null) {\n      this._position = this._computePositionFromOrigin(this.origin);\n    }\n  }\n  ngOnDestroy() {\n    this._dirChangeSubscription.unsubscribe();\n    this._translateTabComplete.complete();\n  }\n  _onTranslateTabStarted(event) {\n    const isCentering = this._isCenterPosition(event.toState);\n    this._beforeCentering.emit(isCentering);\n    if (isCentering) {\n      this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n    }\n  }\n  /** The text direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the provided position state is considered center, regardless of origin. */\n  _isCenterPosition(position) {\n    return position == 'center' || position == 'left-origin-center' || position == 'right-origin-center';\n  }\n  /** Computes the position state that will be used for the tab-body animation trigger. */\n  _computePositionAnimationState(dir = this._getLayoutDirection()) {\n    if (this._positionIndex < 0) {\n      this._position = dir == 'ltr' ? 'left' : 'right';\n    } else if (this._positionIndex > 0) {\n      this._position = dir == 'ltr' ? 'right' : 'left';\n    } else {\n      this._position = 'center';\n    }\n  }\n  /**\n   * Computes the position state based on the specified origin position. This is used if the\n   * tab is becoming visible immediately after creation.\n   */\n  _computePositionFromOrigin(origin) {\n    const dir = this._getLayoutDirection();\n    if (dir == 'ltr' && origin <= 0 || dir == 'rtl' && origin > 0) {\n      return 'left-origin-center';\n    }\n    return 'right-origin-center';\n  }\n  static {\n    this.ɵfac = function _MatTabBodyBase_Factory(t) {\n      return new (t || _MatTabBodyBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatTabBodyBase,\n      inputs: {\n        _content: [\"content\", \"_content\"],\n        origin: \"origin\",\n        animationDuration: \"animationDuration\",\n        preserveContent: \"preserveContent\",\n        position: \"position\"\n      },\n      outputs: {\n        _onCentering: \"_onCentering\",\n        _beforeCentering: \"_beforeCentering\",\n        _afterLeavingCenter: \"_afterLeavingCenter\",\n        _onCentered: \"_onCentered\"\n      }\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabBodyBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    _onCentering: [{\n      type: Output\n    }],\n    _beforeCentering: [{\n      type: Output\n    }],\n    _afterLeavingCenter: [{\n      type: Output\n    }],\n    _onCentered: [{\n      type: Output\n    }],\n    _content: [{\n      type: Input,\n      args: ['content']\n    }],\n    origin: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody extends _MatTabBodyBase {\n  constructor(elementRef, dir, changeDetectorRef) {\n    super(elementRef, dir, changeDetectorRef);\n  }\n  static {\n    this.ɵfac = function MatTabBody_Factory(t) {\n      return new (t || MatTabBody)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabBody,\n      selectors: [[\"mat-tab-body\"]],\n      viewQuery: function MatTabBody_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalHost = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-tab-body\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 3,\n      vars: 6,\n      consts: [[\"cdkScrollable\", \"\", 1, \"mat-mdc-tab-body-content\"], [\"content\", \"\"], [\"matTabBodyHost\", \"\"]],\n      template: function MatTabBody_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0, 1);\n          i0.ɵɵlistener(\"@translateTab.start\", function MatTabBody_Template_div_animation_translateTab_start_0_listener($event) {\n            return ctx._onTranslateTabStarted($event);\n          })(\"@translateTab.done\", function MatTabBody_Template_div_animation_translateTab_done_0_listener($event) {\n            return ctx._translateTabComplete.next($event);\n          });\n          i0.ɵɵtemplate(2, MatTabBody_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"@translateTab\", i0.ɵɵpureFunction2(3, _c1, ctx._position, i0.ɵɵpureFunction1(1, _c0, ctx.animationDuration)));\n        }\n      },\n      dependencies: [MatTabBodyPortal],\n      styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matTabsAnimations.translateTab]\n      }\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBody, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-body',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      animations: [matTabsAnimations.translateTab],\n      host: {\n        'class': 'mat-mdc-tab-body'\n      },\n      template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\",\n      styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    _portalHost: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet]\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n  constructor( /** Content for the tab. */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function MatTabContent_Factory(t) {\n      return new (t || MatTabContent)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTabContent,\n      selectors: [[\"\", \"matTabContent\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_TAB_CONTENT,\n        useExisting: MatTabContent\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabContent, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabContent]',\n      providers: [{\n        provide: MAT_TAB_CONTENT,\n        useExisting: MatTabContent\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n  constructor(templateRef, viewContainerRef, _closestTab) {\n    super(templateRef, viewContainerRef);\n    this._closestTab = _closestTab;\n  }\n  static {\n    this.ɵfac = function MatTabLabel_Factory(t) {\n      return new (t || MatTabLabel)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTabLabel,\n      selectors: [[\"\", \"mat-tab-label\", \"\"], [\"\", \"matTabLabel\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_TAB_LABEL,\n        useExisting: MatTabLabel\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-tab-label], [matTabLabel]',\n      providers: [{\n        provide: MAT_TAB_LABEL,\n        useExisting: MatTabLabel\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TAB]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n  constructor(_items) {\n    this._items = _items;\n  }\n  /** Hides the ink bar. */\n  hide() {\n    this._items.forEach(item => item.deactivateInkBar());\n  }\n  /** Aligns the ink bar to a DOM node. */\n  alignToElement(element) {\n    const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n    const currentItem = this._currentItem;\n    if (correspondingItem === currentItem) {\n      return;\n    }\n    currentItem?.deactivateInkBar();\n    if (correspondingItem) {\n      const clientRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n      // The ink bar won't animate unless we give it the `ClientRect` of the previous item.\n      correspondingItem.activateInkBar(clientRect);\n      this._currentItem = correspondingItem;\n    }\n  }\n}\n/**\n * Mixin that can be used to apply the `MatInkBarItem` behavior to a class.\n * Base on MDC's `MDCSlidingTabIndicatorFoundation`:\n * https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-tab-indicator/sliding-foundation.ts\n * @docs-private\n */\nfunction mixinInkBarItem(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this._fitToContent = false;\n    }\n    /** Whether the ink bar should fit to the entire tab or just its content. */\n    get fitInkBarToContent() {\n      return this._fitToContent;\n    }\n    set fitInkBarToContent(v) {\n      const newValue = coerceBooleanProperty(v);\n      if (this._fitToContent !== newValue) {\n        this._fitToContent = newValue;\n        if (this._inkBarElement) {\n          this._appendInkBarElement();\n        }\n      }\n    }\n    /** Aligns the ink bar to the current item. */\n    activateInkBar(previousIndicatorClientRect) {\n      const element = this.elementRef.nativeElement;\n      // Early exit if no indicator is present to handle cases where an indicator\n      // may be activated without a prior indicator state\n      if (!previousIndicatorClientRect || !element.getBoundingClientRect || !this._inkBarContentElement) {\n        element.classList.add(ACTIVE_CLASS);\n        return;\n      }\n      // This animation uses the FLIP approach. You can read more about it at the link below:\n      // https://aerotwist.com/blog/flip-your-animations/\n      // Calculate the dimensions based on the dimensions of the previous indicator\n      const currentClientRect = element.getBoundingClientRect();\n      const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n      const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n      element.classList.add(NO_TRANSITION_CLASS);\n      this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n      // Force repaint before updating classes and transform to ensure the transform properly takes effect\n      element.getBoundingClientRect();\n      element.classList.remove(NO_TRANSITION_CLASS);\n      element.classList.add(ACTIVE_CLASS);\n      this._inkBarContentElement.style.setProperty('transform', '');\n    }\n    /** Removes the ink bar from the current item. */\n    deactivateInkBar() {\n      this.elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n    }\n    /** Initializes the foundation. */\n    ngOnInit() {\n      this._createInkBarElement();\n    }\n    /** Destroys the foundation. */\n    ngOnDestroy() {\n      this._inkBarElement?.remove();\n      this._inkBarElement = this._inkBarContentElement = null;\n    }\n    /** Creates and appends the ink bar element. */\n    _createInkBarElement() {\n      const documentNode = this.elementRef.nativeElement.ownerDocument || document;\n      this._inkBarElement = documentNode.createElement('span');\n      this._inkBarContentElement = documentNode.createElement('span');\n      this._inkBarElement.className = 'mdc-tab-indicator';\n      this._inkBarContentElement.className = 'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n      this._inkBarElement.appendChild(this._inkBarContentElement);\n      this._appendInkBarElement();\n    }\n    /**\n     * Appends the ink bar to the tab host element or content, depending on whether\n     * the ink bar should fit to content.\n     */\n    _appendInkBarElement() {\n      if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Ink bar element has not been created and cannot be appended');\n      }\n      const parentElement = this._fitToContent ? this.elementRef.nativeElement.querySelector('.mdc-tab__content') : this.elementRef.nativeElement;\n      if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Missing element to host the ink bar');\n      }\n      parentElement.appendChild(this._inkBarElement);\n    }\n  };\n}\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n  const method = element => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0'\n  });\n  return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n  providedIn: 'root',\n  factory: _MAT_INK_BAR_POSITIONER_FACTORY\n});\n\n// Boilerplate for applying mixins to MatTabLabelWrapper.\n/** @docs-private */\nconst _MatTabLabelWrapperMixinBase = mixinDisabled(class {});\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass _MatTabLabelWrapperBase extends _MatTabLabelWrapperMixinBase {\n  constructor(elementRef) {\n    super();\n    this.elementRef = elementRef;\n  }\n  /** Sets focus on the wrapper element */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  getOffsetLeft() {\n    return this.elementRef.nativeElement.offsetLeft;\n  }\n  getOffsetWidth() {\n    return this.elementRef.nativeElement.offsetWidth;\n  }\n  static {\n    this.ɵfac = function _MatTabLabelWrapperBase_Factory(t) {\n      return new (t || _MatTabLabelWrapperBase)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatTabLabelWrapperBase,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabLabelWrapperBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\nconst _MatTabLabelWrapperBaseWithInkBarItem = mixinInkBarItem(_MatTabLabelWrapperBase);\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends _MatTabLabelWrapperBaseWithInkBarItem {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatTabLabelWrapper_BaseFactory;\n      return function MatTabLabelWrapper_Factory(t) {\n        return (ɵMatTabLabelWrapper_BaseFactory || (ɵMatTabLabelWrapper_BaseFactory = i0.ɵɵgetInheritedFactory(MatTabLabelWrapper)))(t || MatTabLabelWrapper);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTabLabelWrapper,\n      selectors: [[\"\", \"matTabLabelWrapper\", \"\"]],\n      hostVars: 3,\n      hostBindings: function MatTabLabelWrapper_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-disabled\", !!ctx.disabled);\n          i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        fitInkBarToContent: \"fitInkBarToContent\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabelWrapper, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabLabelWrapper]',\n      inputs: ['disabled', 'fitInkBarToContent'],\n      host: {\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[attr.aria-disabled]': '!!disabled'\n      }\n    }]\n  }], null, null);\n})();\n\n// Boilerplate for applying mixins to MatTab.\n/** @docs-private */\nconst _MatTabMixinBase = mixinDisabled(class {});\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\n/** @docs-private */\nclass _MatTabBase extends _MatTabMixinBase {\n  /** @docs-private */\n  get content() {\n    return this._contentPortal;\n  }\n  constructor(_viewContainerRef, _closestTabGroup) {\n    super();\n    this._viewContainerRef = _viewContainerRef;\n    this._closestTabGroup = _closestTabGroup;\n    /** Plain text label for the tab, used when there is no template label. */\n    this.textLabel = '';\n    /** Portal that will be the hosted content of the tab */\n    this._contentPortal = null;\n    /** Emits whenever the internal state of the tab changes. */\n    this._stateChanges = new Subject();\n    /**\n     * The relatively indexed position where 0 represents the center, negative is left, and positive\n     * represents the right.\n     */\n    this.position = null;\n    /**\n     * The initial relatively index origin of the tab if it was created and selected after there\n     * was already a selected tab. Provides context of what position the tab should originate from.\n     */\n    this.origin = null;\n    /**\n     * Whether the tab is currently active.\n     */\n    this.isActive = false;\n  }\n  ngOnChanges(changes) {\n    if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n      this._stateChanges.next();\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  ngOnInit() {\n    this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setTemplateLabelInput(value) {\n    // Only update the label if the query managed to find one. This works around an issue where a\n    // user may have manually set `templateLabel` during creation mode, which would then get\n    // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n    // tab matches the current one so that we don't pick up labels from nested tabs.\n    if (value && value._closestTab === this) {\n      this._templateLabel = value;\n    }\n  }\n  static {\n    this.ɵfac = function _MatTabBase_Factory(t) {\n      return new (t || _MatTabBase)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB_GROUP, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatTabBase,\n      viewQuery: function _MatTabBase_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._implicitContent = _t.first);\n        }\n      },\n      inputs: {\n        textLabel: [\"label\", \"textLabel\"],\n        ariaLabel: [\"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n        labelClass: \"labelClass\",\n        bodyClass: \"bodyClass\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TAB_GROUP]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    _implicitContent: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    textLabel: [{\n      type: Input,\n      args: ['label']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    labelClass: [{\n      type: Input\n    }],\n    bodyClass: [{\n      type: Input\n    }]\n  });\n})();\nclass MatTab extends _MatTabBase {\n  constructor() {\n    super(...arguments);\n    /**\n     * Template provided in the tab content that will be used if present, used to enable lazy-loading\n     */\n    this._explicitContent = undefined;\n  }\n  /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n  get templateLabel() {\n    return this._templateLabel;\n  }\n  set templateLabel(value) {\n    this._setTemplateLabelInput(value);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatTab_BaseFactory;\n      return function MatTab_Factory(t) {\n        return (ɵMatTab_BaseFactory || (ɵMatTab_BaseFactory = i0.ɵɵgetInheritedFactory(MatTab)))(t || MatTab);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTab,\n      selectors: [[\"mat-tab\"]],\n      contentQueries: function MatTab_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatTabContent, 7, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, MatTabLabel, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._explicitContent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateLabel = _t.first);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\"\n      },\n      exportAs: [\"matTab\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_TAB,\n        useExisting: MatTab\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c2,\n      decls: 1,\n      vars: 0,\n      template: function MatTab_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, MatTab_ng_template_0_Template, 1, 0, \"ng-template\");\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTab, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab',\n      inputs: ['disabled'],\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matTab',\n      providers: [{\n        provide: MAT_TAB,\n        useExisting: MatTab\n      }],\n      template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\"\n    }]\n  }], null, {\n    _explicitContent: [{\n      type: ContentChild,\n      args: [MatTabContent, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    templateLabel: [{\n      type: ContentChild,\n      args: [MatTabLabel]\n    }]\n  });\n})();\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  get disablePagination() {\n    return this._disablePagination;\n  }\n  set disablePagination(value) {\n    this._disablePagination = coerceBooleanProperty(value);\n  }\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value) {\n    value = coerceNumberProperty(value);\n    if (this._selectedIndex != value) {\n      this._selectedIndexChanged = true;\n      this._selectedIndex = value;\n      if (this._keyManager) {\n        this._keyManager.updateActiveItem(value);\n      }\n    }\n  }\n  constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._viewportRuler = _viewportRuler;\n    this._dir = _dir;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._animationMode = _animationMode;\n    /** The distance in pixels that the tab labels should be translated to the left. */\n    this._scrollDistance = 0;\n    /** Whether the header should scroll to the selected index after the view has been checked. */\n    this._selectedIndexChanged = false;\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /** Whether the controls for pagination should be displayed */\n    this._showPaginationControls = false;\n    /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n    this._disableScrollAfter = true;\n    /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n    this._disableScrollBefore = true;\n    /** Stream that will stop the automated scrolling. */\n    this._stopScrolling = new Subject();\n    this._disablePagination = false;\n    this._selectedIndex = 0;\n    /** Event emitted when the option is selected. */\n    this.selectFocusedIndex = new EventEmitter();\n    /** Event emitted when a label is focused. */\n    this.indexFocused = new EventEmitter();\n    // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n    _ngZone.runOutsideAngular(() => {\n      fromEvent(_elementRef.nativeElement, 'mouseleave').pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._stopInterval();\n      });\n    });\n  }\n  ngAfterViewInit() {\n    // We need to handle these events manually, because we want to bind passive event listeners.\n    fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._handlePaginatorPress('before');\n    });\n    fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._handlePaginatorPress('after');\n    });\n  }\n  ngAfterContentInit() {\n    const dirChange = this._dir ? this._dir.change : of('ltr');\n    const resize = this._viewportRuler.change(150);\n    const realign = () => {\n      this.updatePagination();\n      this._alignInkBarToSelectedTab();\n    };\n    this._keyManager = new FocusKeyManager(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap()\n    // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n    .skipPredicate(() => false);\n    this._keyManager.updateActiveItem(this._selectedIndex);\n    // Defer the first call in order to allow for slower browsers to lay out the elements.\n    // This helps in cases where the user lands directly on a page with paginated tabs.\n    // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n    // can hold up tests that are in a background tab.\n    this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n    // On dir change or window resize, realign the ink bar and update the orientation of\n    // the key manager if the direction has changed.\n    merge(dirChange, resize, this._items.changes, this._itemsResized()).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      // We need to defer this to give the browser some time to recalculate\n      // the element dimensions. The call has to be wrapped in `NgZone.run`,\n      // because the viewport change handler runs outside of Angular.\n      this._ngZone.run(() => {\n        Promise.resolve().then(() => {\n          // Clamp the scroll distance, because it can change with the number of tabs.\n          this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n          realign();\n        });\n      });\n      this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n    });\n    // If there is a change in the focus key manager we need to emit the `indexFocused`\n    // event in order to provide a public event that notifies about focus changes. Also we realign\n    // the tabs container by scrolling the new focused tab into the visible section.\n    this._keyManager.change.subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this._setTabFocus(newFocusIndex);\n    });\n  }\n  /** Sends any changes that could affect the layout of the items. */\n  _itemsResized() {\n    if (typeof ResizeObserver !== 'function') {\n      return EMPTY;\n    }\n    return this._items.changes.pipe(startWith(this._items), switchMap(tabItems => new Observable(observer => this._ngZone.runOutsideAngular(() => {\n      const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n      tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n      return () => {\n        resizeObserver.disconnect();\n      };\n    }))),\n    // Skip the first emit since the resize observer emits when an item\n    // is observed for new items when the tab is already inserted\n    skip(1),\n    // Skip emissions where all the elements are invisible since we don't want\n    // the header to try and re-render with invalid measurements. See #25574.\n    filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n  }\n  ngAfterContentChecked() {\n    // If the number of tab labels have changed, check if scrolling should be enabled\n    if (this._tabLabelCount != this._items.length) {\n      this.updatePagination();\n      this._tabLabelCount = this._items.length;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the selected index has changed, scroll to the label and check if the scrolling controls\n    // should be disabled.\n    if (this._selectedIndexChanged) {\n      this._scrollToLabel(this._selectedIndex);\n      this._checkScrollingControls();\n      this._alignInkBarToSelectedTab();\n      this._selectedIndexChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n    // then translate the header to reflect this.\n    if (this._scrollDistanceChanged) {\n      this._updateTabScrollPosition();\n      this._scrollDistanceChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._stopScrolling.complete();\n  }\n  /** Handles keyboard events on the header. */\n  _handleKeydown(event) {\n    // We don't handle any key bindings with a modifier key.\n    if (hasModifierKey(event)) {\n      return;\n    }\n    switch (event.keyCode) {\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          const item = this._items.get(this.focusIndex);\n          if (item && !item.disabled) {\n            this.selectFocusedIndex.emit(this.focusIndex);\n            this._itemSelected(event);\n          }\n        }\n        break;\n      default:\n        this._keyManager.onKeydown(event);\n    }\n  }\n  /**\n   * Callback for when the MutationObserver detects that the content has changed.\n   */\n  _onContentChanges() {\n    const textContent = this._elementRef.nativeElement.textContent;\n    // We need to diff the text content of the header, because the MutationObserver callback\n    // will fire even if the text content didn't change which is inefficient and is prone\n    // to infinite loops if a poorly constructed expression is passed in (see #14249).\n    if (textContent !== this._currentTextContent) {\n      this._currentTextContent = textContent || '';\n      // The content observer runs outside the `NgZone` by default, which\n      // means that we need to bring the callback back in ourselves.\n      this._ngZone.run(() => {\n        this.updatePagination();\n        this._alignInkBarToSelectedTab();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /**\n   * Updates the view whether pagination should be enabled or not.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    this._checkPaginationEnabled();\n    this._checkScrollingControls();\n    this._updateTabScrollPosition();\n  }\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : 0;\n  }\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value) {\n    if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n      return;\n    }\n    this._keyManager.setActiveItem(value);\n  }\n  /**\n   * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n   * providing a valid index and return true.\n   */\n  _isValidIndex(index) {\n    return this._items ? !!this._items.toArray()[index] : true;\n  }\n  /**\n   * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n   * scrolling is enabled.\n   */\n  _setTabFocus(tabIndex) {\n    if (this._showPaginationControls) {\n      this._scrollToLabel(tabIndex);\n    }\n    if (this._items && this._items.length) {\n      this._items.toArray()[tabIndex].focus();\n      // Do not let the browser manage scrolling to focus the element, this will be handled\n      // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n      // should be the full width minus the offset width.\n      const containerEl = this._tabListContainer.nativeElement;\n      const dir = this._getLayoutDirection();\n      if (dir == 'ltr') {\n        containerEl.scrollLeft = 0;\n      } else {\n        containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n      }\n    }\n  }\n  /** The layout direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n  _updateTabScrollPosition() {\n    if (this.disablePagination) {\n      return;\n    }\n    const scrollDistance = this.scrollDistance;\n    const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n    // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n    // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n    // and ripples will exceed the boundaries of the visible tab bar.\n    // See: https://github.com/angular/components/issues/10276\n    // We round the `transform` here, because transforms with sub-pixel precision cause some\n    // browsers to blur the content of the element.\n    this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n    // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n    // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n    // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n    // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n    if (this._platform.TRIDENT || this._platform.EDGE) {\n      this._tabListContainer.nativeElement.scrollLeft = 0;\n    }\n  }\n  /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n  get scrollDistance() {\n    return this._scrollDistance;\n  }\n  set scrollDistance(value) {\n    this._scrollTo(value);\n  }\n  /**\n   * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n   * the end of the list, respectively). The distance to scroll is computed to be a third of the\n   * length of the tab list view window.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollHeader(direction) {\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    // Move the scroll distance one-third the length of the tab list's viewport.\n    const scrollAmount = (direction == 'before' ? -1 : 1) * viewLength / 3;\n    return this._scrollTo(this._scrollDistance + scrollAmount);\n  }\n  /** Handles click events on the pagination arrows. */\n  _handlePaginatorClick(direction) {\n    this._stopInterval();\n    this._scrollHeader(direction);\n  }\n  /**\n   * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollToLabel(labelIndex) {\n    if (this.disablePagination) {\n      return;\n    }\n    const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n    if (!selectedLabel) {\n      return;\n    }\n    // The view length is the visible width of the tab labels.\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    const {\n      offsetLeft,\n      offsetWidth\n    } = selectedLabel.elementRef.nativeElement;\n    let labelBeforePos, labelAfterPos;\n    if (this._getLayoutDirection() == 'ltr') {\n      labelBeforePos = offsetLeft;\n      labelAfterPos = labelBeforePos + offsetWidth;\n    } else {\n      labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n      labelBeforePos = labelAfterPos - offsetWidth;\n    }\n    const beforeVisiblePos = this.scrollDistance;\n    const afterVisiblePos = this.scrollDistance + viewLength;\n    if (labelBeforePos < beforeVisiblePos) {\n      // Scroll header to move label to the before direction\n      this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n    } else if (labelAfterPos > afterVisiblePos) {\n      // Scroll header to move label to the after direction\n      this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n    }\n  }\n  /**\n   * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n   * tab list is wider than the size of the header container, then the pagination controls should\n   * be shown.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkPaginationEnabled() {\n    if (this.disablePagination) {\n      this._showPaginationControls = false;\n    } else {\n      const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n      if (!isEnabled) {\n        this.scrollDistance = 0;\n      }\n      if (isEnabled !== this._showPaginationControls) {\n        this._changeDetectorRef.markForCheck();\n      }\n      this._showPaginationControls = isEnabled;\n    }\n  }\n  /**\n   * Evaluate whether the before and after controls should be enabled or disabled.\n   * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n   * before button. If the header is at the end of the list (scroll distance is equal to the\n   * maximum distance we can scroll), then disable the after button.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkScrollingControls() {\n    if (this.disablePagination) {\n      this._disableScrollAfter = this._disableScrollBefore = true;\n    } else {\n      // Check if the pagination arrows should be activated.\n      this._disableScrollBefore = this.scrollDistance == 0;\n      this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n   * is equal to the difference in width between the tab list container and tab header container.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _getMaxScrollDistance() {\n    const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    return lengthOfTabList - viewLength || 0;\n  }\n  /** Tells the ink-bar to align itself to the current label wrapper */\n  _alignInkBarToSelectedTab() {\n    const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n    const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n    if (selectedLabelWrapper) {\n      this._inkBar.alignToElement(selectedLabelWrapper);\n    } else {\n      this._inkBar.hide();\n    }\n  }\n  /** Stops the currently-running paginator interval.  */\n  _stopInterval() {\n    this._stopScrolling.next();\n  }\n  /**\n   * Handles the user pressing down on one of the paginators.\n   * Starts scrolling the header after a certain amount of time.\n   * @param direction In which direction the paginator should be scrolled.\n   */\n  _handlePaginatorPress(direction, mouseEvent) {\n    // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n    // null check the `button`, but we do it so we don't break tests that use fake events.\n    if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n      return;\n    }\n    // Avoid overlapping timers.\n    this._stopInterval();\n    // Start a timer after the delay and keep firing based on the interval.\n    timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n    // Keep the timer going until something tells it to stop or the component is destroyed.\n    .pipe(takeUntil(merge(this._stopScrolling, this._destroyed))).subscribe(() => {\n      const {\n        maxScrollDistance,\n        distance\n      } = this._scrollHeader(direction);\n      // Stop the timer if we've reached the start or the end.\n      if (distance === 0 || distance >= maxScrollDistance) {\n        this._stopInterval();\n      }\n    });\n  }\n  /**\n   * Scrolls the header to a given position.\n   * @param position Position to which to scroll.\n   * @returns Information on the current scroll distance and the maximum.\n   */\n  _scrollTo(position) {\n    if (this.disablePagination) {\n      return {\n        maxScrollDistance: 0,\n        distance: 0\n      };\n    }\n    const maxScrollDistance = this._getMaxScrollDistance();\n    this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n    // Mark that the scroll distance has changed so that after the view is checked, the CSS\n    // transformation can move the header.\n    this._scrollDistanceChanged = true;\n    this._checkScrollingControls();\n    return {\n      maxScrollDistance,\n      distance: this._scrollDistance\n    };\n  }\n  static {\n    this.ɵfac = function MatPaginatedTabHeader_Factory(t) {\n      return new (t || MatPaginatedTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatPaginatedTabHeader,\n      inputs: {\n        disablePagination: \"disablePagination\"\n      }\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatedTabHeader, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    disablePagination: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Base class with all of the `MatTabHeader` functionality.\n * @docs-private\n */\nclass _MatTabHeaderBase extends MatPaginatedTabHeader {\n  /** Whether the ripple effect is disabled or not. */\n  get disableRipple() {\n    return this._disableRipple;\n  }\n  set disableRipple(value) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n  constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    this._disableRipple = false;\n  }\n  _itemSelected(event) {\n    event.preventDefault();\n  }\n  static {\n    this.ɵfac = function _MatTabHeaderBase_Factory(t) {\n      return new (t || _MatTabHeaderBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatTabHeaderBase,\n      inputs: {\n        disableRipple: \"disableRipple\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabHeaderBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    disableRipple: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends _MatTabHeaderBase {\n  constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n  static {\n    this.ɵfac = function MatTabHeader_Factory(t) {\n      return new (t || MatTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabHeader,\n      selectors: [[\"mat-tab-header\"]],\n      contentQueries: function MatTabHeader_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatTabLabelWrapper, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n        }\n      },\n      viewQuery: function MatTabHeader_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c3, 7);\n          i0.ɵɵviewQuery(_c4, 7);\n          i0.ɵɵviewQuery(_c5, 7);\n          i0.ɵɵviewQuery(_c6, 5);\n          i0.ɵɵviewQuery(_c7, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-tab-header\"],\n      hostVars: 4,\n      hostBindings: function MatTabHeader_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\");\n        }\n      },\n      inputs: {\n        selectedIndex: \"selectedIndex\"\n      },\n      outputs: {\n        selectFocusedIndex: \"selectFocusedIndex\",\n        indexFocused: \"indexFocused\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c2,\n      decls: 13,\n      vars: 10,\n      consts: [[\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"matRippleDisabled\", \"disabled\", \"click\", \"mousedown\", \"touchend\"], [\"previousPaginator\", \"\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-label-container\", 3, \"keydown\"], [\"tabListContainer\", \"\"], [\"role\", \"tablist\", 1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [\"tabList\", \"\"], [1, \"mat-mdc-tab-labels\"], [\"tabListInner\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"matRippleDisabled\", \"disabled\", \"mousedown\", \"click\", \"touchend\"], [\"nextPaginator\", \"\"]],\n      template: function MatTabHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"button\", 0, 1);\n          i0.ɵɵlistener(\"click\", function MatTabHeader_Template_button_click_0_listener() {\n            return ctx._handlePaginatorClick(\"before\");\n          })(\"mousedown\", function MatTabHeader_Template_button_mousedown_0_listener($event) {\n            return ctx._handlePaginatorPress(\"before\", $event);\n          })(\"touchend\", function MatTabHeader_Template_button_touchend_0_listener() {\n            return ctx._stopInterval();\n          });\n          i0.ɵɵelement(2, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3, 4);\n          i0.ɵɵlistener(\"keydown\", function MatTabHeader_Template_div_keydown_3_listener($event) {\n            return ctx._handleKeydown($event);\n          });\n          i0.ɵɵelementStart(5, \"div\", 5, 6);\n          i0.ɵɵlistener(\"cdkObserveContent\", function MatTabHeader_Template_div_cdkObserveContent_5_listener() {\n            return ctx._onContentChanges();\n          });\n          i0.ɵɵelementStart(7, \"div\", 7, 8);\n          i0.ɵɵprojection(9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"button\", 9, 10);\n          i0.ɵɵlistener(\"mousedown\", function MatTabHeader_Template_button_mousedown_10_listener($event) {\n            return ctx._handlePaginatorPress(\"after\", $event);\n          })(\"click\", function MatTabHeader_Template_button_click_10_listener() {\n            return ctx._handlePaginatorClick(\"after\");\n          })(\"touchend\", function MatTabHeader_Template_button_touchend_10_listener() {\n            return ctx._stopInterval();\n          });\n          i0.ɵɵelement(12, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n        }\n      },\n      dependencies: [i5.MatRipple, i5$1.CdkObserveContent],\n      styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0;--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mdc-secondary-navigation-tab-container-height:48px}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-header',\n      inputs: ['selectedIndex'],\n      outputs: ['selectFocusedIndex', 'indexFocused'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\"\n      },\n      template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\",\n      styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0;--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mdc-secondary-navigation-tab-container-height:48px}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _items: [{\n      type: ContentChildren,\n      args: [MatTabLabelWrapper, {\n        descendants: false\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})();\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n// Boilerplate for applying mixins to MatTabGroup.\n/** @docs-private */\nconst _MatTabGroupMixinBase = mixinColor(mixinDisableRipple(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n}), 'primary');\n/**\n * Base class with all of the `MatTabGroupBase` functionality.\n * @docs-private\n */\nclass _MatTabGroupBase extends _MatTabGroupMixinBase {\n  /** Whether the tab group should grow to the size of the active tab. */\n  get dynamicHeight() {\n    return this._dynamicHeight;\n  }\n  set dynamicHeight(value) {\n    this._dynamicHeight = coerceBooleanProperty(value);\n  }\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value) {\n    this._indexToSelect = coerceNumberProperty(value, null);\n  }\n  /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    this._animationDuration = /^\\d+$/.test(value + '') ? value + 'ms' : value;\n  }\n  /**\n   * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n   * accessibility when the tab does not have focusable elements or if it has scrollable content.\n   * The `tabindex` will be removed automatically for inactive tabs.\n   * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n   */\n  get contentTabIndex() {\n    return this._contentTabIndex;\n  }\n  set contentTabIndex(value) {\n    this._contentTabIndex = coerceNumberProperty(value, null);\n  }\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  get disablePagination() {\n    return this._disablePagination;\n  }\n  set disablePagination(value) {\n    this._disablePagination = coerceBooleanProperty(value);\n  }\n  /**\n   * By default tabs remove their content from the DOM while it's off-screen.\n   * Setting this to `true` will keep it in the DOM which will prevent elements\n   * like iframes and videos from reloading next time it comes back into the view.\n   */\n  get preserveContent() {\n    return this._preserveContent;\n  }\n  set preserveContent(value) {\n    this._preserveContent = coerceBooleanProperty(value);\n  }\n  /** Background color of the tab group. */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  constructor(elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n    super(elementRef);\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    /** All of the tabs that belong to the group. */\n    this._tabs = new QueryList();\n    /** The tab index that should be selected after the content has been checked. */\n    this._indexToSelect = 0;\n    /** Index of the tab that was focused last. */\n    this._lastFocusedTabIndex = null;\n    /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n    this._tabBodyWrapperHeight = 0;\n    /** Subscription to tabs being added/removed. */\n    this._tabsSubscription = Subscription.EMPTY;\n    /** Subscription to changes in the tab labels. */\n    this._tabLabelSubscription = Subscription.EMPTY;\n    this._dynamicHeight = false;\n    this._selectedIndex = null;\n    /** Position of the tab header. */\n    this.headerPosition = 'above';\n    this._disablePagination = false;\n    this._preserveContent = false;\n    /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n    this.selectedIndexChange = new EventEmitter();\n    /** Event emitted when focus has changed within a tab group. */\n    this.focusChange = new EventEmitter();\n    /** Event emitted when the body animation has completed */\n    this.animationDone = new EventEmitter();\n    /** Event emitted when the tab selection has changed. */\n    this.selectedTabChange = new EventEmitter(true);\n    this._groupId = nextId++;\n    this.animationDuration = defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.dynamicHeight = defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n    this.contentTabIndex = defaultConfig?.contentTabIndex ?? null;\n    this.preserveContent = !!defaultConfig?.preserveContent;\n  }\n  /**\n   * After the content is checked, this component knows what tabs have been defined\n   * and what the selected index should be. This is where we can know exactly what position\n   * each tab should be in according to the new selected index, and additionally we know how\n   * a new selected tab should transition in (from the left or right).\n   */\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = this._indexToSelect = this._clampTabIndex(this._indexToSelect);\n    // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this._selectedIndex != indexToSelect) {\n      const isFirstRun = this._selectedIndex == null;\n      if (!isFirstRun) {\n        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n        // Preserve the height so page doesn't scroll up during tab change.\n        // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.minHeight = wrapper.clientHeight + 'px';\n      }\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this._tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n        if (!isFirstRun) {\n          this.selectedIndexChange.emit(indexToSelect);\n          // Clear the min-height, this was needed during tab change to avoid\n          // unnecessary scrolling.\n          this._tabBodyWrapper.nativeElement.style.minHeight = '';\n        }\n      });\n    }\n    // Setup the position for each tab and optionally setup an origin on the next selected tab.\n    this._tabs.forEach((tab, index) => {\n      tab.position = index - indexToSelect;\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n        tab.origin = indexToSelect - this._selectedIndex;\n      }\n    });\n    if (this._selectedIndex !== indexToSelect) {\n      this._selectedIndex = indexToSelect;\n      this._lastFocusedTabIndex = null;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngAfterContentInit() {\n    this._subscribeToAllTabChanges();\n    this._subscribeToTabLabels();\n    // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n    this._tabsSubscription = this._tabs.changes.subscribe(() => {\n      const indexToSelect = this._clampTabIndex(this._indexToSelect);\n      // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this._selectedIndex) {\n        const tabs = this._tabs.toArray();\n        let selectedTab;\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `selectedIndexChange` event.\n            this._indexToSelect = this._selectedIndex = i;\n            this._lastFocusedTabIndex = null;\n            selectedTab = tabs[i];\n            break;\n          }\n        }\n        // If we haven't found an active tab and a tab exists at the selected index, it means\n        // that the active tab was swapped out. Since this won't be picked up by the rendering\n        // loop in `ngAfterContentChecked`, we need to sync it up manually.\n        if (!selectedTab && tabs[indexToSelect]) {\n          Promise.resolve().then(() => {\n            tabs[indexToSelect].isActive = true;\n            this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          });\n        }\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /** Listens to changes in all of the tabs. */\n  _subscribeToAllTabChanges() {\n    // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n    // some that are inside of nested tab groups. We filter them out manually by checking that\n    // the closest group to the tab is the current one.\n    this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe(tabs => {\n      this._tabs.reset(tabs.filter(tab => {\n        return tab._closestTabGroup === this || !tab._closestTabGroup;\n      }));\n      this._tabs.notifyOnChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._tabs.destroy();\n    this._tabsSubscription.unsubscribe();\n    this._tabLabelSubscription.unsubscribe();\n  }\n  /** Re-aligns the ink bar to the selected tab element. */\n  realignInkBar() {\n    if (this._tabHeader) {\n      this._tabHeader._alignInkBarToSelectedTab();\n    }\n  }\n  /**\n   * Recalculates the tab group's pagination dimensions.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    if (this._tabHeader) {\n      this._tabHeader.updatePagination();\n    }\n  }\n  /**\n   * Sets focus to a particular tab.\n   * @param index Index of the tab to be focused.\n   */\n  focusTab(index) {\n    const header = this._tabHeader;\n    if (header) {\n      header.focusIndex = index;\n    }\n  }\n  _focusChanged(index) {\n    this._lastFocusedTabIndex = index;\n    this.focusChange.emit(this._createChangeEvent(index));\n  }\n  _createChangeEvent(index) {\n    const event = new MatTabChangeEvent();\n    event.index = index;\n    if (this._tabs && this._tabs.length) {\n      event.tab = this._tabs.toArray()[index];\n    }\n    return event;\n  }\n  /**\n   * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n   * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n   * binding to be updated, we need to subscribe to changes in it and trigger change detection\n   * manually.\n   */\n  _subscribeToTabLabels() {\n    if (this._tabLabelSubscription) {\n      this._tabLabelSubscription.unsubscribe();\n    }\n    this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Clamps the given index to the bounds of 0 and the tabs length. */\n  _clampTabIndex(index) {\n    // Note the `|| 0`, which ensures that values like NaN can't get through\n    // and which would otherwise throw the component into an infinite loop\n    // (since Math.max(NaN, 0) === NaN).\n    return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n  }\n  /** Returns a unique id for each tab label element */\n  _getTabLabelId(i) {\n    return `mat-tab-label-${this._groupId}-${i}`;\n  }\n  /** Returns a unique id for each tab content element */\n  _getTabContentId(i) {\n    return `mat-tab-content-${this._groupId}-${i}`;\n  }\n  /**\n   * Sets the height of the body wrapper to the height of the activating tab if dynamic\n   * height property is true.\n   */\n  _setTabBodyWrapperHeight(tabHeight) {\n    if (!this._dynamicHeight || !this._tabBodyWrapperHeight) {\n      return;\n    }\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n    // This conditional forces the browser to paint the height so that\n    // the animation to the new height can have an origin.\n    if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n      wrapper.style.height = tabHeight + 'px';\n    }\n  }\n  /** Removes the height of the tab body wrapper. */\n  _removeTabBodyWrapperHeight() {\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    this._tabBodyWrapperHeight = wrapper.clientHeight;\n    wrapper.style.height = '';\n    this.animationDone.emit();\n  }\n  /** Handle click events, setting new selected index if appropriate. */\n  _handleClick(tab, tabHeader, index) {\n    tabHeader.focusIndex = index;\n    if (!tab.disabled) {\n      this.selectedIndex = index;\n    }\n  }\n  /** Retrieves the tabindex for the tab. */\n  _getTabIndex(index) {\n    const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n    return index === targetIndex ? 0 : -1;\n  }\n  /** Callback for when the focused state of a tab has changed. */\n  _tabFocusChanged(focusOrigin, index) {\n    // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n    // can cause the tab to be moved out from under the pointer, interrupting the\n    // click sequence (see #21898). We don't need to scroll the tab into view for\n    // such cases anyway, because it will be done when the tab becomes selected.\n    if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n      this._tabHeader.focusIndex = index;\n    }\n  }\n  static {\n    this.ɵfac = function _MatTabGroupBase_Factory(t) {\n      return new (t || _MatTabGroupBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatTabGroupBase,\n      inputs: {\n        dynamicHeight: \"dynamicHeight\",\n        selectedIndex: \"selectedIndex\",\n        headerPosition: \"headerPosition\",\n        animationDuration: \"animationDuration\",\n        contentTabIndex: \"contentTabIndex\",\n        disablePagination: \"disablePagination\",\n        preserveContent: \"preserveContent\",\n        backgroundColor: \"backgroundColor\"\n      },\n      outputs: {\n        selectedIndexChange: \"selectedIndexChange\",\n        focusChange: \"focusChange\",\n        animationDone: \"animationDone\",\n        selectedTabChange: \"selectedTabChange\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabGroupBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TABS_CONFIG]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    dynamicHeight: [{\n      type: Input\n    }],\n    selectedIndex: [{\n      type: Input\n    }],\n    headerPosition: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    contentTabIndex: [{\n      type: Input\n    }],\n    disablePagination: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    selectedIndexChange: [{\n      type: Output\n    }],\n    focusChange: [{\n      type: Output\n    }],\n    animationDone: [{\n      type: Output\n    }],\n    selectedTabChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup extends _MatTabGroupBase {\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent;\n  }\n  set fitInkBarToContent(v) {\n    this._fitInkBarToContent = coerceBooleanProperty(v);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Whether tabs should be stretched to fill the header. */\n  get stretchTabs() {\n    return this._stretchTabs;\n  }\n  set stretchTabs(v) {\n    this._stretchTabs = coerceBooleanProperty(v);\n  }\n  constructor(elementRef, changeDetectorRef, defaultConfig, animationMode) {\n    super(elementRef, changeDetectorRef, defaultConfig, animationMode);\n    this._fitInkBarToContent = false;\n    this._stretchTabs = true;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n  static {\n    this.ɵfac = function MatTabGroup_Factory(t) {\n      return new (t || MatTabGroup)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabGroup,\n      selectors: [[\"mat-tab-group\"]],\n      contentQueries: function MatTabGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatTab, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTabs = _t);\n        }\n      },\n      viewQuery: function MatTabGroup_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c8, 5);\n          i0.ɵɵviewQuery(_c9, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodyWrapper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabHeader = _t.first);\n        }\n      },\n      hostAttrs: [\"ngSkipHydration\", \"\", 1, \"mat-mdc-tab-group\"],\n      hostVars: 8,\n      hostBindings: function MatTabGroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n          i0.ɵɵclassProp(\"mat-mdc-tab-group-dynamic-height\", ctx.dynamicHeight)(\"mat-mdc-tab-group-inverted-header\", ctx.headerPosition === \"below\")(\"mat-mdc-tab-group-stretch-tabs\", ctx.stretchTabs);\n        }\n      },\n      inputs: {\n        color: \"color\",\n        disableRipple: \"disableRipple\",\n        fitInkBarToContent: \"fitInkBarToContent\",\n        stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\"]\n      },\n      exportAs: [\"matTabGroup\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_TAB_GROUP,\n        useExisting: MatTabGroup\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 6,\n      vars: 7,\n      consts: [[3, \"selectedIndex\", \"disableRipple\", \"disablePagination\", \"indexFocused\", \"selectFocusedIndex\"], [\"tabHeader\", \"\"], [\"class\", \"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\", \"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 3, \"id\", \"mdc-tab--active\", \"ngClass\", \"disabled\", \"fitInkBarToContent\", \"click\", \"cdkFocusChange\", 4, \"ngFor\", \"ngForOf\"], [1, \"mat-mdc-tab-body-wrapper\"], [\"tabBodyWrapper\", \"\"], [\"role\", \"tabpanel\", 3, \"id\", \"mat-mdc-tab-body-active\", \"ngClass\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\", \"_onCentered\", \"_onCentering\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-mdc-focus-indicator\", 3, \"id\", \"ngClass\", \"disabled\", \"fitInkBarToContent\", \"click\", \"cdkFocusChange\"], [\"tabNode\", \"\"], [1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"], [3, \"ngIf\", \"ngIfElse\"], [\"tabTextLabel\", \"\"], [3, \"cdkPortalOutlet\"], [\"role\", \"tabpanel\", 3, \"id\", \"ngClass\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\", \"_onCentered\", \"_onCentering\"]],\n      template: function MatTabGroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-tab-header\", 0, 1);\n          i0.ɵɵlistener(\"indexFocused\", function MatTabGroup_Template_mat_tab_header_indexFocused_0_listener($event) {\n            return ctx._focusChanged($event);\n          })(\"selectFocusedIndex\", function MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener($event) {\n            return ctx.selectedIndex = $event;\n          });\n          i0.ɵɵtemplate(2, MatTabGroup_div_2_Template, 9, 17, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3, 4);\n          i0.ɵɵtemplate(5, MatTabGroup_mat_tab_body_5_Template, 1, 12, \"mat-tab-body\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"selectedIndex\", ctx.selectedIndex || 0)(\"disableRipple\", ctx.disableRipple)(\"disablePagination\", ctx.disablePagination);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx._tabs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx._tabs);\n        }\n      },\n      dependencies: [i1$2.NgClass, i1$2.NgForOf, i1$2.NgIf, i2.CdkPortalOutlet, i5.MatRipple, i4.CdkMonitorFocus, MatTabBody, MatTabLabelWrapper, MatTabHeader],\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-group',\n      exportAs: 'matTabGroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      inputs: ['color', 'disableRipple'],\n      providers: [{\n        provide: MAT_TAB_GROUP,\n        useExisting: MatTabGroup\n      }],\n      host: {\n        'ngSkipHydration': '',\n        'class': 'mat-mdc-tab-group',\n        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n       #tabNode\\n       role=\\\"tab\\\"\\n       matTabLabelWrapper\\n       cdkMonitorElementFocus\\n       *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n       [id]=\\\"_getTabLabelId(i)\\\"\\n       [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n       [attr.aria-posinset]=\\\"i + 1\\\"\\n       [attr.aria-setsize]=\\\"_tabs.length\\\"\\n       [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n       [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n       [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n       [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n       [ngClass]=\\\"tab.labelClass\\\"\\n       [disabled]=\\\"tab.disabled\\\"\\n       [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n       (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n       (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n    <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n    <!-- Needs to be a separate element, because we can't put\\n         `overflow: hidden` on tab due to the ink bar. -->\\n    <div\\n      class=\\\"mat-mdc-tab-ripple\\\"\\n      mat-ripple\\n      [matRippleTrigger]=\\\"tabNode\\\"\\n      [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n    <span class=\\\"mdc-tab__content\\\">\\n      <span class=\\\"mdc-tab__text-label\\\">\\n        <!-- If there is a label template, use it. -->\\n        <ng-template [ngIf]=\\\"tab.templateLabel\\\" [ngIfElse]=\\\"tabTextLabel\\\">\\n          <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n        </ng-template>\\n\\n        <!-- If there is not a label template, fall back to the text label. -->\\n        <ng-template #tabTextLabel>{{tab.textLabel}}</ng-template>\\n      </span>\\n    </span>\\n  </div>\\n</mat-tab-header>\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  <mat-tab-body role=\\\"tabpanel\\\"\\n               *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n               [id]=\\\"_getTabContentId(i)\\\"\\n               [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n               [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n               [attr.aria-hidden]=\\\"selectedIndex !== i\\\"\\n               [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n               [ngClass]=\\\"tab.bodyClass\\\"\\n               [content]=\\\"tab.content!\\\"\\n               [position]=\\\"tab.position!\\\"\\n               [origin]=\\\"tab.origin\\\"\\n               [animationDuration]=\\\"animationDuration\\\"\\n               [preserveContent]=\\\"preserveContent\\\"\\n               (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n               (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n  </mat-tab-body>\\n</div>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TABS_CONFIG]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _allTabs: [{\n      type: ContentChildren,\n      args: [MatTab, {\n        descendants: true\n      }]\n    }],\n    _tabBodyWrapper: [{\n      type: ViewChild,\n      args: ['tabBodyWrapper']\n    }],\n    _tabHeader: [{\n      type: ViewChild,\n      args: ['tabHeader']\n    }],\n    fitInkBarToContent: [{\n      type: Input\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: ['mat-stretch-tabs']\n    }]\n  });\n})();\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {}\n\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n/**\n * Base class with all of the `MatTabNav` functionality.\n * @docs-private\n */\nclass _MatTabNavBase extends MatPaginatedTabHeader {\n  /** Background color of the tab nav. */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  /** Whether the ripple effect is disabled or not. */\n  get disableRipple() {\n    return this._disableRipple;\n  }\n  set disableRipple(value) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n  constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    this._disableRipple = false;\n    /** Theme color of the nav bar. */\n    this.color = 'primary';\n  }\n  _itemSelected() {\n    // noop\n  }\n  ngAfterContentInit() {\n    // We need this to run before the `changes` subscription in parent to ensure that the\n    // selectedIndex is up-to-date by the time the super class starts looking for it.\n    this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      this.updateActiveLink();\n    });\n    super.ngAfterContentInit();\n  }\n  /** Notifies the component that the active link has been changed. */\n  updateActiveLink() {\n    if (!this._items) {\n      return;\n    }\n    const items = this._items.toArray();\n    for (let i = 0; i < items.length; i++) {\n      if (items[i].active) {\n        this.selectedIndex = i;\n        this._changeDetectorRef.markForCheck();\n        if (this.tabPanel) {\n          this.tabPanel._activeTabId = items[i].id;\n        }\n        return;\n      }\n    }\n    // The ink bar should hide itself if no items are active.\n    this.selectedIndex = -1;\n    this._inkBar.hide();\n  }\n  _getRole() {\n    return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n  }\n  static {\n    this.ɵfac = function _MatTabNavBase_Factory(t) {\n      return new (t || _MatTabNavBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatTabNavBase,\n      inputs: {\n        backgroundColor: \"backgroundColor\",\n        disableRipple: \"disableRipple\",\n        color: \"color\",\n        tabPanel: \"tabPanel\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabNavBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    backgroundColor: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    tabPanel: [{\n      type: Input\n    }]\n  });\n})();\n// Boilerplate for applying mixins to MatTabLink.\nconst _MatTabLinkMixinBase = mixinTabIndex(mixinDisableRipple(mixinDisabled(class {})));\n/** Base class with all of the `MatTabLink` functionality. */\nclass _MatTabLinkBase extends _MatTabLinkMixinBase {\n  /** Whether the link is active. */\n  get active() {\n    return this._isActive;\n  }\n  set active(value) {\n    const newValue = coerceBooleanProperty(value);\n    if (newValue !== this._isActive) {\n      this._isActive = newValue;\n      this._tabNavBar.updateActiveLink();\n    }\n  }\n  /**\n   * Whether ripples are disabled on interaction.\n   * @docs-private\n   */\n  get rippleDisabled() {\n    return this.disabled || this.disableRipple || this._tabNavBar.disableRipple || !!this.rippleConfig.disabled;\n  }\n  constructor(_tabNavBar, /** @docs-private */elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n    super();\n    this._tabNavBar = _tabNavBar;\n    this.elementRef = elementRef;\n    this._focusMonitor = _focusMonitor;\n    /** Whether the tab link is active or not. */\n    this._isActive = false;\n    /** Unique id for the tab. */\n    this.id = `mat-tab-link-${nextUniqueId++}`;\n    this.rippleConfig = globalRippleOptions || {};\n    this.tabIndex = parseInt(tabIndex) || 0;\n    if (animationMode === 'NoopAnimations') {\n      this.rippleConfig.animation = {\n        enterDuration: 0,\n        exitDuration: 0\n      };\n    }\n  }\n  /** Focuses the tab link. */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this.elementRef);\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this.elementRef);\n  }\n  _handleFocus() {\n    // Since we allow navigation through tabbing in the nav bar, we\n    // have to update the focused index whenever the link receives focus.\n    this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n  }\n  _handleKeydown(event) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this.disabled) {\n        event.preventDefault();\n      } else if (this._tabNavBar.tabPanel) {\n        this.elementRef.nativeElement.click();\n      }\n    }\n  }\n  _getAriaControls() {\n    return this._tabNavBar.tabPanel ? this._tabNavBar.tabPanel?.id : this.elementRef.nativeElement.getAttribute('aria-controls');\n  }\n  _getAriaSelected() {\n    if (this._tabNavBar.tabPanel) {\n      return this.active ? 'true' : 'false';\n    } else {\n      return this.elementRef.nativeElement.getAttribute('aria-selected');\n    }\n  }\n  _getAriaCurrent() {\n    return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n  }\n  _getRole() {\n    return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n  }\n  _getTabIndex() {\n    if (this._tabNavBar.tabPanel) {\n      return this._isActive && !this.disabled ? 0 : -1;\n    } else {\n      return this.tabIndex;\n    }\n  }\n  static {\n    this.ɵfac = function _MatTabLinkBase_Factory(t) {\n      return new (t || _MatTabLinkBase)(i0.ɵɵdirectiveInject(_MatTabNavBase), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i4.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatTabLinkBase,\n      inputs: {\n        active: \"active\",\n        id: \"id\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabLinkBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: _MatTabNavBase\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: i4.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    active: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\nconst _MatTabLinkBaseWithInkBarItem = mixinInkBarItem(_MatTabLinkBase);\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends _MatTabNavBase {\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent.value;\n  }\n  set fitInkBarToContent(v) {\n    this._fitInkBarToContent.next(coerceBooleanProperty(v));\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Whether tabs should be stretched to fill the header. */\n  get stretchTabs() {\n    return this._stretchTabs;\n  }\n  set stretchTabs(v) {\n    this._stretchTabs = coerceBooleanProperty(v);\n  }\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    this._animationDuration = /^\\d+$/.test(value + '') ? value + 'ms' : value;\n  }\n  constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode, defaultConfig) {\n    super(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode);\n    this._fitInkBarToContent = new BehaviorSubject(false);\n    this._stretchTabs = true;\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n  ngAfterViewInit() {\n    if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n    }\n    super.ngAfterViewInit();\n  }\n  static {\n    this.ɵfac = function MatTabNav_Factory(t) {\n      return new (t || MatTabNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabNav,\n      selectors: [[\"\", \"mat-tab-nav-bar\", \"\"]],\n      contentQueries: function MatTabNav_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatTabLink, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n        }\n      },\n      viewQuery: function MatTabNav_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c3, 7);\n          i0.ɵɵviewQuery(_c4, 7);\n          i0.ɵɵviewQuery(_c5, 7);\n          i0.ɵɵviewQuery(_c6, 5);\n          i0.ɵɵviewQuery(_c7, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-tab-nav-bar\", \"mat-mdc-tab-header\"],\n      hostVars: 17,\n      hostBindings: function MatTabNav_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx._getRole());\n          i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\")(\"mat-mdc-tab-nav-bar-stretch-tabs\", ctx.stretchTabs)(\"mat-primary\", ctx.color !== \"warn\" && ctx.color !== \"accent\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        }\n      },\n      inputs: {\n        color: \"color\",\n        fitInkBarToContent: \"fitInkBarToContent\",\n        stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\"],\n        animationDuration: \"animationDuration\"\n      },\n      exportAs: [\"matTabNavBar\", \"matTabNav\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      attrs: _c10,\n      ngContentSelectors: _c2,\n      decls: 13,\n      vars: 8,\n      consts: [[\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"matRippleDisabled\", \"disabled\", \"click\", \"mousedown\", \"touchend\"], [\"previousPaginator\", \"\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-link-container\", 3, \"keydown\"], [\"tabListContainer\", \"\"], [1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [\"tabList\", \"\"], [1, \"mat-mdc-tab-links\"], [\"tabListInner\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"matRippleDisabled\", \"disabled\", \"mousedown\", \"click\", \"touchend\"], [\"nextPaginator\", \"\"]],\n      template: function MatTabNav_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"button\", 0, 1);\n          i0.ɵɵlistener(\"click\", function MatTabNav_Template_button_click_0_listener() {\n            return ctx._handlePaginatorClick(\"before\");\n          })(\"mousedown\", function MatTabNav_Template_button_mousedown_0_listener($event) {\n            return ctx._handlePaginatorPress(\"before\", $event);\n          })(\"touchend\", function MatTabNav_Template_button_touchend_0_listener() {\n            return ctx._stopInterval();\n          });\n          i0.ɵɵelement(2, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3, 4);\n          i0.ɵɵlistener(\"keydown\", function MatTabNav_Template_div_keydown_3_listener($event) {\n            return ctx._handleKeydown($event);\n          });\n          i0.ɵɵelementStart(5, \"div\", 5, 6);\n          i0.ɵɵlistener(\"cdkObserveContent\", function MatTabNav_Template_div_cdkObserveContent_5_listener() {\n            return ctx._onContentChanges();\n          });\n          i0.ɵɵelementStart(7, \"div\", 7, 8);\n          i0.ɵɵprojection(9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"button\", 9, 10);\n          i0.ɵɵlistener(\"mousedown\", function MatTabNav_Template_button_mousedown_10_listener($event) {\n            return ctx._handlePaginatorPress(\"after\", $event);\n          })(\"click\", function MatTabNav_Template_button_click_10_listener() {\n            return ctx._handlePaginatorClick(\"after\");\n          })(\"touchend\", function MatTabNav_Template_button_touchend_10_listener() {\n            return ctx._stopInterval();\n          });\n          i0.ɵɵelement(12, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n        }\n      },\n      dependencies: [i5.MatRipple, i5$1.CdkObserveContent],\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0;--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mdc-secondary-navigation-tab-container-height:48px}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNav, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-nav-bar]',\n      exportAs: 'matTabNavBar, matTabNav',\n      inputs: ['color'],\n      host: {\n        '[attr.role]': '_getRole()',\n        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0;--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mdc-secondary-navigation-tab-container-height:48px}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_TABS_CONFIG]\n      }]\n    }];\n  }, {\n    fitInkBarToContent: [{\n      type: Input\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: ['mat-stretch-tabs']\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    _items: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatTabLink), {\n        descendants: true\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})();\n/**\n * Link inside of a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends _MatTabLinkBaseWithInkBarItem {\n  constructor(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode) {\n    super(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode);\n    this._destroyed = new Subject();\n    tabNavBar._fitInkBarToContent.pipe(takeUntil(this._destroyed)).subscribe(fitInkBarToContent => {\n      this.fitInkBarToContent = fitInkBarToContent;\n    });\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    super.ngOnDestroy();\n  }\n  static {\n    this.ɵfac = function MatTabLink_Factory(t) {\n      return new (t || MatTabLink)(i0.ɵɵdirectiveInject(MatTabNav), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i4.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabLink,\n      selectors: [[\"\", \"mat-tab-link\", \"\"], [\"\", \"matTabLink\", \"\"]],\n      hostAttrs: [1, \"mdc-tab\", \"mat-mdc-tab-link\", \"mat-mdc-focus-indicator\"],\n      hostVars: 11,\n      hostBindings: function MatTabLink_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatTabLink_focus_HostBindingHandler() {\n            return ctx._handleFocus();\n          })(\"keydown\", function MatTabLink_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-controls\", ctx._getAriaControls())(\"aria-current\", ctx._getAriaCurrent())(\"aria-disabled\", ctx.disabled)(\"aria-selected\", ctx._getAriaSelected())(\"id\", ctx.id)(\"tabIndex\", ctx._getTabIndex())(\"role\", ctx._getRole());\n          i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled)(\"mdc-tab--active\", ctx.active);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        disableRipple: \"disableRipple\",\n        tabIndex: \"tabIndex\",\n        active: \"active\",\n        id: \"id\"\n      },\n      exportAs: [\"matTabLink\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      attrs: _c11,\n      ngContentSelectors: _c2,\n      decls: 5,\n      vars: 2,\n      consts: [[1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"]],\n      template: function MatTabLink_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelement(0, \"span\", 0)(1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n          i0.ɵɵprojection(4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx.elementRef.nativeElement)(\"matRippleDisabled\", ctx.rippleDisabled);\n        }\n      },\n      dependencies: [i5.MatRipple],\n      styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLink, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-link], [matTabLink]',\n      exportAs: 'matTabLink',\n      inputs: ['disabled', 'disableRipple', 'tabIndex', 'active', 'id'],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator',\n        '[attr.aria-controls]': '_getAriaControls()',\n        '[attr.aria-current]': '_getAriaCurrent()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.aria-selected]': '_getAriaSelected()',\n        '[attr.id]': 'id',\n        '[attr.tabIndex]': '_getTabIndex()',\n        '[attr.role]': '_getRole()',\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[class.mdc-tab--active]': 'active',\n        '(focus)': '_handleFocus()',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\",\n      styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatTabNav\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: i4.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n  constructor() {\n    /** Unique id for the tab panel. */\n    this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n  }\n  static {\n    this.ɵfac = function MatTabNavPanel_Factory(t) {\n      return new (t || MatTabNavPanel)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTabNavPanel,\n      selectors: [[\"mat-tab-nav-panel\"]],\n      hostAttrs: [\"role\", \"tabpanel\", 1, \"mat-mdc-tab-nav-panel\"],\n      hostVars: 2,\n      hostBindings: function MatTabNavPanel_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-labelledby\", ctx._activeTabId)(\"id\", ctx.id);\n        }\n      },\n      inputs: {\n        id: \"id\"\n      },\n      exportAs: [\"matTabNavPanel\"],\n      ngContentSelectors: _c2,\n      decls: 1,\n      vars: 0,\n      template: function MatTabNavPanel_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNavPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-nav-panel',\n      exportAs: 'matTabNavPanel',\n      template: '<ng-content></ng-content>',\n      host: {\n        '[attr.aria-labelledby]': '_activeTabId',\n        '[attr.id]': 'id',\n        'class': 'mat-mdc-tab-nav-panel',\n        'role': 'tabpanel'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\nclass MatTabsModule {\n  static {\n    this.ɵfac = function MatTabsModule_Factory(t) {\n      return new (t || MatTabsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatTabsModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, MatCommonModule, PortalModule, MatRippleModule, ObserversModule, A11yModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule, PortalModule, MatRippleModule, ObserversModule, A11yModule],\n      exports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink],\n      declarations: [MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink,\n      // Private directives, should not be exported.\n      MatTabBody, MatTabBodyPortal, MatTabLabelWrapper, MatTabHeader]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, _MatTabBase, _MatTabBodyBase, _MatTabGroupBase, _MatTabHeaderBase, _MatTabLabelWrapperBase, _MatTabLinkBase, _MatTabNavBase, matTabsAnimations };", "map": {"version": 3, "names": ["i1$2", "DOCUMENT", "CommonModule", "i0", "forwardRef", "Directive", "Inject", "EventEmitter", "Optional", "Output", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "InjectionToken", "TemplateRef", "ContentChild", "ContentChildren", "QueryList", "Attribute", "NgModule", "i5", "mixinDisabled", "mixinColor", "mixinDisableRipple", "mixinTabIndex", "MAT_RIPPLE_GLOBAL_OPTIONS", "MatCommonModule", "MatRippleModule", "i2", "CdkPortalOutlet", "CdkPortal", "TemplatePortal", "PortalModule", "i5$1", "ObserversModule", "i4", "FocusKeyManager", "A11yModule", "i1", "Subscription", "Subject", "fromEvent", "of", "merge", "EMPTY", "Observable", "timer", "BehaviorSubject", "startWith", "distinctUntilChanged", "takeUntil", "take", "switchMap", "skip", "filter", "trigger", "state", "style", "transition", "animate", "coerceBooleanProperty", "coerceNumberProperty", "i1$1", "i3", "normalizePassiveListenerOptions", "ANIMATION_MODULE_TYPE", "hasModifierKey", "SPACE", "ENTER", "MatTabBody_ng_template_2_Template", "rf", "ctx", "_c0", "a0", "animationDuration", "_c1", "a1", "value", "params", "MatTab_ng_template_0_Template", "ɵɵprojection", "_c2", "_c3", "_c4", "_c5", "_c6", "_c7", "_c8", "_c9", "MatTabGroup_div_2_ng_template_6_ng_template_0_Template", "MatTabGroup_div_2_ng_template_6_Template", "ɵɵtemplate", "tab_r4", "ɵɵnextContext", "$implicit", "ɵɵproperty", "templateLabel", "MatTabGroup_div_2_ng_template_7_Template", "ɵɵtext", "ɵɵtextInterpolate", "textLabel", "MatTabGroup_div_2_Template", "_r14", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "MatTabGroup_div_2_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "i_r5", "index", "ctx_r13", "_r0", "ɵɵreference", "ɵɵresetView", "_handleClick", "MatTabGroup_div_2_Template_div_cdkFocusChange_0_listener", "$event", "ctx_r15", "_tabFocusChanged", "ɵɵelement", "ɵɵtemplateRefExtractor", "ɵɵelementEnd", "_r6", "_r8", "ctx_r1", "ɵɵclassProp", "selectedIndex", "_getTabLabelId", "labelClass", "disabled", "fitInkBarToContent", "ɵɵattribute", "_getTabIndex", "_tabs", "length", "_getTabContentId", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵadvance", "disable<PERSON><PERSON><PERSON>", "MatTabGroup_mat_tab_body_5_Template", "_r19", "MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentered_0_listener", "ctx_r18", "_removeTabBodyWrapperHeight", "MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentering_0_listener", "ctx_r20", "_setTabBodyWrapperHeight", "tab_r16", "i_r17", "ctx_r3", "bodyClass", "content", "position", "origin", "preserve<PERSON><PERSON>nt", "contentTabIndex", "_c10", "_c11", "matTabsAnimations", "translateTab", "transform", "minHeight", "visibility", "MatTabBodyPortal", "constructor", "componentFactoryResolver", "viewContainerRef", "_host", "_document", "_centeringSub", "_leavingSub", "ngOnInit", "_beforeCentering", "pipe", "_isCenterPosition", "_position", "subscribe", "isCentering", "has<PERSON>tta<PERSON>", "attach", "_content", "_afterLeavingCenter", "detach", "ngOnDestroy", "unsubscribe", "ɵfac", "MatTabBodyPortal_Factory", "t", "ɵɵdirectiveInject", "ComponentFactoryResolver", "ViewContainerRef", "MatTabBody", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "features", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "decorators", "undefined", "_MatTabBodyBase", "_positionIndex", "_computePositionAnimationState", "_elementRef", "_dir", "changeDetectorRef", "_dirChangeSubscription", "_translateTabComplete", "_onCentering", "_onCentered", "change", "dir", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "y", "fromState", "toState", "event", "emit", "_computePositionFromO<PERSON>in", "complete", "_onTranslateTabStarted", "nativeElement", "clientHeight", "_getLayoutDirection", "_MatTabBodyBase_Factory", "ElementRef", "Directionality", "ChangeDetectorRef", "inputs", "outputs", "elementRef", "MatTabBody_Factory", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "MatTabBody_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "_portalHost", "first", "hostAttrs", "decls", "vars", "consts", "template", "MatTabBody_Template", "MatTabBody_Template_div_animation_translateTab_start_0_listener", "MatTabBody_Template_div_animation_translateTab_done_0_listener", "next", "ɵɵpureFunction2", "ɵɵpureFunction1", "dependencies", "styles", "encapsulation", "data", "animation", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "animations", "host", "MAT_TAB_CONTENT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatTabContent_Factory", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "MAT_TAB_LABEL", "MAT_TAB", "MatTab<PERSON><PERSON><PERSON>", "templateRef", "_closestTab", "MatTabLabel_Factory", "ACTIVE_CLASS", "NO_TRANSITION_CLASS", "MatInkBar", "_items", "hide", "for<PERSON>ach", "item", "deactivateInkBar", "alignToElement", "element", "correspondingItem", "find", "currentItem", "_currentItem", "clientRect", "getBoundingClientRect", "activateInkBar", "mixinInkBarItem", "base", "_fitTo<PERSON>ontent", "v", "newValue", "_inkBarElement", "_appendInkBarElement", "previousIndicatorClientRect", "_inkBarContentElement", "classList", "add", "currentClientRect", "<PERSON><PERSON><PERSON><PERSON>", "width", "xPosition", "left", "setProperty", "remove", "_createInkBarElement", "documentNode", "ownerDocument", "document", "createElement", "className", "append<PERSON><PERSON><PERSON>", "Error", "parentElement", "querySelector", "_MAT_INK_BAR_POSITIONER_FACTORY", "method", "offsetLeft", "offsetWidth", "_MAT_INK_BAR_POSITIONER", "providedIn", "factory", "_MatTabLabelWrapperMixinBase", "_MatTabLabelWrapperBase", "focus", "getOffsetLeft", "getOffsetWidth", "_MatTabLabelWrapperBase_Factory", "_MatTabLabelWrapperBaseWithInkBarItem", "MatTabLabelWrapper", "ɵMatTabLabelWrapper_BaseFactory", "MatTabLabelWrapper_Factory", "ɵɵgetInheritedFactory", "hostVars", "hostBindings", "MatTabLabelWrapper_HostBindings", "_MatTabMixinBase", "MAT_TAB_GROUP", "_MatTabBase", "_contentPortal", "_viewContainerRef", "_closestTabGroup", "_stateChanges", "isActive", "ngOnChanges", "changes", "hasOwnProperty", "_explicitContent", "_implicitContent", "_setTemplateLabelInput", "_templateLabel", "_MatTabBase_Factory", "_MatTabBase_Query", "ɵɵNgOnChangesFeature", "static", "Mat<PERSON><PERSON>", "arguments", "ɵMatTab_BaseFactory", "MatTab_Factory", "contentQueries", "MatTab_ContentQueries", "dirIndex", "ɵɵcontentQuery", "exportAs", "ngContentSelectors", "MatTab_Template", "ɵɵprojectionDef", "read", "passiveEventListenerOptions", "passive", "HEADER_SCROLL_DELAY", "HEADER_SCROLL_INTERVAL", "MatPaginatedTabHeader", "disablePagination", "_disablePagination", "_selectedIndex", "_selectedIndexChanged", "_keyManager", "updateActiveItem", "_changeDetectorRef", "_viewportRuler", "_ngZone", "_platform", "_animationMode", "_scrollDistance", "_destroyed", "_showPaginationControls", "_disableScrollAfter", "_disableScrollBefore", "_stopScrolling", "selectFocusedIndex", "indexFocused", "runOutsideAngular", "_stopInterval", "ngAfterViewInit", "_previousPaginator", "_handlePaginatorPress", "_nextPaginator", "ngAfterContentInit", "<PERSON><PERSON><PERSON><PERSON>", "resize", "realign", "updatePagination", "_alignInkBarToSelectedTab", "withHorizontalOrientation", "withHomeAndEnd", "withWrap", "skipPredicate", "onStable", "_itemsResized", "run", "Promise", "resolve", "then", "Math", "max", "min", "_getMaxScrollDistance", "newFocusIndex", "_setTabFocus", "ResizeObserver", "tabItems", "observer", "resizeObserver", "entries", "observe", "disconnect", "some", "e", "contentRect", "height", "ngAfterContentChecked", "_tabLabelCount", "_scrollToLabel", "_checkScrollingControls", "_scrollDistanceChanged", "_updateTabScrollPosition", "destroy", "_handleKeydown", "keyCode", "focusIndex", "get", "_itemSelected", "onKeydown", "_onContentChanges", "textContent", "_currentTextContent", "_checkPaginationEnabled", "activeItemIndex", "_isValidIndex", "setActiveItem", "toArray", "tabIndex", "containerEl", "_tabListContainer", "scrollLeft", "scrollWidth", "scrollDistance", "translateX", "_tabList", "round", "TRIDENT", "EDGE", "_scrollTo", "_scrollHeader", "direction", "viewLength", "scrollAmount", "_handlePaginatorClick", "labelIndex", "<PERSON><PERSON><PERSON><PERSON>", "labelBeforePos", "labelAfterPos", "_tabListInner", "beforeVisiblePos", "afterVisiblePos", "isEnabled", "lengthOfTabList", "selectedItem", "<PERSON><PERSON><PERSON><PERSON>W<PERSON><PERSON>", "_inkBar", "mouseEvent", "button", "maxScrollDistance", "distance", "MatPaginatedTabHeader_Factory", "ViewportRuler", "NgZone", "Platform", "_MatTabHeaderBase", "_disableRipple", "viewportRuler", "ngZone", "platform", "animationMode", "preventDefault", "_MatTabHeaderBase_Factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatTabHeader_Factory", "MatTabHeader_ContentQueries", "MatTabHeader_Query", "MatTabHeader_HostBindings", "MatTabHeader_Template", "MatTabHeader_Template_button_click_0_listener", "MatTabHeader_Template_button_mousedown_0_listener", "MatTab<PERSON><PERSON>er_Template_button_touchend_0_listener", "MatTab<PERSON><PERSON><PERSON>_Template_div_keydown_3_listener", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON>_Template_div_cdkObserveContent_5_listener", "MatTab<PERSON>eader_Template_button_mousedown_10_listener", "MatTab<PERSON><PERSON><PERSON>_Template_button_click_10_listener", "Mat<PERSON>ab<PERSON><PERSON><PERSON>_Template_button_touchend_10_listener", "<PERSON><PERSON><PERSON><PERSON>", "CdkObserveContent", "descendants", "MAT_TABS_CONFIG", "nextId", "_MatTabGroupMixinBase", "_MatTabGroupBase", "dynamicHeight", "_dynamicHeight", "_indexToSelect", "_animationDuration", "test", "_contentTabIndex", "_preserveContent", "backgroundColor", "_backgroundColor", "defaultConfig", "_lastFocusedTabIndex", "_tabBodyWrapperHeight", "_tabsSubscription", "_tabLabelSubscription", "headerPosition", "selectedIndexChange", "focusChange", "animationDone", "selectedTabChange", "_groupId", "indexToSelect", "_clampTabIndex", "isFirstRun", "_createChangeEvent", "wrapper", "_tabBodyWrapper", "tab", "_subscribeToAllTabChanges", "_subscribeToTabLabels", "tabs", "selectedTab", "i", "_allTabs", "reset", "notifyOn<PERSON><PERSON>es", "realignInkBar", "_tabHeader", "focusTab", "header", "_focusChanged", "MatTabChangeEvent", "map", "tabHeight", "offsetHeight", "tabHeader", "targetIndex", "<PERSON><PERSON><PERSON><PERSON>", "_MatTabGroupBase_Factory", "MatTabGroup", "_fitInkBarToContent", "stretchTabs", "_stretchTabs", "MatTabGroup_Factory", "MatTabGroup_ContentQueries", "MatTabGroup_Query", "MatTabGroup_HostBindings", "ɵɵstyleProp", "color", "MatTabGroup_Template", "MatTabGroup_Template_mat_tab_header_indexFocused_0_listener", "MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "CdkMonitorFocus", "nextUniqueId", "_MatTabNavBase", "updateActiveLink", "items", "active", "tabPanel", "_activeTabId", "id", "_getRole", "getAttribute", "_MatTabNavBase_Factory", "_MatTabLinkMixinBase", "_MatTabLinkBase", "_isActive", "_tabNavBar", "rippleDisabled", "rippleConfig", "globalRippleOptions", "_focusMonitor", "parseInt", "enterDuration", "exitDuration", "monitor", "stopMonitoring", "_handleFocus", "indexOf", "click", "_getAriaControls", "_getAriaSelected", "_getAriaCurrent", "_MatTabLinkBase_Factory", "ɵɵinjectAttribute", "FocusMonitor", "_MatTabLinkBaseWithInkBarItem", "MatTabNav", "MatTabNav_Factory", "MatTabNav_ContentQueries", "MatTabLink", "MatTabNav_Query", "MatTabNav_HostBindings", "attrs", "MatTabNav_Template", "MatTabNav_Template_button_click_0_listener", "MatTabNav_Template_button_mousedown_0_listener", "MatTabNav_Template_button_touchend_0_listener", "MatTabNav_Template_div_keydown_3_listener", "MatTabNav_Template_div_cdkObserveContent_5_listener", "MatTabNav_Template_button_mousedown_10_listener", "MatTabNav_Template_button_click_10_listener", "MatTabNav_Template_button_touchend_10_listener", "tabNavBar", "focusMonitor", "MatTabLink_Factory", "MatTabLink_HostBindings", "MatTabLink_focus_HostBindingHandler", "MatTabLink_keydown_HostBindingHandler", "MatTabLink_Template", "OnPush", "MatTabNavPanel", "MatTabNavPanel_Factory", "MatTabNavPanel_HostBindings", "MatTabNavPanel_Template", "MatTabsModule", "MatTabsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/@angular+material@16.2.14_4056c724f738b156ccd72c3e8383c8cb/node_modules/@angular/material/fesm2022/tabs.mjs"], "sourcesContent": ["import * as i1$2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, Directive, Inject, EventEmitter, Optional, Output, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, ContentChild, ContentChildren, QueryList, Attribute, NgModule } from '@angular/core';\nimport * as i5 from '@angular/material/core';\nimport { mixinDisabled, mixinColor, mixinDisableRipple, mixinTabIndex, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/portal';\nimport { CdkPortalOutlet, CdkPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i5$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i4 from '@angular/cdk/a11y';\nimport { FocusKeyManager, A11yModule } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/bidi';\nimport { Subscription, Subject, fromEvent, of, merge, EMPTY, Observable, timer, BehaviorSubject } from 'rxjs';\nimport { startWith, distinctUntilChanged, takeUntil, take, switchMap, skip, filter } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nconst matTabsAnimations = {\n    /** Animation translates a tab along the X axis. */\n    translateTab: trigger('translateTab', [\n        // Transitions to `none` instead of 0, because some browsers might blur the content.\n        state('center, void, left-origin-center, right-origin-center', style({ transform: 'none' })),\n        // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n        // in order to ensure that the element has a height before its state changes. This is\n        // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n        // not have a static height and is not rendered. See related issue: #9465\n        state('left', style({\n            transform: 'translate3d(-100%, 0, 0)',\n            minHeight: '1px',\n            // Normally this is redundant since we detach the content from the DOM, but if the user\n            // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n            visibility: 'hidden',\n        })),\n        state('right', style({\n            transform: 'translate3d(100%, 0, 0)',\n            minHeight: '1px',\n            visibility: 'hidden',\n        })),\n        transition('* => left, * => right, left => center, right => center', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')),\n        transition('void => left-origin-center', [\n            style({ transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n        transition('void => right-origin-center', [\n            style({ transform: 'translate3d(100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n    ]),\n};\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n    constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n        super(componentFactoryResolver, viewContainerRef, _document);\n        this._host = _host;\n        /** Subscription to events for when the tab body begins centering. */\n        this._centeringSub = Subscription.EMPTY;\n        /** Subscription to events for when the tab body finishes leaving from center position. */\n        this._leavingSub = Subscription.EMPTY;\n    }\n    /** Set initial visibility or set up subscription for changing visibility. */\n    ngOnInit() {\n        super.ngOnInit();\n        this._centeringSub = this._host._beforeCentering\n            .pipe(startWith(this._host._isCenterPosition(this._host._position)))\n            .subscribe((isCentering) => {\n            if (isCentering && !this.hasAttached()) {\n                this.attach(this._host._content);\n            }\n        });\n        this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n            if (!this._host.preserveContent) {\n                this.detach();\n            }\n        });\n    }\n    /** Clean up centering subscription. */\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._centeringSub.unsubscribe();\n        this._leavingSub.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabBodyPortal, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.ViewContainerRef }, { token: forwardRef(() => MatTabBody) }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\", usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabBodyPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabBodyHost]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ComponentFactoryResolver }, { type: i0.ViewContainerRef }, { type: MatTabBody, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatTabBody)]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Base class with all of the `MatTabBody` functionality.\n * @docs-private\n */\nclass _MatTabBodyBase {\n    /** The shifted index position of the tab body, where zero represents the active center tab. */\n    set position(position) {\n        this._positionIndex = position;\n        this._computePositionAnimationState();\n    }\n    constructor(_elementRef, _dir, changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        /** Subscription to the directionality change observable. */\n        this._dirChangeSubscription = Subscription.EMPTY;\n        /** Emits when an animation on the tab is complete. */\n        this._translateTabComplete = new Subject();\n        /** Event emitted when the tab begins to animate towards the center as the active tab. */\n        this._onCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._beforeCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._afterLeavingCenter = new EventEmitter();\n        /** Event emitted when the tab completes its animation towards the center. */\n        this._onCentered = new EventEmitter(true);\n        // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n        // anyway to prevent the animations module from throwing an error if the body is used on its own.\n        /** Duration for the tab's animation. */\n        this.animationDuration = '500ms';\n        /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n        this.preserveContent = false;\n        if (_dir) {\n            this._dirChangeSubscription = _dir.change.subscribe((dir) => {\n                this._computePositionAnimationState(dir);\n                changeDetectorRef.markForCheck();\n            });\n        }\n        // Ensure that we get unique animation events, because the `.done` callback can get\n        // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n        this._translateTabComplete\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe(event => {\n            // If the transition to the center is complete, emit an event.\n            if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n                this._onCentered.emit();\n            }\n            if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n                this._afterLeavingCenter.emit();\n            }\n        });\n    }\n    /**\n     * After initialized, check if the content is centered and has an origin. If so, set the\n     * special position states that transition the tab from the left or right before centering.\n     */\n    ngOnInit() {\n        if (this._position == 'center' && this.origin != null) {\n            this._position = this._computePositionFromOrigin(this.origin);\n        }\n    }\n    ngOnDestroy() {\n        this._dirChangeSubscription.unsubscribe();\n        this._translateTabComplete.complete();\n    }\n    _onTranslateTabStarted(event) {\n        const isCentering = this._isCenterPosition(event.toState);\n        this._beforeCentering.emit(isCentering);\n        if (isCentering) {\n            this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n        }\n    }\n    /** The text direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the provided position state is considered center, regardless of origin. */\n    _isCenterPosition(position) {\n        return (position == 'center' || position == 'left-origin-center' || position == 'right-origin-center');\n    }\n    /** Computes the position state that will be used for the tab-body animation trigger. */\n    _computePositionAnimationState(dir = this._getLayoutDirection()) {\n        if (this._positionIndex < 0) {\n            this._position = dir == 'ltr' ? 'left' : 'right';\n        }\n        else if (this._positionIndex > 0) {\n            this._position = dir == 'ltr' ? 'right' : 'left';\n        }\n        else {\n            this._position = 'center';\n        }\n    }\n    /**\n     * Computes the position state based on the specified origin position. This is used if the\n     * tab is becoming visible immediately after creation.\n     */\n    _computePositionFromOrigin(origin) {\n        const dir = this._getLayoutDirection();\n        if ((dir == 'ltr' && origin <= 0) || (dir == 'rtl' && origin > 0)) {\n            return 'left-origin-center';\n        }\n        return 'right-origin-center';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabBodyBase, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatTabBodyBase, inputs: { _content: [\"content\", \"_content\"], origin: \"origin\", animationDuration: \"animationDuration\", preserveContent: \"preserveContent\", position: \"position\" }, outputs: { _onCentering: \"_onCentering\", _beforeCentering: \"_beforeCentering\", _afterLeavingCenter: \"_afterLeavingCenter\", _onCentered: \"_onCentered\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabBodyBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { _onCentering: [{\n                type: Output\n            }], _beforeCentering: [{\n                type: Output\n            }], _afterLeavingCenter: [{\n                type: Output\n            }], _onCentered: [{\n                type: Output\n            }], _content: [{\n                type: Input,\n                args: ['content']\n            }], origin: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }] } });\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody extends _MatTabBodyBase {\n    constructor(elementRef, dir, changeDetectorRef) {\n        super(elementRef, dir, changeDetectorRef);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabBody, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabBody, selector: \"mat-tab-body\", host: { classAttribute: \"mat-mdc-tab-body\" }, viewQueries: [{ propertyName: \"_portalHost\", first: true, predicate: CdkPortalOutlet, descendants: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"], dependencies: [{ kind: \"directive\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\" }], animations: [matTabsAnimations.translateTab], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabBody, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-body', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, animations: [matTabsAnimations.translateTab], host: {\n                        'class': 'mat-mdc-tab-body',\n                    }, template: \"<div class=\\\"mat-mdc-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-mdc-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { _portalHost: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet]\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n    constructor(/** Content for the tab. */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabContent, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabContent, selector: \"[matTabContent]\", providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabContent]',\n                    providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n    constructor(templateRef, viewContainerRef, _closestTab) {\n        super(templateRef, viewContainerRef);\n        this._closestTab = _closestTab;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabLabel, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: MAT_TAB, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabLabel, selector: \"[mat-tab-label], [matTabLabel]\", providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-tab-label], [matTabLabel]',\n                    providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n    constructor(_items) {\n        this._items = _items;\n    }\n    /** Hides the ink bar. */\n    hide() {\n        this._items.forEach(item => item.deactivateInkBar());\n    }\n    /** Aligns the ink bar to a DOM node. */\n    alignToElement(element) {\n        const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n        const currentItem = this._currentItem;\n        if (correspondingItem === currentItem) {\n            return;\n        }\n        currentItem?.deactivateInkBar();\n        if (correspondingItem) {\n            const clientRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n            // The ink bar won't animate unless we give it the `ClientRect` of the previous item.\n            correspondingItem.activateInkBar(clientRect);\n            this._currentItem = correspondingItem;\n        }\n    }\n}\n/**\n * Mixin that can be used to apply the `MatInkBarItem` behavior to a class.\n * Base on MDC's `MDCSlidingTabIndicatorFoundation`:\n * https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-tab-indicator/sliding-foundation.ts\n * @docs-private\n */\nfunction mixinInkBarItem(base) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            this._fitToContent = false;\n        }\n        /** Whether the ink bar should fit to the entire tab or just its content. */\n        get fitInkBarToContent() {\n            return this._fitToContent;\n        }\n        set fitInkBarToContent(v) {\n            const newValue = coerceBooleanProperty(v);\n            if (this._fitToContent !== newValue) {\n                this._fitToContent = newValue;\n                if (this._inkBarElement) {\n                    this._appendInkBarElement();\n                }\n            }\n        }\n        /** Aligns the ink bar to the current item. */\n        activateInkBar(previousIndicatorClientRect) {\n            const element = this.elementRef.nativeElement;\n            // Early exit if no indicator is present to handle cases where an indicator\n            // may be activated without a prior indicator state\n            if (!previousIndicatorClientRect ||\n                !element.getBoundingClientRect ||\n                !this._inkBarContentElement) {\n                element.classList.add(ACTIVE_CLASS);\n                return;\n            }\n            // This animation uses the FLIP approach. You can read more about it at the link below:\n            // https://aerotwist.com/blog/flip-your-animations/\n            // Calculate the dimensions based on the dimensions of the previous indicator\n            const currentClientRect = element.getBoundingClientRect();\n            const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n            const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n            element.classList.add(NO_TRANSITION_CLASS);\n            this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n            // Force repaint before updating classes and transform to ensure the transform properly takes effect\n            element.getBoundingClientRect();\n            element.classList.remove(NO_TRANSITION_CLASS);\n            element.classList.add(ACTIVE_CLASS);\n            this._inkBarContentElement.style.setProperty('transform', '');\n        }\n        /** Removes the ink bar from the current item. */\n        deactivateInkBar() {\n            this.elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n        }\n        /** Initializes the foundation. */\n        ngOnInit() {\n            this._createInkBarElement();\n        }\n        /** Destroys the foundation. */\n        ngOnDestroy() {\n            this._inkBarElement?.remove();\n            this._inkBarElement = this._inkBarContentElement = null;\n        }\n        /** Creates and appends the ink bar element. */\n        _createInkBarElement() {\n            const documentNode = this.elementRef.nativeElement.ownerDocument || document;\n            this._inkBarElement = documentNode.createElement('span');\n            this._inkBarContentElement = documentNode.createElement('span');\n            this._inkBarElement.className = 'mdc-tab-indicator';\n            this._inkBarContentElement.className =\n                'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n            this._inkBarElement.appendChild(this._inkBarContentElement);\n            this._appendInkBarElement();\n        }\n        /**\n         * Appends the ink bar to the tab host element or content, depending on whether\n         * the ink bar should fit to content.\n         */\n        _appendInkBarElement() {\n            if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Ink bar element has not been created and cannot be appended');\n            }\n            const parentElement = this._fitToContent\n                ? this.elementRef.nativeElement.querySelector('.mdc-tab__content')\n                : this.elementRef.nativeElement;\n            if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Missing element to host the ink bar');\n            }\n            parentElement.appendChild(this._inkBarElement);\n        }\n    };\n}\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n    const method = (element) => ({\n        left: element ? (element.offsetLeft || 0) + 'px' : '0',\n        width: element ? (element.offsetWidth || 0) + 'px' : '0',\n    });\n    return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n    providedIn: 'root',\n    factory: _MAT_INK_BAR_POSITIONER_FACTORY,\n});\n\n// Boilerplate for applying mixins to MatTabLabelWrapper.\n/** @docs-private */\nconst _MatTabLabelWrapperMixinBase = mixinDisabled(class {\n});\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass _MatTabLabelWrapperBase extends _MatTabLabelWrapperMixinBase {\n    constructor(elementRef) {\n        super();\n        this.elementRef = elementRef;\n    }\n    /** Sets focus on the wrapper element */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    getOffsetLeft() {\n        return this.elementRef.nativeElement.offsetLeft;\n    }\n    getOffsetWidth() {\n        return this.elementRef.nativeElement.offsetWidth;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabLabelWrapperBase, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatTabLabelWrapperBase, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabLabelWrapperBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\nconst _MatTabLabelWrapperBaseWithInkBarItem = mixinInkBarItem(_MatTabLabelWrapperBase);\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends _MatTabLabelWrapperBaseWithInkBarItem {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabLabelWrapper, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: { disabled: \"disabled\", fitInkBarToContent: \"fitInkBarToContent\" }, host: { properties: { \"class.mat-mdc-tab-disabled\": \"disabled\", \"attr.aria-disabled\": \"!!disabled\" } }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabLabelWrapper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabLabelWrapper]',\n                    inputs: ['disabled', 'fitInkBarToContent'],\n                    host: {\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[attr.aria-disabled]': '!!disabled',\n                    },\n                }]\n        }] });\n\n// Boilerplate for applying mixins to MatTab.\n/** @docs-private */\nconst _MatTabMixinBase = mixinDisabled(class {\n});\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\n/** @docs-private */\nclass _MatTabBase extends _MatTabMixinBase {\n    /** @docs-private */\n    get content() {\n        return this._contentPortal;\n    }\n    constructor(_viewContainerRef, _closestTabGroup) {\n        super();\n        this._viewContainerRef = _viewContainerRef;\n        this._closestTabGroup = _closestTabGroup;\n        /** Plain text label for the tab, used when there is no template label. */\n        this.textLabel = '';\n        /** Portal that will be the hosted content of the tab */\n        this._contentPortal = null;\n        /** Emits whenever the internal state of the tab changes. */\n        this._stateChanges = new Subject();\n        /**\n         * The relatively indexed position where 0 represents the center, negative is left, and positive\n         * represents the right.\n         */\n        this.position = null;\n        /**\n         * The initial relatively index origin of the tab if it was created and selected after there\n         * was already a selected tab. Provides context of what position the tab should originate from.\n         */\n        this.origin = null;\n        /**\n         * Whether the tab is currently active.\n         */\n        this.isActive = false;\n    }\n    ngOnChanges(changes) {\n        if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n            this._stateChanges.next();\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    ngOnInit() {\n        this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setTemplateLabelInput(value) {\n        // Only update the label if the query managed to find one. This works around an issue where a\n        // user may have manually set `templateLabel` during creation mode, which would then get\n        // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n        // tab matches the current one so that we don't pick up labels from nested tabs.\n        if (value && value._closestTab === this) {\n            this._templateLabel = value;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabBase, deps: [{ token: i0.ViewContainerRef }, { token: MAT_TAB_GROUP, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatTabBase, inputs: { textLabel: [\"label\", \"textLabel\"], ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], labelClass: \"labelClass\", bodyClass: \"bodyClass\" }, viewQueries: [{ propertyName: \"_implicitContent\", first: true, predicate: TemplateRef, descendants: true, static: true }], usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB_GROUP]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { _implicitContent: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], textLabel: [{\n                type: Input,\n                args: ['label']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], labelClass: [{\n                type: Input\n            }], bodyClass: [{\n                type: Input\n            }] } });\nclass MatTab extends _MatTabBase {\n    constructor() {\n        super(...arguments);\n        /**\n         * Template provided in the tab content that will be used if present, used to enable lazy-loading\n         */\n        this._explicitContent = undefined;\n    }\n    /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n    get templateLabel() {\n        return this._templateLabel;\n    }\n    set templateLabel(value) {\n        this._setTemplateLabelInput(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTab, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTab, selector: \"mat-tab\", inputs: { disabled: \"disabled\" }, providers: [{ provide: MAT_TAB, useExisting: MatTab }], queries: [{ propertyName: \"_explicitContent\", first: true, predicate: MatTabContent, descendants: true, read: TemplateRef, static: true }, { propertyName: \"templateLabel\", first: true, predicate: MatTabLabel, descendants: true }], exportAs: [\"matTab\"], usesInheritance: true, ngImport: i0, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\", changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab', inputs: ['disabled'], changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, exportAs: 'matTab', providers: [{ provide: MAT_TAB, useExisting: MatTab }], template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\" }]\n        }], propDecorators: { _explicitContent: [{\n                type: ContentChild,\n                args: [MatTabContent, { read: TemplateRef, static: true }]\n            }], templateLabel: [{\n                type: ContentChild,\n                args: [MatTabLabel]\n            }] } });\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n});\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    get disablePagination() {\n        return this._disablePagination;\n    }\n    set disablePagination(value) {\n        this._disablePagination = coerceBooleanProperty(value);\n    }\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        value = coerceNumberProperty(value);\n        if (this._selectedIndex != value) {\n            this._selectedIndexChanged = true;\n            this._selectedIndex = value;\n            if (this._keyManager) {\n                this._keyManager.updateActiveItem(value);\n            }\n        }\n    }\n    constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._viewportRuler = _viewportRuler;\n        this._dir = _dir;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._animationMode = _animationMode;\n        /** The distance in pixels that the tab labels should be translated to the left. */\n        this._scrollDistance = 0;\n        /** Whether the header should scroll to the selected index after the view has been checked. */\n        this._selectedIndexChanged = false;\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Whether the controls for pagination should be displayed */\n        this._showPaginationControls = false;\n        /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n        this._disableScrollAfter = true;\n        /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n        this._disableScrollBefore = true;\n        /** Stream that will stop the automated scrolling. */\n        this._stopScrolling = new Subject();\n        this._disablePagination = false;\n        this._selectedIndex = 0;\n        /** Event emitted when the option is selected. */\n        this.selectFocusedIndex = new EventEmitter();\n        /** Event emitted when a label is focused. */\n        this.indexFocused = new EventEmitter();\n        // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n        _ngZone.runOutsideAngular(() => {\n            fromEvent(_elementRef.nativeElement, 'mouseleave')\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(() => {\n                this._stopInterval();\n            });\n        });\n    }\n    ngAfterViewInit() {\n        // We need to handle these events manually, because we want to bind passive event listeners.\n        fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('before');\n        });\n        fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('after');\n        });\n    }\n    ngAfterContentInit() {\n        const dirChange = this._dir ? this._dir.change : of('ltr');\n        const resize = this._viewportRuler.change(150);\n        const realign = () => {\n            this.updatePagination();\n            this._alignInkBarToSelectedTab();\n        };\n        this._keyManager = new FocusKeyManager(this._items)\n            .withHorizontalOrientation(this._getLayoutDirection())\n            .withHomeAndEnd()\n            .withWrap()\n            // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n            .skipPredicate(() => false);\n        this._keyManager.updateActiveItem(this._selectedIndex);\n        // Defer the first call in order to allow for slower browsers to lay out the elements.\n        // This helps in cases where the user lands directly on a page with paginated tabs.\n        // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n        // can hold up tests that are in a background tab.\n        this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n        // On dir change or window resize, realign the ink bar and update the orientation of\n        // the key manager if the direction has changed.\n        merge(dirChange, resize, this._items.changes, this._itemsResized())\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            // We need to defer this to give the browser some time to recalculate\n            // the element dimensions. The call has to be wrapped in `NgZone.run`,\n            // because the viewport change handler runs outside of Angular.\n            this._ngZone.run(() => {\n                Promise.resolve().then(() => {\n                    // Clamp the scroll distance, because it can change with the number of tabs.\n                    this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n                    realign();\n                });\n            });\n            this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n        });\n        // If there is a change in the focus key manager we need to emit the `indexFocused`\n        // event in order to provide a public event that notifies about focus changes. Also we realign\n        // the tabs container by scrolling the new focused tab into the visible section.\n        this._keyManager.change.subscribe(newFocusIndex => {\n            this.indexFocused.emit(newFocusIndex);\n            this._setTabFocus(newFocusIndex);\n        });\n    }\n    /** Sends any changes that could affect the layout of the items. */\n    _itemsResized() {\n        if (typeof ResizeObserver !== 'function') {\n            return EMPTY;\n        }\n        return this._items.changes.pipe(startWith(this._items), switchMap((tabItems) => new Observable((observer) => this._ngZone.runOutsideAngular(() => {\n            const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n            tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n            return () => {\n                resizeObserver.disconnect();\n            };\n        }))), \n        // Skip the first emit since the resize observer emits when an item\n        // is observed for new items when the tab is already inserted\n        skip(1), \n        // Skip emissions where all the elements are invisible since we don't want\n        // the header to try and re-render with invalid measurements. See #25574.\n        filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n    }\n    ngAfterContentChecked() {\n        // If the number of tab labels have changed, check if scrolling should be enabled\n        if (this._tabLabelCount != this._items.length) {\n            this.updatePagination();\n            this._tabLabelCount = this._items.length;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the selected index has changed, scroll to the label and check if the scrolling controls\n        // should be disabled.\n        if (this._selectedIndexChanged) {\n            this._scrollToLabel(this._selectedIndex);\n            this._checkScrollingControls();\n            this._alignInkBarToSelectedTab();\n            this._selectedIndexChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n        // then translate the header to reflect this.\n        if (this._scrollDistanceChanged) {\n            this._updateTabScrollPosition();\n            this._scrollDistanceChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._stopScrolling.complete();\n    }\n    /** Handles keyboard events on the header. */\n    _handleKeydown(event) {\n        // We don't handle any key bindings with a modifier key.\n        if (hasModifierKey(event)) {\n            return;\n        }\n        switch (event.keyCode) {\n            case ENTER:\n            case SPACE:\n                if (this.focusIndex !== this.selectedIndex) {\n                    const item = this._items.get(this.focusIndex);\n                    if (item && !item.disabled) {\n                        this.selectFocusedIndex.emit(this.focusIndex);\n                        this._itemSelected(event);\n                    }\n                }\n                break;\n            default:\n                this._keyManager.onKeydown(event);\n        }\n    }\n    /**\n     * Callback for when the MutationObserver detects that the content has changed.\n     */\n    _onContentChanges() {\n        const textContent = this._elementRef.nativeElement.textContent;\n        // We need to diff the text content of the header, because the MutationObserver callback\n        // will fire even if the text content didn't change which is inefficient and is prone\n        // to infinite loops if a poorly constructed expression is passed in (see #14249).\n        if (textContent !== this._currentTextContent) {\n            this._currentTextContent = textContent || '';\n            // The content observer runs outside the `NgZone` by default, which\n            // means that we need to bring the callback back in ourselves.\n            this._ngZone.run(() => {\n                this.updatePagination();\n                this._alignInkBarToSelectedTab();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /**\n     * Updates the view whether pagination should be enabled or not.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        this._checkPaginationEnabled();\n        this._checkScrollingControls();\n        this._updateTabScrollPosition();\n    }\n    /** Tracks which element has focus; used for keyboard navigation */\n    get focusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : 0;\n    }\n    /** When the focus index is set, we must manually send focus to the correct label */\n    set focusIndex(value) {\n        if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n            return;\n        }\n        this._keyManager.setActiveItem(value);\n    }\n    /**\n     * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n     * providing a valid index and return true.\n     */\n    _isValidIndex(index) {\n        return this._items ? !!this._items.toArray()[index] : true;\n    }\n    /**\n     * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n     * scrolling is enabled.\n     */\n    _setTabFocus(tabIndex) {\n        if (this._showPaginationControls) {\n            this._scrollToLabel(tabIndex);\n        }\n        if (this._items && this._items.length) {\n            this._items.toArray()[tabIndex].focus();\n            // Do not let the browser manage scrolling to focus the element, this will be handled\n            // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n            // should be the full width minus the offset width.\n            const containerEl = this._tabListContainer.nativeElement;\n            const dir = this._getLayoutDirection();\n            if (dir == 'ltr') {\n                containerEl.scrollLeft = 0;\n            }\n            else {\n                containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n            }\n        }\n    }\n    /** The layout direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n    _updateTabScrollPosition() {\n        if (this.disablePagination) {\n            return;\n        }\n        const scrollDistance = this.scrollDistance;\n        const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n        // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n        // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n        // and ripples will exceed the boundaries of the visible tab bar.\n        // See: https://github.com/angular/components/issues/10276\n        // We round the `transform` here, because transforms with sub-pixel precision cause some\n        // browsers to blur the content of the element.\n        this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n        // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n        // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n        // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n        // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n        if (this._platform.TRIDENT || this._platform.EDGE) {\n            this._tabListContainer.nativeElement.scrollLeft = 0;\n        }\n    }\n    /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n    get scrollDistance() {\n        return this._scrollDistance;\n    }\n    set scrollDistance(value) {\n        this._scrollTo(value);\n    }\n    /**\n     * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n     * the end of the list, respectively). The distance to scroll is computed to be a third of the\n     * length of the tab list view window.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollHeader(direction) {\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        // Move the scroll distance one-third the length of the tab list's viewport.\n        const scrollAmount = ((direction == 'before' ? -1 : 1) * viewLength) / 3;\n        return this._scrollTo(this._scrollDistance + scrollAmount);\n    }\n    /** Handles click events on the pagination arrows. */\n    _handlePaginatorClick(direction) {\n        this._stopInterval();\n        this._scrollHeader(direction);\n    }\n    /**\n     * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollToLabel(labelIndex) {\n        if (this.disablePagination) {\n            return;\n        }\n        const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n        if (!selectedLabel) {\n            return;\n        }\n        // The view length is the visible width of the tab labels.\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        const { offsetLeft, offsetWidth } = selectedLabel.elementRef.nativeElement;\n        let labelBeforePos, labelAfterPos;\n        if (this._getLayoutDirection() == 'ltr') {\n            labelBeforePos = offsetLeft;\n            labelAfterPos = labelBeforePos + offsetWidth;\n        }\n        else {\n            labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n            labelBeforePos = labelAfterPos - offsetWidth;\n        }\n        const beforeVisiblePos = this.scrollDistance;\n        const afterVisiblePos = this.scrollDistance + viewLength;\n        if (labelBeforePos < beforeVisiblePos) {\n            // Scroll header to move label to the before direction\n            this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n        }\n        else if (labelAfterPos > afterVisiblePos) {\n            // Scroll header to move label to the after direction\n            this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n        }\n    }\n    /**\n     * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n     * tab list is wider than the size of the header container, then the pagination controls should\n     * be shown.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkPaginationEnabled() {\n        if (this.disablePagination) {\n            this._showPaginationControls = false;\n        }\n        else {\n            const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n            if (!isEnabled) {\n                this.scrollDistance = 0;\n            }\n            if (isEnabled !== this._showPaginationControls) {\n                this._changeDetectorRef.markForCheck();\n            }\n            this._showPaginationControls = isEnabled;\n        }\n    }\n    /**\n     * Evaluate whether the before and after controls should be enabled or disabled.\n     * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n     * before button. If the header is at the end of the list (scroll distance is equal to the\n     * maximum distance we can scroll), then disable the after button.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkScrollingControls() {\n        if (this.disablePagination) {\n            this._disableScrollAfter = this._disableScrollBefore = true;\n        }\n        else {\n            // Check if the pagination arrows should be activated.\n            this._disableScrollBefore = this.scrollDistance == 0;\n            this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n     * is equal to the difference in width between the tab list container and tab header container.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _getMaxScrollDistance() {\n        const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        return lengthOfTabList - viewLength || 0;\n    }\n    /** Tells the ink-bar to align itself to the current label wrapper */\n    _alignInkBarToSelectedTab() {\n        const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n        const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n        if (selectedLabelWrapper) {\n            this._inkBar.alignToElement(selectedLabelWrapper);\n        }\n        else {\n            this._inkBar.hide();\n        }\n    }\n    /** Stops the currently-running paginator interval.  */\n    _stopInterval() {\n        this._stopScrolling.next();\n    }\n    /**\n     * Handles the user pressing down on one of the paginators.\n     * Starts scrolling the header after a certain amount of time.\n     * @param direction In which direction the paginator should be scrolled.\n     */\n    _handlePaginatorPress(direction, mouseEvent) {\n        // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n        // null check the `button`, but we do it so we don't break tests that use fake events.\n        if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n            return;\n        }\n        // Avoid overlapping timers.\n        this._stopInterval();\n        // Start a timer after the delay and keep firing based on the interval.\n        timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n            // Keep the timer going until something tells it to stop or the component is destroyed.\n            .pipe(takeUntil(merge(this._stopScrolling, this._destroyed)))\n            .subscribe(() => {\n            const { maxScrollDistance, distance } = this._scrollHeader(direction);\n            // Stop the timer if we've reached the start or the end.\n            if (distance === 0 || distance >= maxScrollDistance) {\n                this._stopInterval();\n            }\n        });\n    }\n    /**\n     * Scrolls the header to a given position.\n     * @param position Position to which to scroll.\n     * @returns Information on the current scroll distance and the maximum.\n     */\n    _scrollTo(position) {\n        if (this.disablePagination) {\n            return { maxScrollDistance: 0, distance: 0 };\n        }\n        const maxScrollDistance = this._getMaxScrollDistance();\n        this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n        // Mark that the scroll distance has changed so that after the view is checked, the CSS\n        // transformation can move the header.\n        this._scrollDistanceChanged = true;\n        this._checkScrollingControls();\n        return { maxScrollDistance, distance: this._scrollDistance };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginatedTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatPaginatedTabHeader, inputs: { disablePagination: \"disablePagination\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatPaginatedTabHeader, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { disablePagination: [{\n                type: Input\n            }] } });\n\n/**\n * Base class with all of the `MatTabHeader` functionality.\n * @docs-private\n */\nclass _MatTabHeaderBase extends MatPaginatedTabHeader {\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n        return this._disableRipple;\n    }\n    set disableRipple(value) {\n        this._disableRipple = coerceBooleanProperty(value);\n    }\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        this._disableRipple = false;\n    }\n    _itemSelected(event) {\n        event.preventDefault();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabHeaderBase, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatTabHeaderBase, inputs: { disableRipple: \"disableRipple\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabHeaderBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { disableRipple: [{\n                type: Input\n            }] } });\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends _MatTabHeaderBase {\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        super.ngAfterContentInit();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: { selectedIndex: \"selectedIndex\" }, outputs: { selectFocusedIndex: \"selectFocusedIndex\", indexFocused: \"indexFocused\" }, host: { properties: { \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\" }, classAttribute: \"mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: MatTabLabelWrapper }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], usesInheritance: true, ngImport: i0, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0;--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mdc-secondary-navigation-tab-container-height:48px}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"], dependencies: [{ kind: \"directive\", type: i5.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i5$1.CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-header', inputs: ['selectedIndex'], outputs: ['selectFocusedIndex', 'indexFocused'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                    }, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0;--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mdc-secondary-navigation-tab-container-height:48px}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.mat-mdc-tab::before{margin:5px}.cdk-high-contrast-active .mat-mdc-tab[aria-disabled=true]{color:GrayText}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _items: [{\n                type: ContentChildren,\n                args: [MatTabLabelWrapper, { descendants: false }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n// Boilerplate for applying mixins to MatTabGroup.\n/** @docs-private */\nconst _MatTabGroupMixinBase = mixinColor(mixinDisableRipple(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}), 'primary');\n/**\n * Base class with all of the `MatTabGroupBase` functionality.\n * @docs-private\n */\nclass _MatTabGroupBase extends _MatTabGroupMixinBase {\n    /** Whether the tab group should grow to the size of the active tab. */\n    get dynamicHeight() {\n        return this._dynamicHeight;\n    }\n    set dynamicHeight(value) {\n        this._dynamicHeight = coerceBooleanProperty(value);\n    }\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        this._indexToSelect = coerceNumberProperty(value, null);\n    }\n    /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        this._animationDuration = /^\\d+$/.test(value + '') ? value + 'ms' : value;\n    }\n    /**\n     * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n     * accessibility when the tab does not have focusable elements or if it has scrollable content.\n     * The `tabindex` will be removed automatically for inactive tabs.\n     * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n     */\n    get contentTabIndex() {\n        return this._contentTabIndex;\n    }\n    set contentTabIndex(value) {\n        this._contentTabIndex = coerceNumberProperty(value, null);\n    }\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    get disablePagination() {\n        return this._disablePagination;\n    }\n    set disablePagination(value) {\n        this._disablePagination = coerceBooleanProperty(value);\n    }\n    /**\n     * By default tabs remove their content from the DOM while it's off-screen.\n     * Setting this to `true` will keep it in the DOM which will prevent elements\n     * like iframes and videos from reloading next time it comes back into the view.\n     */\n    get preserveContent() {\n        return this._preserveContent;\n    }\n    set preserveContent(value) {\n        this._preserveContent = coerceBooleanProperty(value);\n    }\n    /** Background color of the tab group. */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    constructor(elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n        super(elementRef);\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** All of the tabs that belong to the group. */\n        this._tabs = new QueryList();\n        /** The tab index that should be selected after the content has been checked. */\n        this._indexToSelect = 0;\n        /** Index of the tab that was focused last. */\n        this._lastFocusedTabIndex = null;\n        /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n        this._tabBodyWrapperHeight = 0;\n        /** Subscription to tabs being added/removed. */\n        this._tabsSubscription = Subscription.EMPTY;\n        /** Subscription to changes in the tab labels. */\n        this._tabLabelSubscription = Subscription.EMPTY;\n        this._dynamicHeight = false;\n        this._selectedIndex = null;\n        /** Position of the tab header. */\n        this.headerPosition = 'above';\n        this._disablePagination = false;\n        this._preserveContent = false;\n        /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n        this.selectedIndexChange = new EventEmitter();\n        /** Event emitted when focus has changed within a tab group. */\n        this.focusChange = new EventEmitter();\n        /** Event emitted when the body animation has completed */\n        this.animationDone = new EventEmitter();\n        /** Event emitted when the tab selection has changed. */\n        this.selectedTabChange = new EventEmitter(true);\n        this._groupId = nextId++;\n        this.animationDuration =\n            defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.dynamicHeight =\n            defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n        this.contentTabIndex = defaultConfig?.contentTabIndex ?? null;\n        this.preserveContent = !!defaultConfig?.preserveContent;\n    }\n    /**\n     * After the content is checked, this component knows what tabs have been defined\n     * and what the selected index should be. This is where we can know exactly what position\n     * each tab should be in according to the new selected index, and additionally we know how\n     * a new selected tab should transition in (from the left or right).\n     */\n    ngAfterContentChecked() {\n        // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n        // the amount of tabs changes before the actual change detection runs.\n        const indexToSelect = (this._indexToSelect = this._clampTabIndex(this._indexToSelect));\n        // If there is a change in selected index, emit a change event. Should not trigger if\n        // the selected index has not yet been initialized.\n        if (this._selectedIndex != indexToSelect) {\n            const isFirstRun = this._selectedIndex == null;\n            if (!isFirstRun) {\n                this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                // Preserve the height so page doesn't scroll up during tab change.\n                // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n                const wrapper = this._tabBodyWrapper.nativeElement;\n                wrapper.style.minHeight = wrapper.clientHeight + 'px';\n            }\n            // Changing these values after change detection has run\n            // since the checked content may contain references to them.\n            Promise.resolve().then(() => {\n                this._tabs.forEach((tab, index) => (tab.isActive = index === indexToSelect));\n                if (!isFirstRun) {\n                    this.selectedIndexChange.emit(indexToSelect);\n                    // Clear the min-height, this was needed during tab change to avoid\n                    // unnecessary scrolling.\n                    this._tabBodyWrapper.nativeElement.style.minHeight = '';\n                }\n            });\n        }\n        // Setup the position for each tab and optionally setup an origin on the next selected tab.\n        this._tabs.forEach((tab, index) => {\n            tab.position = index - indexToSelect;\n            // If there is already a selected tab, then set up an origin for the next selected tab\n            // if it doesn't have one already.\n            if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n                tab.origin = indexToSelect - this._selectedIndex;\n            }\n        });\n        if (this._selectedIndex !== indexToSelect) {\n            this._selectedIndex = indexToSelect;\n            this._lastFocusedTabIndex = null;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngAfterContentInit() {\n        this._subscribeToAllTabChanges();\n        this._subscribeToTabLabels();\n        // Subscribe to changes in the amount of tabs, in order to be\n        // able to re-render the content as new tabs are added or removed.\n        this._tabsSubscription = this._tabs.changes.subscribe(() => {\n            const indexToSelect = this._clampTabIndex(this._indexToSelect);\n            // Maintain the previously-selected tab if a new tab is added or removed and there is no\n            // explicit change that selects a different tab.\n            if (indexToSelect === this._selectedIndex) {\n                const tabs = this._tabs.toArray();\n                let selectedTab;\n                for (let i = 0; i < tabs.length; i++) {\n                    if (tabs[i].isActive) {\n                        // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n                        // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n                        // adding a tab within the `selectedIndexChange` event.\n                        this._indexToSelect = this._selectedIndex = i;\n                        this._lastFocusedTabIndex = null;\n                        selectedTab = tabs[i];\n                        break;\n                    }\n                }\n                // If we haven't found an active tab and a tab exists at the selected index, it means\n                // that the active tab was swapped out. Since this won't be picked up by the rendering\n                // loop in `ngAfterContentChecked`, we need to sync it up manually.\n                if (!selectedTab && tabs[indexToSelect]) {\n                    Promise.resolve().then(() => {\n                        tabs[indexToSelect].isActive = true;\n                        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                    });\n                }\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Listens to changes in all of the tabs. */\n    _subscribeToAllTabChanges() {\n        // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n        // some that are inside of nested tab groups. We filter them out manually by checking that\n        // the closest group to the tab is the current one.\n        this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe((tabs) => {\n            this._tabs.reset(tabs.filter(tab => {\n                return tab._closestTabGroup === this || !tab._closestTabGroup;\n            }));\n            this._tabs.notifyOnChanges();\n        });\n    }\n    ngOnDestroy() {\n        this._tabs.destroy();\n        this._tabsSubscription.unsubscribe();\n        this._tabLabelSubscription.unsubscribe();\n    }\n    /** Re-aligns the ink bar to the selected tab element. */\n    realignInkBar() {\n        if (this._tabHeader) {\n            this._tabHeader._alignInkBarToSelectedTab();\n        }\n    }\n    /**\n     * Recalculates the tab group's pagination dimensions.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        if (this._tabHeader) {\n            this._tabHeader.updatePagination();\n        }\n    }\n    /**\n     * Sets focus to a particular tab.\n     * @param index Index of the tab to be focused.\n     */\n    focusTab(index) {\n        const header = this._tabHeader;\n        if (header) {\n            header.focusIndex = index;\n        }\n    }\n    _focusChanged(index) {\n        this._lastFocusedTabIndex = index;\n        this.focusChange.emit(this._createChangeEvent(index));\n    }\n    _createChangeEvent(index) {\n        const event = new MatTabChangeEvent();\n        event.index = index;\n        if (this._tabs && this._tabs.length) {\n            event.tab = this._tabs.toArray()[index];\n        }\n        return event;\n    }\n    /**\n     * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n     * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n     * binding to be updated, we need to subscribe to changes in it and trigger change detection\n     * manually.\n     */\n    _subscribeToTabLabels() {\n        if (this._tabLabelSubscription) {\n            this._tabLabelSubscription.unsubscribe();\n        }\n        this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Clamps the given index to the bounds of 0 and the tabs length. */\n    _clampTabIndex(index) {\n        // Note the `|| 0`, which ensures that values like NaN can't get through\n        // and which would otherwise throw the component into an infinite loop\n        // (since Math.max(NaN, 0) === NaN).\n        return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n    }\n    /** Returns a unique id for each tab label element */\n    _getTabLabelId(i) {\n        return `mat-tab-label-${this._groupId}-${i}`;\n    }\n    /** Returns a unique id for each tab content element */\n    _getTabContentId(i) {\n        return `mat-tab-content-${this._groupId}-${i}`;\n    }\n    /**\n     * Sets the height of the body wrapper to the height of the activating tab if dynamic\n     * height property is true.\n     */\n    _setTabBodyWrapperHeight(tabHeight) {\n        if (!this._dynamicHeight || !this._tabBodyWrapperHeight) {\n            return;\n        }\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n        // This conditional forces the browser to paint the height so that\n        // the animation to the new height can have an origin.\n        if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n            wrapper.style.height = tabHeight + 'px';\n        }\n    }\n    /** Removes the height of the tab body wrapper. */\n    _removeTabBodyWrapperHeight() {\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        this._tabBodyWrapperHeight = wrapper.clientHeight;\n        wrapper.style.height = '';\n        this.animationDone.emit();\n    }\n    /** Handle click events, setting new selected index if appropriate. */\n    _handleClick(tab, tabHeader, index) {\n        tabHeader.focusIndex = index;\n        if (!tab.disabled) {\n            this.selectedIndex = index;\n        }\n    }\n    /** Retrieves the tabindex for the tab. */\n    _getTabIndex(index) {\n        const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n        return index === targetIndex ? 0 : -1;\n    }\n    /** Callback for when the focused state of a tab has changed. */\n    _tabFocusChanged(focusOrigin, index) {\n        // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n        // can cause the tab to be moved out from under the pointer, interrupting the\n        // click sequence (see #21898). We don't need to scroll the tab into view for\n        // such cases anyway, because it will be done when the tab becomes selected.\n        if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n            this._tabHeader.focusIndex = index;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabGroupBase, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_TABS_CONFIG, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatTabGroupBase, inputs: { dynamicHeight: \"dynamicHeight\", selectedIndex: \"selectedIndex\", headerPosition: \"headerPosition\", animationDuration: \"animationDuration\", contentTabIndex: \"contentTabIndex\", disablePagination: \"disablePagination\", preserveContent: \"preserveContent\", backgroundColor: \"backgroundColor\" }, outputs: { selectedIndexChange: \"selectedIndexChange\", focusChange: \"focusChange\", animationDone: \"animationDone\", selectedTabChange: \"selectedTabChange\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabGroupBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { dynamicHeight: [{\n                type: Input\n            }], selectedIndex: [{\n                type: Input\n            }], headerPosition: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], contentTabIndex: [{\n                type: Input\n            }], disablePagination: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], backgroundColor: [{\n                type: Input\n            }], selectedIndexChange: [{\n                type: Output\n            }], focusChange: [{\n                type: Output\n            }], animationDone: [{\n                type: Output\n            }], selectedTabChange: [{\n                type: Output\n            }] } });\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup extends _MatTabGroupBase {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent;\n    }\n    set fitInkBarToContent(v) {\n        this._fitInkBarToContent = coerceBooleanProperty(v);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Whether tabs should be stretched to fill the header. */\n    get stretchTabs() {\n        return this._stretchTabs;\n    }\n    set stretchTabs(v) {\n        this._stretchTabs = coerceBooleanProperty(v);\n    }\n    constructor(elementRef, changeDetectorRef, defaultConfig, animationMode) {\n        super(elementRef, changeDetectorRef, defaultConfig, animationMode);\n        this._fitInkBarToContent = false;\n        this._stretchTabs = true;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabGroup, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_TABS_CONFIG, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabGroup, selector: \"mat-tab-group\", inputs: { color: \"color\", disableRipple: \"disableRipple\", fitInkBarToContent: \"fitInkBarToContent\", stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\"] }, host: { attributes: { \"ngSkipHydration\": \"\" }, properties: { \"class.mat-mdc-tab-group-dynamic-height\": \"dynamicHeight\", \"class.mat-mdc-tab-group-inverted-header\": \"headerPosition === \\\"below\\\"\", \"class.mat-mdc-tab-group-stretch-tabs\": \"stretchTabs\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-group\" }, providers: [\n            {\n                provide: MAT_TAB_GROUP,\n                useExisting: MatTabGroup,\n            },\n        ], queries: [{ propertyName: \"_allTabs\", predicate: MatTab, descendants: true }], viewQueries: [{ propertyName: \"_tabBodyWrapper\", first: true, predicate: [\"tabBodyWrapper\"], descendants: true }, { propertyName: \"_tabHeader\", first: true, predicate: [\"tabHeader\"], descendants: true }], exportAs: [\"matTabGroup\"], usesInheritance: true, ngImport: i0, template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n       #tabNode\\n       role=\\\"tab\\\"\\n       matTabLabelWrapper\\n       cdkMonitorElementFocus\\n       *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n       [id]=\\\"_getTabLabelId(i)\\\"\\n       [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n       [attr.aria-posinset]=\\\"i + 1\\\"\\n       [attr.aria-setsize]=\\\"_tabs.length\\\"\\n       [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n       [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n       [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n       [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n       [ngClass]=\\\"tab.labelClass\\\"\\n       [disabled]=\\\"tab.disabled\\\"\\n       [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n       (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n       (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n    <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n    <!-- Needs to be a separate element, because we can't put\\n         `overflow: hidden` on tab due to the ink bar. -->\\n    <div\\n      class=\\\"mat-mdc-tab-ripple\\\"\\n      mat-ripple\\n      [matRippleTrigger]=\\\"tabNode\\\"\\n      [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n    <span class=\\\"mdc-tab__content\\\">\\n      <span class=\\\"mdc-tab__text-label\\\">\\n        <!-- If there is a label template, use it. -->\\n        <ng-template [ngIf]=\\\"tab.templateLabel\\\" [ngIfElse]=\\\"tabTextLabel\\\">\\n          <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n        </ng-template>\\n\\n        <!-- If there is not a label template, fall back to the text label. -->\\n        <ng-template #tabTextLabel>{{tab.textLabel}}</ng-template>\\n      </span>\\n    </span>\\n  </div>\\n</mat-tab-header>\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  <mat-tab-body role=\\\"tabpanel\\\"\\n               *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n               [id]=\\\"_getTabContentId(i)\\\"\\n               [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n               [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n               [attr.aria-hidden]=\\\"selectedIndex !== i\\\"\\n               [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n               [ngClass]=\\\"tab.bodyClass\\\"\\n               [content]=\\\"tab.content!\\\"\\n               [position]=\\\"tab.position!\\\"\\n               [origin]=\\\"tab.origin\\\"\\n               [animationDuration]=\\\"animationDuration\\\"\\n               [preserveContent]=\\\"preserveContent\\\"\\n               (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n               (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n  </mat-tab-body>\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"], dependencies: [{ kind: \"directive\", type: i1$2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1$2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1$2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }, { kind: \"directive\", type: i5.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i4.CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: [\"cdkFocusChange\"], exportAs: [\"cdkMonitorFocus\"] }, { kind: \"component\", type: MatTabBody, selector: \"mat-tab-body\" }, { kind: \"directive\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: [\"disabled\", \"fitInkBarToContent\"] }, { kind: \"component\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: [\"selectedIndex\"], outputs: [\"selectFocusedIndex\", \"indexFocused\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-group', exportAs: 'matTabGroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, inputs: ['color', 'disableRipple'], providers: [\n                        {\n                            provide: MAT_TAB_GROUP,\n                            useExisting: MatTabGroup,\n                        },\n                    ], host: {\n                        'ngSkipHydration': '',\n                        'class': 'mat-mdc-tab-group',\n                        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n                        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n                        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  <div class=\\\"mdc-tab mat-mdc-tab mat-mdc-focus-indicator\\\"\\n       #tabNode\\n       role=\\\"tab\\\"\\n       matTabLabelWrapper\\n       cdkMonitorElementFocus\\n       *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n       [id]=\\\"_getTabLabelId(i)\\\"\\n       [attr.tabIndex]=\\\"_getTabIndex(i)\\\"\\n       [attr.aria-posinset]=\\\"i + 1\\\"\\n       [attr.aria-setsize]=\\\"_tabs.length\\\"\\n       [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n       [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n       [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n       [class.mdc-tab--active]=\\\"selectedIndex === i\\\"\\n       [ngClass]=\\\"tab.labelClass\\\"\\n       [disabled]=\\\"tab.disabled\\\"\\n       [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n       (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n       (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n    <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n    <!-- Needs to be a separate element, because we can't put\\n         `overflow: hidden` on tab due to the ink bar. -->\\n    <div\\n      class=\\\"mat-mdc-tab-ripple\\\"\\n      mat-ripple\\n      [matRippleTrigger]=\\\"tabNode\\\"\\n      [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n    <span class=\\\"mdc-tab__content\\\">\\n      <span class=\\\"mdc-tab__text-label\\\">\\n        <!-- If there is a label template, use it. -->\\n        <ng-template [ngIf]=\\\"tab.templateLabel\\\" [ngIfElse]=\\\"tabTextLabel\\\">\\n          <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n        </ng-template>\\n\\n        <!-- If there is not a label template, fall back to the text label. -->\\n        <ng-template #tabTextLabel>{{tab.textLabel}}</ng-template>\\n      </span>\\n    </span>\\n  </div>\\n</mat-tab-header>\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  <mat-tab-body role=\\\"tabpanel\\\"\\n               *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n               [id]=\\\"_getTabContentId(i)\\\"\\n               [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n               [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n               [attr.aria-hidden]=\\\"selectedIndex !== i\\\"\\n               [class.mat-mdc-tab-body-active]=\\\"selectedIndex === i\\\"\\n               [ngClass]=\\\"tab.bodyClass\\\"\\n               [content]=\\\"tab.content!\\\"\\n               [position]=\\\"tab.position!\\\"\\n               [origin]=\\\"tab.origin\\\"\\n               [animationDuration]=\\\"animationDuration\\\"\\n               [preserveContent]=\\\"preserveContent\\\"\\n               (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n               (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n  </mat-tab-body>\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _allTabs: [{\n                type: ContentChildren,\n                args: [MatTab, { descendants: true }]\n            }], _tabBodyWrapper: [{\n                type: ViewChild,\n                args: ['tabBodyWrapper']\n            }], _tabHeader: [{\n                type: ViewChild,\n                args: ['tabHeader']\n            }], fitInkBarToContent: [{\n                type: Input\n            }], stretchTabs: [{\n                type: Input,\n                args: ['mat-stretch-tabs']\n            }] } });\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n}\n\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n/**\n * Base class with all of the `MatTabNav` functionality.\n * @docs-private\n */\nclass _MatTabNavBase extends MatPaginatedTabHeader {\n    /** Background color of the tab nav. */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n        return this._disableRipple;\n    }\n    set disableRipple(value) {\n        this._disableRipple = coerceBooleanProperty(value);\n    }\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        this._disableRipple = false;\n        /** Theme color of the nav bar. */\n        this.color = 'primary';\n    }\n    _itemSelected() {\n        // noop\n    }\n    ngAfterContentInit() {\n        // We need this to run before the `changes` subscription in parent to ensure that the\n        // selectedIndex is up-to-date by the time the super class starts looking for it.\n        this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            this.updateActiveLink();\n        });\n        super.ngAfterContentInit();\n    }\n    /** Notifies the component that the active link has been changed. */\n    updateActiveLink() {\n        if (!this._items) {\n            return;\n        }\n        const items = this._items.toArray();\n        for (let i = 0; i < items.length; i++) {\n            if (items[i].active) {\n                this.selectedIndex = i;\n                this._changeDetectorRef.markForCheck();\n                if (this.tabPanel) {\n                    this.tabPanel._activeTabId = items[i].id;\n                }\n                return;\n            }\n        }\n        // The ink bar should hide itself if no items are active.\n        this.selectedIndex = -1;\n        this._inkBar.hide();\n    }\n    _getRole() {\n        return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabNavBase, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatTabNavBase, inputs: { backgroundColor: \"backgroundColor\", disableRipple: \"disableRipple\", color: \"color\", tabPanel: \"tabPanel\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabNavBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { backgroundColor: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], tabPanel: [{\n                type: Input\n            }] } });\n// Boilerplate for applying mixins to MatTabLink.\nconst _MatTabLinkMixinBase = mixinTabIndex(mixinDisableRipple(mixinDisabled(class {\n})));\n/** Base class with all of the `MatTabLink` functionality. */\nclass _MatTabLinkBase extends _MatTabLinkMixinBase {\n    /** Whether the link is active. */\n    get active() {\n        return this._isActive;\n    }\n    set active(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this._isActive) {\n            this._isActive = newValue;\n            this._tabNavBar.updateActiveLink();\n        }\n    }\n    /**\n     * Whether ripples are disabled on interaction.\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._tabNavBar.disableRipple ||\n            !!this.rippleConfig.disabled);\n    }\n    constructor(_tabNavBar, \n    /** @docs-private */ elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n        super();\n        this._tabNavBar = _tabNavBar;\n        this.elementRef = elementRef;\n        this._focusMonitor = _focusMonitor;\n        /** Whether the tab link is active or not. */\n        this._isActive = false;\n        /** Unique id for the tab. */\n        this.id = `mat-tab-link-${nextUniqueId++}`;\n        this.rippleConfig = globalRippleOptions || {};\n        this.tabIndex = parseInt(tabIndex) || 0;\n        if (animationMode === 'NoopAnimations') {\n            this.rippleConfig.animation = { enterDuration: 0, exitDuration: 0 };\n        }\n    }\n    /** Focuses the tab link. */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this.elementRef);\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this.elementRef);\n    }\n    _handleFocus() {\n        // Since we allow navigation through tabbing in the nav bar, we\n        // have to update the focused index whenever the link receives focus.\n        this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n    }\n    _handleKeydown(event) {\n        if (event.keyCode === SPACE || event.keyCode === ENTER) {\n            if (this.disabled) {\n                event.preventDefault();\n            }\n            else if (this._tabNavBar.tabPanel) {\n                this.elementRef.nativeElement.click();\n            }\n        }\n    }\n    _getAriaControls() {\n        return this._tabNavBar.tabPanel\n            ? this._tabNavBar.tabPanel?.id\n            : this.elementRef.nativeElement.getAttribute('aria-controls');\n    }\n    _getAriaSelected() {\n        if (this._tabNavBar.tabPanel) {\n            return this.active ? 'true' : 'false';\n        }\n        else {\n            return this.elementRef.nativeElement.getAttribute('aria-selected');\n        }\n    }\n    _getAriaCurrent() {\n        return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n    }\n    _getRole() {\n        return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n    }\n    _getTabIndex() {\n        if (this._tabNavBar.tabPanel) {\n            return this._isActive && !this.disabled ? 0 : -1;\n        }\n        else {\n            return this.tabIndex;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabLinkBase, deps: [{ token: _MatTabNavBase }, { token: i0.ElementRef }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }, { token: i4.FocusMonitor }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatTabLinkBase, inputs: { active: \"active\", id: \"id\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatTabLinkBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: _MatTabNavBase }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i4.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { active: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }] } });\nconst _MatTabLinkBaseWithInkBarItem = mixinInkBarItem(_MatTabLinkBase);\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends _MatTabNavBase {\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent.value;\n    }\n    set fitInkBarToContent(v) {\n        this._fitInkBarToContent.next(coerceBooleanProperty(v));\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Whether tabs should be stretched to fill the header. */\n    get stretchTabs() {\n        return this._stretchTabs;\n    }\n    set stretchTabs(v) {\n        this._stretchTabs = coerceBooleanProperty(v);\n    }\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        this._animationDuration = /^\\d+$/.test(value + '') ? value + 'ms' : value;\n    }\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode, defaultConfig) {\n        super(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode);\n        this._fitInkBarToContent = new BehaviorSubject(false);\n        this._stretchTabs = true;\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        super.ngAfterContentInit();\n    }\n    ngAfterViewInit() {\n        if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n        }\n        super.ngAfterViewInit();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabNav, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_TABS_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabNav, selector: \"[mat-tab-nav-bar]\", inputs: { color: \"color\", fitInkBarToContent: \"fitInkBarToContent\", stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\"], animationDuration: \"animationDuration\" }, host: { properties: { \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\", \"class.mat-mdc-tab-nav-bar-stretch-tabs\": \"stretchTabs\", \"class.mat-primary\": \"color !== \\\"warn\\\" && color !== \\\"accent\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-nav-bar mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: i0.forwardRef(function () { return MatTabLink; }), descendants: true }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], exportAs: [\"matTabNavBar\", \"matTabNav\"], usesInheritance: true, ngImport: i0, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0;--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mdc-secondary-navigation-tab-container-height:48px}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"], dependencies: [{ kind: \"directive\", type: i5.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i5$1.CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabNav, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-nav-bar]', exportAs: 'matTabNavBar, matTabNav', inputs: ['color'], host: {\n                        '[attr.role]': '_getRole()',\n                        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n                        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, template: \"<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<!-- TODO: this also had `mat-elevation-z4`. Figure out what we should do with it. -->\\n<button class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mdc-tab{min-width:90px;padding-right:24px;padding-left:24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;margin:0;padding-top:0;padding-bottom:0;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;-webkit-appearance:none;z-index:1}.mdc-tab::-moz-focus-inner{padding:0;border:0}.mdc-tab[hidden]{display:none}.mdc-tab--min-width{flex:0 1 auto}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab__icon{transition:150ms color linear;z-index:2}.mdc-tab--stacked .mdc-tab__content{flex-direction:column;align-items:center;justify-content:center}.mdc-tab--stacked .mdc-tab__text-label{padding-top:6px;padding-bottom:4px}.mdc-tab--active .mdc-tab__text-label,.mdc-tab--active .mdc-tab__icon{transition-delay:100ms}.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label{padding-left:8px;padding-right:0}[dir=rtl] .mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label,.mdc-tab:not(.mdc-tab--stacked) .mdc-tab__icon+.mdc-tab__text-label[dir=rtl]{padding-left:0;padding-right:8px}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator__content--icon{align-self:center;margin:0 auto}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}.mdc-tab-indicator .mdc-tab-indicator__content{transition:250ms transform cubic-bezier(0.4, 0, 0.2, 1)}.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition:150ms opacity linear}.mdc-tab-indicator--active.mdc-tab-indicator--fade .mdc-tab-indicator__content{transition-delay:100ms}.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0;--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mdc-secondary-navigation-tab-container-height:48px}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color)}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}._mat-animation-noopable span.mdc-tab-indicator__content,._mat-animation-noopable span.mdc-tab__text-label{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }] }]; }, propDecorators: { fitInkBarToContent: [{\n                type: Input\n            }], stretchTabs: [{\n                type: Input,\n                args: ['mat-stretch-tabs']\n            }], animationDuration: [{\n                type: Input\n            }], _items: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatTabLink), { descendants: true }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n/**\n * Link inside of a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends _MatTabLinkBaseWithInkBarItem {\n    constructor(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode) {\n        super(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode);\n        this._destroyed = new Subject();\n        tabNavBar._fitInkBarToContent.pipe(takeUntil(this._destroyed)).subscribe(fitInkBarToContent => {\n            this.fitInkBarToContent = fitInkBarToContent;\n        });\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        super.ngOnDestroy();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabLink, deps: [{ token: MatTabNav }, { token: i0.ElementRef }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }, { token: i4.FocusMonitor }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabLink, selector: \"[mat-tab-link], [matTabLink]\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", tabIndex: \"tabIndex\", active: \"active\", id: \"id\" }, host: { listeners: { \"focus\": \"_handleFocus()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-controls\": \"_getAriaControls()\", \"attr.aria-current\": \"_getAriaCurrent()\", \"attr.aria-disabled\": \"disabled\", \"attr.aria-selected\": \"_getAriaSelected()\", \"attr.id\": \"id\", \"attr.tabIndex\": \"_getTabIndex()\", \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-disabled\": \"disabled\", \"class.mdc-tab--active\": \"active\" }, classAttribute: \"mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator\" }, exportAs: [\"matTabLink\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"], dependencies: [{ kind: \"directive\", type: i5.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabLink, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-link], [matTabLink]', exportAs: 'matTabLink', inputs: ['disabled', 'disableRipple', 'tabIndex', 'active', 'id'], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'mdc-tab mat-mdc-tab-link mat-mdc-focus-indicator',\n                        '[attr.aria-controls]': '_getAriaControls()',\n                        '[attr.aria-current]': '_getAriaCurrent()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.aria-selected]': '_getAriaSelected()',\n                        '[attr.id]': 'id',\n                        '[attr.tabIndex]': '_getTabIndex()',\n                        '[attr.role]': '_getRole()',\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[class.mdc-tab--active]': 'active',\n                        '(focus)': '_handleFocus()',\n                        '(keydown)': '_handleKeydown($event)',\n                    }, template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;font-family:var(--mat-tab-header-label-text-font);font-size:var(--mat-tab-header-label-text-size);letter-spacing:var(--mat-tab-header-label-text-tracking);line-height:var(--mat-tab-header-label-text-line-height);font-weight:var(--mat-tab-header-label-text-weight)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-top-width:var(--mdc-tab-indicator-active-indicator-height)}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-radius:var(--mdc-tab-indicator-active-indicator-shape)}.mat-mdc-tab-link:not(.mdc-tab--stacked){height:var(--mdc-secondary-navigation-tab-container-height)}.mat-mdc-tab-link:not(:disabled).mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled.mdc-tab--active .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):hover:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):focus:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:not(:disabled):active:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link:disabled:not(.mdc-tab--active) .mdc-tab__icon{fill:currentColor}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color)}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color)}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color)}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color)}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color);display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color)}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\"] }]\n        }], ctorParameters: function () { return [{ type: MatTabNav }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i4.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; } });\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n    constructor() {\n        /** Unique id for the tab panel. */\n        this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabNavPanel, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatTabNavPanel, selector: \"mat-tab-nav-panel\", inputs: { id: \"id\" }, host: { attributes: { \"role\": \"tabpanel\" }, properties: { \"attr.aria-labelledby\": \"_activeTabId\", \"attr.id\": \"id\" }, classAttribute: \"mat-mdc-tab-nav-panel\" }, exportAs: [\"matTabNavPanel\"], ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabNavPanel, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-tab-nav-panel',\n                    exportAs: 'matTabNavPanel',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        '[attr.aria-labelledby]': '_activeTabId',\n                        '[attr.id]': 'id',\n                        'class': 'mat-mdc-tab-nav-panel',\n                        'role': 'tabpanel',\n                    },\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n\nclass MatTabsModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabsModule, declarations: [MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink,\n            // Private directives, should not be exported.\n            MatTabBody,\n            MatTabBodyPortal,\n            MatTabLabelWrapper,\n            MatTabHeader], imports: [CommonModule,\n            MatCommonModule,\n            PortalModule,\n            MatRippleModule,\n            ObserversModule,\n            A11yModule], exports: [MatCommonModule,\n            MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabsModule, imports: [CommonModule,\n            MatCommonModule,\n            PortalModule,\n            MatRippleModule,\n            ObserversModule,\n            A11yModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatTabsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        MatCommonModule,\n                        PortalModule,\n                        MatRippleModule,\n                        ObserversModule,\n                        A11yModule,\n                    ],\n                    exports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                    declarations: [\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                        // Private directives, should not be exported.\n                        MatTabBody,\n                        MatTabBodyPortal,\n                        MatTabLabelWrapper,\n                        MatTabHeader,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, _MatTabBase, _MatTabBodyBase, _MatTabGroupBase, _MatTabHeaderBase, _MatTabLabelWrapperBase, _MatTabLinkBase, _MatTabNavBase, matTabsAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,iBAAiB;AACvC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAClQ,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAClK,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,SAASC,eAAe,EAAEC,SAAS,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AAC9F,OAAO,KAAKC,IAAI,MAAM,wBAAwB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,EAAEC,UAAU,QAAQ,mBAAmB;AAC/D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,YAAY,EAAEC,OAAO,EAAEC,SAAS,EAAEC,EAAE,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,eAAe,QAAQ,MAAM;AAC7G,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,gBAAgB;AAC1G,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,OAAO,KAAKC,IAAI,MAAM,wBAAwB;AAC9C,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;;AAEpE;AACA;AACA;AACA;AAHA,SAAAC,kCAAAC,EAAA,EAAAC,GAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA;EAAA;IAAAC,iBAAA,EAAAD;EAAA;AAAA;AAAA,MAAAE,GAAA,YAAAA,CAAAF,EAAA,EAAAG,EAAA;EAAA;IAAAC,KAAA,EAAAJ,EAAA;IAAAK,MAAA,EAAAF;EAAA;AAAA;AAAA,SAAAG,8BAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAwEoGrE,EAAE,CAAA+E,YAAA,EA4gB4pB,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uDAAAnB,EAAA,EAAAC,GAAA;AAAA,SAAAmB,yCAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5gB/pBrE,EAAE,CAAA0F,UAAA,IAAAF,sDAAA,yBA+/ComE,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAsB,MAAA,GA//CvmE3F,EAAE,CAAA4F,aAAA,GAAAC,SAAA;IAAF7F,EAAE,CAAA8F,UAAA,oBAAAH,MAAA,CAAAI,aA+/CqlE,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA//CxlErE,EAAE,CAAAiG,MAAA,EA+/CqwE,CAAC;EAAA;EAAA,IAAA5B,EAAA;IAAA,MAAAsB,MAAA,GA//CxwE3F,EAAE,CAAA4F,aAAA,GAAAC,SAAA;IAAF7F,EAAE,CAAAkG,iBAAA,CAAAP,MAAA,CAAAQ,SA+/CqwE,CAAC;EAAA;AAAA;AAAA,SAAAC,2BAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgC,IAAA,GA//CxwErG,EAAE,CAAAsG,gBAAA;IAAFtG,EAAE,CAAAuG,cAAA,eA+/Co+C,CAAC;IA//Cv+CvG,EAAE,CAAAwG,UAAA,mBAAAC,gDAAA;MAAA,MAAAC,WAAA,GAAF1G,EAAE,CAAA2G,aAAA,CAAAN,IAAA;MAAA,MAAAV,MAAA,GAAAe,WAAA,CAAAb,SAAA;MAAA,MAAAe,IAAA,GAAAF,WAAA,CAAAG,KAAA;MAAA,MAAAC,OAAA,GAAF9G,EAAE,CAAA4F,aAAA;MAAA,MAAAmB,GAAA,GAAF/G,EAAE,CAAAgH,WAAA;MAAA,OAAFhH,EAAE,CAAAiH,WAAA,CA+/C04CH,OAAA,CAAAI,YAAA,CAAAvB,MAAA,EAAAoB,GAAA,EAAAH,IAA8B,EAAC;IAAA,CAAC,CAAC,4BAAAO,yDAAAC,MAAA;MAAA,MAAAV,WAAA,GA//C76C1G,EAAE,CAAA2G,aAAA,CAAAN,IAAA;MAAA,MAAAO,IAAA,GAAAF,WAAA,CAAAG,KAAA;MAAA,MAAAQ,OAAA,GAAFrH,EAAE,CAAA4F,aAAA;MAAA,OAAF5F,EAAE,CAAAiH,WAAA,CA+/Cu8CI,OAAA,CAAAC,gBAAA,CAAAF,MAAA,EAAAR,IAA0B,EAAC;IAAA,CAAxD,CAAC;IA//C76C5G,EAAE,CAAAuH,SAAA,aA+/CihD,CAAC,YAAD,CAAC;IA//CphDvH,EAAE,CAAAuG,cAAA,cA+/Ci2D,CAAC,cAAD,CAAC;IA//Cp2DvG,EAAE,CAAA0F,UAAA,IAAAD,wCAAA,yBA+/C4nE,CAAC;IA//C/nEzF,EAAE,CAAA0F,UAAA,IAAAM,wCAAA,iCAAFhG,EAAE,CAAAwH,sBA+/CmxE,CAAC;IA//CtxExH,EAAE,CAAAyH,YAAA,CA+/CkyE,CAAC,CAAD,CAAC,CAAD,CAAC;EAAA;EAAA,IAAApD,EAAA;IAAA,MAAAsB,MAAA,GAAArB,GAAA,CAAAuB,SAAA;IAAA,MAAAe,IAAA,GAAAtC,GAAA,CAAAuC,KAAA;IAAA,MAAAa,GAAA,GA//CryE1H,EAAE,CAAAgH,WAAA;IAAA,MAAAW,GAAA,GAAF3H,EAAE,CAAAgH,WAAA;IAAA,MAAAY,MAAA,GAAF5H,EAAE,CAAA4F,aAAA;IAAF5F,EAAE,CAAA6H,WAAA,oBAAAD,MAAA,CAAAE,aAAA,KAAAlB,IA+/CyvC,CAAC;IA//C5vC5G,EAAE,CAAA8F,UAAA,OAAA8B,MAAA,CAAAG,cAAA,CAAAnB,IAAA,CA+/C6zB,CAAC,YAAAjB,MAAA,CAAAqC,UAAD,CAAC,aAAArC,MAAA,CAAAsC,QAAD,CAAC,uBAAAL,MAAA,CAAAM,kBAAD,CAAC;IA//Ch0BlI,EAAE,CAAAmI,WAAA,aAAAP,MAAA,CAAAQ,YAAA,CAAAxB,IAAA,CA+/Cy2B,CAAC,kBAAAA,IAAA,IAAD,CAAC,iBAAAgB,MAAA,CAAAS,KAAA,CAAAC,MAAD,CAAC,kBAAAV,MAAA,CAAAW,gBAAA,CAAA3B,IAAA,CAAD,CAAC,kBAAAgB,MAAA,CAAAE,aAAA,KAAAlB,IAAD,CAAC,eAAAjB,MAAA,CAAA6C,SAAA,QAAD,CAAC,qBAAA7C,MAAA,CAAA6C,SAAA,IAAA7C,MAAA,CAAA8C,cAAA,GAAA9C,MAAA,CAAA8C,cAAA,OAAD,CAAC;IA//C52BzI,EAAE,CAAA0I,SAAA,EA+/CovD,CAAC;IA//CvvD1I,EAAE,CAAA8F,UAAA,qBAAA4B,GA+/CovD,CAAC,sBAAA/B,MAAA,CAAAsC,QAAA,IAAAL,MAAA,CAAAe,aAAD,CAAC;IA//CvvD3I,EAAE,CAAA0I,SAAA,EA+/Cw/D,CAAC;IA//C3/D1I,EAAE,CAAA8F,UAAA,SAAAH,MAAA,CAAAI,aA+/Cw/D,CAAC,aAAA4B,GAAD,CAAC;EAAA;AAAA;AAAA,SAAAiB,oCAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwE,IAAA,GA//C3/D7I,EAAE,CAAAsG,gBAAA;IAAFtG,EAAE,CAAAuG,cAAA,sBA+/CmzG,CAAC;IA//CtzGvG,EAAE,CAAAwG,UAAA,yBAAAsC,wEAAA;MAAF9I,EAAE,CAAA2G,aAAA,CAAAkC,IAAA;MAAA,MAAAE,OAAA,GAAF/I,EAAE,CAAA4F,aAAA;MAAA,OAAF5F,EAAE,CAAAiH,WAAA,CA+/CgtG8B,OAAA,CAAAC,2BAAA,CAA4B,EAAC;IAAA,CAAC,CAAC,0BAAAC,yEAAA7B,MAAA;MA//CjvGpH,EAAE,CAAA2G,aAAA,CAAAkC,IAAA;MAAA,MAAAK,OAAA,GAAFlJ,EAAE,CAAA4F,aAAA;MAAA,OAAF5F,EAAE,CAAAiH,WAAA,CA+/CixGiC,OAAA,CAAAC,wBAAA,CAAA/B,MAA+B,EAAC;IAAA,CAAnE,CAAC;IA//CjvGpH,EAAE,CAAAyH,YAAA,CA+/Cs0G,CAAC;EAAA;EAAA,IAAApD,EAAA;IAAA,MAAA+E,OAAA,GAAA9E,GAAA,CAAAuB,SAAA;IAAA,MAAAwD,KAAA,GAAA/E,GAAA,CAAAuC,KAAA;IAAA,MAAAyC,MAAA,GA//Cz0GtJ,EAAE,CAAA4F,aAAA;IAAF5F,EAAE,CAAA6H,WAAA,4BAAAyB,MAAA,CAAAxB,aAAA,KAAAuB,KA+/Ck5F,CAAC;IA//Cr5FrJ,EAAE,CAAA8F,UAAA,OAAAwD,MAAA,CAAAf,gBAAA,CAAAc,KAAA,CA+/ComF,CAAC,YAAAD,OAAA,CAAAG,SAAD,CAAC,YAAAH,OAAA,CAAAI,OAAD,CAAC,aAAAJ,OAAA,CAAAK,QAAD,CAAC,WAAAL,OAAA,CAAAM,MAAD,CAAC,sBAAAJ,MAAA,CAAA7E,iBAAD,CAAC,oBAAA6E,MAAA,CAAAK,eAAD,CAAC;IA//CvmF3J,EAAE,CAAAmI,WAAA,aAAAmB,MAAA,CAAAM,eAAA,YAAAN,MAAA,CAAAxB,aAAA,KAAAuB,KAAA,GAAAC,MAAA,CAAAM,eAAA,OA+/CktF,CAAC,oBAAAN,MAAA,CAAAvB,cAAA,CAAAsB,KAAA,CAAD,CAAC,gBAAAC,MAAA,CAAAxB,aAAA,KAAAuB,KAAD,CAAC;EAAA;AAAA;AAAA,MAAAQ,IAAA;AAAA,MAAAC,IAAA;AAnkDzzF,MAAMC,iBAAiB,GAAG;EACtB;EACAC,YAAY,EAAE1G,OAAO,CAAC,cAAc,EAAE;EAClC;EACAC,KAAK,CAAC,uDAAuD,EAAEC,KAAK,CAAC;IAAEyG,SAAS,EAAE;EAAO,CAAC,CAAC,CAAC;EAC5F;EACA;EACA;EACA;EACA1G,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChByG,SAAS,EAAE,0BAA0B;IACrCC,SAAS,EAAE,KAAK;IAChB;IACA;IACAC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC,EACH5G,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;IACjByG,SAAS,EAAE,yBAAyB;IACpCC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC,EACH1G,UAAU,CAAC,wDAAwD,EAAEC,OAAO,CAAC,sDAAsD,CAAC,CAAC,EACrID,UAAU,CAAC,4BAA4B,EAAE,CACrCD,KAAK,CAAC;IAAEyG,SAAS,EAAE,0BAA0B;IAAEE,UAAU,EAAE;EAAS,CAAC,CAAC,EACtEzG,OAAO,CAAC,sDAAsD,CAAC,CAClE,CAAC,EACFD,UAAU,CAAC,6BAA6B,EAAE,CACtCD,KAAK,CAAC;IAAEyG,SAAS,EAAE,yBAAyB;IAAEE,UAAU,EAAE;EAAS,CAAC,CAAC,EACrEzG,OAAO,CAAC,sDAAsD,CAAC,CAClE,CAAC,CACL;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAM0G,gBAAgB,SAASxI,eAAe,CAAC;EAC3CyI,WAAWA,CAACC,wBAAwB,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,SAAS,EAAE;IACtE,KAAK,CAACH,wBAAwB,EAAEC,gBAAgB,EAAEE,SAAS,CAAC;IAC5D,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACE,aAAa,GAAGpI,YAAY,CAACK,KAAK;IACvC;IACA,IAAI,CAACgI,WAAW,GAAGrI,YAAY,CAACK,KAAK;EACzC;EACA;EACAiI,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAACF,aAAa,GAAG,IAAI,CAACF,KAAK,CAACK,gBAAgB,CAC3CC,IAAI,CAAC/H,SAAS,CAAC,IAAI,CAACyH,KAAK,CAACO,iBAAiB,CAAC,IAAI,CAACP,KAAK,CAACQ,SAAS,CAAC,CAAC,CAAC,CACnEC,SAAS,CAAEC,WAAW,IAAK;MAC5B,IAAIA,WAAW,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;QACpC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAAC;MACpC;IACJ,CAAC,CAAC;IACF,IAAI,CAACV,WAAW,GAAG,IAAI,CAACH,KAAK,CAACc,mBAAmB,CAACL,SAAS,CAAC,MAAM;MAC9D,IAAI,CAAC,IAAI,CAACT,KAAK,CAACb,eAAe,EAAE;QAC7B,IAAI,CAAC4B,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA;EACAC,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAACd,aAAa,CAACe,WAAW,CAAC,CAAC;IAChC,IAAI,CAACd,WAAW,CAACc,WAAW,CAAC,CAAC;EAClC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFxB,gBAAgB,EAA1BpK,EAAE,CAAA6L,iBAAA,CAA0C7L,EAAE,CAAC8L,wBAAwB,GAAvE9L,EAAE,CAAA6L,iBAAA,CAAkF7L,EAAE,CAAC+L,gBAAgB,GAAvG/L,EAAE,CAAA6L,iBAAA,CAAkH5L,UAAU,CAAC,MAAM+L,UAAU,CAAC,GAAhJhM,EAAE,CAAA6L,iBAAA,CAA2J/L,QAAQ;IAAA,CAA4C;EAAE;EACnT;IAAS,IAAI,CAACmM,IAAI,kBAD8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EACJ/B,gBAAgB;MAAAgC,SAAA;MAAAC,QAAA,GADdrM,EAAE,CAAAsM,0BAAA;IAAA,EACkF;EAAE;AAC1L;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGvM,EAAE,CAAAwM,iBAAA,CAGXpC,gBAAgB,EAAc,CAAC;IAC9G+B,IAAI,EAAEjM,SAAS;IACfuM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEP,IAAI,EAAEnM,EAAE,CAAC8L;IAAyB,CAAC,EAAE;MAAEK,IAAI,EAAEnM,EAAE,CAAC+L;IAAiB,CAAC,EAAE;MAAEI,IAAI,EAAEH,UAAU;MAAEW,UAAU,EAAE,CAAC;QACrIR,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACxM,UAAU,CAAC,MAAM+L,UAAU,CAAC;MACvC,CAAC;IAAE,CAAC,EAAE;MAAEG,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCR,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAAC3M,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA;AACA,MAAM+M,eAAe,CAAC;EAClB;EACA,IAAIpD,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACqD,cAAc,GAAGrD,QAAQ;IAC9B,IAAI,CAACsD,8BAA8B,CAAC,CAAC;EACzC;EACA1C,WAAWA,CAAC2C,WAAW,EAAEC,IAAI,EAAEC,iBAAiB,EAAE;IAC9C,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAACE,sBAAsB,GAAG7K,YAAY,CAACK,KAAK;IAChD;IACA,IAAI,CAACyK,qBAAqB,GAAG,IAAI7K,OAAO,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC8K,YAAY,GAAG,IAAIjN,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAACyK,gBAAgB,GAAG,IAAIzK,YAAY,CAAC,CAAC;IAC1C;IACA,IAAI,CAACkL,mBAAmB,GAAG,IAAIlL,YAAY,CAAC,CAAC;IAC7C;IACA,IAAI,CAACkN,WAAW,GAAG,IAAIlN,YAAY,CAAC,IAAI,CAAC;IACzC;IACA;IACA;IACA,IAAI,CAACqE,iBAAiB,GAAG,OAAO;IAChC;IACA,IAAI,CAACkF,eAAe,GAAG,KAAK;IAC5B,IAAIsD,IAAI,EAAE;MACN,IAAI,CAACE,sBAAsB,GAAGF,IAAI,CAACM,MAAM,CAACtC,SAAS,CAAEuC,GAAG,IAAK;QACzD,IAAI,CAACT,8BAA8B,CAACS,GAAG,CAAC;QACxCN,iBAAiB,CAACO,YAAY,CAAC,CAAC;MACpC,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,CAACL,qBAAqB,CACrBtC,IAAI,CAAC9H,oBAAoB,CAAC,CAAC0K,CAAC,EAAEC,CAAC,KAAK;MACrC,OAAOD,CAAC,CAACE,SAAS,KAAKD,CAAC,CAACC,SAAS,IAAIF,CAAC,CAACG,OAAO,KAAKF,CAAC,CAACE,OAAO;IACjE,CAAC,CAAC,CAAC,CACE5C,SAAS,CAAC6C,KAAK,IAAI;MACpB;MACA,IAAI,IAAI,CAAC/C,iBAAiB,CAAC+C,KAAK,CAACD,OAAO,CAAC,IAAI,IAAI,CAAC9C,iBAAiB,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;QACjF,IAAI,CAACsC,WAAW,CAACS,IAAI,CAAC,CAAC;MAC3B;MACA,IAAI,IAAI,CAAChD,iBAAiB,CAAC+C,KAAK,CAACF,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC7C,iBAAiB,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;QACpF,IAAI,CAACM,mBAAmB,CAACyC,IAAI,CAAC,CAAC;MACnC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACInD,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACI,SAAS,IAAI,QAAQ,IAAI,IAAI,CAACtB,MAAM,IAAI,IAAI,EAAE;MACnD,IAAI,CAACsB,SAAS,GAAG,IAAI,CAACgD,0BAA0B,CAAC,IAAI,CAACtE,MAAM,CAAC;IACjE;EACJ;EACA8B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2B,sBAAsB,CAAC1B,WAAW,CAAC,CAAC;IACzC,IAAI,CAAC2B,qBAAqB,CAACa,QAAQ,CAAC,CAAC;EACzC;EACAC,sBAAsBA,CAACJ,KAAK,EAAE;IAC1B,MAAM5C,WAAW,GAAG,IAAI,CAACH,iBAAiB,CAAC+C,KAAK,CAACD,OAAO,CAAC;IACzD,IAAI,CAAChD,gBAAgB,CAACkD,IAAI,CAAC7C,WAAW,CAAC;IACvC,IAAIA,WAAW,EAAE;MACb,IAAI,CAACmC,YAAY,CAACU,IAAI,CAAC,IAAI,CAACf,WAAW,CAACmB,aAAa,CAACC,YAAY,CAAC;IACvE;EACJ;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACpB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACrI,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAmG,iBAAiBA,CAACtB,QAAQ,EAAE;IACxB,OAAQA,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,IAAI,oBAAoB,IAAIA,QAAQ,IAAI,qBAAqB;EACzG;EACA;EACAsD,8BAA8BA,CAACS,GAAG,GAAG,IAAI,CAACa,mBAAmB,CAAC,CAAC,EAAE;IAC7D,IAAI,IAAI,CAACvB,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC9B,SAAS,GAAGwC,GAAG,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO;IACpD,CAAC,MACI,IAAI,IAAI,CAACV,cAAc,GAAG,CAAC,EAAE;MAC9B,IAAI,CAAC9B,SAAS,GAAGwC,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM;IACpD,CAAC,MACI;MACD,IAAI,CAACxC,SAAS,GAAG,QAAQ;IAC7B;EACJ;EACA;AACJ;AACA;AACA;EACIgD,0BAA0BA,CAACtE,MAAM,EAAE;IAC/B,MAAM8D,GAAG,GAAG,IAAI,CAACa,mBAAmB,CAAC,CAAC;IACtC,IAAKb,GAAG,IAAI,KAAK,IAAI9D,MAAM,IAAI,CAAC,IAAM8D,GAAG,IAAI,KAAK,IAAI9D,MAAM,GAAG,CAAE,EAAE;MAC/D,OAAO,oBAAoB;IAC/B;IACA,OAAO,qBAAqB;EAChC;EACA;IAAS,IAAI,CAACgC,IAAI,YAAA4C,wBAAA1C,CAAA;MAAA,YAAAA,CAAA,IAAwFiB,eAAe,EAvHzB7M,EAAE,CAAA6L,iBAAA,CAuHyC7L,EAAE,CAACuO,UAAU,GAvHxDvO,EAAE,CAAA6L,iBAAA,CAuHmExJ,EAAE,CAACmM,cAAc,MAvHtFxO,EAAE,CAAA6L,iBAAA,CAuHiH7L,EAAE,CAACyO,iBAAiB;IAAA,CAA4C;EAAE;EACrR;IAAS,IAAI,CAACxC,IAAI,kBAxH8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EAwHJU,eAAe;MAAA6B,MAAA;QAAArD,QAAA;QAAA3B,MAAA;QAAAjF,iBAAA;QAAAkF,eAAA;QAAAF,QAAA;MAAA;MAAAkF,OAAA;QAAAtB,YAAA;QAAAxC,gBAAA;QAAAS,mBAAA;QAAAgC,WAAA;MAAA;IAAA,EAA6U;EAAE;AAChc;AACA;EAAA,QAAAf,SAAA,oBAAAA,SAAA,KA1HoGvM,EAAE,CAAAwM,iBAAA,CA0HXK,eAAe,EAAc,CAAC;IAC7GV,IAAI,EAAEjM;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiM,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAE9J,EAAE,CAACmM,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAC/FR,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAEnM,EAAE,CAACyO;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEpB,YAAY,EAAE,CAAC;MAC3ElB,IAAI,EAAE7L;IACV,CAAC,CAAC;IAAEuK,gBAAgB,EAAE,CAAC;MACnBsB,IAAI,EAAE7L;IACV,CAAC,CAAC;IAAEgL,mBAAmB,EAAE,CAAC;MACtBa,IAAI,EAAE7L;IACV,CAAC,CAAC;IAAEgN,WAAW,EAAE,CAAC;MACdnB,IAAI,EAAE7L;IACV,CAAC,CAAC;IAAE+K,QAAQ,EAAE,CAAC;MACXc,IAAI,EAAE5L,KAAK;MACXkM,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE/C,MAAM,EAAE,CAAC;MACTyC,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEkE,iBAAiB,EAAE,CAAC;MACpB0H,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEoJ,eAAe,EAAE,CAAC;MAClBwC,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEkJ,QAAQ,EAAE,CAAC;MACX0C,IAAI,EAAE5L;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMyL,UAAU,SAASa,eAAe,CAAC;EACrCxC,WAAWA,CAACuE,UAAU,EAAEpB,GAAG,EAAEN,iBAAiB,EAAE;IAC5C,KAAK,CAAC0B,UAAU,EAAEpB,GAAG,EAAEN,iBAAiB,CAAC;EAC7C;EACA;IAAS,IAAI,CAACxB,IAAI,YAAAmD,mBAAAjD,CAAA;MAAA,YAAAA,CAAA,IAAwFI,UAAU,EA1JpBhM,EAAE,CAAA6L,iBAAA,CA0JoC7L,EAAE,CAACuO,UAAU,GA1JnDvO,EAAE,CAAA6L,iBAAA,CA0J8DxJ,EAAE,CAACmM,cAAc,MA1JjFxO,EAAE,CAAA6L,iBAAA,CA0J4G7L,EAAE,CAACyO,iBAAiB;IAAA,CAA4C;EAAE;EAChR;IAAS,IAAI,CAACK,IAAI,kBA3J8E9O,EAAE,CAAA+O,iBAAA;MAAA5C,IAAA,EA2JJH,UAAU;MAAAI,SAAA;MAAA4C,SAAA,WAAAC,iBAAA5K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3JRrE,EAAE,CAAAkP,WAAA,CA2JqJtN,eAAe;QAAA;QAAA,IAAAyC,EAAA;UAAA,IAAA8K,EAAA;UA3JtKnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAgL,WAAA,GAAAH,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAnD,QAAA,GAAFrM,EAAE,CAAAsM,0BAAA;MAAAmD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oBAAAxL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAAuG,cAAA,eA2JwiB,CAAC;UA3J3iBvG,EAAE,CAAAwG,UAAA,iCAAAsJ,gEAAA1I,MAAA;YAAA,OA2Jkb9C,GAAA,CAAA4J,sBAAA,CAAA9G,MAA6B,CAAC;UAAA,CAAC,CAAC,gCAAA2I,+DAAA3I,MAAA;YAAA,OAA8B9C,GAAA,CAAA8I,qBAAA,CAAA4C,IAAA,CAAA5I,MAAiC,CAAC;UAAA,CAAjE,CAAC;UA3JpdpH,EAAE,CAAA0F,UAAA,IAAAtB,iCAAA,wBA2JslB,CAAC;UA3JzlBpE,EAAE,CAAAyH,YAAA,CA2J8lB,CAAC;QAAA;QAAA,IAAApD,EAAA;UA3JjmBrE,EAAE,CAAA8F,UAAA,kBAAF9F,EAAE,CAAAiQ,eAAA,IAAAvL,GAAA,EAAAJ,GAAA,CAAA0G,SAAA,EAAFhL,EAAE,CAAAkQ,eAAA,IAAA3L,GAAA,EAAAD,GAAA,CAAAG,iBAAA,EA2JkZ,CAAC;QAAA;MAAA;MAAA0L,YAAA,GAA6yB/F,gBAAgB;MAAAgG,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAA+C,CAACxG,iBAAiB,CAACC,YAAY;MAAC;IAAA,EAAkG;EAAE;AACz+C;AACA;EAAA,QAAAuC,SAAA,oBAAAA,SAAA,KA7JoGvM,EAAE,CAAAwM,iBAAA,CA6JXR,UAAU,EAAc,CAAC;IACxGG,IAAI,EAAE3L,SAAS;IACfiM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAE2D,aAAa,EAAE5P,iBAAiB,CAAC+P,IAAI;MAAEC,eAAe,EAAE/P,uBAAuB,CAACgQ,OAAO;MAAEC,UAAU,EAAE,CAAC5G,iBAAiB,CAACC,YAAY,CAAC;MAAE4G,IAAI,EAAE;QACpK,OAAO,EAAE;MACb,CAAC;MAAEhB,QAAQ,EAAE,uXAAuX;MAAEQ,MAAM,EAAE,CAAC,siBAAsiB;IAAE,CAAC;EACp8B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjE,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAE9J,EAAE,CAACmM,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAC/FR,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAEnM,EAAE,CAACyO;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEa,WAAW,EAAE,CAAC;MAC1EnD,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC7K,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMiP,eAAe,GAAG,IAAIjQ,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA,MAAMkQ,aAAa,CAAC;EAChBzG,WAAWA,CAAA,CAAC,2BAA4BuF,QAAQ,EAAE;IAC9C,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAAClE,IAAI,YAAAqF,sBAAAnF,CAAA;MAAA,YAAAA,CAAA,IAAwFkF,aAAa,EApLvB9Q,EAAE,CAAA6L,iBAAA,CAoLuC7L,EAAE,CAACa,WAAW;IAAA,CAA4C;EAAE;EACrM;IAAS,IAAI,CAACoL,IAAI,kBArL8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EAqLJ2E,aAAa;MAAA1E,SAAA;MAAAC,QAAA,GArLXrM,EAAE,CAAAgR,kBAAA,CAqLmD,CAAC;QAAEC,OAAO,EAAEJ,eAAe;QAAEK,WAAW,EAAEJ;MAAc,CAAC,CAAC;IAAA,EAAiB;EAAE;AACtO;AACA;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KAvLoGvM,EAAE,CAAAwM,iBAAA,CAuLXsE,aAAa,EAAc,CAAC;IAC3G3E,IAAI,EAAEjM,SAAS;IACfuM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3ByE,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEJ,eAAe;QAAEK,WAAW,EAAEJ;MAAc,CAAC;IACxE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE3E,IAAI,EAAEnM,EAAE,CAACa;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE9E;AACA;AACA;AACA;AACA;AACA,MAAMuQ,aAAa,GAAG,IAAIxQ,cAAc,CAAC,aAAa,CAAC;AACvD;AACA;AACA;AACA;AACA,MAAMyQ,OAAO,GAAG,IAAIzQ,cAAc,CAAC,SAAS,CAAC;AAC7C;AACA,MAAM0Q,WAAW,SAASzP,SAAS,CAAC;EAChCwI,WAAWA,CAACkH,WAAW,EAAEhH,gBAAgB,EAAEiH,WAAW,EAAE;IACpD,KAAK,CAACD,WAAW,EAAEhH,gBAAgB,CAAC;IACpC,IAAI,CAACiH,WAAW,GAAGA,WAAW;EAClC;EACA;IAAS,IAAI,CAAC9F,IAAI,YAAA+F,oBAAA7F,CAAA;MAAA,YAAAA,CAAA,IAAwF0F,WAAW,EAhNrBtR,EAAE,CAAA6L,iBAAA,CAgNqC7L,EAAE,CAACa,WAAW,GAhNrDb,EAAE,CAAA6L,iBAAA,CAgNgE7L,EAAE,CAAC+L,gBAAgB,GAhNrF/L,EAAE,CAAA6L,iBAAA,CAgNgGwF,OAAO;IAAA,CAA4D;EAAE;EACvQ;IAAS,IAAI,CAACpF,IAAI,kBAjN8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EAiNJmF,WAAW;MAAAlF,SAAA;MAAAC,QAAA,GAjNTrM,EAAE,CAAAgR,kBAAA,CAiNgE,CAAC;QAAEC,OAAO,EAAEG,aAAa;QAAEF,WAAW,EAAEI;MAAY,CAAC,CAAC,GAjNxHtR,EAAE,CAAAsM,0BAAA;IAAA,EAiN8J;EAAE;AACtQ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnNoGvM,EAAE,CAAAwM,iBAAA,CAmNX8E,WAAW,EAAc,CAAC;IACzGnF,IAAI,EAAEjM,SAAS;IACfuM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCAAgC;MAC1CyE,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEG,aAAa;QAAEF,WAAW,EAAEI;MAAY,CAAC;IACpE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEnF,IAAI,EAAEnM,EAAE,CAACa;IAAY,CAAC,EAAE;MAAEsL,IAAI,EAAEnM,EAAE,CAAC+L;IAAiB,CAAC,EAAE;MAAEI,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QACvHR,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAAC4E,OAAO;MAClB,CAAC,EAAE;QACClF,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,MAAMqR,YAAY,GAAG,2BAA2B;AAChD;AACA,MAAMC,mBAAmB,GAAG,kCAAkC;AAC9D;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZvH,WAAWA,CAACwH,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACD,MAAM,CAACE,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC;EACxD;EACA;EACAC,cAAcA,CAACC,OAAO,EAAE;IACpB,MAAMC,iBAAiB,GAAG,IAAI,CAACP,MAAM,CAACQ,IAAI,CAACL,IAAI,IAAIA,IAAI,CAACpD,UAAU,CAACT,aAAa,KAAKgE,OAAO,CAAC;IAC7F,MAAMG,WAAW,GAAG,IAAI,CAACC,YAAY;IACrC,IAAIH,iBAAiB,KAAKE,WAAW,EAAE;MACnC;IACJ;IACAA,WAAW,EAAEL,gBAAgB,CAAC,CAAC;IAC/B,IAAIG,iBAAiB,EAAE;MACnB,MAAMI,UAAU,GAAGF,WAAW,EAAE1D,UAAU,CAACT,aAAa,CAACsE,qBAAqB,GAAG,CAAC;MAClF;MACAL,iBAAiB,CAACM,cAAc,CAACF,UAAU,CAAC;MAC5C,IAAI,CAACD,YAAY,GAAGH,iBAAiB;IACzC;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,eAAeA,CAACC,IAAI,EAAE;EAC3B,OAAO,cAAcA,IAAI,CAAC;IACtBvI,WAAWA,CAAC,GAAGoC,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACoG,aAAa,GAAG,KAAK;IAC9B;IACA;IACA,IAAI3K,kBAAkBA,CAAA,EAAG;MACrB,OAAO,IAAI,CAAC2K,aAAa;IAC7B;IACA,IAAI3K,kBAAkBA,CAAC4K,CAAC,EAAE;MACtB,MAAMC,QAAQ,GAAGpP,qBAAqB,CAACmP,CAAC,CAAC;MACzC,IAAI,IAAI,CAACD,aAAa,KAAKE,QAAQ,EAAE;QACjC,IAAI,CAACF,aAAa,GAAGE,QAAQ;QAC7B,IAAI,IAAI,CAACC,cAAc,EAAE;UACrB,IAAI,CAACC,oBAAoB,CAAC,CAAC;QAC/B;MACJ;IACJ;IACA;IACAP,cAAcA,CAACQ,2BAA2B,EAAE;MACxC,MAAMf,OAAO,GAAG,IAAI,CAACvD,UAAU,CAACT,aAAa;MAC7C;MACA;MACA,IAAI,CAAC+E,2BAA2B,IAC5B,CAACf,OAAO,CAACM,qBAAqB,IAC9B,CAAC,IAAI,CAACU,qBAAqB,EAAE;QAC7BhB,OAAO,CAACiB,SAAS,CAACC,GAAG,CAAC3B,YAAY,CAAC;QACnC;MACJ;MACA;MACA;MACA;MACA,MAAM4B,iBAAiB,GAAGnB,OAAO,CAACM,qBAAqB,CAAC,CAAC;MACzD,MAAMc,UAAU,GAAGL,2BAA2B,CAACM,KAAK,GAAGF,iBAAiB,CAACE,KAAK;MAC9E,MAAMC,SAAS,GAAGP,2BAA2B,CAACQ,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;MAC3EvB,OAAO,CAACiB,SAAS,CAACC,GAAG,CAAC1B,mBAAmB,CAAC;MAC1C,IAAI,CAACwB,qBAAqB,CAAC3P,KAAK,CAACmQ,WAAW,CAAC,WAAW,EAAG,cAAaF,SAAU,cAAaF,UAAW,GAAE,CAAC;MAC7G;MACApB,OAAO,CAACM,qBAAqB,CAAC,CAAC;MAC/BN,OAAO,CAACiB,SAAS,CAACQ,MAAM,CAACjC,mBAAmB,CAAC;MAC7CQ,OAAO,CAACiB,SAAS,CAACC,GAAG,CAAC3B,YAAY,CAAC;MACnC,IAAI,CAACyB,qBAAqB,CAAC3P,KAAK,CAACmQ,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;IACjE;IACA;IACA1B,gBAAgBA,CAAA,EAAG;MACf,IAAI,CAACrD,UAAU,CAACT,aAAa,CAACiF,SAAS,CAACQ,MAAM,CAAClC,YAAY,CAAC;IAChE;IACA;IACA9G,QAAQA,CAAA,EAAG;MACP,IAAI,CAACiJ,oBAAoB,CAAC,CAAC;IAC/B;IACA;IACArI,WAAWA,CAAA,EAAG;MACV,IAAI,CAACwH,cAAc,EAAEY,MAAM,CAAC,CAAC;MAC7B,IAAI,CAACZ,cAAc,GAAG,IAAI,CAACG,qBAAqB,GAAG,IAAI;IAC3D;IACA;IACAU,oBAAoBA,CAAA,EAAG;MACnB,MAAMC,YAAY,GAAG,IAAI,CAAClF,UAAU,CAACT,aAAa,CAAC4F,aAAa,IAAIC,QAAQ;MAC5E,IAAI,CAAChB,cAAc,GAAGc,YAAY,CAACG,aAAa,CAAC,MAAM,CAAC;MACxD,IAAI,CAACd,qBAAqB,GAAGW,YAAY,CAACG,aAAa,CAAC,MAAM,CAAC;MAC/D,IAAI,CAACjB,cAAc,CAACkB,SAAS,GAAG,mBAAmB;MACnD,IAAI,CAACf,qBAAqB,CAACe,SAAS,GAChC,kEAAkE;MACtE,IAAI,CAAClB,cAAc,CAACmB,WAAW,CAAC,IAAI,CAAChB,qBAAqB,CAAC;MAC3D,IAAI,CAACF,oBAAoB,CAAC,CAAC;IAC/B;IACA;AACR;AACA;AACA;IACQA,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAAC,IAAI,CAACD,cAAc,KAAK,OAAOzG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACzE,MAAM6H,KAAK,CAAC,6DAA6D,CAAC;MAC9E;MACA,MAAMC,aAAa,GAAG,IAAI,CAACxB,aAAa,GAClC,IAAI,CAACjE,UAAU,CAACT,aAAa,CAACmG,aAAa,CAAC,mBAAmB,CAAC,GAChE,IAAI,CAAC1F,UAAU,CAACT,aAAa;MACnC,IAAI,CAACkG,aAAa,KAAK,OAAO9H,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACnE,MAAM6H,KAAK,CAAC,qCAAqC,CAAC;MACtD;MACAC,aAAa,CAACF,WAAW,CAAC,IAAI,CAACnB,cAAc,CAAC;IAClD;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,SAASuB,+BAA+BA,CAAA,EAAG;EACvC,MAAMC,MAAM,GAAIrC,OAAO,KAAM;IACzBuB,IAAI,EAAEvB,OAAO,GAAG,CAACA,OAAO,CAACsC,UAAU,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;IACtDjB,KAAK,EAAErB,OAAO,GAAG,CAACA,OAAO,CAACuC,WAAW,IAAI,CAAC,IAAI,IAAI,GAAG;EACzD,CAAC,CAAC;EACF,OAAOF,MAAM;AACjB;AACA;AACA,MAAMG,uBAAuB,GAAG,IAAI/T,cAAc,CAAC,qBAAqB,EAAE;EACtEgU,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEN;AACb,CAAC,CAAC;;AAEF;AACA;AACA,MAAMO,4BAA4B,GAAG1T,aAAa,CAAC,MAAM,EACxD,CAAC;AACF;AACA;AACA;AACA;AACA,MAAM2T,uBAAuB,SAASD,4BAA4B,CAAC;EAC/DzK,WAAWA,CAACuE,UAAU,EAAE;IACpB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA;EACAoG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACpG,UAAU,CAACT,aAAa,CAAC6G,KAAK,CAAC,CAAC;EACzC;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrG,UAAU,CAACT,aAAa,CAACsG,UAAU;EACnD;EACAS,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACtG,UAAU,CAACT,aAAa,CAACuG,WAAW;EACpD;EACA;IAAS,IAAI,CAAChJ,IAAI,YAAAyJ,gCAAAvJ,CAAA;MAAA,YAAAA,CAAA,IAAwFmJ,uBAAuB,EApYjC/U,EAAE,CAAA6L,iBAAA,CAoYiD7L,EAAE,CAACuO,UAAU;IAAA,CAA4C;EAAE;EAC9M;IAAS,IAAI,CAACtC,IAAI,kBArY8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EAqYJ4I,uBAAuB;MAAA1I,QAAA,GArYrBrM,EAAE,CAAAsM,0BAAA;IAAA,EAqY2D;EAAE;AACnK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvYoGvM,EAAE,CAAAwM,iBAAA,CAuYXuI,uBAAuB,EAAc,CAAC;IACrH5I,IAAI,EAAEjM;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiM,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7E,MAAM6G,qCAAqC,GAAGzC,eAAe,CAACoC,uBAAuB,CAAC;AACtF;AACA;AACA;AACA;AACA,MAAMM,kBAAkB,SAASD,qCAAqC,CAAC;EACnE;IAAS,IAAI,CAAC1J,IAAI;MAAA,IAAA4J,+BAAA;MAAA,gBAAAC,2BAAA3J,CAAA;QAAA,QAAA0J,+BAAA,KAAAA,+BAAA,GAhZ8EtV,EAAE,CAAAwV,qBAAA,CAgZQH,kBAAkB,IAAAzJ,CAAA,IAAlByJ,kBAAkB;MAAA;IAAA,GAAqD;EAAE;EACnL;IAAS,IAAI,CAACpJ,IAAI,kBAjZ8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EAiZJkJ,kBAAkB;MAAAjJ,SAAA;MAAAqJ,QAAA;MAAAC,YAAA,WAAAC,gCAAAtR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjZhBrE,EAAE,CAAAmI,WAAA,oBAAA7D,GAAA,CAAA2D,QAAA;UAAFjI,EAAE,CAAA6H,WAAA,yBAAAvD,GAAA,CAAA2D,QAAA;QAAA;MAAA;MAAAyG,MAAA;QAAAzG,QAAA;QAAAC,kBAAA;MAAA;MAAAmE,QAAA,GAAFrM,EAAE,CAAAsM,0BAAA;IAAA,EAiZ4Q;EAAE;AACpX;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnZoGvM,EAAE,CAAAwM,iBAAA,CAmZX6I,kBAAkB,EAAc,CAAC;IAChHlJ,IAAI,EAAEjM,SAAS;IACfuM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCgC,MAAM,EAAE,CAAC,UAAU,EAAE,oBAAoB,CAAC;MAC1CkC,IAAI,EAAE;QACF,8BAA8B,EAAE,UAAU;QAC1C,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA,MAAMgF,gBAAgB,GAAGxU,aAAa,CAAC,MAAM,EAC5C,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMyU,aAAa,GAAG,IAAIjV,cAAc,CAAC,eAAe,CAAC;AACzD;AACA,MAAMkV,WAAW,SAASF,gBAAgB,CAAC;EACvC;EACA,IAAIpM,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACuM,cAAc;EAC9B;EACA1L,WAAWA,CAAC2L,iBAAiB,EAAEC,gBAAgB,EAAE;IAC7C,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC;IACA,IAAI,CAAC9P,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAAC4P,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACG,aAAa,GAAG,IAAI3T,OAAO,CAAC,CAAC;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAACkH,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB;AACR;AACA;IACQ,IAAI,CAACyM,QAAQ,GAAG,KAAK;EACzB;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,cAAc,CAAC,WAAW,CAAC,IAAID,OAAO,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;MAC3E,IAAI,CAACJ,aAAa,CAAClG,IAAI,CAAC,CAAC;IAC7B;EACJ;EACAxE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0K,aAAa,CAACjI,QAAQ,CAAC,CAAC;EACjC;EACArD,QAAQA,CAAA,EAAG;IACP,IAAI,CAACmL,cAAc,GAAG,IAAIjU,cAAc,CAAC,IAAI,CAACyU,gBAAgB,IAAI,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACR,iBAAiB,CAAC;EACpH;EACA;AACJ;AACA;AACA;AACA;AACA;EACIS,sBAAsBA,CAAC7R,KAAK,EAAE;IAC1B;IACA;IACA;IACA;IACA,IAAIA,KAAK,IAAIA,KAAK,CAAC4M,WAAW,KAAK,IAAI,EAAE;MACrC,IAAI,CAACkF,cAAc,GAAG9R,KAAK;IAC/B;EACJ;EACA;IAAS,IAAI,CAAC8G,IAAI,YAAAiL,oBAAA/K,CAAA;MAAA,YAAAA,CAAA,IAAwFkK,WAAW,EAjerB9V,EAAE,CAAA6L,iBAAA,CAieqC7L,EAAE,CAAC+L,gBAAgB,GAje1D/L,EAAE,CAAA6L,iBAAA,CAieqEgK,aAAa;IAAA,CAA4D;EAAE;EAClP;IAAS,IAAI,CAAC5J,IAAI,kBAle8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EAkeJ2J,WAAW;MAAA9G,SAAA,WAAA4H,kBAAAvS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAleTrE,EAAE,CAAAkP,WAAA,CAkemRrO,WAAW;QAAA;QAAA,IAAAwD,EAAA;UAAA,IAAA8K,EAAA;UAlehSnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAkS,gBAAA,GAAArH,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAb,MAAA;QAAAvI,SAAA;QAAAqC,SAAA;QAAAC,cAAA;QAAAT,UAAA;QAAAuB,SAAA;MAAA;MAAA8C,QAAA,GAAFrM,EAAE,CAAAsM,0BAAA,EAAFtM,EAAE,CAAA6W,oBAAA;IAAA,EAke+X;EAAE;AACve;AACA;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KApeoGvM,EAAE,CAAAwM,iBAAA,CAoeXsJ,WAAW,EAAc,CAAC;IACzG3J,IAAI,EAAEjM;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiM,IAAI,EAAEnM,EAAE,CAAC+L;IAAiB,CAAC,EAAE;MAAEI,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAC7FR,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACoJ,aAAa;MACxB,CAAC,EAAE;QACC1J,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEmW,gBAAgB,EAAE,CAAC;MAC/CrK,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC5L,WAAW,EAAE;QAAEiW,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAE3Q,SAAS,EAAE,CAAC;MACZgG,IAAI,EAAE5L,KAAK;MACXkM,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEjE,SAAS,EAAE,CAAC;MACZ2D,IAAI,EAAE5L,KAAK;MACXkM,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEhE,cAAc,EAAE,CAAC;MACjB0D,IAAI,EAAE5L,KAAK;MACXkM,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEzE,UAAU,EAAE,CAAC;MACbmE,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEgJ,SAAS,EAAE,CAAC;MACZ4C,IAAI,EAAE5L;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMwW,MAAM,SAASjB,WAAW,CAAC;EAC7BzL,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG2M,SAAS,CAAC;IACnB;AACR;AACA;IACQ,IAAI,CAACT,gBAAgB,GAAG3J,SAAS;EACrC;EACA;EACA,IAAI7G,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC2Q,cAAc;EAC9B;EACA,IAAI3Q,aAAaA,CAACnB,KAAK,EAAE;IACrB,IAAI,CAAC6R,sBAAsB,CAAC7R,KAAK,CAAC;EACtC;EACA;IAAS,IAAI,CAAC8G,IAAI;MAAA,IAAAuL,mBAAA;MAAA,gBAAAC,eAAAtL,CAAA;QAAA,QAAAqL,mBAAA,KAAAA,mBAAA,GA3gB8EjX,EAAE,CAAAwV,qBAAA,CA2gBQuB,MAAM,IAAAnL,CAAA,IAANmL,MAAM;MAAA;IAAA,GAAqD;EAAE;EACvK;IAAS,IAAI,CAACjI,IAAI,kBA5gB8E9O,EAAE,CAAA+O,iBAAA;MAAA5C,IAAA,EA4gBJ4K,MAAM;MAAA3K,SAAA;MAAA+K,cAAA,WAAAC,sBAAA/S,EAAA,EAAAC,GAAA,EAAA+S,QAAA;QAAA,IAAAhT,EAAA;UA5gBJrE,EAAE,CAAAsX,cAAA,CAAAD,QAAA,EA4gByLvG,aAAa,KAA2BjQ,WAAW;UA5gB9Ob,EAAE,CAAAsX,cAAA,CAAAD,QAAA,EA4gBuT/F,WAAW;QAAA;QAAA,IAAAjN,EAAA;UAAA,IAAA8K,EAAA;UA5gBpUnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAiS,gBAAA,GAAApH,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAyB,aAAA,GAAAoJ,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAb,MAAA;QAAAzG,QAAA;MAAA;MAAAsP,QAAA;MAAAlL,QAAA,GAAFrM,EAAE,CAAAgR,kBAAA,CA4gBsE,CAAC;QAAEC,OAAO,EAAEI,OAAO;QAAEH,WAAW,EAAE6F;MAAO,CAAC,CAAC,GA5gBnH/W,EAAE,CAAAsM,0BAAA;MAAAkL,kBAAA,EAAAxS,GAAA;MAAAyK,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAA6H,gBAAApT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAA0X,eAAA;UAAF1X,EAAE,CAAA0F,UAAA,IAAAZ,6BAAA,qBA4gB0qB,CAAC;QAAA;MAAA;MAAAuL,aAAA;IAAA,EAAqG;EAAE;AACx3B;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KA9gBoGvM,EAAE,CAAAwM,iBAAA,CA8gBXuK,MAAM,EAAc,CAAC;IACpG5K,IAAI,EAAE3L,SAAS;IACfiM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAEgC,MAAM,EAAE,CAAC,UAAU,CAAC;MAAE+B,eAAe,EAAE/P,uBAAuB,CAACgQ,OAAO;MAAEL,aAAa,EAAE5P,iBAAiB,CAAC+P,IAAI;MAAE+G,QAAQ,EAAE,QAAQ;MAAEpG,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEI,OAAO;QAAEH,WAAW,EAAE6F;MAAO,CAAC,CAAC;MAAEnH,QAAQ,EAAE;IAAgR,CAAC;EACxf,CAAC,CAAC,QAAkB;IAAE2G,gBAAgB,EAAE,CAAC;MACjCpK,IAAI,EAAErL,YAAY;MAClB2L,IAAI,EAAE,CAACqE,aAAa,EAAE;QAAE6G,IAAI,EAAE9W,WAAW;QAAEiW,MAAM,EAAE;MAAK,CAAC;IAC7D,CAAC,CAAC;IAAE/Q,aAAa,EAAE,CAAC;MAChBoG,IAAI,EAAErL,YAAY;MAClB2L,IAAI,EAAE,CAAC6E,WAAW;IACtB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMsG,2BAA2B,GAAG7T,+BAA+B,CAAC;EAChE8T,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAG;AAC/B;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,GAAG;AAClC;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxB;AACJ;AACA;AACA;EACI,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,iBAAiBA,CAACrT,KAAK,EAAE;IACzB,IAAI,CAACsT,kBAAkB,GAAGvU,qBAAqB,CAACiB,KAAK,CAAC;EAC1D;EACA;EACA,IAAIkD,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACqQ,cAAc;EAC9B;EACA,IAAIrQ,aAAaA,CAAClD,KAAK,EAAE;IACrBA,KAAK,GAAGhB,oBAAoB,CAACgB,KAAK,CAAC;IACnC,IAAI,IAAI,CAACuT,cAAc,IAAIvT,KAAK,EAAE;MAC9B,IAAI,CAACwT,qBAAqB,GAAG,IAAI;MACjC,IAAI,CAACD,cAAc,GAAGvT,KAAK;MAC3B,IAAI,IAAI,CAACyT,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACC,gBAAgB,CAAC1T,KAAK,CAAC;MAC5C;IACJ;EACJ;EACAyF,WAAWA,CAAC2C,WAAW,EAAEuL,kBAAkB,EAAEC,cAAc,EAAEvL,IAAI,EAAEwL,OAAO,EAAEC,SAAS,EAAEC,cAAc,EAAE;IACnG,IAAI,CAAC3L,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACuL,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACvL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACwL,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;IACA,IAAI,CAACR,qBAAqB,GAAG,KAAK;IAClC;IACA,IAAI,CAACS,UAAU,GAAG,IAAItW,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACuW,uBAAuB,GAAG,KAAK;IACpC;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,cAAc,GAAG,IAAI1W,OAAO,CAAC,CAAC;IACnC,IAAI,CAAC2V,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACe,kBAAkB,GAAG,IAAI9Y,YAAY,CAAC,CAAC;IAC5C;IACA,IAAI,CAAC+Y,YAAY,GAAG,IAAI/Y,YAAY,CAAC,CAAC;IACtC;IACAqY,OAAO,CAACW,iBAAiB,CAAC,MAAM;MAC5B5W,SAAS,CAACwK,WAAW,CAACmB,aAAa,EAAE,YAAY,CAAC,CAC7CrD,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC4V,UAAU,CAAC,CAAC,CAChC5N,SAAS,CAAC,MAAM;QACjB,IAAI,CAACoO,aAAa,CAAC,CAAC;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd;IACA9W,SAAS,CAAC,IAAI,CAAC+W,kBAAkB,CAACpL,aAAa,EAAE,YAAY,EAAEyJ,2BAA2B,CAAC,CACtF9M,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC4V,UAAU,CAAC,CAAC,CAChC5N,SAAS,CAAC,MAAM;MACjB,IAAI,CAACuO,qBAAqB,CAAC,QAAQ,CAAC;IACxC,CAAC,CAAC;IACFhX,SAAS,CAAC,IAAI,CAACiX,cAAc,CAACtL,aAAa,EAAE,YAAY,EAAEyJ,2BAA2B,CAAC,CAClF9M,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC4V,UAAU,CAAC,CAAC,CAChC5N,SAAS,CAAC,MAAM;MACjB,IAAI,CAACuO,qBAAqB,CAAC,OAAO,CAAC;IACvC,CAAC,CAAC;EACN;EACAE,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAG,IAAI,CAAC1M,IAAI,GAAG,IAAI,CAACA,IAAI,CAACM,MAAM,GAAG9K,EAAE,CAAC,KAAK,CAAC;IAC1D,MAAMmX,MAAM,GAAG,IAAI,CAACpB,cAAc,CAACjL,MAAM,CAAC,GAAG,CAAC;IAC9C,MAAMsM,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpC,CAAC;IACD,IAAI,CAAC1B,WAAW,GAAG,IAAIlW,eAAe,CAAC,IAAI,CAAC0P,MAAM,CAAC,CAC9CmI,yBAAyB,CAAC,IAAI,CAAC3L,mBAAmB,CAAC,CAAC,CAAC,CACrD4L,cAAc,CAAC,CAAC,CAChBC,QAAQ,CAAC;IACV;IAAA,CACCC,aAAa,CAAC,MAAM,KAAK,CAAC;IAC/B,IAAI,CAAC9B,WAAW,CAACC,gBAAgB,CAAC,IAAI,CAACH,cAAc,CAAC;IACtD;IACA;IACA;IACA;IACA,IAAI,CAACM,OAAO,CAAC2B,QAAQ,CAACtP,IAAI,CAAC5H,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC+H,SAAS,CAAC4O,OAAO,CAAC;IACtD;IACA;IACAnX,KAAK,CAACiX,SAAS,EAAEC,MAAM,EAAE,IAAI,CAAC/H,MAAM,CAACwE,OAAO,EAAE,IAAI,CAACgE,aAAa,CAAC,CAAC,CAAC,CAC9DvP,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC4V,UAAU,CAAC,CAAC,CAChC5N,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACwN,OAAO,CAAC6B,GAAG,CAAC,MAAM;QACnBC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB;UACA,IAAI,CAAC7B,eAAe,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACjC,eAAe,CAAC,CAAC;UAChGiB,OAAO,CAAC,CAAC;QACb,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACxB,WAAW,CAAC2B,yBAAyB,CAAC,IAAI,CAAC3L,mBAAmB,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAACgK,WAAW,CAAC9K,MAAM,CAACtC,SAAS,CAAC6P,aAAa,IAAI;MAC/C,IAAI,CAAC3B,YAAY,CAACpL,IAAI,CAAC+M,aAAa,CAAC;MACrC,IAAI,CAACC,YAAY,CAACD,aAAa,CAAC;IACpC,CAAC,CAAC;EACN;EACA;EACAT,aAAaA,CAAA,EAAG;IACZ,IAAI,OAAOW,cAAc,KAAK,UAAU,EAAE;MACtC,OAAOrY,KAAK;IAChB;IACA,OAAO,IAAI,CAACkP,MAAM,CAACwE,OAAO,CAACvL,IAAI,CAAC/H,SAAS,CAAC,IAAI,CAAC8O,MAAM,CAAC,EAAE1O,SAAS,CAAE8X,QAAQ,IAAK,IAAIrY,UAAU,CAAEsY,QAAQ,IAAK,IAAI,CAACzC,OAAO,CAACW,iBAAiB,CAAC,MAAM;MAC9I,MAAM+B,cAAc,GAAG,IAAIH,cAAc,CAACI,OAAO,IAAIF,QAAQ,CAAClL,IAAI,CAACoL,OAAO,CAAC,CAAC;MAC5EH,QAAQ,CAAClJ,OAAO,CAACC,IAAI,IAAImJ,cAAc,CAACE,OAAO,CAACrJ,IAAI,CAACpD,UAAU,CAACT,aAAa,CAAC,CAAC;MAC/E,OAAO,MAAM;QACTgN,cAAc,CAACG,UAAU,CAAC,CAAC;MAC/B,CAAC;IACL,CAAC,CAAC,CAAC,CAAC;IACJ;IACA;IACAlY,IAAI,CAAC,CAAC,CAAC;IACP;IACA;IACAC,MAAM,CAAC+X,OAAO,IAAIA,OAAO,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAACjI,KAAK,GAAG,CAAC,IAAIgI,CAAC,CAACC,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9F;EACAC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACC,cAAc,IAAI,IAAI,CAAC/J,MAAM,CAACvJ,MAAM,EAAE;MAC3C,IAAI,CAACwR,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAAC8B,cAAc,GAAG,IAAI,CAAC/J,MAAM,CAACvJ,MAAM;MACxC,IAAI,CAACiQ,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAAC2K,qBAAqB,EAAE;MAC5B,IAAI,CAACyD,cAAc,CAAC,IAAI,CAAC1D,cAAc,CAAC;MACxC,IAAI,CAAC2D,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAAC/B,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAAC3B,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACG,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAACsO,sBAAsB,EAAE;MAC7B,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACD,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACxD,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAjC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6M,WAAW,EAAE4D,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACpD,UAAU,CAAC7I,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC6I,UAAU,CAAC5K,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACgL,cAAc,CAAChL,QAAQ,CAAC,CAAC;EAClC;EACA;EACAiO,cAAcA,CAACpO,KAAK,EAAE;IAClB;IACA,IAAI7J,cAAc,CAAC6J,KAAK,CAAC,EAAE;MACvB;IACJ;IACA,QAAQA,KAAK,CAACqO,OAAO;MACjB,KAAKhY,KAAK;MACV,KAAKD,KAAK;QACN,IAAI,IAAI,CAACkY,UAAU,KAAK,IAAI,CAACtU,aAAa,EAAE;UACxC,MAAMkK,IAAI,GAAG,IAAI,CAACH,MAAM,CAACwK,GAAG,CAAC,IAAI,CAACD,UAAU,CAAC;UAC7C,IAAIpK,IAAI,IAAI,CAACA,IAAI,CAAC/J,QAAQ,EAAE;YACxB,IAAI,CAACiR,kBAAkB,CAACnL,IAAI,CAAC,IAAI,CAACqO,UAAU,CAAC;YAC7C,IAAI,CAACE,aAAa,CAACxO,KAAK,CAAC;UAC7B;QACJ;QACA;MACJ;QACI,IAAI,CAACuK,WAAW,CAACkE,SAAS,CAACzO,KAAK,CAAC;IACzC;EACJ;EACA;AACJ;AACA;EACI0O,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,WAAW,GAAG,IAAI,CAACzP,WAAW,CAACmB,aAAa,CAACsO,WAAW;IAC9D;IACA;IACA;IACA,IAAIA,WAAW,KAAK,IAAI,CAACC,mBAAmB,EAAE;MAC1C,IAAI,CAACA,mBAAmB,GAAGD,WAAW,IAAI,EAAE;MAC5C;MACA;MACA,IAAI,CAAChE,OAAO,CAAC6B,GAAG,CAAC,MAAM;QACnB,IAAI,CAACR,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;QAChC,IAAI,CAACxB,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqM,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC6C,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACb,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACE,wBAAwB,CAAC,CAAC;EACnC;EACA;EACA,IAAII,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC/D,WAAW,GAAG,IAAI,CAACA,WAAW,CAACuE,eAAe,GAAG,CAAC;EAClE;EACA;EACA,IAAIR,UAAUA,CAACxX,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACiY,aAAa,CAACjY,KAAK,CAAC,IAAI,IAAI,CAACwX,UAAU,KAAKxX,KAAK,IAAI,CAAC,IAAI,CAACyT,WAAW,EAAE;MAC9E;IACJ;IACA,IAAI,CAACA,WAAW,CAACyE,aAAa,CAAClY,KAAK,CAAC;EACzC;EACA;AACJ;AACA;AACA;EACIiY,aAAaA,CAAChW,KAAK,EAAE;IACjB,OAAO,IAAI,CAACgL,MAAM,GAAG,CAAC,CAAC,IAAI,CAACA,MAAM,CAACkL,OAAO,CAAC,CAAC,CAAClW,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIkU,YAAYA,CAACiC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAAClE,uBAAuB,EAAE;MAC9B,IAAI,CAAC+C,cAAc,CAACmB,QAAQ,CAAC;IACjC;IACA,IAAI,IAAI,CAACnL,MAAM,IAAI,IAAI,CAACA,MAAM,CAACvJ,MAAM,EAAE;MACnC,IAAI,CAACuJ,MAAM,CAACkL,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAChI,KAAK,CAAC,CAAC;MACvC;MACA;MACA;MACA,MAAMiI,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC/O,aAAa;MACxD,MAAMX,GAAG,GAAG,IAAI,CAACa,mBAAmB,CAAC,CAAC;MACtC,IAAIb,GAAG,IAAI,KAAK,EAAE;QACdyP,WAAW,CAACE,UAAU,GAAG,CAAC;MAC9B,CAAC,MACI;QACDF,WAAW,CAACE,UAAU,GAAGF,WAAW,CAACG,WAAW,GAAGH,WAAW,CAACvI,WAAW;MAC9E;IACJ;EACJ;EACA;EACArG,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACpB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACrI,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAoX,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC/D,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAMoF,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,MAAMC,UAAU,GAAG,IAAI,CAACjP,mBAAmB,CAAC,CAAC,KAAK,KAAK,GAAG,CAACgP,cAAc,GAAGA,cAAc;IAC1F;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACE,QAAQ,CAACpP,aAAa,CAAC3K,KAAK,CAACyG,SAAS,GAAI,cAAayQ,IAAI,CAAC8C,KAAK,CAACF,UAAU,CAAE,KAAI;IACvF;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC5E,SAAS,CAAC+E,OAAO,IAAI,IAAI,CAAC/E,SAAS,CAACgF,IAAI,EAAE;MAC/C,IAAI,CAACR,iBAAiB,CAAC/O,aAAa,CAACgP,UAAU,GAAG,CAAC;IACvD;EACJ;EACA;EACA,IAAIE,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACzE,eAAe;EAC/B;EACA,IAAIyE,cAAcA,CAACzY,KAAK,EAAE;IACtB,IAAI,CAAC+Y,SAAS,CAAC/Y,KAAK,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIgZ,aAAaA,CAACC,SAAS,EAAE;IACrB,MAAMC,UAAU,GAAG,IAAI,CAACZ,iBAAiB,CAAC/O,aAAa,CAACuG,WAAW;IACnE;IACA,MAAMqJ,YAAY,GAAI,CAACF,SAAS,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIC,UAAU,GAAI,CAAC;IACxE,OAAO,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC/E,eAAe,GAAGmF,YAAY,CAAC;EAC9D;EACA;EACAC,qBAAqBA,CAACH,SAAS,EAAE;IAC7B,IAAI,CAACxE,aAAa,CAAC,CAAC;IACpB,IAAI,CAACuE,aAAa,CAACC,SAAS,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIhC,cAAcA,CAACoC,UAAU,EAAE;IACvB,IAAI,IAAI,CAAChG,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAMiG,aAAa,GAAG,IAAI,CAACrM,MAAM,GAAG,IAAI,CAACA,MAAM,CAACkL,OAAO,CAAC,CAAC,CAACkB,UAAU,CAAC,GAAG,IAAI;IAC5E,IAAI,CAACC,aAAa,EAAE;MAChB;IACJ;IACA;IACA,MAAMJ,UAAU,GAAG,IAAI,CAACZ,iBAAiB,CAAC/O,aAAa,CAACuG,WAAW;IACnE,MAAM;MAAED,UAAU;MAAEC;IAAY,CAAC,GAAGwJ,aAAa,CAACtP,UAAU,CAACT,aAAa;IAC1E,IAAIgQ,cAAc,EAAEC,aAAa;IACjC,IAAI,IAAI,CAAC/P,mBAAmB,CAAC,CAAC,IAAI,KAAK,EAAE;MACrC8P,cAAc,GAAG1J,UAAU;MAC3B2J,aAAa,GAAGD,cAAc,GAAGzJ,WAAW;IAChD,CAAC,MACI;MACD0J,aAAa,GAAG,IAAI,CAACC,aAAa,CAAClQ,aAAa,CAACuG,WAAW,GAAGD,UAAU;MACzE0J,cAAc,GAAGC,aAAa,GAAG1J,WAAW;IAChD;IACA,MAAM4J,gBAAgB,GAAG,IAAI,CAACjB,cAAc;IAC5C,MAAMkB,eAAe,GAAG,IAAI,CAAClB,cAAc,GAAGS,UAAU;IACxD,IAAIK,cAAc,GAAGG,gBAAgB,EAAE;MACnC;MACA,IAAI,CAACjB,cAAc,IAAIiB,gBAAgB,GAAGH,cAAc;IAC5D,CAAC,MACI,IAAIC,aAAa,GAAGG,eAAe,EAAE;MACtC;MACA,IAAI,CAAClB,cAAc,IAAI3C,IAAI,CAACE,GAAG,CAACwD,aAAa,GAAGG,eAAe,EAAEJ,cAAc,GAAGG,gBAAgB,CAAC;IACvG;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI3B,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC1E,iBAAiB,EAAE;MACxB,IAAI,CAACa,uBAAuB,GAAG,KAAK;IACxC,CAAC,MACI;MACD,MAAM0F,SAAS,GAAG,IAAI,CAACH,aAAa,CAAClQ,aAAa,CAACiP,WAAW,GAAG,IAAI,CAACpQ,WAAW,CAACmB,aAAa,CAACuG,WAAW;MAC3G,IAAI,CAAC8J,SAAS,EAAE;QACZ,IAAI,CAACnB,cAAc,GAAG,CAAC;MAC3B;MACA,IAAImB,SAAS,KAAK,IAAI,CAAC1F,uBAAuB,EAAE;QAC5C,IAAI,CAACP,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;MAC1C;MACA,IAAI,CAACqL,uBAAuB,GAAG0F,SAAS;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI1C,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC7D,iBAAiB,EAAE;MACxB,IAAI,CAACc,mBAAmB,GAAG,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAC/D,CAAC,MACI;MACD;MACA,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAACqE,cAAc,IAAI,CAAC;MACpD,IAAI,CAACtE,mBAAmB,GAAG,IAAI,CAACsE,cAAc,IAAI,IAAI,CAACxC,qBAAqB,CAAC,CAAC;MAC9E,IAAI,CAACtC,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIoN,qBAAqBA,CAAA,EAAG;IACpB,MAAM4D,eAAe,GAAG,IAAI,CAACJ,aAAa,CAAClQ,aAAa,CAACiP,WAAW;IACpE,MAAMU,UAAU,GAAG,IAAI,CAACZ,iBAAiB,CAAC/O,aAAa,CAACuG,WAAW;IACnE,OAAO+J,eAAe,GAAGX,UAAU,IAAI,CAAC;EAC5C;EACA;EACA/D,yBAAyBA,CAAA,EAAG;IACxB,MAAM2E,YAAY,GAAG,IAAI,CAAC7M,MAAM,IAAI,IAAI,CAACA,MAAM,CAACvJ,MAAM,GAAG,IAAI,CAACuJ,MAAM,CAACkL,OAAO,CAAC,CAAC,CAAC,IAAI,CAACjV,aAAa,CAAC,GAAG,IAAI;IACzG,MAAM6W,oBAAoB,GAAGD,YAAY,GAAGA,YAAY,CAAC9P,UAAU,CAACT,aAAa,GAAG,IAAI;IACxF,IAAIwQ,oBAAoB,EAAE;MACtB,IAAI,CAACC,OAAO,CAAC1M,cAAc,CAACyM,oBAAoB,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACC,OAAO,CAAC9M,IAAI,CAAC,CAAC;IACvB;EACJ;EACA;EACAuH,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACJ,cAAc,CAACjJ,IAAI,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIwJ,qBAAqBA,CAACqE,SAAS,EAAEgB,UAAU,EAAE;IACzC;IACA;IACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAM,IAAI,IAAI,IAAID,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACpE;IACJ;IACA;IACA,IAAI,CAACzF,aAAa,CAAC,CAAC;IACpB;IACAxW,KAAK,CAACiV,mBAAmB,EAAEC,sBAAsB;IAC7C;IAAA,CACCjN,IAAI,CAAC7H,SAAS,CAACP,KAAK,CAAC,IAAI,CAACuW,cAAc,EAAE,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC,CAC5D5N,SAAS,CAAC,MAAM;MACjB,MAAM;QAAE8T,iBAAiB;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACpB,aAAa,CAACC,SAAS,CAAC;MACrE;MACA,IAAImB,QAAQ,KAAK,CAAC,IAAIA,QAAQ,IAAID,iBAAiB,EAAE;QACjD,IAAI,CAAC1F,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIsE,SAASA,CAAClU,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACwO,iBAAiB,EAAE;MACxB,OAAO;QAAE8G,iBAAiB,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAE,CAAC;IAChD;IACA,MAAMD,iBAAiB,GAAG,IAAI,CAAClE,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACjC,eAAe,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACmE,iBAAiB,EAAEtV,QAAQ,CAAC,CAAC;IACzE;IACA;IACA,IAAI,CAACsS,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACD,uBAAuB,CAAC,CAAC;IAC9B,OAAO;MAAEiD,iBAAiB;MAAEC,QAAQ,EAAE,IAAI,CAACpG;IAAgB,CAAC;EAChE;EACA;IAAS,IAAI,CAAClN,IAAI,YAAAuT,8BAAArT,CAAA;MAAA,YAAAA,CAAA,IAAwFoM,qBAAqB,EAz/B/BhY,EAAE,CAAA6L,iBAAA,CAy/B+C7L,EAAE,CAACuO,UAAU,GAz/B9DvO,EAAE,CAAA6L,iBAAA,CAy/ByE7L,EAAE,CAACyO,iBAAiB,GAz/B/FzO,EAAE,CAAA6L,iBAAA,CAy/B0GhI,IAAI,CAACqb,aAAa,GAz/B9Hlf,EAAE,CAAA6L,iBAAA,CAy/ByIxJ,EAAE,CAACmM,cAAc,MAz/B5JxO,EAAE,CAAA6L,iBAAA,CAy/BuL7L,EAAE,CAACmf,MAAM,GAz/BlMnf,EAAE,CAAA6L,iBAAA,CAy/B6M/H,EAAE,CAACsb,QAAQ,GAz/B1Npf,EAAE,CAAA6L,iBAAA,CAy/BqO7H,qBAAqB;IAAA,CAA4D;EAAE;EAC1Z;IAAS,IAAI,CAACiI,IAAI,kBA1/B8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EA0/BJ6L,qBAAqB;MAAAtJ,MAAA;QAAAuJ,iBAAA;MAAA;IAAA,EAAqE;EAAE;AAC9L;AACA;EAAA,QAAA1L,SAAA,oBAAAA,SAAA,KA5/BoGvM,EAAE,CAAAwM,iBAAA,CA4/BXwL,qBAAqB,EAAc,CAAC;IACnH7L,IAAI,EAAEjM;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiM,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAEnM,EAAE,CAACyO;IAAkB,CAAC,EAAE;MAAEtC,IAAI,EAAEtI,IAAI,CAACqb;IAAc,CAAC,EAAE;MAAE/S,IAAI,EAAE9J,EAAE,CAACmM,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAC7JR,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAEnM,EAAE,CAACmf;IAAO,CAAC,EAAE;MAAEhT,IAAI,EAAErI,EAAE,CAACsb;IAAS,CAAC,EAAE;MAAEjT,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAC9ER,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACzI,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEiU,iBAAiB,EAAE,CAAC;MAChD9L,IAAI,EAAE5L;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM8e,iBAAiB,SAASrH,qBAAqB,CAAC;EAClD;EACA,IAAIrP,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC2W,cAAc;EAC9B;EACA,IAAI3W,aAAaA,CAAC/D,KAAK,EAAE;IACrB,IAAI,CAAC0a,cAAc,GAAG3b,qBAAqB,CAACiB,KAAK,CAAC;EACtD;EACAyF,WAAWA,CAACuE,UAAU,EAAE1B,iBAAiB,EAAEqS,aAAa,EAAE/R,GAAG,EAAEgS,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC5F,KAAK,CAAC9Q,UAAU,EAAE1B,iBAAiB,EAAEqS,aAAa,EAAE/R,GAAG,EAAEgS,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzF,IAAI,CAACJ,cAAc,GAAG,KAAK;EAC/B;EACAhD,aAAaA,CAACxO,KAAK,EAAE;IACjBA,KAAK,CAAC6R,cAAc,CAAC,CAAC;EAC1B;EACA;IAAS,IAAI,CAACjU,IAAI,YAAAkU,0BAAAhU,CAAA;MAAA,YAAAA,CAAA,IAAwFyT,iBAAiB,EA5hC3Brf,EAAE,CAAA6L,iBAAA,CA4hC2C7L,EAAE,CAACuO,UAAU,GA5hC1DvO,EAAE,CAAA6L,iBAAA,CA4hCqE7L,EAAE,CAACyO,iBAAiB,GA5hC3FzO,EAAE,CAAA6L,iBAAA,CA4hCsGhI,IAAI,CAACqb,aAAa,GA5hC1Hlf,EAAE,CAAA6L,iBAAA,CA4hCqIxJ,EAAE,CAACmM,cAAc,MA5hCxJxO,EAAE,CAAA6L,iBAAA,CA4hCmL7L,EAAE,CAACmf,MAAM,GA5hC9Lnf,EAAE,CAAA6L,iBAAA,CA4hCyM/H,EAAE,CAACsb,QAAQ,GA5hCtNpf,EAAE,CAAA6L,iBAAA,CA4hCiO7H,qBAAqB;IAAA,CAA4D;EAAE;EACtZ;IAAS,IAAI,CAACiI,IAAI,kBA7hC8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EA6hCJkT,iBAAiB;MAAA3Q,MAAA;QAAA/F,aAAA;MAAA;MAAA0D,QAAA,GA7hCfrM,EAAE,CAAAsM,0BAAA;IAAA,EA6hCiG;EAAE;AACzM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/hCoGvM,EAAE,CAAAwM,iBAAA,CA+hCX6S,iBAAiB,EAAc,CAAC;IAC/GlT,IAAI,EAAEjM;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiM,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAEnM,EAAE,CAACyO;IAAkB,CAAC,EAAE;MAAEtC,IAAI,EAAEtI,IAAI,CAACqb;IAAc,CAAC,EAAE;MAAE/S,IAAI,EAAE9J,EAAE,CAACmM,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAC7JR,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAEnM,EAAE,CAACmf;IAAO,CAAC,EAAE;MAAEhT,IAAI,EAAErI,EAAE,CAACsb;IAAS,CAAC,EAAE;MAAEjT,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAC9ER,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACzI,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE2E,aAAa,EAAE,CAAC;MAC5CwD,IAAI,EAAE5L;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsf,YAAY,SAASR,iBAAiB,CAAC;EACzChV,WAAWA,CAACuE,UAAU,EAAE1B,iBAAiB,EAAEqS,aAAa,EAAE/R,GAAG,EAAEgS,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC5F,KAAK,CAAC9Q,UAAU,EAAE1B,iBAAiB,EAAEqS,aAAa,EAAE/R,GAAG,EAAEgS,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;EAC7F;EACAhG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACkF,OAAO,GAAG,IAAIhN,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC,KAAK,CAAC6H,kBAAkB,CAAC,CAAC;EAC9B;EACA;IAAS,IAAI,CAAChO,IAAI,YAAAoU,qBAAAlU,CAAA;MAAA,YAAAA,CAAA,IAAwFiU,YAAY,EA1jCtB7f,EAAE,CAAA6L,iBAAA,CA0jCsC7L,EAAE,CAACuO,UAAU,GA1jCrDvO,EAAE,CAAA6L,iBAAA,CA0jCgE7L,EAAE,CAACyO,iBAAiB,GA1jCtFzO,EAAE,CAAA6L,iBAAA,CA0jCiGhI,IAAI,CAACqb,aAAa,GA1jCrHlf,EAAE,CAAA6L,iBAAA,CA0jCgIxJ,EAAE,CAACmM,cAAc,MA1jCnJxO,EAAE,CAAA6L,iBAAA,CA0jC8K7L,EAAE,CAACmf,MAAM,GA1jCzLnf,EAAE,CAAA6L,iBAAA,CA0jCoM/H,EAAE,CAACsb,QAAQ,GA1jCjNpf,EAAE,CAAA6L,iBAAA,CA0jC4N7H,qBAAqB;IAAA,CAA4D;EAAE;EACjZ;IAAS,IAAI,CAAC8K,IAAI,kBA3jC8E9O,EAAE,CAAA+O,iBAAA;MAAA5C,IAAA,EA2jCJ0T,YAAY;MAAAzT,SAAA;MAAA+K,cAAA,WAAA4I,4BAAA1b,EAAA,EAAAC,GAAA,EAAA+S,QAAA;QAAA,IAAAhT,EAAA;UA3jCVrE,EAAE,CAAAsX,cAAA,CAAAD,QAAA,EA2jC2ahC,kBAAkB;QAAA;QAAA,IAAAhR,EAAA;UAAA,IAAA8K,EAAA;UA3jC/bnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAuN,MAAA,GAAA1C,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAAgR,mBAAA3b,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAAkP,WAAA,CAAAjK,GAAA;UAAFjF,EAAE,CAAAkP,WAAA,CAAAhK,GAAA;UAAFlF,EAAE,CAAAkP,WAAA,CAAA/J,GAAA;UAAFnF,EAAE,CAAAkP,WAAA,CAAA9J,GAAA;UAAFpF,EAAE,CAAAkP,WAAA,CAAA7J,GAAA;QAAA;QAAA,IAAAhB,EAAA;UAAA,IAAA8K,EAAA;UAAFnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAA4Y,iBAAA,GAAA/N,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAiZ,QAAA,GAAApO,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAA+Z,aAAA,GAAAlP,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAmV,cAAA,GAAAtK,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAiV,kBAAA,GAAApK,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAiG,QAAA;MAAAC,YAAA,WAAAuK,0BAAA5b,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAA6H,WAAA,mDAAAvD,GAAA,CAAAwU,uBAAA,4BAAAxU,GAAA,CAAA+J,mBAAA;QAAA;MAAA;MAAAK,MAAA;QAAA5G,aAAA;MAAA;MAAA6G,OAAA;QAAAuK,kBAAA;QAAAC,YAAA;MAAA;MAAA9M,QAAA,GAAFrM,EAAE,CAAAsM,0BAAA;MAAAkL,kBAAA,EAAAxS,GAAA;MAAAyK,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAsQ,sBAAA7b,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAA0X,eAAA;UAAF1X,EAAE,CAAAuG,cAAA,kBA2jC2oD,CAAC;UA3jC9oDvG,EAAE,CAAAwG,UAAA,mBAAA2Z,8CAAA;YAAA,OA2jCugD7b,GAAA,CAAA0Z,qBAAA,CAAsB,QAAQ,CAAC;UAAA,CAAC,CAAC,uBAAAoC,kDAAAhZ,MAAA;YAAA,OAAqB9C,GAAA,CAAAkV,qBAAA,CAAsB,QAAQ,EAAApS,MAAQ,CAAC;UAAA,CAA7D,CAAC,sBAAAiZ,iDAAA;YAAA,OAAkF/b,GAAA,CAAA+U,aAAA,CAAc,CAAC;UAAA,CAAlG,CAAC;UA3jC1iDrZ,EAAE,CAAAuH,SAAA,YA2jC0sD,CAAC;UA3jC7sDvH,EAAE,CAAAyH,YAAA,CA2jCqtD,CAAC;UA3jCxtDzH,EAAE,CAAAuG,cAAA,eA2jC+4D,CAAC;UA3jCl5DvG,EAAE,CAAAwG,UAAA,qBAAA8Z,6CAAAlZ,MAAA;YAAA,OA2jC4yD9C,GAAA,CAAA4X,cAAA,CAAA9U,MAAqB,CAAC;UAAA,CAAC,CAAC;UA3jCt0DpH,EAAE,CAAAuG,cAAA,eA2jC6gE,CAAC;UA3jChhEvG,EAAE,CAAAwG,UAAA,+BAAA+Z,uDAAA;YAAA,OA2jCw/Djc,GAAA,CAAAkY,iBAAA,CAAkB,CAAC;UAAA,CAAC,CAAC;UA3jC/gExc,EAAE,CAAAuG,cAAA,eA2jCmkE,CAAC;UA3jCtkEvG,EAAE,CAAA+E,YAAA,EA2jComE,CAAC;UA3jCvmE/E,EAAE,CAAAyH,YAAA,CA2jCgnE,CAAC,CAAD,CAAC,CAAD,CAAC;UA3jCnnEzH,EAAE,CAAAuG,cAAA,oBA2jCmvF,CAAC;UA3jCtvFvG,EAAE,CAAAwG,UAAA,uBAAAga,mDAAApZ,MAAA;YAAA,OA2jCqnF9C,GAAA,CAAAkV,qBAAA,CAAsB,OAAO,EAAApS,MAAQ,CAAC;UAAA,CAAC,CAAC,mBAAAqZ,+CAAA;YAAA,OAAiBnc,GAAA,CAAA0Z,qBAAA,CAAsB,OAAO,CAAC;UAAA,CAAhD,CAAC,sBAAA0C,kDAAA;YAAA,OAAqEpc,GAAA,CAAA+U,aAAA,CAAc,CAAC;UAAA,CAArF,CAAC;UA3jC/pFrZ,EAAE,CAAAuH,SAAA,aA2jCkzF,CAAC;UA3jCrzFvH,EAAE,CAAAyH,YAAA,CA2jC6zF,CAAC;QAAA;QAAA,IAAApD,EAAA;UA3jCh0FrE,EAAE,CAAA6H,WAAA,2CAAAvD,GAAA,CAAA0U,oBA2jCm8C,CAAC;UA3jCt8ChZ,EAAE,CAAA8F,UAAA,sBAAAxB,GAAA,CAAA0U,oBAAA,IAAA1U,GAAA,CAAAqE,aA2jCq3C,CAAC,aAAArE,GAAA,CAAA0U,oBAAA,QAAD,CAAC;UA3jCx3ChZ,EAAE,CAAA0I,SAAA,EA2jC84D,CAAC;UA3jCj5D1I,EAAE,CAAA6H,WAAA,4BAAAvD,GAAA,CAAAqU,cAAA,qBA2jC84D,CAAC;UA3jCj5D3Y,EAAE,CAAA0I,SAAA,EA2jCwhF,CAAC;UA3jC3hF1I,EAAE,CAAA6H,WAAA,2CAAAvD,GAAA,CAAAyU,mBA2jCwhF,CAAC;UA3jC3hF/Y,EAAE,CAAA8F,UAAA,sBAAAxB,GAAA,CAAAyU,mBAAA,IAAAzU,GAAA,CAAAqE,aA2jC28E,CAAC,aAAArE,GAAA,CAAAyU,mBAAA,QAAD,CAAC;QAAA;MAAA;MAAA5I,YAAA,GAAs9FhP,EAAE,CAACwf,SAAS,EAAwP3e,IAAI,CAAC4e,iBAAiB;MAAAxQ,MAAA;MAAAC,aAAA;IAAA,EAA0P;EAAE;AAC9hM;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KA7jCoGvM,EAAE,CAAAwM,iBAAA,CA6jCXqT,YAAY,EAAc,CAAC;IAC1G1T,IAAI,EAAE3L,SAAS;IACfiM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAEgC,MAAM,EAAE,CAAC,eAAe,CAAC;MAAEC,OAAO,EAAE,CAAC,oBAAoB,EAAE,cAAc,CAAC;MAAE0B,aAAa,EAAE5P,iBAAiB,CAAC+P,IAAI;MAAEC,eAAe,EAAE/P,uBAAuB,CAACgQ,OAAO;MAAEE,IAAI,EAAE;QACpM,OAAO,EAAE,oBAAoB;QAC7B,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE;MACtC,CAAC;MAAEhB,QAAQ,EAAE,6yDAA6yD;MAAEQ,MAAM,EAAE,CAAC,yiFAAyiF;IAAE,CAAC;EAC73I,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjE,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAEnM,EAAE,CAACyO;IAAkB,CAAC,EAAE;MAAEtC,IAAI,EAAEtI,IAAI,CAACqb;IAAc,CAAC,EAAE;MAAE/S,IAAI,EAAE9J,EAAE,CAACmM,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAC7JR,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAEnM,EAAE,CAACmf;IAAO,CAAC,EAAE;MAAEhT,IAAI,EAAErI,EAAE,CAACsb;IAAS,CAAC,EAAE;MAAEjT,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAC9ER,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACzI,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE6N,MAAM,EAAE,CAAC;MACrC1F,IAAI,EAAEpL,eAAe;MACrB0L,IAAI,EAAE,CAAC4I,kBAAkB,EAAE;QAAEwL,WAAW,EAAE;MAAM,CAAC;IACrD,CAAC,CAAC;IAAE3D,iBAAiB,EAAE,CAAC;MACpB/Q,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEqK,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEyG,QAAQ,EAAE,CAAC;MACXpR,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEqK,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEuH,aAAa,EAAE,CAAC;MAChBlS,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEqK,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAE2C,cAAc,EAAE,CAAC;MACjBtN,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE8M,kBAAkB,EAAE,CAAC;MACrBpN,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMqU,eAAe,GAAG,IAAIlgB,cAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA,IAAImgB,MAAM,GAAG,CAAC;AACd;AACA;AACA,MAAMC,qBAAqB,GAAG3f,UAAU,CAACC,kBAAkB,CAAC,MAAM;EAC9D+I,WAAWA,CAAC2C,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ,CAAC,CAAC,EAAE,SAAS,CAAC;AACd;AACA;AACA;AACA;AACA,MAAMiU,gBAAgB,SAASD,qBAAqB,CAAC;EACjD;EACA,IAAIE,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAaA,CAACtc,KAAK,EAAE;IACrB,IAAI,CAACuc,cAAc,GAAGxd,qBAAqB,CAACiB,KAAK,CAAC;EACtD;EACA;EACA,IAAIkD,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACqQ,cAAc;EAC9B;EACA,IAAIrQ,aAAaA,CAAClD,KAAK,EAAE;IACrB,IAAI,CAACwc,cAAc,GAAGxd,oBAAoB,CAACgB,KAAK,EAAE,IAAI,CAAC;EAC3D;EACA;EACA,IAAIH,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC4c,kBAAkB;EAClC;EACA,IAAI5c,iBAAiBA,CAACG,KAAK,EAAE;IACzB,IAAI,CAACyc,kBAAkB,GAAG,OAAO,CAACC,IAAI,CAAC1c,KAAK,GAAG,EAAE,CAAC,GAAGA,KAAK,GAAG,IAAI,GAAGA,KAAK;EAC7E;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIgF,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC2X,gBAAgB;EAChC;EACA,IAAI3X,eAAeA,CAAChF,KAAK,EAAE;IACvB,IAAI,CAAC2c,gBAAgB,GAAG3d,oBAAoB,CAACgB,KAAK,EAAE,IAAI,CAAC;EAC7D;EACA;AACJ;AACA;AACA;EACI,IAAIqT,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,iBAAiBA,CAACrT,KAAK,EAAE;IACzB,IAAI,CAACsT,kBAAkB,GAAGvU,qBAAqB,CAACiB,KAAK,CAAC;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI+E,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC6X,gBAAgB;EAChC;EACA,IAAI7X,eAAeA,CAAC/E,KAAK,EAAE;IACvB,IAAI,CAAC4c,gBAAgB,GAAG7d,qBAAqB,CAACiB,KAAK,CAAC;EACxD;EACA;EACA,IAAI6c,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAAC7c,KAAK,EAAE;IACvB,MAAMwO,SAAS,GAAG,IAAI,CAACpG,WAAW,CAACmB,aAAa,CAACiF,SAAS;IAC1DA,SAAS,CAACQ,MAAM,CAAC,0BAA0B,EAAG,kBAAiB,IAAI,CAAC6N,eAAgB,EAAC,CAAC;IACtF,IAAI7c,KAAK,EAAE;MACPwO,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAG,kBAAiBzO,KAAM,EAAC,CAAC;IACxE;IACA,IAAI,CAAC8c,gBAAgB,GAAG9c,KAAK;EACjC;EACAyF,WAAWA,CAACuE,UAAU,EAAE2J,kBAAkB,EAAEoJ,aAAa,EAAEhJ,cAAc,EAAE;IACvE,KAAK,CAAC/J,UAAU,CAAC;IACjB,IAAI,CAAC2J,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACI,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACtQ,KAAK,GAAG,IAAIrH,SAAS,CAAC,CAAC;IAC5B;IACA,IAAI,CAACogB,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACQ,oBAAoB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,qBAAqB,GAAG,CAAC;IAC9B;IACA,IAAI,CAACC,iBAAiB,GAAGxf,YAAY,CAACK,KAAK;IAC3C;IACA,IAAI,CAACof,qBAAqB,GAAGzf,YAAY,CAACK,KAAK;IAC/C,IAAI,CAACwe,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAChJ,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAAC6J,cAAc,GAAG,OAAO;IAC7B,IAAI,CAAC9J,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACsJ,gBAAgB,GAAG,KAAK;IAC7B;IACA,IAAI,CAACS,mBAAmB,GAAG,IAAI7hB,YAAY,CAAC,CAAC;IAC7C;IACA,IAAI,CAAC8hB,WAAW,GAAG,IAAI9hB,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAAC+hB,aAAa,GAAG,IAAI/hB,YAAY,CAAC,CAAC;IACvC;IACA,IAAI,CAACgiB,iBAAiB,GAAG,IAAIhiB,YAAY,CAAC,IAAI,CAAC;IAC/C,IAAI,CAACiiB,QAAQ,GAAGtB,MAAM,EAAE;IACxB,IAAI,CAACtc,iBAAiB,GAClBkd,aAAa,IAAIA,aAAa,CAACld,iBAAiB,GAAGkd,aAAa,CAACld,iBAAiB,GAAG,OAAO;IAChG,IAAI,CAACwT,iBAAiB,GAClB0J,aAAa,IAAIA,aAAa,CAAC1J,iBAAiB,IAAI,IAAI,GAClD0J,aAAa,CAAC1J,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAACiJ,aAAa,GACdS,aAAa,IAAIA,aAAa,CAACT,aAAa,IAAI,IAAI,GAAGS,aAAa,CAACT,aAAa,GAAG,KAAK;IAC9F,IAAI,CAACtX,eAAe,GAAG+X,aAAa,EAAE/X,eAAe,IAAI,IAAI;IAC7D,IAAI,CAACD,eAAe,GAAG,CAAC,CAACgY,aAAa,EAAEhY,eAAe;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgS,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,MAAM2G,aAAa,GAAI,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACmB,cAAc,CAAC,IAAI,CAACnB,cAAc,CAAE;IACtF;IACA;IACA,IAAI,IAAI,CAACjJ,cAAc,IAAImK,aAAa,EAAE;MACtC,MAAME,UAAU,GAAG,IAAI,CAACrK,cAAc,IAAI,IAAI;MAC9C,IAAI,CAACqK,UAAU,EAAE;QACb,IAAI,CAACJ,iBAAiB,CAACrU,IAAI,CAAC,IAAI,CAAC0U,kBAAkB,CAACH,aAAa,CAAC,CAAC;QACnE;QACA;QACA,MAAMI,OAAO,GAAG,IAAI,CAACC,eAAe,CAACxU,aAAa;QAClDuU,OAAO,CAAClf,KAAK,CAAC0G,SAAS,GAAGwY,OAAO,CAACtU,YAAY,GAAG,IAAI;MACzD;MACA;MACA;MACAmM,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAACpS,KAAK,CAAC0J,OAAO,CAAC,CAAC6Q,GAAG,EAAE/b,KAAK,KAAM+b,GAAG,CAACzM,QAAQ,GAAGtP,KAAK,KAAKyb,aAAc,CAAC;QAC5E,IAAI,CAACE,UAAU,EAAE;UACb,IAAI,CAACP,mBAAmB,CAAClU,IAAI,CAACuU,aAAa,CAAC;UAC5C;UACA;UACA,IAAI,CAACK,eAAe,CAACxU,aAAa,CAAC3K,KAAK,CAAC0G,SAAS,GAAG,EAAE;QAC3D;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC7B,KAAK,CAAC0J,OAAO,CAAC,CAAC6Q,GAAG,EAAE/b,KAAK,KAAK;MAC/B+b,GAAG,CAACnZ,QAAQ,GAAG5C,KAAK,GAAGyb,aAAa;MACpC;MACA;MACA,IAAI,IAAI,CAACnK,cAAc,IAAI,IAAI,IAAIyK,GAAG,CAACnZ,QAAQ,IAAI,CAAC,IAAI,CAACmZ,GAAG,CAAClZ,MAAM,EAAE;QACjEkZ,GAAG,CAAClZ,MAAM,GAAG4Y,aAAa,GAAG,IAAI,CAACnK,cAAc;MACpD;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACA,cAAc,KAAKmK,aAAa,EAAE;MACvC,IAAI,CAACnK,cAAc,GAAGmK,aAAa;MACnC,IAAI,CAACV,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACrJ,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAiM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACmJ,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B;IACA;IACA,IAAI,CAAChB,iBAAiB,GAAG,IAAI,CAACzZ,KAAK,CAACgO,OAAO,CAACpL,SAAS,CAAC,MAAM;MACxD,MAAMqX,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACnB,cAAc,CAAC;MAC9D;MACA;MACA,IAAIkB,aAAa,KAAK,IAAI,CAACnK,cAAc,EAAE;QACvC,MAAM4K,IAAI,GAAG,IAAI,CAAC1a,KAAK,CAAC0U,OAAO,CAAC,CAAC;QACjC,IAAIiG,WAAW;QACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACza,MAAM,EAAE2a,CAAC,EAAE,EAAE;UAClC,IAAIF,IAAI,CAACE,CAAC,CAAC,CAAC9M,QAAQ,EAAE;YAClB;YACA;YACA;YACA,IAAI,CAACiL,cAAc,GAAG,IAAI,CAACjJ,cAAc,GAAG8K,CAAC;YAC7C,IAAI,CAACrB,oBAAoB,GAAG,IAAI;YAChCoB,WAAW,GAAGD,IAAI,CAACE,CAAC,CAAC;YACrB;UACJ;QACJ;QACA;QACA;QACA;QACA,IAAI,CAACD,WAAW,IAAID,IAAI,CAACT,aAAa,CAAC,EAAE;UACrC/H,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;YACzBsI,IAAI,CAACT,aAAa,CAAC,CAACnM,QAAQ,GAAG,IAAI;YACnC,IAAI,CAACiM,iBAAiB,CAACrU,IAAI,CAAC,IAAI,CAAC0U,kBAAkB,CAACH,aAAa,CAAC,CAAC;UACvE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAAC/J,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;EACAoV,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA,IAAI,CAACK,QAAQ,CAAC7M,OAAO,CAACvL,IAAI,CAAC/H,SAAS,CAAC,IAAI,CAACmgB,QAAQ,CAAC,CAAC,CAACjY,SAAS,CAAE8X,IAAI,IAAK;MACrE,IAAI,CAAC1a,KAAK,CAAC8a,KAAK,CAACJ,IAAI,CAAC1f,MAAM,CAACuf,GAAG,IAAI;QAChC,OAAOA,GAAG,CAAC3M,gBAAgB,KAAK,IAAI,IAAI,CAAC2M,GAAG,CAAC3M,gBAAgB;MACjE,CAAC,CAAC,CAAC;MACH,IAAI,CAAC5N,KAAK,CAAC+a,eAAe,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACA5X,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnD,KAAK,CAAC4T,OAAO,CAAC,CAAC;IACpB,IAAI,CAAC6F,iBAAiB,CAACrW,WAAW,CAAC,CAAC;IACpC,IAAI,CAACsW,qBAAqB,CAACtW,WAAW,CAAC,CAAC;EAC5C;EACA;EACA4X,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACvJ,yBAAyB,CAAC,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACwJ,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACxJ,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACIyJ,QAAQA,CAAC1c,KAAK,EAAE;IACZ,MAAM2c,MAAM,GAAG,IAAI,CAACF,UAAU;IAC9B,IAAIE,MAAM,EAAE;MACRA,MAAM,CAACpH,UAAU,GAAGvV,KAAK;IAC7B;EACJ;EACA4c,aAAaA,CAAC5c,KAAK,EAAE;IACjB,IAAI,CAAC+a,oBAAoB,GAAG/a,KAAK;IACjC,IAAI,CAACqb,WAAW,CAACnU,IAAI,CAAC,IAAI,CAAC0U,kBAAkB,CAAC5b,KAAK,CAAC,CAAC;EACzD;EACA4b,kBAAkBA,CAAC5b,KAAK,EAAE;IACtB,MAAMiH,KAAK,GAAG,IAAI4V,iBAAiB,CAAC,CAAC;IACrC5V,KAAK,CAACjH,KAAK,GAAGA,KAAK;IACnB,IAAI,IAAI,CAACwB,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,MAAM,EAAE;MACjCwF,KAAK,CAAC8U,GAAG,GAAG,IAAI,CAACva,KAAK,CAAC0U,OAAO,CAAC,CAAC,CAAClW,KAAK,CAAC;IAC3C;IACA,OAAOiH,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgV,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACf,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAACtW,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAACsW,qBAAqB,GAAGrf,KAAK,CAAC,GAAG,IAAI,CAAC2F,KAAK,CAACsb,GAAG,CAACf,GAAG,IAAIA,GAAG,CAAC1M,aAAa,CAAC,CAAC,CAACjL,SAAS,CAAC,MAAM,IAAI,CAACsN,kBAAkB,CAAC9K,YAAY,CAAC,CAAC,CAAC;EAC3I;EACA;EACA8U,cAAcA,CAAC1b,KAAK,EAAE;IAClB;IACA;IACA;IACA,OAAO6T,IAAI,CAACE,GAAG,CAAC,IAAI,CAACvS,KAAK,CAACC,MAAM,GAAG,CAAC,EAAEoS,IAAI,CAACC,GAAG,CAAC9T,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE;EACA;EACAkB,cAAcA,CAACkb,CAAC,EAAE;IACd,OAAQ,iBAAgB,IAAI,CAACZ,QAAS,IAAGY,CAAE,EAAC;EAChD;EACA;EACA1a,gBAAgBA,CAAC0a,CAAC,EAAE;IAChB,OAAQ,mBAAkB,IAAI,CAACZ,QAAS,IAAGY,CAAE,EAAC;EAClD;EACA;AACJ;AACA;AACA;EACI9Z,wBAAwBA,CAACya,SAAS,EAAE;IAChC,IAAI,CAAC,IAAI,CAACzC,cAAc,IAAI,CAAC,IAAI,CAACU,qBAAqB,EAAE;MACrD;IACJ;IACA,MAAMa,OAAO,GAAG,IAAI,CAACC,eAAe,CAACxU,aAAa;IAClDuU,OAAO,CAAClf,KAAK,CAACkY,MAAM,GAAG,IAAI,CAACmG,qBAAqB,GAAG,IAAI;IACxD;IACA;IACA,IAAI,IAAI,CAACc,eAAe,CAACxU,aAAa,CAAC0V,YAAY,EAAE;MACjDnB,OAAO,CAAClf,KAAK,CAACkY,MAAM,GAAGkI,SAAS,GAAG,IAAI;IAC3C;EACJ;EACA;EACA5a,2BAA2BA,CAAA,EAAG;IAC1B,MAAM0Z,OAAO,GAAG,IAAI,CAACC,eAAe,CAACxU,aAAa;IAClD,IAAI,CAAC0T,qBAAqB,GAAGa,OAAO,CAACtU,YAAY;IACjDsU,OAAO,CAAClf,KAAK,CAACkY,MAAM,GAAG,EAAE;IACzB,IAAI,CAACyG,aAAa,CAACpU,IAAI,CAAC,CAAC;EAC7B;EACA;EACA7G,YAAYA,CAAC0b,GAAG,EAAEkB,SAAS,EAAEjd,KAAK,EAAE;IAChCid,SAAS,CAAC1H,UAAU,GAAGvV,KAAK;IAC5B,IAAI,CAAC+b,GAAG,CAAC3a,QAAQ,EAAE;MACf,IAAI,CAACH,aAAa,GAAGjB,KAAK;IAC9B;EACJ;EACA;EACAuB,YAAYA,CAACvB,KAAK,EAAE;IAChB,MAAMkd,WAAW,GAAG,IAAI,CAACnC,oBAAoB,IAAI,IAAI,CAAC9Z,aAAa;IACnE,OAAOjB,KAAK,KAAKkd,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;EACzC;EACA;EACAzc,gBAAgBA,CAAC0c,WAAW,EAAEnd,KAAK,EAAE;IACjC;IACA;IACA;IACA;IACA,IAAImd,WAAW,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,EAAE;MACnE,IAAI,CAACV,UAAU,CAAClH,UAAU,GAAGvV,KAAK;IACtC;EACJ;EACA;IAAS,IAAI,CAAC6E,IAAI,YAAAuY,yBAAArY,CAAA;MAAA,YAAAA,CAAA,IAAwFqV,gBAAgB,EAj7C1BjhB,EAAE,CAAA6L,iBAAA,CAi7C0C7L,EAAE,CAACuO,UAAU,GAj7CzDvO,EAAE,CAAA6L,iBAAA,CAi7CoE7L,EAAE,CAACyO,iBAAiB,GAj7C1FzO,EAAE,CAAA6L,iBAAA,CAi7CqGiV,eAAe,MAj7CtH9gB,EAAE,CAAA6L,iBAAA,CAi7CiJ7H,qBAAqB;IAAA,CAA4D;EAAE;EACtU;IAAS,IAAI,CAACiI,IAAI,kBAl7C8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EAk7CJ8U,gBAAgB;MAAAvS,MAAA;QAAAwS,aAAA;QAAApZ,aAAA;QAAAka,cAAA;QAAAvd,iBAAA;QAAAmF,eAAA;QAAAqO,iBAAA;QAAAtO,eAAA;QAAA8X,eAAA;MAAA;MAAA9S,OAAA;QAAAsT,mBAAA;QAAAC,WAAA;QAAAC,aAAA;QAAAC,iBAAA;MAAA;MAAA/V,QAAA,GAl7CdrM,EAAE,CAAAsM,0BAAA;IAAA,EAk7C2f;EAAE;AACnmB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAp7CoGvM,EAAE,CAAAwM,iBAAA,CAo7CXyU,gBAAgB,EAAc,CAAC;IAC9G9U,IAAI,EAAEjM;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiM,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAEnM,EAAE,CAACyO;IAAkB,CAAC,EAAE;MAAEtC,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QACvHR,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACqU,eAAe;MAC1B,CAAC,EAAE;QACC3U,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCR,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACzI,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkd,aAAa,EAAE,CAAC;MAC5C/U,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEuH,aAAa,EAAE,CAAC;MAChBqE,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEyhB,cAAc,EAAE,CAAC;MACjB7V,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEkE,iBAAiB,EAAE,CAAC;MACpB0H,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEqJ,eAAe,EAAE,CAAC;MAClBuC,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAE0X,iBAAiB,EAAE,CAAC;MACpB9L,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEoJ,eAAe,EAAE,CAAC;MAClBwC,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEkhB,eAAe,EAAE,CAAC;MAClBtV,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAE0hB,mBAAmB,EAAE,CAAC;MACtB9V,IAAI,EAAE7L;IACV,CAAC,CAAC;IAAE4hB,WAAW,EAAE,CAAC;MACd/V,IAAI,EAAE7L;IACV,CAAC,CAAC;IAAE6hB,aAAa,EAAE,CAAC;MAChBhW,IAAI,EAAE7L;IACV,CAAC,CAAC;IAAE8hB,iBAAiB,EAAE,CAAC;MACpBjW,IAAI,EAAE7L;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,MAAM4jB,WAAW,SAASjD,gBAAgB,CAAC;EACvC;EACA,IAAI/Y,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACic,mBAAmB;EACnC;EACA,IAAIjc,kBAAkBA,CAAC4K,CAAC,EAAE;IACtB,IAAI,CAACqR,mBAAmB,GAAGxgB,qBAAqB,CAACmP,CAAC,CAAC;IACnD,IAAI,CAACyF,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAI2W,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACtR,CAAC,EAAE;IACf,IAAI,CAACuR,YAAY,GAAG1gB,qBAAqB,CAACmP,CAAC,CAAC;EAChD;EACAzI,WAAWA,CAACuE,UAAU,EAAE1B,iBAAiB,EAAEyU,aAAa,EAAEjC,aAAa,EAAE;IACrE,KAAK,CAAC9Q,UAAU,EAAE1B,iBAAiB,EAAEyU,aAAa,EAAEjC,aAAa,CAAC;IAClE,IAAI,CAACyE,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACE,YAAY,GAAG,IAAI;IACxB,IAAI,CAACnc,kBAAkB,GACnByZ,aAAa,IAAIA,aAAa,CAACzZ,kBAAkB,IAAI,IAAI,GACnDyZ,aAAa,CAACzZ,kBAAkB,GAChC,KAAK;IACf,IAAI,CAACkc,WAAW,GACZzC,aAAa,IAAIA,aAAa,CAACyC,WAAW,IAAI,IAAI,GAAGzC,aAAa,CAACyC,WAAW,GAAG,IAAI;EAC7F;EACA;IAAS,IAAI,CAAC1Y,IAAI,YAAA4Y,oBAAA1Y,CAAA;MAAA,YAAAA,CAAA,IAAwFsY,WAAW,EAz/CrBlkB,EAAE,CAAA6L,iBAAA,CAy/CqC7L,EAAE,CAACuO,UAAU,GAz/CpDvO,EAAE,CAAA6L,iBAAA,CAy/C+D7L,EAAE,CAACyO,iBAAiB,GAz/CrFzO,EAAE,CAAA6L,iBAAA,CAy/CgGiV,eAAe,MAz/CjH9gB,EAAE,CAAA6L,iBAAA,CAy/C4I7H,qBAAqB;IAAA,CAA4D;EAAE;EACjU;IAAS,IAAI,CAAC8K,IAAI,kBA1/C8E9O,EAAE,CAAA+O,iBAAA;MAAA5C,IAAA,EA0/CJ+X,WAAW;MAAA9X,SAAA;MAAA+K,cAAA,WAAAoN,2BAAAlgB,EAAA,EAAAC,GAAA,EAAA+S,QAAA;QAAA,IAAAhT,EAAA;UA1/CTrE,EAAE,CAAAsX,cAAA,CAAAD,QAAA,EA+/C1CN,MAAM;QAAA;QAAA,IAAA1S,EAAA;UAAA,IAAA8K,EAAA;UA//CkCnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAA4e,QAAA,GAAA/T,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAAwV,kBAAAngB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAAkP,WAAA,CAAA5J,GAAA;UAAFtF,EAAE,CAAAkP,WAAA,CAAA3J,GAAA;QAAA;QAAA,IAAAlB,EAAA;UAAA,IAAA8K,EAAA;UAAFnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAqe,eAAA,GAAAxT,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAgf,UAAA,GAAAnU,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAC,SAAA,sBA0/CqO,EAAE;MAAAiG,QAAA;MAAAC,YAAA,WAAA+O,yBAAApgB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1/CzOrE,EAAE,CAAA0kB,WAAA,iCAAApgB,GAAA,CAAAG,iBAAA;UAAFzE,EAAE,CAAA6H,WAAA,qCAAAvD,GAAA,CAAA4c,aAAA,uCAAA5c,GAAA,CAAA0d,cAAA,gDAAA1d,GAAA,CAAA8f,WAAA;QAAA;MAAA;MAAA1V,MAAA;QAAAiW,KAAA;QAAAhc,aAAA;QAAAT,kBAAA;QAAAkc,WAAA;MAAA;MAAA7M,QAAA;MAAAlL,QAAA,GAAFrM,EAAE,CAAAgR,kBAAA,CA0/CqiB,CAC/nB;QACIC,OAAO,EAAE4E,aAAa;QACtB3E,WAAW,EAAEgT;MACjB,CAAC,CACJ,GA//C2FlkB,EAAE,CAAAsM,0BAAA;MAAAmD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgV,qBAAAvgB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAAuG,cAAA,0BA+/CukB,CAAC;UA//C1kBvG,EAAE,CAAAwG,UAAA,0BAAAqe,4DAAAzd,MAAA;YAAA,OA+/C+e9C,GAAA,CAAAmf,aAAA,CAAArc,MAAoB,CAAC;UAAA,CAAC,CAAC,gCAAA0d,kEAAA1d,MAAA;YAAA,OAAA9C,GAAA,CAAAwD,aAAA,GAAAV,MAAA;UAAA,CAAD,CAAC;UA//CxgBpH,EAAE,CAAA0F,UAAA,IAAAU,0BAAA,iBA+/CyzE,CAAC;UA//C5zEpG,EAAE,CAAAyH,YAAA,CA+/C40E,CAAC;UA//C/0EzH,EAAE,CAAAuG,cAAA,eA+/Cy9E,CAAC;UA//C59EvG,EAAE,CAAA0F,UAAA,IAAAkD,mCAAA,0BA+/Cs0G,CAAC;UA//Cz0G5I,EAAE,CAAAyH,YAAA,CA+/C80G,CAAC;QAAA;QAAA,IAAApD,EAAA;UA//Cj1GrE,EAAE,CAAA8F,UAAA,kBAAAxB,GAAA,CAAAwD,aAAA,KA+/C6V,CAAC,kBAAAxD,GAAA,CAAAqE,aAAD,CAAC,sBAAArE,GAAA,CAAA2T,iBAAD,CAAC;UA//ChWjY,EAAE,CAAA0I,SAAA,EA+/C2wB,CAAC;UA//C9wB1I,EAAE,CAAA8F,UAAA,YAAAxB,GAAA,CAAA+D,KA+/C2wB,CAAC;UA//C9wBrI,EAAE,CAAA0I,SAAA,EA+/Cq8E,CAAC;UA//Cx8E1I,EAAE,CAAA6H,WAAA,4BAAAvD,GAAA,CAAAqU,cAAA,qBA+/Cq8E,CAAC;UA//Cx8E3Y,EAAE,CAAA0I,SAAA,EA+/CwiF,CAAC;UA//C3iF1I,EAAE,CAAA8F,UAAA,YAAAxB,GAAA,CAAA+D,KA+/CwiF,CAAC;QAAA;MAAA;MAAA8H,YAAA,GAAovStQ,IAAI,CAACklB,OAAO,EAAoFllB,IAAI,CAACmlB,OAAO,EAAmHnlB,IAAI,CAAColB,IAAI,EAA6FtjB,EAAE,CAACC,eAAe,EAAiJT,EAAE,CAACwf,SAAS,EAAwPze,EAAE,CAACgjB,eAAe,EAA2JlZ,UAAU,EAAyDqJ,kBAAkB,EAA6GwK,YAAY;MAAAzP,MAAA;MAAAC,aAAA;IAAA,EAA6M;EAAE;AACzra;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KAjgDoGvM,EAAE,CAAAwM,iBAAA,CAigDX0X,WAAW,EAAc,CAAC;IACzG/X,IAAI,EAAE3L,SAAS;IACfiM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAE6K,QAAQ,EAAE,aAAa;MAAElH,aAAa,EAAE5P,iBAAiB,CAAC+P,IAAI;MAAEC,eAAe,EAAE/P,uBAAuB,CAACgQ,OAAO;MAAEhC,MAAM,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;MAAEyC,SAAS,EAAE,CACzL;QACIF,OAAO,EAAE4E,aAAa;QACtB3E,WAAW,EAAEgT;MACjB,CAAC,CACJ;MAAEtT,IAAI,EAAE;QACL,iBAAiB,EAAE,EAAE;QACrB,OAAO,EAAE,mBAAmB;QAC5B,0CAA0C,EAAE,eAAe;QAC3D,2CAA2C,EAAE,4BAA4B;QACzE,wCAAwC,EAAE,aAAa;QACvD,sCAAsC,EAAE;MAC5C,CAAC;MAAEhB,QAAQ,EAAE,ukGAAukG;MAAEQ,MAAM,EAAE,CAAC,m5QAAm5Q;IAAE,CAAC;EACjgX,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjE,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAEnM,EAAE,CAACyO;IAAkB,CAAC,EAAE;MAAEtC,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QACvHR,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACqU,eAAe;MAC1B,CAAC,EAAE;QACC3U,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCR,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACzI,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkf,QAAQ,EAAE,CAAC;MACvC/W,IAAI,EAAEpL,eAAe;MACrB0L,IAAI,EAAE,CAACsK,MAAM,EAAE;QAAE8J,WAAW,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAE8B,eAAe,EAAE,CAAC;MAClBxW,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAE6W,UAAU,EAAE,CAAC;MACbnX,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEvE,kBAAkB,EAAE,CAAC;MACrBiE,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAE6jB,WAAW,EAAE,CAAC;MACdjY,IAAI,EAAE5L,KAAK;MACXkM,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMiX,iBAAiB,CAAC;;AAGxB;AACA,IAAIyB,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAASpN,qBAAqB,CAAC;EAC/C;EACA,IAAIyJ,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAAC7c,KAAK,EAAE;IACvB,MAAMwO,SAAS,GAAG,IAAI,CAACpG,WAAW,CAACmB,aAAa,CAACiF,SAAS;IAC1DA,SAAS,CAACQ,MAAM,CAAC,0BAA0B,EAAG,kBAAiB,IAAI,CAAC6N,eAAgB,EAAC,CAAC;IACtF,IAAI7c,KAAK,EAAE;MACPwO,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAG,kBAAiBzO,KAAM,EAAC,CAAC;IACxE;IACA,IAAI,CAAC8c,gBAAgB,GAAG9c,KAAK;EACjC;EACA;EACA,IAAI+D,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC2W,cAAc;EAC9B;EACA,IAAI3W,aAAaA,CAAC/D,KAAK,EAAE;IACrB,IAAI,CAAC0a,cAAc,GAAG3b,qBAAqB,CAACiB,KAAK,CAAC;EACtD;EACAyF,WAAWA,CAACuE,UAAU,EAAEpB,GAAG,EAAEgS,MAAM,EAAEtS,iBAAiB,EAAEqS,aAAa,EAAEE,QAAQ,EAAEC,aAAa,EAAE;IAC5F,KAAK,CAAC9Q,UAAU,EAAE1B,iBAAiB,EAAEqS,aAAa,EAAE/R,GAAG,EAAEgS,MAAM,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IACzF,IAAI,CAACJ,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACqF,KAAK,GAAG,SAAS;EAC1B;EACArI,aAAaA,CAAA,EAAG;IACZ;EAAA;EAEJ5C,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA,IAAI,CAAC7H,MAAM,CAACwE,OAAO,CAACvL,IAAI,CAAC/H,SAAS,CAAC,IAAI,CAAC,EAAEE,SAAS,CAAC,IAAI,CAAC4V,UAAU,CAAC,CAAC,CAAC5N,SAAS,CAAC,MAAM;MAClF,IAAI,CAACoa,gBAAgB,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,KAAK,CAAC3L,kBAAkB,CAAC,CAAC;EAC9B;EACA;EACA2L,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACxT,MAAM,EAAE;MACd;IACJ;IACA,MAAMyT,KAAK,GAAG,IAAI,CAACzT,MAAM,CAACkL,OAAO,CAAC,CAAC;IACnC,KAAK,IAAIkG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,KAAK,CAAChd,MAAM,EAAE2a,CAAC,EAAE,EAAE;MACnC,IAAIqC,KAAK,CAACrC,CAAC,CAAC,CAACsC,MAAM,EAAE;QACjB,IAAI,CAACzd,aAAa,GAAGmb,CAAC;QACtB,IAAI,CAAC1K,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;QACtC,IAAI,IAAI,CAAC+X,QAAQ,EAAE;UACf,IAAI,CAACA,QAAQ,CAACC,YAAY,GAAGH,KAAK,CAACrC,CAAC,CAAC,CAACyC,EAAE;QAC5C;QACA;MACJ;IACJ;IACA;IACA,IAAI,CAAC5d,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAAC8W,OAAO,CAAC9M,IAAI,CAAC,CAAC;EACvB;EACA6T,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACH,QAAQ,GAAG,SAAS,GAAG,IAAI,CAACxY,WAAW,CAACmB,aAAa,CAACyX,YAAY,CAAC,MAAM,CAAC;EAC1F;EACA;IAAS,IAAI,CAACla,IAAI,YAAAma,uBAAAja,CAAA;MAAA,YAAAA,CAAA,IAAwFwZ,cAAc,EA/mDxBplB,EAAE,CAAA6L,iBAAA,CA+mDwC7L,EAAE,CAACuO,UAAU,GA/mDvDvO,EAAE,CAAA6L,iBAAA,CA+mDkExJ,EAAE,CAACmM,cAAc,MA/mDrFxO,EAAE,CAAA6L,iBAAA,CA+mDgH7L,EAAE,CAACmf,MAAM,GA/mD3Hnf,EAAE,CAAA6L,iBAAA,CA+mDsI7L,EAAE,CAACyO,iBAAiB,GA/mD5JzO,EAAE,CAAA6L,iBAAA,CA+mDuKhI,IAAI,CAACqb,aAAa,GA/mD3Llf,EAAE,CAAA6L,iBAAA,CA+mDsM/H,EAAE,CAACsb,QAAQ,GA/mDnNpf,EAAE,CAAA6L,iBAAA,CA+mD8N7H,qBAAqB;IAAA,CAA4D;EAAE;EACnZ;IAAS,IAAI,CAACiI,IAAI,kBAhnD8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EAgnDJiZ,cAAc;MAAA1W,MAAA;QAAA+S,eAAA;QAAA9Y,aAAA;QAAAgc,KAAA;QAAAa,QAAA;MAAA;MAAAnZ,QAAA,GAhnDZrM,EAAE,CAAAsM,0BAAA;IAAA,EAgnDwK;EAAE;AAChR;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlnDoGvM,EAAE,CAAAwM,iBAAA,CAknDX4Y,cAAc,EAAc,CAAC;IAC5GjZ,IAAI,EAAEjM;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiM,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAE9J,EAAE,CAACmM,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAC/FR,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAEnM,EAAE,CAACmf;IAAO,CAAC,EAAE;MAAEhT,IAAI,EAAEnM,EAAE,CAACyO;IAAkB,CAAC,EAAE;MAAEtC,IAAI,EAAEtI,IAAI,CAACqb;IAAc,CAAC,EAAE;MAAE/S,IAAI,EAAErI,EAAE,CAACsb;IAAS,CAAC,EAAE;MAAEjT,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAC5IR,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACzI,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEyd,eAAe,EAAE,CAAC;MAC9CtV,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEoI,aAAa,EAAE,CAAC;MAChBwD,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEokB,KAAK,EAAE,CAAC;MACRxY,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEilB,QAAQ,EAAE,CAAC;MACXrZ,IAAI,EAAE5L;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMulB,oBAAoB,GAAGvkB,aAAa,CAACD,kBAAkB,CAACF,aAAa,CAAC,MAAM,EACjF,CAAC,CAAC,CAAC;AACJ;AACA,MAAM2kB,eAAe,SAASD,oBAAoB,CAAC;EAC/C;EACA,IAAIP,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACS,SAAS;EACzB;EACA,IAAIT,MAAMA,CAAC3gB,KAAK,EAAE;IACd,MAAMmO,QAAQ,GAAGpP,qBAAqB,CAACiB,KAAK,CAAC;IAC7C,IAAImO,QAAQ,KAAK,IAAI,CAACiT,SAAS,EAAE;MAC7B,IAAI,CAACA,SAAS,GAAGjT,QAAQ;MACzB,IAAI,CAACkT,UAAU,CAACZ,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIa,cAAcA,CAAA,EAAG;IACjB,OAAQ,IAAI,CAACje,QAAQ,IACjB,IAAI,CAACU,aAAa,IAClB,IAAI,CAACsd,UAAU,CAACtd,aAAa,IAC7B,CAAC,CAAC,IAAI,CAACwd,YAAY,CAACle,QAAQ;EACpC;EACAoC,WAAWA,CAAC4b,UAAU,EACtB,oBAAqBrX,UAAU,EAAEwX,mBAAmB,EAAEpJ,QAAQ,EAAEqJ,aAAa,EAAE3G,aAAa,EAAE;IAC1F,KAAK,CAAC,CAAC;IACP,IAAI,CAACuG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACrX,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACyX,aAAa,GAAGA,aAAa;IAClC;IACA,IAAI,CAACL,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACN,EAAE,GAAI,gBAAeP,YAAY,EAAG,EAAC;IAC1C,IAAI,CAACgB,YAAY,GAAGC,mBAAmB,IAAI,CAAC,CAAC;IAC7C,IAAI,CAACpJ,QAAQ,GAAGsJ,QAAQ,CAACtJ,QAAQ,CAAC,IAAI,CAAC;IACvC,IAAI0C,aAAa,KAAK,gBAAgB,EAAE;MACpC,IAAI,CAACyG,YAAY,CAAC5V,SAAS,GAAG;QAAEgW,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC;IACvE;EACJ;EACA;EACAxR,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACpG,UAAU,CAACT,aAAa,CAAC6G,KAAK,CAAC,CAAC;EACzC;EACAsE,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC+M,aAAa,CAACI,OAAO,CAAC,IAAI,CAAC7X,UAAU,CAAC;EAC/C;EACApD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6a,aAAa,CAACK,cAAc,CAAC,IAAI,CAAC9X,UAAU,CAAC;EACtD;EACA+X,YAAYA,CAAA,EAAG;IACX;IACA;IACA,IAAI,CAACV,UAAU,CAAC7J,UAAU,GAAG,IAAI,CAAC6J,UAAU,CAACpU,MAAM,CAACkL,OAAO,CAAC,CAAC,CAAC6J,OAAO,CAAC,IAAI,CAAC;EAC/E;EACA1K,cAAcA,CAACpO,KAAK,EAAE;IAClB,IAAIA,KAAK,CAACqO,OAAO,KAAKjY,KAAK,IAAI4J,KAAK,CAACqO,OAAO,KAAKhY,KAAK,EAAE;MACpD,IAAI,IAAI,CAAC8D,QAAQ,EAAE;QACf6F,KAAK,CAAC6R,cAAc,CAAC,CAAC;MAC1B,CAAC,MACI,IAAI,IAAI,CAACsG,UAAU,CAACT,QAAQ,EAAE;QAC/B,IAAI,CAAC5W,UAAU,CAACT,aAAa,CAAC0Y,KAAK,CAAC,CAAC;MACzC;IACJ;EACJ;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACb,UAAU,CAACT,QAAQ,GACzB,IAAI,CAACS,UAAU,CAACT,QAAQ,EAAEE,EAAE,GAC5B,IAAI,CAAC9W,UAAU,CAACT,aAAa,CAACyX,YAAY,CAAC,eAAe,CAAC;EACrE;EACAmB,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACd,UAAU,CAACT,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACD,MAAM,GAAG,MAAM,GAAG,OAAO;IACzC,CAAC,MACI;MACD,OAAO,IAAI,CAAC3W,UAAU,CAACT,aAAa,CAACyX,YAAY,CAAC,eAAe,CAAC;IACtE;EACJ;EACAoB,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACzB,MAAM,IAAI,CAAC,IAAI,CAACU,UAAU,CAACT,QAAQ,GAAG,MAAM,GAAG,IAAI;EACnE;EACAG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACM,UAAU,CAACT,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC5W,UAAU,CAACT,aAAa,CAACyX,YAAY,CAAC,MAAM,CAAC;EAChG;EACAxd,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC6d,UAAU,CAACT,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACQ,SAAS,IAAI,CAAC,IAAI,CAAC/d,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC,MACI;MACD,OAAO,IAAI,CAAC+U,QAAQ;IACxB;EACJ;EACA;IAAS,IAAI,CAACtR,IAAI,YAAAub,wBAAArb,CAAA;MAAA,YAAAA,CAAA,IAAwFma,eAAe,EAluDzB/lB,EAAE,CAAA6L,iBAAA,CAkuDyCuZ,cAAc,GAluDzDplB,EAAE,CAAA6L,iBAAA,CAkuDoE7L,EAAE,CAACuO,UAAU,GAluDnFvO,EAAE,CAAA6L,iBAAA,CAkuD8FrK,yBAAyB,MAluDzHxB,EAAE,CAAAknB,iBAAA,CAkuDoJ,UAAU,GAluDhKlnB,EAAE,CAAA6L,iBAAA,CAkuD4L3J,EAAE,CAACilB,YAAY,GAluD7MnnB,EAAE,CAAA6L,iBAAA,CAkuDwN7H,qBAAqB;IAAA,CAA4D;EAAE;EAC7Y;IAAS,IAAI,CAACiI,IAAI,kBAnuD8EjM,EAAE,CAAAkM,iBAAA;MAAAC,IAAA,EAmuDJ4Z,eAAe;MAAArX,MAAA;QAAA6W,MAAA;QAAAG,EAAA;MAAA;MAAArZ,QAAA,GAnuDbrM,EAAE,CAAAsM,0BAAA;IAAA,EAmuD2F;EAAE;AACnM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAruDoGvM,EAAE,CAAAwM,iBAAA,CAquDXuZ,eAAe,EAAc,CAAC;IAC7G5Z,IAAI,EAAEjM;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiM,IAAI,EAAEiZ;IAAe,CAAC,EAAE;MAAEjZ,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QACjHR,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACjL,yBAAyB;MACpC,CAAC;IAAE,CAAC,EAAE;MAAE2K,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCR,IAAI,EAAElL,SAAS;QACfwL,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEN,IAAI,EAAEjK,EAAE,CAACilB;IAAa,CAAC,EAAE;MAAEhb,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAC7DR,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACzI,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEuhB,MAAM,EAAE,CAAC;MACrCpZ,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEmlB,EAAE,EAAE,CAAC;MACLvZ,IAAI,EAAE5L;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM6mB,6BAA6B,GAAGzU,eAAe,CAACoT,eAAe,CAAC;AACtE;AACA;AACA;AACA;AACA,MAAMsB,SAAS,SAASjC,cAAc,CAAC;EACnC;EACA,IAAIld,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACic,mBAAmB,CAACvf,KAAK;EACzC;EACA,IAAIsD,kBAAkBA,CAAC4K,CAAC,EAAE;IACtB,IAAI,CAACqR,mBAAmB,CAACnU,IAAI,CAACrM,qBAAqB,CAACmP,CAAC,CAAC,CAAC;IACvD,IAAI,CAACyF,kBAAkB,CAAC9K,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAI2W,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACtR,CAAC,EAAE;IACf,IAAI,CAACuR,YAAY,GAAG1gB,qBAAqB,CAACmP,CAAC,CAAC;EAChD;EACA,IAAIrO,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC4c,kBAAkB;EAClC;EACA,IAAI5c,iBAAiBA,CAACG,KAAK,EAAE;IACzB,IAAI,CAACyc,kBAAkB,GAAG,OAAO,CAACC,IAAI,CAAC1c,KAAK,GAAG,EAAE,CAAC,GAAGA,KAAK,GAAG,IAAI,GAAGA,KAAK;EAC7E;EACAyF,WAAWA,CAACuE,UAAU,EAAEpB,GAAG,EAAEgS,MAAM,EAAEtS,iBAAiB,EAAEqS,aAAa,EAAEE,QAAQ,EAAEC,aAAa,EAAEiC,aAAa,EAAE;IAC3G,KAAK,CAAC/S,UAAU,EAAEpB,GAAG,EAAEgS,MAAM,EAAEtS,iBAAiB,EAAEqS,aAAa,EAAEE,QAAQ,EAAEC,aAAa,CAAC;IACzF,IAAI,CAACyE,mBAAmB,GAAG,IAAIrhB,eAAe,CAAC,KAAK,CAAC;IACrD,IAAI,CAACuhB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACpM,iBAAiB,GAClB0J,aAAa,IAAIA,aAAa,CAAC1J,iBAAiB,IAAI,IAAI,GAClD0J,aAAa,CAAC1J,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAAC/P,kBAAkB,GACnByZ,aAAa,IAAIA,aAAa,CAACzZ,kBAAkB,IAAI,IAAI,GACnDyZ,aAAa,CAACzZ,kBAAkB,GAChC,KAAK;IACf,IAAI,CAACkc,WAAW,GACZzC,aAAa,IAAIA,aAAa,CAACyC,WAAW,IAAI,IAAI,GAAGzC,aAAa,CAACyC,WAAW,GAAG,IAAI;EAC7F;EACA1K,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACkF,OAAO,GAAG,IAAIhN,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC,KAAK,CAAC6H,kBAAkB,CAAC,CAAC;EAC9B;EACAJ,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACkM,QAAQ,KAAK,OAAOjZ,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAM,IAAI6H,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACA,KAAK,CAACkF,eAAe,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAAC5N,IAAI,YAAA4b,kBAAA1b,CAAA;MAAA,YAAAA,CAAA,IAAwFyb,SAAS,EA7yDnBrnB,EAAE,CAAA6L,iBAAA,CA6yDmC7L,EAAE,CAACuO,UAAU,GA7yDlDvO,EAAE,CAAA6L,iBAAA,CA6yD6DxJ,EAAE,CAACmM,cAAc,MA7yDhFxO,EAAE,CAAA6L,iBAAA,CA6yD2G7L,EAAE,CAACmf,MAAM,GA7yDtHnf,EAAE,CAAA6L,iBAAA,CA6yDiI7L,EAAE,CAACyO,iBAAiB,GA7yDvJzO,EAAE,CAAA6L,iBAAA,CA6yDkKhI,IAAI,CAACqb,aAAa,GA7yDtLlf,EAAE,CAAA6L,iBAAA,CA6yDiM/H,EAAE,CAACsb,QAAQ,GA7yD9Mpf,EAAE,CAAA6L,iBAAA,CA6yDyN7H,qBAAqB,MA7yDhPhE,EAAE,CAAA6L,iBAAA,CA6yD2QiV,eAAe;IAAA,CAA4D;EAAE;EAC1b;IAAS,IAAI,CAAChS,IAAI,kBA9yD8E9O,EAAE,CAAA+O,iBAAA;MAAA5C,IAAA,EA8yDJkb,SAAS;MAAAjb,SAAA;MAAA+K,cAAA,WAAAoQ,yBAAAljB,EAAA,EAAAC,GAAA,EAAA+S,QAAA;QAAA,IAAAhT,EAAA;UA9yDPrE,EAAE,CAAAsX,cAAA,CAAAD,QAAA,EA8yDi3BmQ,UAAU;QAAA;QAAA,IAAAnjB,EAAA;UAAA,IAAA8K,EAAA;UA9yD73BnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAuN,MAAA,GAAA1C,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAAyY,gBAAApjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAAkP,WAAA,CAAAjK,GAAA;UAAFjF,EAAE,CAAAkP,WAAA,CAAAhK,GAAA;UAAFlF,EAAE,CAAAkP,WAAA,CAAA/J,GAAA;UAAFnF,EAAE,CAAAkP,WAAA,CAAA9J,GAAA;UAAFpF,EAAE,CAAAkP,WAAA,CAAA7J,GAAA;QAAA;QAAA,IAAAhB,EAAA;UAAA,IAAA8K,EAAA;UAAFnP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAA4Y,iBAAA,GAAA/N,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAiZ,QAAA,GAAApO,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAA+Z,aAAA,GAAAlP,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAmV,cAAA,GAAAtK,EAAA,CAAAI,KAAA;UAAFvP,EAAE,CAAAoP,cAAA,CAAAD,EAAA,GAAFnP,EAAE,CAAAqP,WAAA,QAAA/K,GAAA,CAAAiV,kBAAA,GAAApK,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAiG,QAAA;MAAAC,YAAA,WAAAgS,uBAAArjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAAmI,WAAA,SAAA7D,GAAA,CAAAqhB,QAAA;UAAF3lB,EAAE,CAAA0kB,WAAA,iCAAApgB,GAAA,CAAAG,iBAAA;UAAFzE,EAAE,CAAA6H,WAAA,mDAAAvD,GAAA,CAAAwU,uBAAA,4BAAAxU,GAAA,CAAA+J,mBAAA,iDAAA/J,GAAA,CAAA8f,WAAA,iBAAA9f,GAAA,CAAAqgB,KAAA,eAAArgB,GAAA,CAAAqgB,KAAA,6BAAArgB,GAAA,CAAAqgB,KAAA,2BAAArgB,GAAA,CAAAqgB,KAAA,wCAAArgB,GAAA,CAAAqU,cAAA;QAAA;MAAA;MAAAjK,MAAA;QAAAiW,KAAA;QAAAzc,kBAAA;QAAAkc,WAAA;QAAA3f,iBAAA;MAAA;MAAA8S,QAAA;MAAAlL,QAAA,GAAFrM,EAAE,CAAAsM,0BAAA;MAAAqb,KAAA,EAAA9d,IAAA;MAAA2N,kBAAA,EAAAxS,GAAA;MAAAyK,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgY,mBAAAvjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAA0X,eAAA;UAAF1X,EAAE,CAAAuG,cAAA,kBA8yDyoE,CAAC;UA9yD5oEvG,EAAE,CAAAwG,UAAA,mBAAAqhB,2CAAA;YAAA,OA8yDqgEvjB,GAAA,CAAA0Z,qBAAA,CAAsB,QAAQ,CAAC;UAAA,CAAC,CAAC,uBAAA8J,+CAAA1gB,MAAA;YAAA,OAAqB9C,GAAA,CAAAkV,qBAAA,CAAsB,QAAQ,EAAApS,MAAQ,CAAC;UAAA,CAA7D,CAAC,sBAAA2gB,8CAAA;YAAA,OAAkFzjB,GAAA,CAAA+U,aAAA,CAAc,CAAC;UAAA,CAAlG,CAAC;UA9yDxiErZ,EAAE,CAAAuH,SAAA,YA8yDwsE,CAAC;UA9yD3sEvH,EAAE,CAAAyH,YAAA,CA8yDmtE,CAAC;UA9yDttEzH,EAAE,CAAAuG,cAAA,eA8yDwzE,CAAC;UA9yD3zEvG,EAAE,CAAAwG,UAAA,qBAAAwhB,0CAAA5gB,MAAA;YAAA,OA8yDgyE9C,GAAA,CAAA4X,cAAA,CAAA9U,MAAqB,CAAC;UAAA,CAAC,CAAC;UA9yD1zEpH,EAAE,CAAAuG,cAAA,eA8yDi5E,CAAC;UA9yDp5EvG,EAAE,CAAAwG,UAAA,+BAAAyhB,oDAAA;YAAA,OA8yD43E3jB,GAAA,CAAAkY,iBAAA,CAAkB,CAAC;UAAA,CAAC,CAAC;UA9yDn5Exc,EAAE,CAAAuG,cAAA,eA8yDs8E,CAAC;UA9yDz8EvG,EAAE,CAAA+E,YAAA,EA8yDu+E,CAAC;UA9yD1+E/E,EAAE,CAAAyH,YAAA,CA8yDm/E,CAAC,CAAD,CAAC,CAAD,CAAC;UA9yDt/EzH,EAAE,CAAAuG,cAAA,oBA8yDsnG,CAAC;UA9yDznGvG,EAAE,CAAAwG,UAAA,uBAAA0hB,gDAAA9gB,MAAA;YAAA,OA8yDw/F9C,GAAA,CAAAkV,qBAAA,CAAsB,OAAO,EAAApS,MAAQ,CAAC;UAAA,CAAC,CAAC,mBAAA+gB,4CAAA;YAAA,OAAiB7jB,GAAA,CAAA0Z,qBAAA,CAAsB,OAAO,CAAC;UAAA,CAAhD,CAAC,sBAAAoK,+CAAA;YAAA,OAAqE9jB,GAAA,CAAA+U,aAAA,CAAc,CAAC;UAAA,CAArF,CAAC;UA9yDliGrZ,EAAE,CAAAuH,SAAA,aA8yDqrG,CAAC;UA9yDxrGvH,EAAE,CAAAyH,YAAA,CA8yDgsG,CAAC;QAAA;QAAA,IAAApD,EAAA;UA9yDnsGrE,EAAE,CAAA6H,WAAA,2CAAAvD,GAAA,CAAA0U,oBA8yDi8D,CAAC;UA9yDp8DhZ,EAAE,CAAA8F,UAAA,sBAAAxB,GAAA,CAAA0U,oBAAA,IAAA1U,GAAA,CAAAqE,aA8yDm3D,CAAC,aAAArE,GAAA,CAAA0U,oBAAA,QAAD,CAAC;UA9yDt3DhZ,EAAE,CAAA0I,SAAA,GA8yD25F,CAAC;UA9yD95F1I,EAAE,CAAA6H,WAAA,2CAAAvD,GAAA,CAAAyU,mBA8yD25F,CAAC;UA9yD95F/Y,EAAE,CAAA8F,UAAA,sBAAAxB,GAAA,CAAAyU,mBAAA,IAAAzU,GAAA,CAAAqE,aA8yD80F,CAAC,aAAArE,GAAA,CAAAyU,mBAAA,QAAD,CAAC;QAAA;MAAA;MAAA5I,YAAA,GAAysOhP,EAAE,CAACwf,SAAS,EAAwP3e,IAAI,CAAC4e,iBAAiB;MAAAxQ,MAAA;MAAAC,aAAA;IAAA,EAA0P;EAAE;AACppV;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KAhzDoGvM,EAAE,CAAAwM,iBAAA,CAgzDX6a,SAAS,EAAc,CAAC;IACvGlb,IAAI,EAAE3L,SAAS;IACfiM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAE6K,QAAQ,EAAE,yBAAyB;MAAE7I,MAAM,EAAE,CAAC,OAAO,CAAC;MAAEkC,IAAI,EAAE;QAC1F,aAAa,EAAE,YAAY;QAC3B,OAAO,EAAE,wCAAwC;QACjD,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE,gCAAgC;QAClE,0CAA0C,EAAE,aAAa;QACzD,qBAAqB,EAAE,wCAAwC;QAC/D,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,iCAAiC,EAAE,qCAAqC;QACxE,sCAAsC,EAAE;MAC5C,CAAC;MAAEP,aAAa,EAAE5P,iBAAiB,CAAC+P,IAAI;MAAEC,eAAe,EAAE/P,uBAAuB,CAACgQ,OAAO;MAAEd,QAAQ,EAAE,krDAAkrD;MAAEQ,MAAM,EAAE,CAAC,4xNAA4xN;IAAE,CAAC;EAC9kR,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjE,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAE9J,EAAE,CAACmM,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAC/FR,IAAI,EAAE9L;MACV,CAAC;IAAE,CAAC,EAAE;MAAE8L,IAAI,EAAEnM,EAAE,CAACmf;IAAO,CAAC,EAAE;MAAEhT,IAAI,EAAEnM,EAAE,CAACyO;IAAkB,CAAC,EAAE;MAAEtC,IAAI,EAAEtI,IAAI,CAACqb;IAAc,CAAC,EAAE;MAAE/S,IAAI,EAAErI,EAAE,CAACsb;IAAS,CAAC,EAAE;MAAEjT,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAC5IR,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACzI,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAEmI,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCR,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACqU,eAAe;MAC1B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE5Y,kBAAkB,EAAE,CAAC;MACjDiE,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAE6jB,WAAW,EAAE,CAAC;MACdjY,IAAI,EAAE5L,KAAK;MACXkM,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEhI,iBAAiB,EAAE,CAAC;MACpB0H,IAAI,EAAE5L;IACV,CAAC,CAAC;IAAEsR,MAAM,EAAE,CAAC;MACT1F,IAAI,EAAEpL,eAAe;MACrB0L,IAAI,EAAE,CAACxM,UAAU,CAAC,MAAMunB,UAAU,CAAC,EAAE;QAAE3G,WAAW,EAAE;MAAK,CAAC;IAC9D,CAAC,CAAC;IAAE3D,iBAAiB,EAAE,CAAC;MACpB/Q,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEqK,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEyG,QAAQ,EAAE,CAAC;MACXpR,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEqK,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEuH,aAAa,EAAE,CAAC;MAChBlS,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEqK,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAE2C,cAAc,EAAE,CAAC;MACjBtN,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE8M,kBAAkB,EAAE,CAAC;MACrBpN,IAAI,EAAExL,SAAS;MACf8L,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAM+a,UAAU,SAASJ,6BAA6B,CAAC;EACnD/c,WAAWA,CAACge,SAAS,EAAEzZ,UAAU,EAAEwX,mBAAmB,EAAEpJ,QAAQ,EAAEsL,YAAY,EAAE5I,aAAa,EAAE;IAC3F,KAAK,CAAC2I,SAAS,EAAEzZ,UAAU,EAAEwX,mBAAmB,EAAEpJ,QAAQ,EAAEsL,YAAY,EAAE5I,aAAa,CAAC;IACxF,IAAI,CAAC7G,UAAU,GAAG,IAAItW,OAAO,CAAC,CAAC;IAC/B8lB,SAAS,CAAClE,mBAAmB,CAACrZ,IAAI,CAAC7H,SAAS,CAAC,IAAI,CAAC4V,UAAU,CAAC,CAAC,CAAC5N,SAAS,CAAC/C,kBAAkB,IAAI;MAC3F,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAChD,CAAC,CAAC;EACN;EACAsD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqN,UAAU,CAAC7I,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC6I,UAAU,CAAC5K,QAAQ,CAAC,CAAC;IAC1B,KAAK,CAACzC,WAAW,CAAC,CAAC;EACvB;EACA;IAAS,IAAI,CAACE,IAAI,YAAA6c,mBAAA3c,CAAA;MAAA,YAAAA,CAAA,IAAwF4b,UAAU,EAp3DpBxnB,EAAE,CAAA6L,iBAAA,CAo3DoCwb,SAAS,GAp3D/CrnB,EAAE,CAAA6L,iBAAA,CAo3D0D7L,EAAE,CAACuO,UAAU,GAp3DzEvO,EAAE,CAAA6L,iBAAA,CAo3DoFrK,yBAAyB,MAp3D/GxB,EAAE,CAAAknB,iBAAA,CAo3D0I,UAAU,GAp3DtJlnB,EAAE,CAAA6L,iBAAA,CAo3DkL3J,EAAE,CAACilB,YAAY,GAp3DnMnnB,EAAE,CAAA6L,iBAAA,CAo3D8M7H,qBAAqB;IAAA,CAA4D;EAAE;EACnY;IAAS,IAAI,CAAC8K,IAAI,kBAr3D8E9O,EAAE,CAAA+O,iBAAA;MAAA5C,IAAA,EAq3DJqb,UAAU;MAAApb,SAAA;MAAAoD,SAAA;MAAAiG,QAAA;MAAAC,YAAA,WAAA8S,wBAAAnkB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAr3DRrE,EAAE,CAAAwG,UAAA,mBAAAiiB,oCAAA;YAAA,OAq3DJnkB,GAAA,CAAAqiB,YAAA,CAAa,CAAC;UAAA,uBAAA+B,sCAAAthB,MAAA;YAAA,OAAd9C,GAAA,CAAA4X,cAAA,CAAA9U,MAAqB,CAAC;UAAA;QAAA;QAAA,IAAA/C,EAAA;UAr3DpBrE,EAAE,CAAAmI,WAAA,kBAAA7D,GAAA,CAAAwiB,gBAAA,oBAAAxiB,GAAA,CAAA0iB,eAAA,qBAAA1iB,GAAA,CAAA2D,QAAA,mBAAA3D,GAAA,CAAAyiB,gBAAA,UAAAziB,GAAA,CAAAohB,EAAA,cAAAphB,GAAA,CAAA8D,YAAA,YAAA9D,GAAA,CAAAqhB,QAAA;UAAF3lB,EAAE,CAAA6H,WAAA,yBAAAvD,GAAA,CAAA2D,QAAA,qBAAA3D,GAAA,CAAAihB,MAAA;QAAA;MAAA;MAAA7W,MAAA;QAAAzG,QAAA;QAAAU,aAAA;QAAAqU,QAAA;QAAAuI,MAAA;QAAAG,EAAA;MAAA;MAAAnO,QAAA;MAAAlL,QAAA,GAAFrM,EAAE,CAAAsM,0BAAA;MAAAqb,KAAA,EAAA7d,IAAA;MAAA0N,kBAAA,EAAAxS,GAAA;MAAAyK,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA+Y,oBAAAtkB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAA0X,eAAA;UAAF1X,EAAE,CAAAuH,SAAA,aAq3DmwB,CAAC,YAAD,CAAC;UAr3DtwBvH,EAAE,CAAAuG,cAAA,aAq3Dk8B,CAAC,aAAD,CAAC;UAr3Dr8BvG,EAAE,CAAA+E,YAAA,EAq3DygC,CAAC;UAr3D5gC/E,EAAE,CAAAyH,YAAA,CAq3DohC,CAAC,CAAD,CAAC;QAAA;QAAA,IAAApD,EAAA;UAr3DvhCrE,EAAE,CAAA0I,SAAA,EAq3D42B,CAAC;UAr3D/2B1I,EAAE,CAAA8F,UAAA,qBAAAxB,GAAA,CAAAsK,UAAA,CAAAT,aAq3D42B,CAAC,sBAAA7J,GAAA,CAAA4hB,cAAD,CAAC;QAAA;MAAA;MAAA/V,YAAA,GAAssIhP,EAAE,CAACwf,SAAS;MAAAvQ,MAAA;MAAAC,aAAA;MAAAI,eAAA;IAAA,EAA6T;EAAE;AACp+K;AACA;EAAA,QAAAlE,SAAA,oBAAAA,SAAA,KAv3DoGvM,EAAE,CAAAwM,iBAAA,CAu3DXgb,UAAU,EAAc,CAAC;IACxGrb,IAAI,EAAE3L,SAAS;IACfiM,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,8BAA8B;MAAE6K,QAAQ,EAAE,YAAY;MAAE7I,MAAM,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC;MAAE+B,eAAe,EAAE/P,uBAAuB,CAACkoB,MAAM;MAAEvY,aAAa,EAAE5P,iBAAiB,CAAC+P,IAAI;MAAEI,IAAI,EAAE;QAChO,OAAO,EAAE,kDAAkD;QAC3D,sBAAsB,EAAE,oBAAoB;QAC5C,qBAAqB,EAAE,mBAAmB;QAC1C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,oBAAoB;QAC5C,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,gBAAgB;QACnC,aAAa,EAAE,YAAY;QAC3B,8BAA8B,EAAE,UAAU;QAC1C,yBAAyB,EAAE,QAAQ;QACnC,SAAS,EAAE,gBAAgB;QAC3B,WAAW,EAAE;MACjB,CAAC;MAAEhB,QAAQ,EAAE,uUAAuU;MAAEQ,MAAM,EAAE,CAAC,w9HAAw9H;IAAE,CAAC;EACt0I,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjE,IAAI,EAAEkb;IAAU,CAAC,EAAE;MAAElb,IAAI,EAAEnM,EAAE,CAACuO;IAAW,CAAC,EAAE;MAAEpC,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAC5GR,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACjL,yBAAyB;MACpC,CAAC;IAAE,CAAC,EAAE;MAAE2K,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCR,IAAI,EAAElL,SAAS;QACfwL,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEN,IAAI,EAAEjK,EAAE,CAACilB;IAAa,CAAC,EAAE;MAAEhb,IAAI,EAAES,SAAS;MAAED,UAAU,EAAE,CAAC;QAC7DR,IAAI,EAAE9L;MACV,CAAC,EAAE;QACC8L,IAAI,EAAEhM,MAAM;QACZsM,IAAI,EAAE,CAACzI,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA,MAAM6kB,cAAc,CAAC;EACjBxe,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACqb,EAAE,GAAI,qBAAoBP,YAAY,EAAG,EAAC;EACnD;EACA;IAAS,IAAI,CAACzZ,IAAI,YAAAod,uBAAAld,CAAA;MAAA,YAAAA,CAAA,IAAwFid,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAC/Z,IAAI,kBA95D8E9O,EAAE,CAAA+O,iBAAA;MAAA5C,IAAA,EA85DJ0c,cAAc;MAAAzc,SAAA;MAAAoD,SAAA,WAAqF,UAAU;MAAAiG,QAAA;MAAAC,YAAA,WAAAqT,4BAAA1kB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA95D3GrE,EAAE,CAAAmI,WAAA,oBAAA7D,GAAA,CAAAmhB,YAAA,QAAAnhB,GAAA,CAAAohB,EAAA;QAAA;MAAA;MAAAhX,MAAA;QAAAgX,EAAA;MAAA;MAAAnO,QAAA;MAAAC,kBAAA,EAAAxS,GAAA;MAAAyK,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAoZ,wBAAA3kB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFrE,EAAE,CAAA0X,eAAA;UAAF1X,EAAE,CAAA+E,YAAA,EA85DgT,CAAC;QAAA;MAAA;MAAAsL,aAAA;MAAAI,eAAA;IAAA,EAAkH;EAAE;AAC3gB;AACA;EAAA,QAAAlE,SAAA,oBAAAA,SAAA,KAh6DoGvM,EAAE,CAAAwM,iBAAA,CAg6DXqc,cAAc,EAAc,CAAC;IAC5G1c,IAAI,EAAE3L,SAAS;IACfiM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7B6K,QAAQ,EAAE,gBAAgB;MAC1B3H,QAAQ,EAAE,2BAA2B;MACrCgB,IAAI,EAAE;QACF,wBAAwB,EAAE,cAAc;QACxC,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,uBAAuB;QAChC,MAAM,EAAE;MACZ,CAAC;MACDP,aAAa,EAAE5P,iBAAiB,CAAC+P,IAAI;MACrCC,eAAe,EAAE/P,uBAAuB,CAACkoB;IAC7C,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElD,EAAE,EAAE,CAAC;MACnBvZ,IAAI,EAAE5L;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0oB,aAAa,CAAC;EAChB;IAAS,IAAI,CAACvd,IAAI,YAAAwd,sBAAAtd,CAAA;MAAA,YAAAA,CAAA,IAAwFqd,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAr7D8EnpB,EAAE,CAAAopB,gBAAA;MAAAjd,IAAA,EAq7DS8c;IAAa,EAuBlG;EAAE;EACxB;IAAS,IAAI,CAACI,IAAI,kBA78D8ErpB,EAAE,CAAAspB,gBAAA;MAAAC,OAAA,GA68DkCxpB,YAAY,EACxI0B,eAAe,EACfM,YAAY,EACZL,eAAe,EACfO,eAAe,EACfG,UAAU,EAAEX,eAAe;IAAA,EAAI;EAAE;AAC7C;AACA;EAAA,QAAA8K,SAAA,oBAAAA,SAAA,KAp9DoGvM,EAAE,CAAAwM,iBAAA,CAo9DXyc,aAAa,EAAc,CAAC;IAC3G9c,IAAI,EAAEjL,QAAQ;IACduL,IAAI,EAAE,CAAC;MACC8c,OAAO,EAAE,CACLxpB,YAAY,EACZ0B,eAAe,EACfM,YAAY,EACZL,eAAe,EACfO,eAAe,EACfG,UAAU,CACb;MACDonB,OAAO,EAAE,CACL/nB,eAAe,EACfqP,aAAa,EACbQ,WAAW,EACXyF,MAAM,EACNmN,WAAW,EACXmD,SAAS,EACTwB,cAAc,EACdrB,UAAU,CACb;MACDiC,YAAY,EAAE,CACV3Y,aAAa,EACbQ,WAAW,EACXyF,MAAM,EACNmN,WAAW,EACXmD,SAAS,EACTwB,cAAc,EACdrB,UAAU;MACV;MACAxb,UAAU,EACV5B,gBAAgB,EAChBiL,kBAAkB,EAClBwK,YAAY;IAEpB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASxO,OAAO,EAAEyP,eAAe,EAAEjQ,eAAe,EAAEgF,aAAa,EAAEzE,aAAa,EAAEQ,SAAS,EAAEoG,qBAAqB,EAAEjB,MAAM,EAAE/K,UAAU,EAAE5B,gBAAgB,EAAEsZ,iBAAiB,EAAE5S,aAAa,EAAEoT,WAAW,EAAErE,YAAY,EAAEvO,WAAW,EAAE+D,kBAAkB,EAAEmS,UAAU,EAAEH,SAAS,EAAEwB,cAAc,EAAEI,aAAa,EAAEtU,uBAAuB,EAAEJ,+BAA+B,EAAEuB,WAAW,EAAEjJ,eAAe,EAAEoU,gBAAgB,EAAE5B,iBAAiB,EAAEtK,uBAAuB,EAAEgR,eAAe,EAAEX,cAAc,EAAErb,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}