{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Recent Tasks Component\n * Displays a list of recently updated tasks\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\nexport let RecentTasksComponent = class RecentTasksComponent {\n  /**\n   * Constructor with dependency injection\n   * @param router - Router for navigation\n   */\n  constructor(router) {\n    this.router = router;\n    /**\n     * List of recent tasks\n     */\n    this.tasks = [];\n    /**\n     * Columns to display in the task table\n     */\n    this.displayedColumns = ['title', 'priority', 'status', 'updatedAt', 'actions'];\n  }\n  /**\n   * Navigate to task detail page\n   * @param taskId - ID of the task to view\n   */\n  viewTask(taskId) {\n    this.router.navigate(['/tasks', taskId]);\n  }\n  /**\n   * Get CSS class for task priority\n   * @param priority - Task priority\n   * @returns CSS class name\n   */\n  getPriorityClass(priority) {\n    switch (priority) {\n      case 'high':\n        return 'priority-high';\n      case 'medium':\n        return 'priority-medium';\n      case 'low':\n        return 'priority-low';\n      default:\n        return '';\n    }\n  }\n  /**\n   * Get CSS class for task status\n   * @param status - Task status\n   * @returns CSS class name\n   */\n  getStatusClass(status) {\n    switch (status) {\n      case 'todo':\n        return 'status-todo';\n      case 'in_progress':\n        return 'status-in-progress';\n      case 'done':\n        return 'status-done';\n      default:\n        return '';\n    }\n  }\n  /**\n   * Format task status for display\n   * @param status - Task status\n   * @returns Formatted status string\n   */\n  formatStatus(status) {\n    switch (status) {\n      case 'todo':\n        return 'To Do';\n      case 'in_progress':\n        return 'In Progress';\n      case 'done':\n        return 'Done';\n      default:\n        return status;\n    }\n  }\n};\n__decorate([Input()], RecentTasksComponent.prototype, \"tasks\", void 0);\nRecentTasksComponent = __decorate([Component({\n  selector: 'app-recent-tasks',\n  templateUrl: './recent-tasks.component.html',\n  styleUrls: ['./recent-tasks.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], RecentTasksComponent);", "map": {"version": 3, "names": ["Component", "Input", "ChangeDetectionStrategy", "RecentTasksComponent", "constructor", "router", "tasks", "displayedColumns", "viewTask", "taskId", "navigate", "getPriorityClass", "priority", "getStatusClass", "status", "formatStatus", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\recent-tasks\\recent-tasks.component.ts"], "sourcesContent": ["/**\n * Recent Tasks Component\n * Displays a list of recently updated tasks\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Task } from '../../../../core/models/task.model';\n\n@Component({\n  selector: 'app-recent-tasks',\n  templateUrl: './recent-tasks.component.html',\n  styleUrls: ['./recent-tasks.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class RecentTasksComponent {\n  /**\n   * List of recent tasks\n   */\n  @Input() tasks: Task[] = [];\n  \n  /**\n   * Columns to display in the task table\n   */\n  displayedColumns: string[] = ['title', 'priority', 'status', 'updatedAt', 'actions'];\n  \n  /**\n   * Constructor with dependency injection\n   * @param router - Router for navigation\n   */\n  constructor(private router: Router) {}\n  \n  /**\n   * Navigate to task detail page\n   * @param taskId - ID of the task to view\n   */\n  viewTask(taskId: string): void {\n    this.router.navigate(['/tasks', taskId]);\n  }\n  \n  /**\n   * Get CSS class for task priority\n   * @param priority - Task priority\n   * @returns CSS class name\n   */\n  getPriorityClass(priority: string): string {\n    switch (priority) {\n      case 'high':\n        return 'priority-high';\n      case 'medium':\n        return 'priority-medium';\n      case 'low':\n        return 'priority-low';\n      default:\n        return '';\n    }\n  }\n  \n  /**\n   * Get CSS class for task status\n   * @param status - Task status\n   * @returns CSS class name\n   */\n  getStatusClass(status: string): string {\n    switch (status) {\n      case 'todo':\n        return 'status-todo';\n      case 'in_progress':\n        return 'status-in-progress';\n      case 'done':\n        return 'status-done';\n      default:\n        return '';\n    }\n  }\n  \n  /**\n   * Format task status for display\n   * @param status - Task status\n   * @returns Formatted status string\n   */\n  formatStatus(status: string): string {\n    switch (status) {\n      case 'todo':\n        return 'To Do';\n      case 'in_progress':\n        return 'In Progress';\n      case 'done':\n        return 'Done';\n      default:\n        return status;\n    }\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,eAAe;AAUlE,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAW/B;;;;EAIAC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAd1B;;;IAGS,KAAAC,KAAK,GAAW,EAAE;IAE3B;;;IAGA,KAAAC,gBAAgB,GAAa,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;EAM/C;EAErC;;;;EAIAC,QAAQA,CAACC,MAAc;IACrB,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,QAAQ,EAAED,MAAM,CAAC,CAAC;EAC1C;EAEA;;;;;EAKAE,gBAAgBA,CAACC,QAAgB;IAC/B,QAAQA,QAAQ;MACd,KAAK,MAAM;QACT,OAAO,eAAe;MACxB,KAAK,QAAQ;QACX,OAAO,iBAAiB;MAC1B,KAAK,KAAK;QACR,OAAO,cAAc;MACvB;QACE,OAAO,EAAE;;EAEf;EAEA;;;;;EAKAC,cAAcA,CAACC,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,aAAa;MACtB,KAAK,aAAa;QAChB,OAAO,oBAAoB;MAC7B,KAAK,MAAM;QACT,OAAO,aAAa;MACtB;QACE,OAAO,EAAE;;EAEf;EAEA;;;;;EAKAC,YAAYA,CAACD,MAAc;IACzB,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,OAAO;MAChB,KAAK,aAAa;QAChB,OAAO,aAAa;MACtB,KAAK,MAAM;QACT,OAAO,MAAM;MACf;QACE,OAAOA,MAAM;;EAEnB;CACD;AA1EUE,UAAA,EAARf,KAAK,EAAE,C,kDAAoB;AAJjBE,oBAAoB,GAAAa,UAAA,EANhChB,SAAS,CAAC;EACTiB,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B,CAAC;EAC5CC,eAAe,EAAElB,uBAAuB,CAACmB;CAC1C,CAAC,C,EACWlB,oBAAoB,CA8EhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}