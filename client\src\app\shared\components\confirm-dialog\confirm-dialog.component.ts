/**
 * Confirm Dialog Component
 * Reusable dialog for confirming user actions
 */
import { Component, Inject, ChangeDetectionStrategy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

/**
 * Interface for dialog data
 */
export interface ConfirmDialogData {
  /**
   * Dialog title
   */
  title: string;
  
  /**
   * Dialog message
   */
  message: string;
  
  /**
   * Confirm button text
   */
  confirmText?: string;
  
  /**
   * Cancel button text
   */
  cancelText?: string;
  
  /**
   * Confirm button color
   */
  confirmColor?: 'primary' | 'accent' | 'warn';
}

@Component({
  selector: 'app-confirm-dialog',
  templateUrl: './confirm-dialog.component.html',
  styleUrls: ['./confirm-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConfirmDialogComponent {
  /**
   * Constructor with dependency injection
   * @param dialogRef - Reference to the dialog
   * @param data - Data passed to the dialog
   */
  constructor(
    public dialogRef: MatDialogRef<ConfirmDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDialogData
  ) {}

  /**
   * Close the dialog with false result (cancel)
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * Close the dialog with true result (confirm)
   */
  onConfirm(): void {
    this.dialogRef.close(true);
  }
}
