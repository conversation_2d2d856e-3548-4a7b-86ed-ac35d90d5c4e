{"ast": null, "code": "import { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class AuthInterceptor {\n  /**\n   * Intercepts HTTP requests and adds authentication token\n   * @param request - The outgoing HTTP request\n   * @param next - The next interceptor in the chain\n   * @returns Observable of HTTP event\n   */\n  intercept(request, next) {\n    // Get token from local storage\n    const token = localStorage.getItem('token');\n    // Only add token to API requests\n    if (token && request.url.startsWith(environment.apiUrl)) {\n      // Clone the request and add the authorization header\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    // Pass the modified request to the next handler\n    return next.handle(request);\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "AuthInterceptor", "intercept", "request", "next", "token", "localStorage", "getItem", "url", "startsWith", "apiUrl", "clone", "setHeaders", "Authorization", "handle", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["/**\n * Authentication Interceptor\n * Adds authentication token to outgoing HTTP requests\n */\nimport { Injectable } from '@angular/core';\nimport {\n  HttpRe<PERSON>,\n  <PERSON>ttpHandler,\n  HttpEvent,\n  HttpInterceptor\n} from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '@environments/environment';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  /**\n   * Intercepts HTTP requests and adds authentication token\n   * @param request - The outgoing HTTP request\n   * @param next - The next interceptor in the chain\n   * @returns Observable of HTTP event\n   */\n  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {\n    // Get token from local storage\n    const token = localStorage.getItem('token');\n    \n    // Only add token to API requests\n    if (token && request.url.startsWith(environment.apiUrl)) {\n      // Clone the request and add the authorization header\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    \n    // Pass the modified request to the next handler\n    return next.handle(request);\n  }\n}\n"], "mappings": "AAYA,SAASA,WAAW,QAAQ,2BAA2B;;AAGvD,OAAM,MAAOC,eAAe;EAC1B;;;;;;EAMAC,SAASA,CAACC,OAA6B,EAAEC,IAAiB;IACxD;IACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C;IACA,IAAIF,KAAK,IAAIF,OAAO,CAACK,GAAG,CAACC,UAAU,CAACT,WAAW,CAACU,MAAM,CAAC,EAAE;MACvD;MACAP,OAAO,GAAGA,OAAO,CAACQ,KAAK,CAAC;QACtBC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUR,KAAK;;OAEjC,CAAC;;IAGJ;IACA,OAAOD,IAAI,CAACU,MAAM,CAACX,OAAO,CAAC;EAC7B;;;uBAvBWF,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAAc,OAAA,EAAfd,eAAe,CAAAe;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}