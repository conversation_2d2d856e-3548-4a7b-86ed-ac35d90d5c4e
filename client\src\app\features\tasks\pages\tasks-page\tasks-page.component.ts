/**
 * Tasks Page Component
 * Main page for displaying and managing tasks
 */
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TaskService } from '../../../../core/services/task.service';
import { UserService } from '../../../../core/services/user.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { Task } from '../../../../core/models/task.model';
import { User } from '../../../../core/models/user.model';
import { TaskFilter } from '../../../../core/models/task-filter.model';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-tasks-page',
  templateUrl: './tasks-page.component.html',
  styleUrls: ['./tasks-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TasksPageComponent implements OnInit, OnDestroy {
  /**
   * List of tasks
   */
  tasks: Task[] = [];
  
  /**
   * List of users
   */
  users: User[] = [];
  
  /**
   * Current filter applied to tasks
   */
  filter: TaskFilter = {};
  
  /**
   * Loading state indicator
   */
  loading = false;
  
  /**
   * Error message if loading failed
   */
  error: string | null = null;
  
  /**
   * Subject for unsubscribing from observables
   */
  private destroy$ = new Subject<void>();

  /**
   * Constructor with dependency injection
   * @param taskService - Task service for CRUD operations
   * @param userService - User service for user data
   * @param notificationService - Notification service for displaying messages
   * @param dialog - Dialog service for confirmation dialogs
   * @param router - Router for navigation
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(
    private taskService: TaskService,
    private userService: UserService,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Loads tasks and users
   */
  ngOnInit(): void {
    this.loadTasks();
    this.loadUsers();
  }

  /**
   * Lifecycle hook that is called before component destruction
   * Unsubscribes from observables
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load tasks with current filter
   */
  loadTasks(): void {
    this.loading = true;
    this.error = null;
    this.cdr.markForCheck();
    
    this.taskService.getTasks(this.filter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tasks) => {
          this.tasks = tasks;
          this.loading = false;
          this.cdr.markForCheck();
        },
        error: (err) => {
          this.error = 'Failed to load tasks. Please try again.';
          this.loading = false;
          console.error('Error loading tasks:', err);
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Load users for assignee selection
   */
  loadUsers(): void {
    this.userService.getUsers()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (users) => {
          this.users = users;
          this.cdr.markForCheck();
        },
        error: (err) => {
          console.error('Error loading users:', err);
        }
      });
  }

  /**
   * Handle task selection
   * @param task - Selected task
   */
  onTaskSelected(task: Task): void {
    this.router.navigate(['/tasks', task.id]);
  }

  /**
   * Handle task deletion
   * @param taskId - ID of task to delete
   */
  onTaskDeleted(taskId: string): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Delete Task',
        message: 'Are you sure you want to delete this task? This action cannot be undone.',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        confirmColor: 'warn'
      }
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.taskService.deleteTask(taskId)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.tasks = this.tasks.filter(task => task.id !== taskId);
              this.notificationService.success('Task deleted successfully');
              this.cdr.markForCheck();
            },
            error: (err) => {
              this.notificationService.error('Failed to delete task');
              console.error('Error deleting task:', err);
            }
          });
      }
    });
  }

  /**
   * Handle task status change
   * @param event - Object containing taskId and status
   */
  onStatusChanged(event: { taskId: string; status: string }): void {
    const { taskId, status } = event;
    
    this.taskService.updateTask(taskId, { status })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedTask) => {
          // Update task in list
          this.tasks = this.tasks.map(task => 
            task.id === taskId ? updatedTask : task
          );
          this.notificationService.success('Task status updated');
          this.cdr.markForCheck();
        },
        error: (err) => {
          this.notificationService.error('Failed to update task status');
          console.error('Error updating task status:', err);
        }
      });
  }

  /**
   * Handle filter changes
   * @param filter - New filter
   */
  onFilterChanged(filter: TaskFilter): void {
    this.filter = filter;
    this.loadTasks();
  }

  /**
   * Handle refresh request
   */
  onRefresh(): void {
    this.loadTasks();
  }
}
