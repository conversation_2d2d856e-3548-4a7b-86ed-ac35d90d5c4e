/**
 * Task form component styles
 */

/* Container for the entire task form */
.task-form-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

/* Loading state container */
.task-form-loading {
  padding: 32px;
  display: flex;
  justify-content: center;
}

/* Task form */
.task-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Full width form fields */
.full-width {
  width: 100%;
}

/* Form row for side-by-side fields */
.form-row {
  display: flex;
  gap: 16px;
  
  mat-form-field {
    flex: 1;
  }
}

/* Form actions container */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
}
