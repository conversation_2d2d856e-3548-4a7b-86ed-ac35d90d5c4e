{"ast": null, "code": "/**\n * Forgot Password Form Component\n * Handles password reset request with email\n */\nimport { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nimport * as i8 from \"../../../../shared/components/error-message/error-message.component\";\nfunction ForgotPasswordFormComponent_app_error_message_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-error-message\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"message\", ctx_r0.error);\n  }\n}\nfunction ForgotPasswordFormComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.success);\n  }\n}\nfunction ForgotPasswordFormComponent_form_3_mat_error_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordFormComponent_form_3_mat_error_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordFormComponent_form_3_mat_spinner_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 16);\n  }\n}\nfunction ForgotPasswordFormComponent_form_3_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Reset Password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordFormComponent_form_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 7);\n    i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordFormComponent_form_3_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"mat-form-field\", 8)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 9);\n    i0.ɵɵelementStart(5, \"mat-icon\", 10);\n    i0.ɵɵtext(6, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ForgotPasswordFormComponent_form_3_mat_error_7_Template, 2, 0, \"mat-error\", 11);\n    i0.ɵɵtemplate(8, ForgotPasswordFormComponent_form_3_mat_error_8_Template, 2, 0, \"mat-error\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 12);\n    i0.ɵɵtemplate(10, ForgotPasswordFormComponent_form_3_mat_spinner_10_Template, 1, 0, \"mat-spinner\", 13);\n    i0.ɵɵtemplate(11, ForgotPasswordFormComponent_form_3_span_11_Template, 2, 0, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 14);\n    i0.ɵɵtext(13, \" Remember your password? \");\n    i0.ɵɵelementStart(14, \"a\", 15);\n    i0.ɵɵlistener(\"click\", function ForgotPasswordFormComponent_form_3_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onLogin());\n    });\n    i0.ɵɵtext(15, \"Back to Login\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.forgotPasswordForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasError(\"email\", \"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasError(\"email\", \"email\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading);\n  }\n}\nfunction ForgotPasswordFormComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ForgotPasswordFormComponent_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onLogin());\n    });\n    i0.ɵɵtext(1, \" Back to Login \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ForgotPasswordFormComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed request\n     */\n    this.error = null;\n    /**\n     * Success message after successful request\n     */\n    this.success = null;\n    /**\n     * Event emitted when form is submitted\n     */\n    this.formSubmit = new EventEmitter();\n    /**\n     * Event emitted when login link is clicked\n     */\n    this.login = new EventEmitter();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the forgot password form\n   */\n  ngOnInit() {\n    this.initForm();\n  }\n  /**\n   * Initialize forgot password form with validation\n   */\n  initForm() {\n    this.forgotPasswordForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]]\n    });\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.forgotPasswordForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.forgotPasswordForm.markAllAsTouched();\n      return;\n    }\n    const {\n      email\n    } = this.forgotPasswordForm.value;\n    this.formSubmit.emit({\n      email\n    });\n  }\n  /**\n   * Handle login link click\n   */\n  onLogin() {\n    this.login.emit();\n  }\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName, errorName) {\n    const control = this.forgotPasswordForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n  static {\n    this.ɵfac = function ForgotPasswordFormComponent_Factory(t) {\n      return new (t || ForgotPasswordFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotPasswordFormComponent,\n      selectors: [[\"app-forgot-password-form\"]],\n      inputs: {\n        loading: \"loading\",\n        error: \"error\",\n        success: \"success\"\n      },\n      outputs: {\n        formSubmit: \"formSubmit\",\n        login: \"login\"\n      },\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"forgot-password-form-container\"], [3, \"message\", 4, \"ngIf\"], [\"class\", \"success-message\", 4, \"ngIf\"], [\"class\", \"forgot-password-form\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"class\", \"back-to-login-button\", 3, \"click\", 4, \"ngIf\"], [3, \"message\"], [1, \"success-message\"], [1, \"forgot-password-form\", 3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\"], [\"matPrefix\", \"\"], [4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"login-link\"], [3, \"click\"], [\"diameter\", \"20\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"back-to-login-button\", 3, \"click\"]],\n      template: function ForgotPasswordFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ForgotPasswordFormComponent_app_error_message_1_Template, 1, 1, \"app-error-message\", 1);\n          i0.ɵɵtemplate(2, ForgotPasswordFormComponent_div_2_Template, 5, 1, \"div\", 2);\n          i0.ɵɵtemplate(3, ForgotPasswordFormComponent_form_3_Template, 16, 6, \"form\", 3);\n          i0.ɵɵtemplate(4, ForgotPasswordFormComponent_button_4_Template, 2, 0, \"button\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.success);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.success);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.success);\n        }\n      },\n      dependencies: [i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.MatButton, i4.MatFormField, i4.MatLabel, i4.MatError, i4.MatPrefix, i5.MatIcon, i6.MatInput, i7.MatProgressSpinner, i8.ErrorMessageComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.forgot-password-form-container[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  padding: 24px;\\n  max-width: 400px;\\n  width: 100%;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.forgot-password-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n\\n\\n.success-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  background-color: #e8f5e9;\\n  border-radius: 4px;\\n  margin-bottom: 24px;\\n}\\n.success-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin-right: 12px;\\n}\\n.success-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.submit-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1rem;\\n  margin-top: 8px;\\n}\\n.submit-button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.back-to-login-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1rem;\\n  width: 100%;\\n}\\n\\n\\n\\n.login-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 16px;\\n  font-size: 0.9rem;\\n}\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  cursor: pointer;\\n  margin-left: 4px;\\n}\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .forgot-password-form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    box-shadow: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "error", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "success", "ɵɵlistener", "ForgotPasswordFormComponent_form_3_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ɵɵtemplate", "ForgotPasswordFormComponent_form_3_mat_error_7_Template", "ForgotPasswordFormComponent_form_3_mat_error_8_Template", "ForgotPasswordFormComponent_form_3_mat_spinner_10_Template", "ForgotPasswordFormComponent_form_3_span_11_Template", "ForgotPasswordFormComponent_form_3_Template_a_click_14_listener", "ctx_r10", "onLogin", "ctx_r2", "forgotPasswordForm", "<PERSON><PERSON><PERSON><PERSON>", "loading", "ForgotPasswordFormComponent_button_4_Template_button_click_0_listener", "_r12", "ctx_r11", "ForgotPasswordFormComponent", "constructor", "fb", "formSubmit", "login", "ngOnInit", "initForm", "group", "email", "required", "invalid", "mark<PERSON>llAsTouched", "value", "emit", "controlName", "errorName", "control", "get", "touched", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ForgotPasswordFormComponent_Template", "rf", "ctx", "ForgotPasswordFormComponent_app_error_message_1_Template", "ForgotPasswordFormComponent_div_2_Template", "ForgotPasswordFormComponent_form_3_Template", "ForgotPasswordFormComponent_button_4_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\forgot-password-form\\forgot-password-form.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\forgot-password-form\\forgot-password-form.component.html"], "sourcesContent": ["/**\n * Forgot Password Form Component\n * Handles password reset request with email\n */\nimport { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\n\n@Component({\n  selector: 'app-forgot-password-form',\n  templateUrl: './forgot-password-form.component.html',\n  styleUrls: ['./forgot-password-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ForgotPasswordFormComponent implements OnInit {\n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Error message from failed request\n   */\n  @Input() error: string | null = null;\n  \n  /**\n   * Success message after successful request\n   */\n  @Input() success: string | null = null;\n  \n  /**\n   * Event emitted when form is submitted\n   */\n  @Output() formSubmit = new EventEmitter<{ email: string }>();\n  \n  /**\n   * Event emitted when login link is clicked\n   */\n  @Output() login = new EventEmitter<void>();\n  \n  /**\n   * Forgot password form group\n   */\n  forgotPasswordForm!: FormGroup;\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the forgot password form\n   */\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  /**\n   * Initialize forgot password form with validation\n   */\n  private initForm(): void {\n    this.forgotPasswordForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]]\n    });\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.forgotPasswordForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.forgotPasswordForm.markAllAsTouched();\n      return;\n    }\n    \n    const { email } = this.forgotPasswordForm.value;\n    this.formSubmit.emit({ email });\n  }\n\n  /**\n   * Handle login link click\n   */\n  onLogin(): void {\n    this.login.emit();\n  }\n\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName: string, errorName: string): boolean {\n    const control = this.forgotPasswordForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n}\n", "<!-- Forgot password form container -->\n<div class=\"forgot-password-form-container\">\n  <!-- Error message -->\n  <app-error-message \n    *ngIf=\"error\" \n    [message]=\"error\">\n  </app-error-message>\n\n  <!-- Success message -->\n  <div *ngIf=\"success\" class=\"success-message\">\n    <mat-icon>check_circle</mat-icon>\n    <span>{{ success }}</span>\n  </div>\n\n  <!-- Forgot password form -->\n  <form [formGroup]=\"forgotPasswordForm\" (ngSubmit)=\"onSubmit()\" class=\"forgot-password-form\" *ngIf=\"!success\">\n    <!-- Email field -->\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\n      <mat-label>Email</mat-label>\n      <input \n        matInput \n        type=\"email\" \n        formControlName=\"email\" \n        placeholder=\"Enter your email\"\n        autocomplete=\"email\">\n      <mat-icon matPrefix>email</mat-icon>\n      <mat-error *ngIf=\"hasError('email', 'required')\">\n        Email is required\n      </mat-error>\n      <mat-error *ngIf=\"hasError('email', 'email')\">\n        Please enter a valid email address\n      </mat-error>\n    </mat-form-field>\n\n    <!-- Submit button -->\n    <button \n      mat-raised-button \n      color=\"primary\" \n      type=\"submit\" \n      class=\"submit-button\"\n      [disabled]=\"loading\">\n      <mat-spinner diameter=\"20\" *ngIf=\"loading\"></mat-spinner>\n      <span *ngIf=\"!loading\">Reset Password</span>\n    </button>\n\n    <!-- Login link -->\n    <div class=\"login-link\">\n      Remember your password?\n      <a (click)=\"onLogin()\">Back to Login</a>\n    </div>\n  </form>\n\n  <!-- Back to login button (shown after success) -->\n  <button \n    *ngIf=\"success\"\n    mat-raised-button \n    color=\"primary\" \n    (click)=\"onLogin()\" \n    class=\"back-to-login-button\">\n    Back to Login\n  </button>\n</div>\n"], "mappings": "AAAA;;;;AAIA,SAAoCA,YAAY,QAAwC,eAAe;AACvG,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;ICFjEC,EAAA,CAAAC,SAAA,2BAGoB;;;;IADlBD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,KAAA,CAAiB;;;;;IAInBJ,EAAA,CAAAK,cAAA,aAA6C;IACjCL,EAAA,CAAAM,MAAA,mBAAY;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACjCP,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,GAAa;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;IAApBP,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAa;;;;;IAejBX,EAAA,CAAAK,cAAA,gBAAiD;IAC/CL,EAAA,CAAAM,MAAA,0BACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAA8C;IAC5CL,EAAA,CAAAM,MAAA,2CACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAUZP,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAK,cAAA,WAAuB;IAAAL,EAAA,CAAAM,MAAA,qBAAc;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;;;IA3BhDP,EAAA,CAAAK,cAAA,cAA6G;IAAtEL,EAAA,CAAAY,UAAA,sBAAAC,qEAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAYjB,EAAA,CAAAkB,WAAA,CAAAF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAE5DnB,EAAA,CAAAK,cAAA,wBAAwD;IAC3CL,EAAA,CAAAM,MAAA,YAAK;IAAAN,EAAA,CAAAO,YAAA,EAAY;IAC5BP,EAAA,CAAAC,SAAA,eAKuB;IACvBD,EAAA,CAAAK,cAAA,mBAAoB;IAAAL,EAAA,CAAAM,MAAA,YAAK;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACpCP,EAAA,CAAAoB,UAAA,IAAAC,uDAAA,wBAEY;IACZrB,EAAA,CAAAoB,UAAA,IAAAE,uDAAA,wBAEY;IACdtB,EAAA,CAAAO,YAAA,EAAiB;IAGjBP,EAAA,CAAAK,cAAA,iBAKuB;IACrBL,EAAA,CAAAoB,UAAA,KAAAG,0DAAA,0BAAyD;IACzDvB,EAAA,CAAAoB,UAAA,KAAAI,mDAAA,mBAA4C;IAC9CxB,EAAA,CAAAO,YAAA,EAAS;IAGTP,EAAA,CAAAK,cAAA,eAAwB;IACtBL,EAAA,CAAAM,MAAA,iCACA;IAAAN,EAAA,CAAAK,cAAA,aAAuB;IAApBL,EAAA,CAAAY,UAAA,mBAAAa,gEAAA;MAAAzB,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAW,OAAA,GAAA1B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAQ,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAAC3B,EAAA,CAAAM,MAAA,qBAAa;IAAAN,EAAA,CAAAO,YAAA,EAAI;;;;IAjCtCP,EAAA,CAAAE,UAAA,cAAA0B,MAAA,CAAAC,kBAAA,CAAgC;IAWtB7B,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAE,UAAA,SAAA0B,MAAA,CAAAE,QAAA,sBAAmC;IAGnC9B,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAE,UAAA,SAAA0B,MAAA,CAAAE,QAAA,mBAAgC;IAW5C9B,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAE,UAAA,aAAA0B,MAAA,CAAAG,OAAA,CAAoB;IACQ/B,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAAE,UAAA,SAAA0B,MAAA,CAAAG,OAAA,CAAa;IAClC/B,EAAA,CAAAQ,SAAA,GAAc;IAAdR,EAAA,CAAAE,UAAA,UAAA0B,MAAA,CAAAG,OAAA,CAAc;;;;;;IAWzB/B,EAAA,CAAAK,cAAA,iBAK+B;IAD7BL,EAAA,CAAAY,UAAA,mBAAAoB,sEAAA;MAAAhC,EAAA,CAAAc,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAgB,OAAA,CAAAP,OAAA,EAAS;IAAA,EAAC;IAEnB3B,EAAA,CAAAM,MAAA,sBACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;AD/CX,OAAM,MAAO4B,2BAA2B;EA+BtC;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAlCtB;;;IAGS,KAAAN,OAAO,GAAG,KAAK;IAExB;;;IAGS,KAAA3B,KAAK,GAAkB,IAAI;IAEpC;;;IAGS,KAAAO,OAAO,GAAkB,IAAI;IAEtC;;;IAGU,KAAA2B,UAAU,GAAG,IAAIxC,YAAY,EAAqB;IAE5D;;;IAGU,KAAAyC,KAAK,GAAG,IAAIzC,YAAY,EAAQ;EAWJ;EAEtC;;;;EAIA0C,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEA;;;EAGQA,QAAQA,CAAA;IACd,IAAI,CAACZ,kBAAkB,GAAG,IAAI,CAACQ,EAAE,CAACK,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC5C,UAAU,CAAC6C,QAAQ,EAAE7C,UAAU,CAAC4C,KAAK,CAAC;KACpD,CAAC;EACJ;EAEA;;;EAGAxB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACU,kBAAkB,CAACgB,OAAO,IAAI,IAAI,CAACd,OAAO,EAAE;MACnD;MACA,IAAI,CAACF,kBAAkB,CAACiB,gBAAgB,EAAE;MAC1C;;IAGF,MAAM;MAAEH;IAAK,CAAE,GAAG,IAAI,CAACd,kBAAkB,CAACkB,KAAK;IAC/C,IAAI,CAACT,UAAU,CAACU,IAAI,CAAC;MAAEL;IAAK,CAAE,CAAC;EACjC;EAEA;;;EAGAhB,OAAOA,CAAA;IACL,IAAI,CAACY,KAAK,CAACS,IAAI,EAAE;EACnB;EAEA;;;;;;EAMAlB,QAAQA,CAACmB,WAAmB,EAAEC,SAAiB;IAC7C,MAAMC,OAAO,GAAG,IAAI,CAACtB,kBAAkB,CAACuB,GAAG,CAACH,WAAW,CAAC;IACxD,OAAO,CAAC,EAAEE,OAAO,IAAIA,OAAO,CAACE,OAAO,IAAIF,OAAO,CAACrB,QAAQ,CAACoB,SAAS,CAAC,CAAC;EACtE;;;uBApFWf,2BAA2B,EAAAnC,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA3BrB,2BAA2B;MAAAsB,SAAA;MAAAC,MAAA;QAAA3B,OAAA;QAAA3B,KAAA;QAAAO,OAAA;MAAA;MAAAgD,OAAA;QAAArB,UAAA;QAAAC,KAAA;MAAA;MAAAqB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZxCjE,EAAA,CAAAK,cAAA,aAA4C;UAE1CL,EAAA,CAAAoB,UAAA,IAAA+C,wDAAA,+BAGoB;UAGpBnE,EAAA,CAAAoB,UAAA,IAAAgD,0CAAA,iBAGM;UAGNpE,EAAA,CAAAoB,UAAA,IAAAiD,2CAAA,mBAmCO;UAGPrE,EAAA,CAAAoB,UAAA,IAAAkD,6CAAA,oBAOS;UACXtE,EAAA,CAAAO,YAAA,EAAM;;;UAzDDP,EAAA,CAAAQ,SAAA,GAAW;UAAXR,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAA9D,KAAA,CAAW;UAKRJ,EAAA,CAAAQ,SAAA,GAAa;UAAbR,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAvD,OAAA,CAAa;UAM0EX,EAAA,CAAAQ,SAAA,GAAc;UAAdR,EAAA,CAAAE,UAAA,UAAAgE,GAAA,CAAAvD,OAAA,CAAc;UAuCxGX,EAAA,CAAAQ,SAAA,GAAa;UAAbR,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAvD,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}