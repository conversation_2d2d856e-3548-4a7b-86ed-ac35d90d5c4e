{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function sample(notifier) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue = null;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      lastValue = value;\n    }));\n    innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, () => {\n      if (hasValue) {\n        hasValue = false;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    }, noop));\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "operate", "noop", "createOperatorSubscriber", "sample", "notifier", "source", "subscriber", "hasValue", "lastValue", "subscribe", "value", "next"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/esm/internal/operators/sample.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function sample(notifier) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        let lastValue = null;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            lastValue = value;\n        }));\n        innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, () => {\n            if (hasValue) {\n                hasValue = false;\n                const value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n        }, noop));\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,MAAMA,CAACC,QAAQ,EAAE;EAC7B,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpBH,MAAM,CAACI,SAAS,CAACP,wBAAwB,CAACI,UAAU,EAAGI,KAAK,IAAK;MAC7DH,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGE,KAAK;IACrB,CAAC,CAAC,CAAC;IACHX,SAAS,CAACK,QAAQ,CAAC,CAACK,SAAS,CAACP,wBAAwB,CAACI,UAAU,EAAE,MAAM;MACrE,IAAIC,QAAQ,EAAE;QACVA,QAAQ,GAAG,KAAK;QAChB,MAAMG,KAAK,GAAGF,SAAS;QACvBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACK,IAAI,CAACD,KAAK,CAAC;MAC1B;IACJ,CAAC,EAAET,IAAI,CAAC,CAAC;EACb,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}