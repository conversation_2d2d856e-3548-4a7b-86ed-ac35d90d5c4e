{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { CoreModule } from './core/core.module';\nimport { SharedModule } from './shared/shared.module';\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: AuthInterceptor,\n        multi: true\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: ErrorInterceptor,\n        multi: true\n      }],\n      imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, AppRoutingModule, CoreModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, AppRoutingModule, CoreModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "AppRoutingModule", "AppComponent", "CoreModule", "SharedModule", "AuthInterceptor", "ErrorInterceptor", "AppModule", "bootstrap", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\app.module.ts"], "sourcesContent": ["/**\n * Main application module\n * Imports and configures all necessary modules for the application\n */\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { CoreModule } from './core/core.module';\nimport { SharedModule } from './shared/shared.module';\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\n\n@NgModule({\n  declarations: [\n    AppComponent\n  ],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    HttpClientModule,\n    AppRoutingModule,\n    CoreModule,\n    SharedModule\n  ],\n  providers: [\n    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },\n    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AAKA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAE1E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,gBAAgB,QAAQ,uCAAuC;;AAoBxE,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRN,YAAY;IAAA;EAAA;;;iBAJb,CACT;QAAEO,OAAO,EAAET,iBAAiB;QAAEU,QAAQ,EAAEL,eAAe;QAAEM,KAAK,EAAE;MAAI,CAAE,EACtE;QAAEF,OAAO,EAAET,iBAAiB;QAAEU,QAAQ,EAAEJ,gBAAgB;QAAEK,KAAK,EAAE;MAAI,CAAE,CACxE;MAAAC,OAAA,GAVCf,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBE,gBAAgB,EAChBE,UAAU,EACVC,YAAY;IAAA;EAAA;;;2EAQHG,SAAS;IAAAM,YAAA,GAhBlBX,YAAY;IAAAU,OAAA,GAGZf,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBE,gBAAgB,EAChBE,UAAU,EACVC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}