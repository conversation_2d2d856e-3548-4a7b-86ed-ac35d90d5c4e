{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/icon\";\nfunction TaskStatusChartComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"insert_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No data available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskStatusChartComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵtext(2, \"To Do\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"div\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"height\", ctx_r2.getBarHeight(ctx_r2.todoCount), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.todoCount);\n  }\n}\nfunction TaskStatusChartComponent_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵtext(2, \"In Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"div\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"height\", ctx_r3.getBarHeight(ctx_r3.inProgressCount), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.inProgressCount);\n  }\n}\nfunction TaskStatusChartComponent_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵtext(2, \"Done\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"div\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"height\", ctx_r4.getBarHeight(ctx_r4.doneCount), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.doneCount);\n  }\n}\nfunction TaskStatusChartComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n    i0.ɵɵtemplate(2, TaskStatusChartComponent_div_2_div_2_Template, 6, 3, \"div\", 6);\n    i0.ɵɵtemplate(3, TaskStatusChartComponent_div_2_div_3_Template, 6, 3, \"div\", 6);\n    i0.ɵɵtemplate(4, TaskStatusChartComponent_div_2_div_4_Template, 6, 3, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8);\n    i0.ɵɵelement(7, \"div\", 9);\n    i0.ɵɵelementStart(8, \"div\", 10);\n    i0.ɵɵtext(9, \"To Do\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 8);\n    i0.ɵɵelement(11, \"div\", 11);\n    i0.ɵɵelementStart(12, \"div\", 10);\n    i0.ɵɵtext(13, \"In Progress\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 8);\n    i0.ɵɵelement(15, \"div\", 12);\n    i0.ɵɵelementStart(16, \"div\", 10);\n    i0.ɵɵtext(17, \"Done\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.todoCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.inProgressCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.doneCount > 0);\n  }\n}\nexport class TaskStatusChartComponent {\n  constructor() {\n    /**\n     * Number of tasks with 'todo' status\n     */\n    this.todoCount = 0;\n    /**\n     * Number of tasks with 'in_progress' status\n     */\n    this.inProgressCount = 0;\n    /**\n     * Number of tasks with 'done' status\n     */\n    this.doneCount = 0;\n    /**\n     * Chart data for rendering\n     */\n    this.chartData = [];\n    /**\n     * Chart view dimensions\n     */\n    this.view = [300, 200];\n    /**\n     * Chart color scheme\n     */\n    this.colorScheme = {\n      domain: ['#ff9800', '#2196f3', '#4caf50']\n    };\n    /**\n     * Flag to show/hide chart labels\n     */\n    this.showLabels = true;\n    /**\n     * Flag to enable/disable chart animations\n     */\n    this.animations = true;\n    /**\n     * Flag to show/hide legend\n     */\n    this.showLegend = true;\n    /**\n     * Flag to enable/disable gradient fills\n     */\n    this.gradient = false;\n  }\n  /**\n   * Lifecycle hook that is called when input properties change\n   * Updates chart data when counts change\n   * @param changes - Input changes\n   */\n  ngOnChanges(changes) {\n    this.updateChartData();\n  }\n  /**\n   * Update chart data based on current counts\n   */\n  updateChartData() {\n    this.chartData = [{\n      name: 'To Do',\n      value: this.todoCount\n    }, {\n      name: 'In Progress',\n      value: this.inProgressCount\n    }, {\n      name: 'Done',\n      value: this.doneCount\n    }];\n  }\n  /**\n   * Calculate the height percentage for a bar based on its count\n   * @param count - The count value for the bar\n   * @returns Percentage height (0-100)\n   */\n  getBarHeight(count) {\n    const total = this.todoCount + this.inProgressCount + this.doneCount;\n    if (total === 0) return 0;\n    // Calculate percentage with a minimum height of 5% for visibility\n    const percentage = count / total * 100;\n    return Math.max(percentage, 5);\n  }\n  static {\n    this.ɵfac = function TaskStatusChartComponent_Factory(t) {\n      return new (t || TaskStatusChartComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskStatusChartComponent,\n      selectors: [[\"app-task-status-chart\"]],\n      inputs: {\n        todoCount: \"todoCount\",\n        inProgressCount: \"inProgressCount\",\n        doneCount: \"doneCount\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"chart-container\"], [\"class\", \"empty-chart\", 4, \"ngIf\"], [\"class\", \"chart\", 4, \"ngIf\"], [1, \"empty-chart\"], [1, \"chart\"], [1, \"chart-bars\"], [\"class\", \"chart-bar-container\", 4, \"ngIf\"], [1, \"chart-legend\"], [1, \"legend-item\"], [1, \"legend-color\", \"todo-color\"], [1, \"legend-label\"], [1, \"legend-color\", \"in-progress-color\"], [1, \"legend-color\", \"done-color\"], [1, \"chart-bar-container\"], [1, \"chart-label\"], [1, \"chart-bar\", \"todo-bar\"], [1, \"chart-value\"], [1, \"chart-bar\", \"in-progress-bar\"], [1, \"chart-bar\", \"done-bar\"]],\n      template: function TaskStatusChartComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TaskStatusChartComponent_div_1_Template, 5, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, TaskStatusChartComponent_div_2_Template, 18, 3, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.todoCount === 0 && ctx.inProgressCount === 0 && ctx.doneCount === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.todoCount > 0 || ctx.inProgressCount > 0 || ctx.doneCount > 0);\n        }\n      },\n      dependencies: [i1.NgIf, i2.MatIcon],\n      styles: [\"\\n\\n\\n\\n\\n\\n.chart-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  min-height: 250px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n\\n\\n.chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n\\n\\n.chart-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  align-items: flex-end;\\n  width: 100%;\\n  height: 180px;\\n  margin-bottom: 16px;\\n}\\n\\n\\n\\n.chart-bar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 30%;\\n  max-width: 80px;\\n}\\n\\n\\n\\n.chart-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 4px 4px 0 0;\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  padding-top: 8px;\\n  transition: height 0.3s ease;\\n  min-height: 20px;\\n}\\n\\n\\n\\n.todo-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 152, 0, 0.7);\\n}\\n\\n.in-progress-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(33, 150, 243, 0.7);\\n}\\n\\n.done-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.7);\\n}\\n\\n\\n\\n.chart-value[_ngcontent-%COMP%] {\\n  color: white;\\n  font-weight: 500;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.chart-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 0.8rem;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n\\n\\n.chart-legend[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n  margin-top: 16px;\\n}\\n\\n\\n\\n.legend-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0 8px 8px;\\n}\\n\\n\\n\\n.legend-color[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 2px;\\n  margin-right: 4px;\\n}\\n\\n\\n\\n.todo-color[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 152, 0, 0.7);\\n}\\n\\n.in-progress-color[_ngcontent-%COMP%] {\\n  background-color: rgba(33, 150, 243, 0.7);\\n}\\n\\n.done-color[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.7);\\n}\\n\\n\\n\\n.legend-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n\\n\\n.empty-chart[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  color: #9e9e9e;\\n}\\n.empty-chart[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n}\\n.empty-chart[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 600px) {\\n  .chart-bars[_ngcontent-%COMP%] {\\n    height: 150px;\\n  }\\n  .chart-bar-container[_ngcontent-%COMP%] {\\n    width: 28%;\\n  }\\n  .chart-value[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .chart-label[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "ctx_r2", "getBarHeight", "todoCount", "ɵɵtextInterpolate", "ctx_r3", "inProgressCount", "ctx_r4", "doneCount", "ɵɵtemplate", "TaskStatusChartComponent_div_2_div_2_Template", "TaskStatusChartComponent_div_2_div_3_Template", "TaskStatusChartComponent_div_2_div_4_Template", "ɵɵelement", "ɵɵproperty", "ctx_r1", "TaskStatusChartComponent", "constructor", "chartData", "view", "colorScheme", "domain", "showLabels", "animations", "showLegend", "gradient", "ngOnChanges", "changes", "updateChartData", "name", "value", "count", "total", "percentage", "Math", "max", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "TaskStatusChartComponent_Template", "rf", "ctx", "TaskStatusChartComponent_div_1_Template", "TaskStatusChartComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\task-status-chart\\task-status-chart.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\task-status-chart\\task-status-chart.component.html"], "sourcesContent": ["/**\n * Task Status Chart Component\n * Displays a chart showing task distribution by status\n */\nimport { Component, Input, OnChanges, SimpleChanges, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-task-status-chart',\n  templateUrl: './task-status-chart.component.html',\n  styleUrls: ['./task-status-chart.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskStatusChartComponent implements OnChanges {\n  /**\n   * Number of tasks with 'todo' status\n   */\n  @Input() todoCount = 0;\n  \n  /**\n   * Number of tasks with 'in_progress' status\n   */\n  @Input() inProgressCount = 0;\n  \n  /**\n   * Number of tasks with 'done' status\n   */\n  @Input() doneCount = 0;\n  \n  /**\n   * Chart data for rendering\n   */\n  chartData: any[] = [];\n  \n  /**\n   * Chart view dimensions\n   */\n  view: [number, number] = [300, 200];\n  \n  /**\n   * Chart color scheme\n   */\n  colorScheme = {\n    domain: ['#ff9800', '#2196f3', '#4caf50']\n  };\n  \n  /**\n   * Flag to show/hide chart labels\n   */\n  showLabels = true;\n  \n  /**\n   * Flag to enable/disable chart animations\n   */\n  animations = true;\n  \n  /**\n   * Flag to show/hide legend\n   */\n  showLegend = true;\n  \n  /**\n   * Flag to enable/disable gradient fills\n   */\n  gradient = false;\n  \n  /**\n   * Lifecycle hook that is called when input properties change\n   * Updates chart data when counts change\n   * @param changes - Input changes\n   */\n  ngOnChanges(changes: SimpleChanges): void {\n    this.updateChartData();\n  }\n  \n  /**\n   * Update chart data based on current counts\n   */\n  private updateChartData(): void {\n    this.chartData = [\n      {\n        name: 'To Do',\n        value: this.todoCount\n      },\n      {\n        name: 'In Progress',\n        value: this.inProgressCount\n      },\n      {\n        name: 'Done',\n        value: this.doneCount\n      }\n    ];\n  }\n  \n  /**\n   * Calculate the height percentage for a bar based on its count\n   * @param count - The count value for the bar\n   * @returns Percentage height (0-100)\n   */\n  getBarHeight(count: number): number {\n    const total = this.todoCount + this.inProgressCount + this.doneCount;\n    if (total === 0) return 0;\n    \n    // Calculate percentage with a minimum height of 5% for visibility\n    const percentage = (count / total) * 100;\n    return Math.max(percentage, 5);\n  }\n}\n", "<!-- Task status chart container -->\n<div class=\"chart-container\">\n  <!-- Empty state message -->\n  <div *ngIf=\"todoCount === 0 && inProgressCount === 0 && doneCount === 0\" class=\"empty-chart\">\n    <mat-icon>insert_chart</mat-icon>\n    <p>No data available</p>\n  </div>\n\n  <!-- Chart visualization -->\n  <div *ngIf=\"todoCount > 0 || inProgressCount > 0 || doneCount > 0\" class=\"chart\">\n    <!-- Simple chart implementation using CSS -->\n    <div class=\"chart-bars\">\n      <!-- Todo bar -->\n      <div class=\"chart-bar-container\" *ngIf=\"todoCount > 0\">\n        <div class=\"chart-label\">To Do</div>\n        <div class=\"chart-bar todo-bar\" [style.height.%]=\"getBarHeight(todoCount)\">\n          <div class=\"chart-value\">{{ todoCount }}</div>\n        </div>\n      </div>\n      \n      <!-- In progress bar -->\n      <div class=\"chart-bar-container\" *ngIf=\"inProgressCount > 0\">\n        <div class=\"chart-label\">In Progress</div>\n        <div class=\"chart-bar in-progress-bar\" [style.height.%]=\"getBarHeight(inProgressCount)\">\n          <div class=\"chart-value\">{{ inProgressCount }}</div>\n        </div>\n      </div>\n      \n      <!-- Done bar -->\n      <div class=\"chart-bar-container\" *ngIf=\"doneCount > 0\">\n        <div class=\"chart-label\">Done</div>\n        <div class=\"chart-bar done-bar\" [style.height.%]=\"getBarHeight(doneCount)\">\n          <div class=\"chart-value\">{{ doneCount }}</div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- Chart legend -->\n    <div class=\"chart-legend\">\n      <div class=\"legend-item\">\n        <div class=\"legend-color todo-color\"></div>\n        <div class=\"legend-label\">To Do</div>\n      </div>\n      <div class=\"legend-item\">\n        <div class=\"legend-color in-progress-color\"></div>\n        <div class=\"legend-label\">In Progress</div>\n      </div>\n      <div class=\"legend-item\">\n        <div class=\"legend-color done-color\"></div>\n        <div class=\"legend-label\">Done</div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;ICGEA,EAAA,CAAAC,cAAA,aAA6F;IACjFD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAQtBH,EAAA,CAAAC,cAAA,cAAuD;IAC5BD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpCH,EAAA,CAAAC,cAAA,cAA2E;IAChDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADhBH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,WAAA,WAAAC,MAAA,CAAAC,YAAA,CAAAD,MAAA,CAAAE,SAAA,OAA0C;IAC/CR,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAE,SAAA,CAAe;;;;;IAK5CR,EAAA,CAAAC,cAAA,cAA6D;IAClCD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,cAAwF;IAC7DD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADfH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,WAAA,WAAAK,MAAA,CAAAH,YAAA,CAAAG,MAAA,CAAAC,eAAA,OAAgD;IAC5DX,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAC,eAAA,CAAqB;;;;;IAKlDX,EAAA,CAAAC,cAAA,cAAuD;IAC5BD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnCH,EAAA,CAAAC,cAAA,cAA2E;IAChDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADhBH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,WAAA,WAAAO,MAAA,CAAAL,YAAA,CAAAK,MAAA,CAAAC,SAAA,OAA0C;IAC/Cb,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAS,iBAAA,CAAAG,MAAA,CAAAC,SAAA,CAAe;;;;;IAvBhDb,EAAA,CAAAC,cAAA,aAAiF;IAI7ED,EAAA,CAAAc,UAAA,IAAAC,6CAAA,iBAKM;IAGNf,EAAA,CAAAc,UAAA,IAAAE,6CAAA,iBAKM;IAGNhB,EAAA,CAAAc,UAAA,IAAAG,6CAAA,iBAKM;IACRjB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,aAA0B;IAEtBD,EAAA,CAAAkB,SAAA,aAA2C;IAC3ClB,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEvCH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAkB,SAAA,eAAkD;IAClDlB,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE7CH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAkB,SAAA,eAA2C;IAC3ClB,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IApCJH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAmB,UAAA,SAAAC,MAAA,CAAAZ,SAAA,KAAmB;IAQnBR,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAmB,UAAA,SAAAC,MAAA,CAAAT,eAAA,KAAyB;IAQzBX,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAmB,UAAA,SAAAC,MAAA,CAAAP,SAAA,KAAmB;;;ADjB3D,OAAM,MAAOQ,wBAAwB;EANrCC,YAAA;IAOE;;;IAGS,KAAAd,SAAS,GAAG,CAAC;IAEtB;;;IAGS,KAAAG,eAAe,GAAG,CAAC;IAE5B;;;IAGS,KAAAE,SAAS,GAAG,CAAC;IAEtB;;;IAGA,KAAAU,SAAS,GAAU,EAAE;IAErB;;;IAGA,KAAAC,IAAI,GAAqB,CAAC,GAAG,EAAE,GAAG,CAAC;IAEnC;;;IAGA,KAAAC,WAAW,GAAG;MACZC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;KACzC;IAED;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,QAAQ,GAAG,KAAK;;EAEhB;;;;;EAKAC,WAAWA,CAACC,OAAsB;IAChC,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGQA,eAAeA,CAAA;IACrB,IAAI,CAACV,SAAS,GAAG,CACf;MACEW,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,IAAI,CAAC3B;KACb,EACD;MACE0B,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,IAAI,CAACxB;KACb,EACD;MACEuB,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI,CAACtB;KACb,CACF;EACH;EAEA;;;;;EAKAN,YAAYA,CAAC6B,KAAa;IACxB,MAAMC,KAAK,GAAG,IAAI,CAAC7B,SAAS,GAAG,IAAI,CAACG,eAAe,GAAG,IAAI,CAACE,SAAS;IACpE,IAAIwB,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzB;IACA,MAAMC,UAAU,GAAIF,KAAK,GAAGC,KAAK,GAAI,GAAG;IACxC,OAAOE,IAAI,CAACC,GAAG,CAACF,UAAU,EAAE,CAAC,CAAC;EAChC;;;uBA9FWjB,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAoB,SAAA;MAAAC,MAAA;QAAAlC,SAAA;QAAAG,eAAA;QAAAE,SAAA;MAAA;MAAA8B,QAAA,GAAA3C,EAAA,CAAA4C,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXrClD,EAAA,CAAAC,cAAA,aAA6B;UAE3BD,EAAA,CAAAc,UAAA,IAAAsC,uCAAA,iBAGM;UAGNpD,EAAA,CAAAc,UAAA,IAAAuC,uCAAA,kBA2CM;UACRrD,EAAA,CAAAG,YAAA,EAAM;;;UAlDEH,EAAA,CAAAI,SAAA,GAAiE;UAAjEJ,EAAA,CAAAmB,UAAA,SAAAgC,GAAA,CAAA3C,SAAA,UAAA2C,GAAA,CAAAxC,eAAA,UAAAwC,GAAA,CAAAtC,SAAA,OAAiE;UAMjEb,EAAA,CAAAI,SAAA,GAA2D;UAA3DJ,EAAA,CAAAmB,UAAA,SAAAgC,GAAA,CAAA3C,SAAA,QAAA2C,GAAA,CAAAxC,eAAA,QAAAwC,GAAA,CAAAtC,SAAA,KAA2D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}