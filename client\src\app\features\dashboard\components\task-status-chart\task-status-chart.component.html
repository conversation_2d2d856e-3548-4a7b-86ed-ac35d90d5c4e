<!-- Task status chart container -->
<div class="chart-container">
  <!-- Empty state message -->
  <div *ngIf="todoCount === 0 && inProgressCount === 0 && doneCount === 0" class="empty-chart">
    <mat-icon>insert_chart</mat-icon>
    <p>No data available</p>
  </div>

  <!-- Chart visualization -->
  <div *ngIf="todoCount > 0 || inProgressCount > 0 || doneCount > 0" class="chart">
    <!-- Simple chart implementation using CSS -->
    <div class="chart-bars">
      <!-- Todo bar -->
      <div class="chart-bar-container" *ngIf="todoCount > 0">
        <div class="chart-label">To Do</div>
        <div class="chart-bar todo-bar" [style.height.%]="getBarHeight(todoCount)">
          <div class="chart-value">{{ todoCount }}</div>
        </div>
      </div>
      
      <!-- In progress bar -->
      <div class="chart-bar-container" *ngIf="inProgressCount > 0">
        <div class="chart-label">In Progress</div>
        <div class="chart-bar in-progress-bar" [style.height.%]="getBarHeight(inProgressCount)">
          <div class="chart-value">{{ inProgressCount }}</div>
        </div>
      </div>
      
      <!-- Done bar -->
      <div class="chart-bar-container" *ngIf="doneCount > 0">
        <div class="chart-label">Done</div>
        <div class="chart-bar done-bar" [style.height.%]="getBarHeight(doneCount)">
          <div class="chart-value">{{ doneCount }}</div>
        </div>
      </div>
    </div>
    
    <!-- Chart legend -->
    <div class="chart-legend">
      <div class="legend-item">
        <div class="legend-color todo-color"></div>
        <div class="legend-label">To Do</div>
      </div>
      <div class="legend-item">
        <div class="legend-color in-progress-color"></div>
        <div class="legend-label">In Progress</div>
      </div>
      <div class="legend-item">
        <div class="legend-color done-color"></div>
        <div class="legend-label">Done</div>
      </div>
    </div>
  </div>
</div>
