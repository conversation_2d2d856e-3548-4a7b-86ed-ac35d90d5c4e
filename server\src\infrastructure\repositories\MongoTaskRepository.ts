/**
 * MongoDB implementation of Task Repository
 * Implements the ITaskRepository interface using Mongoose
 */
import mongoose from 'mongoose';
import { ITask, ITaskDocument, Task } from '../../domain/entities/Task';
import { ITaskRepository } from '../../domain/repositories/ITaskRepository';
import { AppError } from '../../domain/common/AppError';

/**
 * MongoDB implementation of Task Repository
 * Handles all data access operations for tasks
 */
export class MongoTaskRepository implements ITaskRepository {
  /**
   * Creates a new task in the database
   * @param taskData - Task data to create
   * @returns Promise resolving to created task
   */
  async create(taskData: ITask): Promise<ITaskDocument> {
    try {
      // Create new task using Mongoose model
      const task = new Task(taskData);
      return await task.save();
    } catch (error: any) {
      // Handle validation errors
      if (error.name === 'ValidationError') {
        throw new AppError(`Validation error: ${error.message}`, 400);
      }
      throw new AppError(`Failed to create task: ${error.message}`, 500);
    }
  }

  /**
   * Finds a task by its ID
   * @param id - Task ID
   * @returns Promise resolving to found task or null if not found
   */
  async findById(id: string): Promise<ITaskDocument | null> {
    try {
      // Validate ID format
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new AppError('Invalid task ID format', 400);
      }
      
      // Find task by ID
      return await Task.findById(id);
    } catch (error: any) {
      // Only rethrow AppErrors, convert others to internal server errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error finding task: ${error.message}`, 500);
    }
  }

  /**
   * Finds all tasks with optional filtering
   * @param filter - Optional filter criteria
   * @returns Promise resolving to array of tasks
   */
  async findAll(filter: Partial<ITask> = {}): Promise<ITaskDocument[]> {
    try {
      // Find tasks with optional filter
      return await Task.find(filter)
        .populate('assigneeId', 'name email') // Populate assignee details
        .sort({ createdAt: -1 }); // Sort by creation date, newest first
    } catch (error: any) {
      throw new AppError(`Error fetching tasks: ${error.message}`, 500);
    }
  }

  /**
   * Updates a task by ID
   * @param id - Task ID
   * @param updateData - Data to update
   * @returns Promise resolving to updated task or null if not found
   */
  async update(id: string, updateData: Partial<ITask>): Promise<ITaskDocument | null> {
    try {
      // Validate ID format
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new AppError('Invalid task ID format', 400);
      }
      
      // Update task with new data and return updated document
      return await Task.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true } // Return updated doc and run validators
      );
    } catch (error: any) {
      // Handle validation errors
      if (error.name === 'ValidationError') {
        throw new AppError(`Validation error: ${error.message}`, 400);
      }
      // Only rethrow AppErrors, convert others to internal server errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error updating task: ${error.message}`, 500);
    }
  }

  /**
   * Deletes a task by ID
   * @param id - Task ID
   * @returns Promise resolving to true if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    try {
      // Validate ID format
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new AppError('Invalid task ID format', 400);
      }
      
      // Delete task and check if it existed
      const result = await Task.findByIdAndDelete(id);
      return result !== null;
    } catch (error: any) {
      // Only rethrow AppErrors, convert others to internal server errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error deleting task: ${error.message}`, 500);
    }
  }

  /**
   * Finds tasks assigned to a specific user
   * @param userId - User ID
   * @returns Promise resolving to array of tasks
   */
  async findByAssignee(userId: mongoose.Types.ObjectId | string): Promise<ITaskDocument[]> {
    try {
      // Validate ID format
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new AppError('Invalid user ID format', 400);
      }
      
      // Find tasks assigned to user
      return await Task.find({ assigneeId: userId })
        .sort({ dueDate: 1, priority: -1 }); // Sort by due date (asc) and priority (desc)
    } catch (error: any) {
      // Only rethrow AppErrors, convert others to internal server errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error fetching user tasks: ${error.message}`, 500);
    }
  }
}
