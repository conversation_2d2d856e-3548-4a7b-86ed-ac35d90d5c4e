/**
 * Guest Guard
 * Protects routes that should only be accessible to non-authenticated users
 * Redirects authenticated users to dashboard
 */
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import type { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree } from '@angular/router';
import type { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class GuestGuard implements CanActivate {
  /**
   * Constructor with dependency injection
   * @param authService - Authentication service
   * @param router - Router for navigation
   */
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  /**
   * Determines if a route can be activated
   * @param route - The route being activated
   * @param state - Router state
   * @returns Boolean or UrlTree indicating if route can be activated
   */
  canActivate(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _route: ActivatedRouteSnapshot,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.authService.isAuthenticated$.pipe(
      take(1),
      map((isAuthenticated: boolean) => {
        // If not authenticated, allow access
        if (!isAuthenticated) {
          return true;
        }
        
        // If authenticated, redirect to dashboard
        return this.router.createUrlTree(['/dashboard']);
      })
    );
  }
}
