{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../core/services/user.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"../../../../core/services/notification.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction ProfilePageComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ProfilePageComponent_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleEditMode());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfilePageComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"mat-spinner\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfilePageComponent_div_6_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.currentUser.name.charAt(0));\n  }\n}\nfunction ProfilePageComponent_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵtemplate(3, ProfilePageComponent_div_6_div_1_span_3_Template, 2, 1, \"span\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 15)(5, \"div\", 16)(6, \"span\", 17);\n    i0.ɵɵtext(7, \"Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 18);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 16)(11, \"span\", 17);\n    i0.ɵɵtext(12, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 18);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 16)(16, \"span\", 17);\n    i0.ɵɵtext(17, \"Role:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 18);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 16)(22, \"span\", 17);\n    i0.ɵɵtext(23, \"Member Since:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 18);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"date\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", ctx_r5.currentUser.avatarUrl ? \"url(\" + ctx_r5.currentUser.avatarUrl + \")\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.currentUser.avatarUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r5.currentUser.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r5.currentUser.email);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 7, ctx_r5.currentUser.role));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(26, 9, ctx_r5.currentUser.createdAt, \"mediumDate\"));\n  }\n}\nfunction ProfilePageComponent_div_6_form_2_mat_error_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfilePageComponent_div_6_form_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 19);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfilePageComponent_div_6_form_2_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.updateProfile());\n    });\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"mat-form-field\", 21)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 22);\n    i0.ɵɵtemplate(6, ProfilePageComponent_div_6_form_2_mat_error_6_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 20)(8, \"mat-form-field\", 21)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"mat-form-field\", 21)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Avatar URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 25)(18, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProfilePageComponent_div_6_form_2_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.toggleEditMode());\n    });\n    i0.ɵɵtext(19, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 27);\n    i0.ɵɵtext(21, \" Save Changes \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r6.profileForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r6.profileForm.get(\"name\")) == null ? null : tmp_1_0.hasError(\"required\"));\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.profileForm.invalid);\n  }\n}\nfunction ProfilePageComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, ProfilePageComponent_div_6_div_1_Template, 27, 12, \"div\", 9);\n    i0.ɵɵtemplate(2, ProfilePageComponent_div_6_form_2_Template, 22, 3, \"form\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEditMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode);\n  }\n}\nexport class ProfilePageComponent {\n  /**\n   * Constructor\n   * @param fb - FormBuilder for creating reactive forms\n   * @param userService - Service for user-related operations\n   * @param authService - Service for authentication operations\n   * @param notificationService - Service for displaying notifications\n   */\n  constructor(fb, userService, authService, notificationService) {\n    this.fb = fb;\n    this.userService = userService;\n    this.authService = authService;\n    this.notificationService = notificationService;\n    // Current user profile data\n    this.currentUser = null;\n    // Loading state\n    this.isLoading = false;\n    // Edit mode toggle\n    this.isEditMode = false;\n    // Initialize the form\n    this.profileForm = this.fb.group({\n      name: ['', [Validators.required]],\n      email: [{\n        value: '',\n        disabled: true\n      }],\n      avatarUrl: ['']\n    });\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   */\n  ngOnInit() {\n    // Load the current user profile\n    this.loadUserProfile();\n  }\n  /**\n   * Loads the current user profile data\n   */\n  loadUserProfile() {\n    this.isLoading = true;\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (user) {\n        // Populate the form with user data\n        this.profileForm.patchValue({\n          name: user.name,\n          email: user.email,\n          avatarUrl: user.avatarUrl || ''\n        });\n      }\n      this.isLoading = false;\n    }, error => {\n      this.notificationService.error('Failed to load profile');\n      this.isLoading = false;\n    });\n  }\n  /**\n   * Toggles edit mode for the profile form\n   */\n  toggleEditMode() {\n    this.isEditMode = !this.isEditMode;\n    if (!this.isEditMode) {\n      // Reset form to original values when canceling edit\n      this.profileForm.patchValue({\n        name: this.currentUser?.name || '',\n        avatarUrl: this.currentUser?.avatarUrl || ''\n      });\n    }\n  }\n  /**\n   * Submits the profile update form\n   */\n  updateProfile() {\n    if (this.profileForm.invalid) {\n      return;\n    }\n    this.isLoading = true;\n    const updatedProfile = {\n      name: this.profileForm.get('name')?.value,\n      avatarUrl: this.profileForm.get('avatarUrl')?.value\n    };\n    if (this.currentUser) {\n      this.userService.updateUser(this.currentUser.id, updatedProfile).subscribe(response => {\n        this.notificationService.success('Profile updated successfully');\n        this.isLoading = false;\n        this.isEditMode = false;\n        // Update the current user in auth service\n        // Reload the current user data\n        this.loadUserProfile();\n      }, error => {\n        this.notificationService.error('Failed to update profile');\n        this.isLoading = false;\n      });\n    }\n  }\n  static {\n    this.ɵfac = function ProfilePageComponent_Factory(t) {\n      return new (t || ProfilePageComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfilePageComponent,\n      selectors: [[\"app-profile-page\"]],\n      decls: 7,\n      vars: 3,\n      consts: [[1, \"profile-container\"], [1, \"profile-header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"profile-content\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"profile-content\"], [\"class\", \"profile-view\", 4, \"ngIf\"], [\"class\", \"profile-form\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"profile-view\"], [1, \"profile-avatar\"], [1, \"avatar-image\"], [4, \"ngIf\"], [1, \"profile-details\"], [1, \"profile-field\"], [1, \"field-label\"], [1, \"field-value\"], [1, \"profile-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-field\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Your name\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"placeholder\", \"Your email\"], [\"matInput\", \"\", \"formControlName\", \"avatarUrl\", \"placeholder\", \"URL to your avatar image\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"]],\n      template: function ProfilePageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"User Profile\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ProfilePageComponent_button_4_Template, 4, 0, \"button\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, ProfilePageComponent_div_5_Template, 2, 0, \"div\", 3);\n          i0.ɵɵtemplate(6, ProfilePageComponent_div_6_Template, 3, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.currentUser);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i7.MatFormField, i7.MatLabel, i7.MatError, i8.MatIcon, i9.MatInput, i10.MatProgressSpinner, i5.TitleCasePipe, i5.DatePipe],\n      styles: [\".profile-container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 2rem auto;\\n  padding: 1.5rem;\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 1px solid #eee;\\n}\\n.profile-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.8rem;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 2rem;\\n}\\n\\n.profile-content[_ngcontent-%COMP%] {\\n  padding: 1rem 0;\\n}\\n\\n.profile-view[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 2rem;\\n}\\n@media (max-width: 600px) {\\n  .profile-view[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.profile-avatar[_ngcontent-%COMP%] {\\n  flex: 0 0 150px;\\n}\\n.profile-avatar[_ngcontent-%COMP%]   .avatar-image[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  border-radius: 50%;\\n  background-color: #3f51b5;\\n  background-size: cover;\\n  background-position: center;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  color: white;\\n  font-size: 3rem;\\n  font-weight: 500;\\n}\\n\\n.profile-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.profile-field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.profile-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.9rem;\\n  color: #666;\\n  margin-bottom: 0.3rem;\\n}\\n.profile-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #333;\\n}\\n\\n.profile-form[_ngcontent-%COMP%] {\\n  max-width: 500px;\\n}\\n.profile-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.profile-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.profile-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 1rem;\\n  margin-top: 2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵlistener", "ProfilePageComponent_button_4_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "toggleEditMode", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r7", "currentUser", "name", "char<PERSON>t", "ɵɵtemplate", "ProfilePageComponent_div_6_div_1_span_3_Template", "ɵɵstyleProp", "ctx_r5", "avatarUrl", "ɵɵproperty", "email", "ɵɵpipeBind1", "role", "ɵɵpipeBind2", "createdAt", "ProfilePageComponent_div_6_form_2_Template_form_ngSubmit_0_listener", "_r10", "ctx_r9", "updateProfile", "ProfilePageComponent_div_6_form_2_mat_error_6_Template", "ProfilePageComponent_div_6_form_2_Template_button_click_18_listener", "ctx_r11", "ctx_r6", "profileForm", "tmp_1_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "invalid", "ProfilePageComponent_div_6_div_1_Template", "ProfilePageComponent_div_6_form_2_Template", "ctx_r2", "isEditMode", "ProfilePageComponent", "constructor", "fb", "userService", "authService", "notificationService", "isLoading", "group", "required", "value", "disabled", "ngOnInit", "loadUserProfile", "currentUser$", "subscribe", "user", "patchValue", "error", "updatedProfile", "updateUser", "id", "response", "success", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "UserService", "i3", "AuthService", "i4", "NotificationService", "selectors", "decls", "vars", "consts", "template", "ProfilePageComponent_Template", "rf", "ctx", "ProfilePageComponent_button_4_Template", "ProfilePageComponent_div_5_Template", "ProfilePageComponent_div_6_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\profile\\pages\\profile-page\\profile-page.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\profile\\pages\\profile-page\\profile-page.component.html"], "sourcesContent": ["/**\n * Profile page component\n * Displays and allows editing of user profile information\n */\nimport { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { UserService } from '../../../../core/services/user.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-profile-page',\n  templateUrl: './profile-page.component.html',\n  styleUrls: ['./profile-page.component.scss']\n})\nexport class ProfilePageComponent implements OnInit {\n  // Current user profile data\n  currentUser: User | null = null;\n  \n  // Form for editing profile\n  profileForm: FormGroup;\n  \n  // Loading state\n  isLoading = false;\n  \n  // Edit mode toggle\n  isEditMode = false;\n\n  /**\n   * Constructor\n   * @param fb - FormBuilder for creating reactive forms\n   * @param userService - Service for user-related operations\n   * @param authService - Service for authentication operations\n   * @param notificationService - Service for displaying notifications\n   */\n  constructor(\n    private fb: FormBuilder,\n    private userService: UserService,\n    private authService: AuthService,\n    private notificationService: NotificationService\n  ) {\n    // Initialize the form\n    this.profileForm = this.fb.group({\n      name: ['', [Validators.required]],\n      email: [{value: '', disabled: true}],\n      avatarUrl: ['']\n    });\n  }\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   */\n  ngOnInit(): void {\n    // Load the current user profile\n    this.loadUserProfile();\n  }\n\n  /**\n   * Loads the current user profile data\n   */\n  loadUserProfile(): void {\n    this.isLoading = true;\n    \n    this.authService.currentUser$.subscribe(\n      (user: User | null) => {\n        this.currentUser = user;\n        \n        if (user) {\n          // Populate the form with user data\n          this.profileForm.patchValue({\n            name: user.name,\n            email: user.email,\n            avatarUrl: user.avatarUrl || ''\n          });\n        }\n        \n        this.isLoading = false;\n      },\n      (error: any) => {\n        this.notificationService.error('Failed to load profile');\n        this.isLoading = false;\n      }\n    );\n  }\n\n  /**\n   * Toggles edit mode for the profile form\n   */\n  toggleEditMode(): void {\n    this.isEditMode = !this.isEditMode;\n    \n    if (!this.isEditMode) {\n      // Reset form to original values when canceling edit\n      this.profileForm.patchValue({\n        name: this.currentUser?.name || '',\n        avatarUrl: this.currentUser?.avatarUrl || ''\n      });\n    }\n  }\n\n  /**\n   * Submits the profile update form\n   */\n  updateProfile(): void {\n    if (this.profileForm.invalid) {\n      return;\n    }\n    \n    this.isLoading = true;\n    \n    const updatedProfile = {\n      name: this.profileForm.get('name')?.value,\n      avatarUrl: this.profileForm.get('avatarUrl')?.value\n    };\n    \n    if (this.currentUser) {\n      this.userService.updateUser(this.currentUser.id, updatedProfile).subscribe(\n        (response: any) => {\n          this.notificationService.success('Profile updated successfully');\n          this.isLoading = false;\n          this.isEditMode = false;\n          \n          // Update the current user in auth service\n          // Reload the current user data\n          this.loadUserProfile();\n        },\n        (error: any) => {\n          this.notificationService.error('Failed to update profile');\n          this.isLoading = false;\n        }\n      );\n    }\n  }\n}\n", "<div class=\"profile-container\">\n  <div class=\"profile-header\">\n    <h1>User Profile</h1>\n    <button mat-raised-button color=\"primary\" (click)=\"toggleEditMode()\" *ngIf=\"!isEditMode\">\n      <mat-icon>edit</mat-icon>\n      Edit Profile\n    </button>\n  </div>\n\n  <!-- Loading spinner -->\n  <div class=\"loading-container\" *ngIf=\"isLoading\">\n    <mat-spinner diameter=\"40\"></mat-spinner>\n  </div>\n\n  <!-- Profile content -->\n  <div class=\"profile-content\" *ngIf=\"!isLoading && currentUser\">\n    <!-- View mode -->\n    <div class=\"profile-view\" *ngIf=\"!isEditMode\">\n      <div class=\"profile-avatar\">\n        <div class=\"avatar-image\" [style.backgroundImage]=\"currentUser.avatarUrl ? 'url(' + currentUser.avatarUrl + ')' : ''\">\n          <span *ngIf=\"!currentUser.avatarUrl\">{{ currentUser.name.charAt(0) }}</span>\n        </div>\n      </div>\n      \n      <div class=\"profile-details\">\n        <div class=\"profile-field\">\n          <span class=\"field-label\">Name:</span>\n          <span class=\"field-value\">{{ currentUser.name }}</span>\n        </div>\n        \n        <div class=\"profile-field\">\n          <span class=\"field-label\">Email:</span>\n          <span class=\"field-value\">{{ currentUser.email }}</span>\n        </div>\n        \n        <div class=\"profile-field\">\n          <span class=\"field-label\">Role:</span>\n          <span class=\"field-value\">{{ currentUser.role | titlecase }}</span>\n        </div>\n        \n        <div class=\"profile-field\">\n          <span class=\"field-label\">Member Since:</span>\n          <span class=\"field-value\">{{ currentUser.createdAt | date:'mediumDate' }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- Edit mode -->\n    <form [formGroup]=\"profileForm\" (ngSubmit)=\"updateProfile()\" class=\"profile-form\" *ngIf=\"isEditMode\">\n      <div class=\"form-field\">\n        <mat-form-field appearance=\"outline\">\n          <mat-label>Name</mat-label>\n          <input matInput formControlName=\"name\" placeholder=\"Your name\">\n          <mat-error *ngIf=\"profileForm.get('name')?.hasError('required')\">\n            Name is required\n          </mat-error>\n        </mat-form-field>\n      </div>\n      \n      <div class=\"form-field\">\n        <mat-form-field appearance=\"outline\">\n          <mat-label>Email</mat-label>\n          <input matInput formControlName=\"email\" placeholder=\"Your email\">\n        </mat-form-field>\n      </div>\n      \n      <div class=\"form-field\">\n        <mat-form-field appearance=\"outline\">\n          <mat-label>Avatar URL</mat-label>\n          <input matInput formControlName=\"avatarUrl\" placeholder=\"URL to your avatar image\">\n        </mat-form-field>\n      </div>\n      \n      <div class=\"form-actions\">\n        <button mat-button type=\"button\" (click)=\"toggleEditMode()\">\n          Cancel\n        </button>\n        <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"profileForm.invalid\">\n          Save Changes\n        </button>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": "AAKA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICF/DC,EAAA,CAAAC,cAAA,gBAAyF;IAA/CD,EAAA,CAAAE,UAAA,mBAAAC,+DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAClET,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,WAAI;IAAAV,EAAA,CAAAW,YAAA,EAAW;IACzBX,EAAA,CAAAU,MAAA,qBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAIXX,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAY,SAAA,qBAAyC;IAC3CZ,EAAA,CAAAW,YAAA,EAAM;;;;;IAQEX,EAAA,CAAAC,cAAA,WAAqC;IAAAD,EAAA,CAAAU,MAAA,GAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAAvCX,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,IAAA,CAAAC,MAAA,IAAgC;;;;;IAH3ElB,EAAA,CAAAC,cAAA,cAA8C;IAGxCD,EAAA,CAAAmB,UAAA,IAAAC,gDAAA,mBAA4E;IAC9EpB,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,cAA6B;IAECD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACtCX,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,GAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGzDX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACvCX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAG1DX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACtCX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAkC;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGrEX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC9CX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAA+C;;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAvBxDX,EAAA,CAAAa,SAAA,GAA2F;IAA3Fb,EAAA,CAAAqB,WAAA,qBAAAC,MAAA,CAAAN,WAAA,CAAAO,SAAA,YAAAD,MAAA,CAAAN,WAAA,CAAAO,SAAA,YAA2F;IAC5GvB,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAwB,UAAA,UAAAF,MAAA,CAAAN,WAAA,CAAAO,SAAA,CAA4B;IAOTvB,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,iBAAA,CAAAQ,MAAA,CAAAN,WAAA,CAAAC,IAAA,CAAsB;IAKtBjB,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,iBAAA,CAAAQ,MAAA,CAAAN,WAAA,CAAAS,KAAA,CAAuB;IAKvBzB,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAA0B,WAAA,QAAAJ,MAAA,CAAAN,WAAA,CAAAW,IAAA,EAAkC;IAKlC3B,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAA4B,WAAA,QAAAN,MAAA,CAAAN,WAAA,CAAAa,SAAA,gBAA+C;;;;;IAWzE7B,EAAA,CAAAC,cAAA,gBAAiE;IAC/DD,EAAA,CAAAU,MAAA,yBACF;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;;;IAPlBX,EAAA,CAAAC,cAAA,eAAqG;IAArED,EAAA,CAAAE,UAAA,sBAAA4B,oEAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAC,MAAA,GAAAhC,EAAA,CAAAO,aAAA;MAAA,OAAYP,EAAA,CAAAQ,WAAA,CAAAwB,MAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1DjC,EAAA,CAAAC,cAAA,cAAwB;IAETD,EAAA,CAAAU,MAAA,WAAI;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC3BX,EAAA,CAAAY,SAAA,gBAA+D;IAC/DZ,EAAA,CAAAmB,UAAA,IAAAe,sDAAA,wBAEY;IACdlC,EAAA,CAAAW,YAAA,EAAiB;IAGnBX,EAAA,CAAAC,cAAA,cAAwB;IAETD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAY;IAC5BX,EAAA,CAAAY,SAAA,iBAAiE;IACnEZ,EAAA,CAAAW,YAAA,EAAiB;IAGnBX,EAAA,CAAAC,cAAA,eAAwB;IAETD,EAAA,CAAAU,MAAA,kBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAY;IACjCX,EAAA,CAAAY,SAAA,iBAAmF;IACrFZ,EAAA,CAAAW,YAAA,EAAiB;IAGnBX,EAAA,CAAAC,cAAA,eAA0B;IACSD,EAAA,CAAAE,UAAA,mBAAAiC,oEAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAK,OAAA,GAAApC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA4B,OAAA,CAAA3B,cAAA,EAAgB;IAAA,EAAC;IACzDT,EAAA,CAAAU,MAAA,gBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAyF;IACvFD,EAAA,CAAAU,MAAA,sBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IA/BPX,EAAA,CAAAwB,UAAA,cAAAa,MAAA,CAAAC,WAAA,CAAyB;IAKbtC,EAAA,CAAAa,SAAA,GAAmD;IAAnDb,EAAA,CAAAwB,UAAA,UAAAe,OAAA,GAAAF,MAAA,CAAAC,WAAA,CAAAE,GAAA,2BAAAD,OAAA,CAAAE,QAAA,aAAmD;IAwBTzC,EAAA,CAAAa,SAAA,IAAgC;IAAhCb,EAAA,CAAAwB,UAAA,aAAAa,MAAA,CAAAC,WAAA,CAAAI,OAAA,CAAgC;;;;;IA9D9F1C,EAAA,CAAAC,cAAA,aAA+D;IAE7DD,EAAA,CAAAmB,UAAA,IAAAwB,yCAAA,mBA4BM;IAGN3C,EAAA,CAAAmB,UAAA,IAAAyB,0CAAA,oBAiCO;IACT5C,EAAA,CAAAW,YAAA,EAAM;;;;IAjEuBX,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAwB,UAAA,UAAAqB,MAAA,CAAAC,UAAA,CAAiB;IA+BuC9C,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAwB,UAAA,SAAAqB,MAAA,CAAAC,UAAA,CAAgB;;;ADhCvG,OAAM,MAAOC,oBAAoB;EAa/B;;;;;;;EAOAC,YACUC,EAAe,EACfC,WAAwB,EACxBC,WAAwB,EACxBC,mBAAwC;IAHxC,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAvB7B;IACA,KAAApC,WAAW,GAAgB,IAAI;IAK/B;IACA,KAAAqC,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAP,UAAU,GAAG,KAAK;IAehB;IACA,IAAI,CAACR,WAAW,GAAG,IAAI,CAACW,EAAE,CAACK,KAAK,CAAC;MAC/BrC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACwD,QAAQ,CAAC,CAAC;MACjC9B,KAAK,EAAE,CAAC;QAAC+B,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAC,CAAC;MACpClC,SAAS,EAAE,CAAC,EAAE;KACf,CAAC;EACJ;EAEA;;;EAGAmC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGAA,eAAeA,CAAA;IACb,IAAI,CAACN,SAAS,GAAG,IAAI;IAErB,IAAI,CAACF,WAAW,CAACS,YAAY,CAACC,SAAS,CACpCC,IAAiB,IAAI;MACpB,IAAI,CAAC9C,WAAW,GAAG8C,IAAI;MAEvB,IAAIA,IAAI,EAAE;QACR;QACA,IAAI,CAACxB,WAAW,CAACyB,UAAU,CAAC;UAC1B9C,IAAI,EAAE6C,IAAI,CAAC7C,IAAI;UACfQ,KAAK,EAAEqC,IAAI,CAACrC,KAAK;UACjBF,SAAS,EAAEuC,IAAI,CAACvC,SAAS,IAAI;SAC9B,CAAC;;MAGJ,IAAI,CAAC8B,SAAS,GAAG,KAAK;IACxB,CAAC,EACAW,KAAU,IAAI;MACb,IAAI,CAACZ,mBAAmB,CAACY,KAAK,CAAC,wBAAwB,CAAC;MACxD,IAAI,CAACX,SAAS,GAAG,KAAK;IACxB,CAAC,CACF;EACH;EAEA;;;EAGA5C,cAAcA,CAAA;IACZ,IAAI,CAACqC,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAElC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB;MACA,IAAI,CAACR,WAAW,CAACyB,UAAU,CAAC;QAC1B9C,IAAI,EAAE,IAAI,CAACD,WAAW,EAAEC,IAAI,IAAI,EAAE;QAClCM,SAAS,EAAE,IAAI,CAACP,WAAW,EAAEO,SAAS,IAAI;OAC3C,CAAC;;EAEN;EAEA;;;EAGAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAACK,WAAW,CAACI,OAAO,EAAE;MAC5B;;IAGF,IAAI,CAACW,SAAS,GAAG,IAAI;IAErB,MAAMY,cAAc,GAAG;MACrBhD,IAAI,EAAE,IAAI,CAACqB,WAAW,CAACE,GAAG,CAAC,MAAM,CAAC,EAAEgB,KAAK;MACzCjC,SAAS,EAAE,IAAI,CAACe,WAAW,CAACE,GAAG,CAAC,WAAW,CAAC,EAAEgB;KAC/C;IAED,IAAI,IAAI,CAACxC,WAAW,EAAE;MACpB,IAAI,CAACkC,WAAW,CAACgB,UAAU,CAAC,IAAI,CAAClD,WAAW,CAACmD,EAAE,EAAEF,cAAc,CAAC,CAACJ,SAAS,CACvEO,QAAa,IAAI;QAChB,IAAI,CAAChB,mBAAmB,CAACiB,OAAO,CAAC,8BAA8B,CAAC;QAChE,IAAI,CAAChB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACP,UAAU,GAAG,KAAK;QAEvB;QACA;QACA,IAAI,CAACa,eAAe,EAAE;MACxB,CAAC,EACAK,KAAU,IAAI;QACb,IAAI,CAACZ,mBAAmB,CAACY,KAAK,CAAC,0BAA0B,CAAC;QAC1D,IAAI,CAACX,SAAS,GAAG,KAAK;MACxB,CAAC,CACF;;EAEL;;;uBArHWN,oBAAoB,EAAA/C,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxE,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1E,EAAA,CAAAsE,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA5E,EAAA,CAAAsE,iBAAA,CAAAO,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAApB/B,oBAAoB;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBjCrF,EAAA,CAAAC,cAAA,aAA+B;UAEvBD,EAAA,CAAAU,MAAA,mBAAY;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACrBX,EAAA,CAAAmB,UAAA,IAAAoE,sCAAA,oBAGS;UACXvF,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAmB,UAAA,IAAAqE,mCAAA,iBAEM;UAGNxF,EAAA,CAAAmB,UAAA,IAAAsE,mCAAA,iBAmEM;UACRzF,EAAA,CAAAW,YAAA,EAAM;;;UAhFoEX,EAAA,CAAAa,SAAA,GAAiB;UAAjBb,EAAA,CAAAwB,UAAA,UAAA8D,GAAA,CAAAxC,UAAA,CAAiB;UAOzD9C,EAAA,CAAAa,SAAA,GAAe;UAAfb,EAAA,CAAAwB,UAAA,SAAA8D,GAAA,CAAAjC,SAAA,CAAe;UAKjBrD,EAAA,CAAAa,SAAA,GAA+B;UAA/Bb,EAAA,CAAAwB,UAAA,UAAA8D,GAAA,CAAAjC,SAAA,IAAAiC,GAAA,CAAAtE,WAAA,CAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}