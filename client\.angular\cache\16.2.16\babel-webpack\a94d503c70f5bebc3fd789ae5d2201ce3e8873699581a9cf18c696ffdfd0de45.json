{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/icon\";\nfunction TaskPriorityChartComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"pie_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No data available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TaskPriorityChartComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 16);\n    i0.ɵɵtext(5, \"High\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"height\", ctx_r2.getBarHeight(ctx_r2.highCount), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.highCount);\n  }\n}\nfunction TaskPriorityChartComponent_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 17)(2, \"div\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 16);\n    i0.ɵɵtext(5, \"Medium\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"height\", ctx_r3.getBarHeight(ctx_r3.mediumCount), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.mediumCount);\n  }\n}\nfunction TaskPriorityChartComponent_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 18)(2, \"div\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 16);\n    i0.ɵɵtext(5, \"Low\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"height\", ctx_r4.getBarHeight(ctx_r4.lowCount), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.lowCount);\n  }\n}\nfunction TaskPriorityChartComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n    i0.ɵɵtemplate(2, TaskPriorityChartComponent_div_2_div_2_Template, 6, 3, \"div\", 6);\n    i0.ɵɵtemplate(3, TaskPriorityChartComponent_div_2_div_3_Template, 6, 3, \"div\", 6);\n    i0.ɵɵtemplate(4, TaskPriorityChartComponent_div_2_div_4_Template, 6, 3, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8);\n    i0.ɵɵelement(7, \"div\", 9);\n    i0.ɵɵelementStart(8, \"div\", 10);\n    i0.ɵɵtext(9, \"High\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 8);\n    i0.ɵɵelement(11, \"div\", 11);\n    i0.ɵɵelementStart(12, \"div\", 10);\n    i0.ɵɵtext(13, \"Medium\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 8);\n    i0.ɵɵelement(15, \"div\", 12);\n    i0.ɵɵelementStart(16, \"div\", 10);\n    i0.ɵɵtext(17, \"Low\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.highCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.mediumCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lowCount > 0);\n  }\n}\nexport class TaskPriorityChartComponent {\n  constructor() {\n    /**\n     * Number of tasks with 'high' priority\n     */\n    this.highCount = 0;\n    /**\n     * Number of tasks with 'medium' priority\n     */\n    this.mediumCount = 0;\n    /**\n     * Number of tasks with 'low' priority\n     */\n    this.lowCount = 0;\n    /**\n     * Chart data for rendering\n     */\n    this.chartData = [];\n    /**\n     * Chart view dimensions\n     */\n    this.view = [300, 200];\n    /**\n     * Chart color scheme\n     */\n    this.colorScheme = {\n      domain: ['#f44336', '#ff9800', '#4caf50']\n    };\n    /**\n     * Flag to show/hide chart labels\n     */\n    this.showLabels = true;\n    /**\n     * Flag to enable/disable chart animations\n     */\n    this.animations = true;\n    /**\n     * Flag to show/hide legend\n     */\n    this.showLegend = true;\n    /**\n     * Flag to enable/disable gradient fills\n     */\n    this.gradient = false;\n  }\n  /**\n   * Lifecycle hook that is called when input properties change\n   * Updates chart data when counts change\n   * @param changes - Input changes\n   */\n  ngOnChanges(changes) {\n    this.updateChartData();\n  }\n  /**\n   * Update chart data based on current counts\n   */\n  updateChartData() {\n    this.chartData = [{\n      name: 'High',\n      value: this.highCount\n    }, {\n      name: 'Medium',\n      value: this.mediumCount\n    }, {\n      name: 'Low',\n      value: this.lowCount\n    }];\n  }\n  /**\n   * Calculate the height percentage for a bar based on its count\n   * @param count - The count value for the bar\n   * @returns Percentage height (0-100)\n   */\n  getBarHeight(count) {\n    const total = this.highCount + this.mediumCount + this.lowCount;\n    if (total === 0) return 0;\n    // Calculate percentage with a minimum height of 5% for visibility\n    const percentage = count / total * 100;\n    return Math.max(percentage, 5);\n  }\n  static {\n    this.ɵfac = function TaskPriorityChartComponent_Factory(t) {\n      return new (t || TaskPriorityChartComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskPriorityChartComponent,\n      selectors: [[\"app-task-priority-chart\"]],\n      inputs: {\n        highCount: \"highCount\",\n        mediumCount: \"mediumCount\",\n        lowCount: \"lowCount\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"chart-container\"], [\"class\", \"empty-chart\", 4, \"ngIf\"], [\"class\", \"chart\", 4, \"ngIf\"], [1, \"empty-chart\"], [1, \"chart\"], [1, \"chart-bars\"], [\"class\", \"chart-bar-container\", 4, \"ngIf\"], [1, \"chart-legend\"], [1, \"legend-item\"], [1, \"legend-color\", \"high-color\"], [1, \"legend-label\"], [1, \"legend-color\", \"medium-color\"], [1, \"legend-color\", \"low-color\"], [1, \"chart-bar-container\"], [1, \"chart-bar\", \"high-bar\"], [1, \"chart-value\"], [1, \"chart-label\"], [1, \"chart-bar\", \"medium-bar\"], [1, \"chart-bar\", \"low-bar\"]],\n      template: function TaskPriorityChartComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TaskPriorityChartComponent_div_1_Template, 5, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, TaskPriorityChartComponent_div_2_Template, 18, 3, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.highCount === 0 && ctx.mediumCount === 0 && ctx.lowCount === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.highCount > 0 || ctx.mediumCount > 0 || ctx.lowCount > 0);\n        }\n      },\n      dependencies: [i1.NgIf, i2.MatIcon],\n      styles: [\"\\n\\n\\n\\n\\n\\n.chart-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  min-height: 250px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n\\n\\n.chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n\\n\\n.chart-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  align-items: flex-end;\\n  width: 100%;\\n  height: 180px;\\n  margin-bottom: 16px;\\n}\\n\\n\\n\\n.chart-bar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 30%;\\n  max-width: 80px;\\n}\\n\\n\\n\\n.chart-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 4px 4px 0 0;\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  padding-top: 8px;\\n  transition: height 0.3s ease;\\n  min-height: 20px;\\n}\\n\\n\\n\\n.high-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(244, 67, 54, 0.7);\\n}\\n\\n.medium-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 152, 0, 0.7);\\n}\\n\\n.low-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.7);\\n}\\n\\n\\n\\n.chart-value[_ngcontent-%COMP%] {\\n  color: white;\\n  font-weight: 500;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.chart-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 0.8rem;\\n  text-align: center;\\n  color: #666;\\n}\\n\\n\\n\\n.chart-legend[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n  margin-top: 16px;\\n}\\n\\n\\n\\n.legend-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0 8px 8px;\\n}\\n\\n\\n\\n.legend-color[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 2px;\\n  margin-right: 4px;\\n}\\n\\n\\n\\n.high-color[_ngcontent-%COMP%] {\\n  background-color: rgba(244, 67, 54, 0.7);\\n}\\n\\n.medium-color[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 152, 0, 0.7);\\n}\\n\\n.low-color[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.7);\\n}\\n\\n\\n\\n.legend-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n\\n\\n.empty-chart[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  color: #9e9e9e;\\n}\\n.empty-chart[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n}\\n.empty-chart[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 600px) {\\n  .chart-bars[_ngcontent-%COMP%] {\\n    height: 150px;\\n  }\\n  .chart-bar-container[_ngcontent-%COMP%] {\\n    width: 28%;\\n  }\\n  .chart-value[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .chart-label[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGFzaGJvYXJkL2NvbXBvbmVudHMvdGFzay1wcmlvcml0eS1jaGFydC90YXNrLXByaW9yaXR5LWNoYXJ0LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOztFQUFBO0FBSUEsb0JBQUE7QUFDQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBQUY7O0FBR0Esd0JBQUE7QUFDQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7QUFBRjs7QUFHQSx5QkFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLDZCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0FBQUY7O0FBR0EsNkJBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsVUFBQTtFQUNBLGVBQUE7QUFBRjs7QUFHQSxjQUFBO0FBQ0E7RUFDRSxXQUFBO0VBQ0EsMEJBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0VBQ0EsNEJBQUE7RUFDQSxnQkFBQTtBQUFGOztBQUdBLGVBQUE7QUFDQTtFQUNFLHdDQUFBO0FBQUY7O0FBR0E7RUFDRSx3Q0FBQTtBQUFGOztBQUdBO0VBQ0Usd0NBQUE7QUFBRjs7QUFHQSxnQkFBQTtBQUNBO0VBQ0UsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7QUFBRjs7QUFHQSxnQkFBQTtBQUNBO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0FBQUY7O0FBR0EsaUJBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQUFGOztBQUdBLGdCQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtBQUFGOztBQUdBLHFCQUFBO0FBQ0E7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7QUFBRjs7QUFHQSwwQkFBQTtBQUNBO0VBQ0Usd0NBQUE7QUFBRjs7QUFHQTtFQUNFLHdDQUFBO0FBQUY7O0FBR0E7RUFDRSx3Q0FBQTtBQUFGOztBQUdBLGlCQUFBO0FBQ0E7RUFDRSxpQkFBQTtFQUNBLFdBQUE7QUFBRjs7QUFHQSxzQkFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0FBQUY7QUFFRTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0FBQUo7QUFHRTtFQUNFLGVBQUE7RUFDQSxTQUFBO0FBREo7O0FBS0EsMkJBQUE7QUFDQTtFQUNFO0lBQ0UsYUFBQTtFQUZGO0VBS0E7SUFDRSxVQUFBO0VBSEY7RUFNQTtJQUNFLGlCQUFBO0VBSkY7RUFPQTtJQUNFLGlCQUFBO0VBTEY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGFzayBwcmlvcml0eSBjaGFydCBjb21wb25lbnQgc3R5bGVzXG4gKi9cblxuLyogQ2hhcnQgY29udGFpbmVyICovXG4uY2hhcnQtY29udGFpbmVyIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgbWluLWhlaWdodDogMjUwcHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xufVxuXG4vKiBDaGFydCB2aXN1YWxpemF0aW9uICovXG4uY2hhcnQge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG4vKiBDaGFydCBiYXJzIGNvbnRhaW5lciAqL1xuLmNoYXJ0LWJhcnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDtcbiAgYWxpZ24taXRlbXM6IGZsZXgtZW5kO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxODBweDtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbn1cblxuLyogSW5kaXZpZHVhbCBiYXIgY29udGFpbmVyICovXG4uY2hhcnQtYmFyLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHdpZHRoOiAzMCU7XG4gIG1heC13aWR0aDogODBweDtcbn1cblxuLyogQ2hhcnQgYmFyICovXG4uY2hhcnQtYmFyIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGJvcmRlci1yYWRpdXM6IDRweCA0cHggMCAwO1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gIHBhZGRpbmctdG9wOiA4cHg7XG4gIHRyYW5zaXRpb246IGhlaWdodCAwLjNzIGVhc2U7XG4gIG1pbi1oZWlnaHQ6IDIwcHg7XG59XG5cbi8qIEJhciBjb2xvcnMgKi9cbi5oaWdoLWJhciB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjQ0LCA2NywgNTQsIDAuNyk7XG59XG5cbi5tZWRpdW0tYmFyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDE1MiwgMCwgMC43KTtcbn1cblxuLmxvdy1iYXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDc2LCAxNzUsIDgwLCAwLjcpO1xufVxuXG4vKiBDaGFydCB2YWx1ZSAqL1xuLmNoYXJ0LXZhbHVlIHtcbiAgY29sb3I6IHdoaXRlO1xuICBmb250LXdlaWdodDogNTAwO1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuLyogQ2hhcnQgbGFiZWwgKi9cbi5jaGFydC1sYWJlbCB7XG4gIG1hcmdpbi10b3A6IDhweDtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgY29sb3I6ICM2NjY7XG59XG5cbi8qIENoYXJ0IGxlZ2VuZCAqL1xuLmNoYXJ0LWxlZ2VuZCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBmbGV4LXdyYXA6IHdyYXA7XG4gIG1hcmdpbi10b3A6IDE2cHg7XG59XG5cbi8qIExlZ2VuZCBpdGVtICovXG4ubGVnZW5kLWl0ZW0ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBtYXJnaW46IDAgOHB4IDhweDtcbn1cblxuLyogTGVnZW5kIGNvbG9yIGJveCAqL1xuLmxlZ2VuZC1jb2xvciB7XG4gIHdpZHRoOiAxNnB4O1xuICBoZWlnaHQ6IDE2cHg7XG4gIGJvcmRlci1yYWRpdXM6IDJweDtcbiAgbWFyZ2luLXJpZ2h0OiA0cHg7XG59XG5cbi8qIExlZ2VuZCBjb2xvciB2YXJpYW50cyAqL1xuLmhpZ2gtY29sb3Ige1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI0NCwgNjcsIDU0LCAwLjcpO1xufVxuXG4ubWVkaXVtLWNvbG9yIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDE1MiwgMCwgMC43KTtcbn1cblxuLmxvdy1jb2xvciB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNzYsIDE3NSwgODAsIDAuNyk7XG59XG5cbi8qIExlZ2VuZCBsYWJlbCAqL1xuLmxlZ2VuZC1sYWJlbCB7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBjb2xvcjogIzY2Njtcbn1cblxuLyogRW1wdHkgY2hhcnQgc3RhdGUgKi9cbi5lbXB0eS1jaGFydCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBoZWlnaHQ6IDEwMCU7XG4gIGNvbG9yOiAjOWU5ZTllO1xuICBcbiAgbWF0LWljb24ge1xuICAgIGZvbnQtc2l6ZTogNDhweDtcbiAgICBoZWlnaHQ6IDQ4cHg7XG4gICAgd2lkdGg6IDQ4cHg7XG4gICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgfVxuICBcbiAgcCB7XG4gICAgZm9udC1zaXplOiAxcmVtO1xuICAgIG1hcmdpbjogMDtcbiAgfVxufVxuXG4vKiBSZXNwb25zaXZlIGFkanVzdG1lbnRzICovXG5AbWVkaWEgKG1heC13aWR0aDogNjAwcHgpIHtcbiAgLmNoYXJ0LWJhcnMge1xuICAgIGhlaWdodDogMTUwcHg7XG4gIH1cbiAgXG4gIC5jaGFydC1iYXItY29udGFpbmVyIHtcbiAgICB3aWR0aDogMjglO1xuICB9XG4gIFxuICAuY2hhcnQtdmFsdWUge1xuICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICB9XG4gIFxuICAuY2hhcnQtbGFiZWwge1xuICAgIGZvbnQtc2l6ZTogMC43cmVtO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "ctx_r2", "getBarHeight", "highCount", "ɵɵtextInterpolate", "ctx_r3", "mediumCount", "ctx_r4", "lowCount", "ɵɵtemplate", "TaskPriorityChartComponent_div_2_div_2_Template", "TaskPriorityChartComponent_div_2_div_3_Template", "TaskPriorityChartComponent_div_2_div_4_Template", "ɵɵelement", "ɵɵproperty", "ctx_r1", "TaskPriorityChartComponent", "constructor", "chartData", "view", "colorScheme", "domain", "showLabels", "animations", "showLegend", "gradient", "ngOnChanges", "changes", "updateChartData", "name", "value", "count", "total", "percentage", "Math", "max", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "TaskPriorityChartComponent_Template", "rf", "ctx", "TaskPriorityChartComponent_div_1_Template", "TaskPriorityChartComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\task-priority-chart\\task-priority-chart.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\task-priority-chart\\task-priority-chart.component.html"], "sourcesContent": ["/**\n * Task Priority Chart Component\n * Displays a chart showing task distribution by priority\n */\nimport { Component, Input, OnChanges, SimpleChanges, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-task-priority-chart',\n  templateUrl: './task-priority-chart.component.html',\n  styleUrls: ['./task-priority-chart.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskPriorityChartComponent implements OnChanges {\n  /**\n   * Number of tasks with 'high' priority\n   */\n  @Input() highCount = 0;\n  \n  /**\n   * Number of tasks with 'medium' priority\n   */\n  @Input() mediumCount = 0;\n  \n  /**\n   * Number of tasks with 'low' priority\n   */\n  @Input() lowCount = 0;\n  \n  /**\n   * Chart data for rendering\n   */\n  chartData: any[] = [];\n  \n  /**\n   * Chart view dimensions\n   */\n  view: [number, number] = [300, 200];\n  \n  /**\n   * Chart color scheme\n   */\n  colorScheme = {\n    domain: ['#f44336', '#ff9800', '#4caf50']\n  };\n  \n  /**\n   * Flag to show/hide chart labels\n   */\n  showLabels = true;\n  \n  /**\n   * Flag to enable/disable chart animations\n   */\n  animations = true;\n  \n  /**\n   * Flag to show/hide legend\n   */\n  showLegend = true;\n  \n  /**\n   * Flag to enable/disable gradient fills\n   */\n  gradient = false;\n  \n  /**\n   * Lifecycle hook that is called when input properties change\n   * Updates chart data when counts change\n   * @param changes - Input changes\n   */\n  ngOnChanges(changes: SimpleChanges): void {\n    this.updateChartData();\n  }\n  \n  /**\n   * Update chart data based on current counts\n   */\n  private updateChartData(): void {\n    this.chartData = [\n      {\n        name: 'High',\n        value: this.highCount\n      },\n      {\n        name: 'Medium',\n        value: this.mediumCount\n      },\n      {\n        name: 'Low',\n        value: this.lowCount\n      }\n    ];\n  }\n  \n  /**\n   * Calculate the height percentage for a bar based on its count\n   * @param count - The count value for the bar\n   * @returns Percentage height (0-100)\n   */\n  getBarHeight(count: number): number {\n    const total = this.highCount + this.mediumCount + this.lowCount;\n    if (total === 0) return 0;\n    \n    // Calculate percentage with a minimum height of 5% for visibility\n    const percentage = (count / total) * 100;\n    return Math.max(percentage, 5);\n  }\n}\n", "<!-- Task priority chart container -->\n<div class=\"chart-container\">\n  <!-- Empty state message -->\n  <div *ngIf=\"highCount === 0 && mediumCount === 0 && lowCount === 0\" class=\"empty-chart\">\n    <mat-icon>pie_chart</mat-icon>\n    <p>No data available</p>\n  </div>\n\n  <!-- Chart visualization -->\n  <div *ngIf=\"highCount > 0 || mediumCount > 0 || lowCount > 0\" class=\"chart\">\n    <!-- Simple chart implementation using CSS -->\n    <div class=\"chart-bars\">\n      <!-- High priority bar -->\n      <div class=\"chart-bar-container\" *ngIf=\"highCount > 0\">\n        <div class=\"chart-bar high-bar\" [style.height.%]=\"getBarHeight(highCount)\">\n          <div class=\"chart-value\">{{ highCount }}</div>\n        </div>\n        <div class=\"chart-label\">High</div>\n      </div>\n      \n      <!-- Medium priority bar -->\n      <div class=\"chart-bar-container\" *ngIf=\"mediumCount > 0\">\n        <div class=\"chart-bar medium-bar\" [style.height.%]=\"getBarHeight(mediumCount)\">\n          <div class=\"chart-value\">{{ mediumCount }}</div>\n        </div>\n        <div class=\"chart-label\">Medium</div>\n      </div>\n      \n      <!-- Low priority bar -->\n      <div class=\"chart-bar-container\" *ngIf=\"lowCount > 0\">\n        <div class=\"chart-bar low-bar\" [style.height.%]=\"getBarHeight(lowCount)\">\n          <div class=\"chart-value\">{{ lowCount }}</div>\n        </div>\n        <div class=\"chart-label\">Low</div>\n      </div>\n    </div>\n    \n    <!-- Chart legend -->\n    <div class=\"chart-legend\">\n      <div class=\"legend-item\">\n        <div class=\"legend-color high-color\"></div>\n        <div class=\"legend-label\">High</div>\n      </div>\n      <div class=\"legend-item\">\n        <div class=\"legend-color medium-color\"></div>\n        <div class=\"legend-label\">Medium</div>\n      </div>\n      <div class=\"legend-item\">\n        <div class=\"legend-color low-color\"></div>\n        <div class=\"legend-label\">Low</div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;ICGEA,EAAA,CAAAC,cAAA,aAAwF;IAC5ED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAQtBH,EAAA,CAAAC,cAAA,cAAuD;IAE1BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEhDH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHHH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,WAAA,WAAAC,MAAA,CAAAC,YAAA,CAAAD,MAAA,CAAAE,SAAA,OAA0C;IAC/CR,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAE,SAAA,CAAe;;;;;IAM5CR,EAAA,CAAAC,cAAA,cAAyD;IAE5BD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAElDH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHHH,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAK,WAAA,WAAAK,MAAA,CAAAH,YAAA,CAAAG,MAAA,CAAAC,WAAA,OAA4C;IACnDX,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAiB;;;;;IAM9CX,EAAA,CAAAC,cAAA,cAAsD;IAEzBD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE/CH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHHH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,WAAA,WAAAO,MAAA,CAAAL,YAAA,CAAAK,MAAA,CAAAC,QAAA,OAAyC;IAC7Cb,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAS,iBAAA,CAAAG,MAAA,CAAAC,QAAA,CAAc;;;;;IAtB/Cb,EAAA,CAAAC,cAAA,aAA4E;IAIxED,EAAA,CAAAc,UAAA,IAAAC,+CAAA,iBAKM;IAGNf,EAAA,CAAAc,UAAA,IAAAE,+CAAA,iBAKM;IAGNhB,EAAA,CAAAc,UAAA,IAAAG,+CAAA,iBAKM;IACRjB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,aAA0B;IAEtBD,EAAA,CAAAkB,SAAA,aAA2C;IAC3ClB,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEtCH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAkB,SAAA,eAA6C;IAC7ClB,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAExCH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAkB,SAAA,eAA0C;IAC1ClB,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IApCHH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAmB,UAAA,SAAAC,MAAA,CAAAZ,SAAA,KAAmB;IAQnBR,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAmB,UAAA,SAAAC,MAAA,CAAAT,WAAA,KAAqB;IAQrBX,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAmB,UAAA,SAAAC,MAAA,CAAAP,QAAA,KAAkB;;;ADjB1D,OAAM,MAAOQ,0BAA0B;EANvCC,YAAA;IAOE;;;IAGS,KAAAd,SAAS,GAAG,CAAC;IAEtB;;;IAGS,KAAAG,WAAW,GAAG,CAAC;IAExB;;;IAGS,KAAAE,QAAQ,GAAG,CAAC;IAErB;;;IAGA,KAAAU,SAAS,GAAU,EAAE;IAErB;;;IAGA,KAAAC,IAAI,GAAqB,CAAC,GAAG,EAAE,GAAG,CAAC;IAEnC;;;IAGA,KAAAC,WAAW,GAAG;MACZC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;KACzC;IAED;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,QAAQ,GAAG,KAAK;;EAEhB;;;;;EAKAC,WAAWA,CAACC,OAAsB;IAChC,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGQA,eAAeA,CAAA;IACrB,IAAI,CAACV,SAAS,GAAG,CACf;MACEW,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI,CAAC3B;KACb,EACD;MACE0B,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,IAAI,CAACxB;KACb,EACD;MACEuB,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,IAAI,CAACtB;KACb,CACF;EACH;EAEA;;;;;EAKAN,YAAYA,CAAC6B,KAAa;IACxB,MAAMC,KAAK,GAAG,IAAI,CAAC7B,SAAS,GAAG,IAAI,CAACG,WAAW,GAAG,IAAI,CAACE,QAAQ;IAC/D,IAAIwB,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzB;IACA,MAAMC,UAAU,GAAIF,KAAK,GAAGC,KAAK,GAAI,GAAG;IACxC,OAAOE,IAAI,CAACC,GAAG,CAACF,UAAU,EAAE,CAAC,CAAC;EAChC;;;uBA9FWjB,0BAA0B;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAAoB,SAAA;MAAAC,MAAA;QAAAlC,SAAA;QAAAG,WAAA;QAAAE,QAAA;MAAA;MAAA8B,QAAA,GAAA3C,EAAA,CAAA4C,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXvClD,EAAA,CAAAC,cAAA,aAA6B;UAE3BD,EAAA,CAAAc,UAAA,IAAAsC,yCAAA,iBAGM;UAGNpD,EAAA,CAAAc,UAAA,IAAAuC,yCAAA,kBA2CM;UACRrD,EAAA,CAAAG,YAAA,EAAM;;;UAlDEH,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAAmB,UAAA,SAAAgC,GAAA,CAAA3C,SAAA,UAAA2C,GAAA,CAAAxC,WAAA,UAAAwC,GAAA,CAAAtC,QAAA,OAA4D;UAM5Db,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAmB,UAAA,SAAAgC,GAAA,CAAA3C,SAAA,QAAA2C,GAAA,CAAAxC,WAAA,QAAAwC,GAAA,CAAAtC,QAAA,KAAsD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}