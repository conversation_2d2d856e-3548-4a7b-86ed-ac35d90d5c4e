<!-- Task priority chart container -->
<div class="chart-container">
  <!-- Empty state message -->
  <div *ngIf="highCount === 0 && mediumCount === 0 && lowCount === 0" class="empty-chart">
    <mat-icon>pie_chart</mat-icon>
    <p>No data available</p>
  </div>

  <!-- Chart visualization -->
  <div *ngIf="highCount > 0 || mediumCount > 0 || lowCount > 0" class="chart">
    <!-- Simple chart implementation using CSS -->
    <div class="chart-bars">
      <!-- High priority bar -->
      <div class="chart-bar-container" *ngIf="highCount > 0">
        <div class="chart-bar high-bar" [style.height.%]="getBarHeight(highCount)">
          <div class="chart-value">{{ highCount }}</div>
        </div>
        <div class="chart-label">High</div>
      </div>
      
      <!-- Medium priority bar -->
      <div class="chart-bar-container" *ngIf="mediumCount > 0">
        <div class="chart-bar medium-bar" [style.height.%]="getBarHeight(mediumCount)">
          <div class="chart-value">{{ mediumCount }}</div>
        </div>
        <div class="chart-label">Medium</div>
      </div>
      
      <!-- Low priority bar -->
      <div class="chart-bar-container" *ngIf="lowCount > 0">
        <div class="chart-bar low-bar" [style.height.%]="getBarHeight(lowCount)">
          <div class="chart-value">{{ lowCount }}</div>
        </div>
        <div class="chart-label">Low</div>
      </div>
    </div>
    
    <!-- Chart legend -->
    <div class="chart-legend">
      <div class="legend-item">
        <div class="legend-color high-color"></div>
        <div class="legend-label">High</div>
      </div>
      <div class="legend-item">
        <div class="legend-color medium-color"></div>
        <div class="legend-label">Medium</div>
      </div>
      <div class="legend-item">
        <div class="legend-color low-color"></div>
        <div class="legend-label">Low</div>
      </div>
    </div>
  </div>
</div>
