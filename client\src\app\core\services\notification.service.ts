/**
 * Notification Service
 * Provides methods for displaying notifications to the user
 */
import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  /**
   * Default duration for notifications in milliseconds
   */
  private defaultDuration = 5000;

  /**
   * Constructor with dependency injection
   * @param snackBar - Angular Material SnackBar service
   */
  constructor(private snackBar: MatSnackBar) {}

  /**
   * Display a success notification
   * @param message - Message to display
   * @param duration - Optional duration in milliseconds
   */
  success(message: string, duration?: number): void {
    this.snackBar.open(message, 'Close', {
      duration: duration || this.defaultDuration,
      panelClass: ['notification-success'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  /**
   * Display an error notification
   * @param message - Error message to display
   * @param duration - Optional duration in milliseconds
   */
  error(message: string, duration?: number): void {
    this.snackBar.open(message, 'Close', {
      duration: duration || this.defaultDuration,
      panelClass: ['notification-error'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  /**
   * Display an info notification
   * @param message - Message to display
   * @param duration - Optional duration in milliseconds
   */
  info(message: string, duration?: number): void {
    this.snackBar.open(message, 'Close', {
      duration: duration || this.defaultDuration,
      panelClass: ['notification-info'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  /**
   * Display a warning notification
   * @param message - Warning message to display
   * @param duration - Optional duration in milliseconds
   */
  warning(message: string, duration?: number): void {
    this.snackBar.open(message, 'Close', {
      duration: duration || this.defaultDuration,
      panelClass: ['notification-warning'],
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }
}
