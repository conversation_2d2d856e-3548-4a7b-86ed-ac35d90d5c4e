/**
 * Update Task Use Case
 * Handles business logic for updating an existing task
 */
import { ITask, ITaskDocument } from '../../entities/Task';
import { ITaskRepository } from '../../repositories/ITaskRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for updating an existing task
 * Implements business rules and validation for task updates
 */
export class UpdateTaskUseCase {
  /**
   * Constructor for UpdateTaskUseCase
   * @param taskRepository - Repository for task data access
   */
  constructor(private taskRepository: ITaskRepository) {}

  /**
   * Executes the use case to update an existing task
   * @param taskId - ID of the task to update
   * @param updateData - Data to update on the task
   * @returns Promise resolving to the updated task
   * @throws AppError if task not found or validation fails
   */
  async execute(taskId: string, updateData: Partial<ITask>): Promise<ITaskDocument> {
    try {
      // Validate task ID
      if (!taskId) {
        throw new AppError('Task ID is required', 400);
      }

      // Validate update data
      if (Object.keys(updateData).length === 0) {
        throw new AppError('No update data provided', 400);
      }

      // Validate title length if provided
      if (updateData.title && updateData.title.length > 100) {
        throw new AppError('Task title cannot exceed 100 characters', 400);
      }

      // Validate description length if provided
      if (updateData.description && updateData.description.length > 500) {
        throw new AppError('Task description cannot exceed 500 characters', 400);
      }

      // Update task
      const updatedTask = await this.taskRepository.update(taskId, updateData);
      
      // Check if task exists
      if (!updatedTask) {
        throw new AppError(`Task with ID ${taskId} not found`, 404);
      }
      
      return updatedTask;
    } catch (error) {
      // Rethrow AppErrors, wrap other errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error updating task: ${(error as Error).message}`, 500);
    }
  }
}
