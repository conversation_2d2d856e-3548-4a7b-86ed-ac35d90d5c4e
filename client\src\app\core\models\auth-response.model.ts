/**
 * Authentication Response model interface
 * Represents the response from authentication endpoints
 */
export interface AuthResponse {
  /**
   * Indicates if the authentication was successful
   */
  success: boolean;
  
  /**
   * Optional message from the server
   */
  message?: string;
  
  /**
   * Authentication data (only present if success is true)
   */
  data?: {
    /**
     * JWT token for authenticated requests
     */
    token: string;
    
    /**
     * User information
     */
    user: {
      /**
       * User ID
       */
      id: string;
      
      /**
       * User's name
       */
      name: string;
      
      /**
       * User's email
       */
      email: string;
      
      /**
       * User's role
       */
      role: 'admin' | 'user';
      
      /**
       * User's avatar URL
       */
      avatarUrl?: string;
    }
  };
}
