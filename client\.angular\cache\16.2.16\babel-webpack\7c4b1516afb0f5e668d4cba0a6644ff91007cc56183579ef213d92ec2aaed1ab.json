{"ast": null, "code": "/**\n * Register Form Component\n * Handles user registration with name, email, and password\n */\nimport { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/checkbox\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/progress-spinner\";\nimport * as i9 from \"../../../../shared/components/error-message/error-message.component\";\nfunction RegisterFormComponent_app_error_message_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-error-message\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"message\", ctx_r0.error);\n  }\n}\nfunction RegisterFormComponent_mat_error_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_error_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Name must be at least 2 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 8 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_error_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must include uppercase, lowercase, number, and special character \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please confirm your password \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_error_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Passwords do not match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_error_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\", 20);\n    i0.ɵɵtext(1, \" You must accept the terms and conditions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterFormComponent_mat_spinner_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 21);\n  }\n}\nfunction RegisterFormComponent_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Register\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterFormComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed registration attempt\n     */\n    this.error = null;\n    /**\n     * Event emitted when form is submitted\n     */\n    this.formSubmit = new EventEmitter();\n    /**\n     * Event emitted when login link is clicked\n     */\n    this.login = new EventEmitter();\n    /**\n     * Flag to toggle password visibility\n     */\n    this.hidePassword = true;\n    /**\n     * Flag to toggle confirm password visibility\n     */\n    this.hideConfirmPassword = true;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the registration form\n   */\n  ngOnInit() {\n    this.initForm();\n  }\n  /**\n   * Initialize registration form with validation\n   */\n  initForm() {\n    this.registerForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8), this.passwordStrengthValidator]],\n      confirmPassword: ['', Validators.required],\n      termsAccepted: [false, Validators.requiredTrue]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  /**\n   * Custom validator for password strength\n   * @param control - Form control\n   * @returns Validation errors or null\n   */\n  passwordStrengthValidator(control) {\n    const value = control.value || '';\n    if (!value) {\n      return null;\n    }\n    const hasUpperCase = /[A-Z]+/.test(value);\n    const hasLowerCase = /[a-z]+/.test(value);\n    const hasNumeric = /[0-9]+/.test(value);\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+/.test(value);\n    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n    return !passwordValid ? {\n      passwordStrength: true\n    } : null;\n  }\n  /**\n   * Custom validator for password matching\n   * @param group - Form group\n   * @returns Validation errors or null\n   */\n  passwordMatchValidator(group) {\n    const password = group.get('password')?.value;\n    const confirmPassword = group.get('confirmPassword')?.value;\n    return password === confirmPassword ? null : {\n      passwordMismatch: true\n    };\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.registerForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.registerForm.markAllAsTouched();\n      return;\n    }\n    const {\n      name,\n      email,\n      password\n    } = this.registerForm.value;\n    this.formSubmit.emit({\n      name,\n      email,\n      password\n    });\n  }\n  /**\n   * Handle login link click\n   */\n  onLogin() {\n    this.login.emit();\n  }\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName, errorName) {\n    const control = this.registerForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n  /**\n   * Check if form has error\n   * @param errorName - Name of error\n   * @returns True if form has error\n   */\n  hasFormError(errorName) {\n    return this.registerForm.touched && this.registerForm.hasError(errorName);\n  }\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility() {\n    this.hidePassword = !this.hidePassword;\n  }\n  /**\n   * Toggle confirm password visibility\n   */\n  toggleConfirmPasswordVisibility() {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n  static {\n    this.ɵfac = function RegisterFormComponent_Factory(t) {\n      return new (t || RegisterFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterFormComponent,\n      selectors: [[\"app-register-form\"]],\n      inputs: {\n        loading: \"loading\",\n        error: \"error\"\n      },\n      outputs: {\n        formSubmit: \"formSubmit\",\n        login: \"login\"\n      },\n      decls: 58,\n      vars: 23,\n      consts: [[1, \"register-form-container\"], [3, \"message\", 4, \"ngIf\"], [1, \"register-form\", 3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Enter your full name\", \"autocomplete\", \"name\"], [\"matPrefix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Create a password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm your password\", \"autocomplete\", \"new-password\", 3, \"type\"], [1, \"terms-container\"], [\"formControlName\", \"termsAccepted\", \"color\", \"primary\"], [\"href\", \"#\", \"target\", \"_blank\"], [\"class\", \"terms-error\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"login-link\"], [3, \"click\"], [3, \"message\"], [1, \"terms-error\"], [\"diameter\", \"20\"]],\n      template: function RegisterFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, RegisterFormComponent_app_error_message_1_Template, 1, 1, \"app-error-message\", 1);\n          i0.ɵɵelementStart(2, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterFormComponent_Template_form_ngSubmit_2_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(3, \"mat-form-field\", 3)(4, \"mat-label\");\n          i0.ɵɵtext(5, \"Full Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"input\", 4);\n          i0.ɵɵelementStart(7, \"mat-icon\", 5);\n          i0.ɵɵtext(8, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, RegisterFormComponent_mat_error_9_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵtemplate(10, RegisterFormComponent_mat_error_10_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"mat-form-field\", 3)(12, \"mat-label\");\n          i0.ɵɵtext(13, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 7);\n          i0.ɵɵelementStart(15, \"mat-icon\", 5);\n          i0.ɵɵtext(16, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, RegisterFormComponent_mat_error_17_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵtemplate(18, RegisterFormComponent_mat_error_18_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 3)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 8);\n          i0.ɵɵelementStart(23, \"mat-icon\", 5);\n          i0.ɵɵtext(24, \"lock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function RegisterFormComponent_Template_button_click_25_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelementStart(26, \"mat-icon\");\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(28, RegisterFormComponent_mat_error_28_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵtemplate(29, RegisterFormComponent_mat_error_29_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵtemplate(30, RegisterFormComponent_mat_error_30_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"mat-form-field\", 3)(32, \"mat-label\");\n          i0.ɵɵtext(33, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 10);\n          i0.ɵɵelementStart(35, \"mat-icon\", 5);\n          i0.ɵɵtext(36, \"lock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function RegisterFormComponent_Template_button_click_37_listener() {\n            return ctx.toggleConfirmPasswordVisibility();\n          });\n          i0.ɵɵelementStart(38, \"mat-icon\");\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(40, RegisterFormComponent_mat_error_40_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵtemplate(41, RegisterFormComponent_mat_error_41_Template, 2, 0, \"mat-error\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 11)(43, \"mat-checkbox\", 12);\n          i0.ɵɵtext(44, \" I agree to the \");\n          i0.ɵɵelementStart(45, \"a\", 13);\n          i0.ɵɵtext(46, \"Terms of Service\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(47, \" and \");\n          i0.ɵɵelementStart(48, \"a\", 13);\n          i0.ɵɵtext(49, \"Privacy Policy\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(50, RegisterFormComponent_mat_error_50_Template, 2, 0, \"mat-error\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"button\", 15);\n          i0.ɵɵtemplate(52, RegisterFormComponent_mat_spinner_52_Template, 1, 0, \"mat-spinner\", 16);\n          i0.ɵɵtemplate(53, RegisterFormComponent_span_53_Template, 2, 0, \"span\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 17);\n          i0.ɵɵtext(55, \" Already have an account? \");\n          i0.ɵɵelementStart(56, \"a\", 18);\n          i0.ɵɵlistener(\"click\", function RegisterFormComponent_Template_a_click_56_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵtext(57, \"Login\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"name\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"name\", \"minlength\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"email\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"email\", \"email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"password\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"password\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"password\", \"passwordStrength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵattribute(\"aria-label\", \"Hide confirm password\")(\"aria-pressed\", ctx.hideConfirmPassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"confirmPassword\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasFormError(\"passwordMismatch\"));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"termsAccepted\", \"requiredTrue\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.MatButton, i3.MatIconButton, i4.MatCheckbox, i5.MatFormField, i5.MatLabel, i5.MatError, i5.MatPrefix, i5.MatSuffix, i6.MatIcon, i7.MatInput, i8.MatProgressSpinner, i9.ErrorMessageComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.register-form-container[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  padding: 24px;\\n  max-width: 400px;\\n  width: 100%;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.register-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n\\n\\n.terms-container[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n}\\n.terms-container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  text-decoration: none;\\n}\\n.terms-container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.terms-error[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin-top: 4px;\\n  color: var(--warn-color);\\n}\\n\\n\\n\\n.submit-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1rem;\\n  margin-top: 8px;\\n}\\n.submit-button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.login-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 16px;\\n  font-size: 0.9rem;\\n}\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  cursor: pointer;\\n  margin-left: 4px;\\n}\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .register-form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    box-shadow: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYXV0aC9jb21wb25lbnRzL3JlZ2lzdGVyLWZvcm0vcmVnaXN0ZXItZm9ybS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7RUFBQTtBQUlBLDJDQUFBO0FBQ0E7RUFDRSxzQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUNBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0VBQ0EsY0FBQTtBQUFGOztBQUdBLGtCQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBQUY7O0FBR0EsMkJBQUE7QUFDQTtFQUNFLFdBQUE7QUFBRjs7QUFHQSxtQ0FBQTtBQUNBO0VBQ0UsZUFBQTtBQUFGO0FBRUU7RUFDRSwyQkFBQTtFQUNBLHFCQUFBO0FBQUo7QUFFSTtFQUNFLDBCQUFBO0FBQU47O0FBS0Esd0JBQUE7QUFDQTtFQUNFLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLHdCQUFBO0FBRkY7O0FBS0Esa0JBQUE7QUFDQTtFQUNFLFlBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtBQUZGO0FBSUU7RUFDRSxxQkFBQTtFQUNBLGNBQUE7QUFGSjs7QUFNQSx5QkFBQTtBQUNBO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0FBSEY7QUFLRTtFQUNFLDJCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBSEo7QUFLSTtFQUNFLDBCQUFBO0FBSE47O0FBUUEsMkJBQUE7QUFDQTtFQUNFO0lBQ0UsYUFBQTtJQUNBLGdCQUFBO0VBTEY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmVnaXN0ZXIgZm9ybSBjb21wb25lbnQgc3R5bGVzXG4gKi9cblxuLyogQ29udGFpbmVyIGZvciB0aGUgZW50aXJlIHJlZ2lzdGVyIGZvcm0gKi9cbi5yZWdpc3Rlci1mb3JtLWNvbnRhaW5lciB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYm94LXNoYWRvdzogMCAycHggMTBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHBhZGRpbmc6IDI0cHg7XG4gIG1heC13aWR0aDogNDAwcHg7XG4gIHdpZHRoOiAxMDAlO1xuICBtYXJnaW46IDAgYXV0bztcbn1cblxuLyogUmVnaXN0ZXIgZm9ybSAqL1xuLnJlZ2lzdGVyLWZvcm0ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDE2cHg7XG59XG5cbi8qIEZ1bGwgd2lkdGggZm9ybSBmaWVsZHMgKi9cbi5mdWxsLXdpZHRoIHtcbiAgd2lkdGg6IDEwMCU7XG59XG5cbi8qIFRlcm1zIGFuZCBjb25kaXRpb25zIGNvbnRhaW5lciAqL1xuLnRlcm1zLWNvbnRhaW5lciB7XG4gIG1hcmdpbi10b3A6IDhweDtcbiAgXG4gIGEge1xuICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgXG4gICAgJjpob3ZlciB7XG4gICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgICB9XG4gIH1cbn1cblxuLyogVGVybXMgZXJyb3IgbWVzc2FnZSAqL1xuLnRlcm1zLWVycm9yIHtcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xuICBtYXJnaW4tdG9wOiA0cHg7XG4gIGNvbG9yOiB2YXIoLS13YXJuLWNvbG9yKTtcbn1cblxuLyogU3VibWl0IGJ1dHRvbiAqL1xuLnN1Ym1pdC1idXR0b24ge1xuICBoZWlnaHQ6IDQ4cHg7XG4gIGZvbnQtc2l6ZTogMXJlbTtcbiAgbWFyZ2luLXRvcDogOHB4O1xuICBcbiAgbWF0LXNwaW5uZXIge1xuICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgICBtYXJnaW46IDAgYXV0bztcbiAgfVxufVxuXG4vKiBMb2dpbiBsaW5rIGNvbnRhaW5lciAqL1xuLmxvZ2luLWxpbmsge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbi10b3A6IDE2cHg7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICBcbiAgYSB7XG4gICAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBtYXJnaW4tbGVmdDogNHB4O1xuICAgIFxuICAgICY6aG92ZXIge1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG4gICAgfVxuICB9XG59XG5cbi8qIFJlc3BvbnNpdmUgYWRqdXN0bWVudHMgKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAucmVnaXN0ZXItZm9ybS1jb250YWluZXIge1xuICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgYm94LXNoYWRvdzogbm9uZTtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "error", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "RegisterFormComponent", "constructor", "fb", "loading", "formSubmit", "login", "hidePassword", "hideConfirmPassword", "ngOnInit", "initForm", "registerForm", "group", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "password", "passwordStrengthValidator", "confirmPassword", "termsAccepted", "requiredTrue", "validators", "passwordMatchValidator", "control", "value", "hasUpperCase", "test", "hasLowerCase", "hasNumeric", "hasSpecialChar", "passwordValid", "passwordStrength", "get", "passwordMismatch", "onSubmit", "invalid", "mark<PERSON>llAsTouched", "emit", "onLogin", "<PERSON><PERSON><PERSON><PERSON>", "controlName", "errorName", "touched", "hasFormError", "togglePasswordVisibility", "toggleConfirmPasswordVisibility", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "RegisterFormComponent_Template", "rf", "ctx", "ɵɵtemplate", "RegisterFormComponent_app_error_message_1_Template", "ɵɵlistener", "RegisterFormComponent_Template_form_ngSubmit_2_listener", "RegisterFormComponent_mat_error_9_Template", "RegisterFormComponent_mat_error_10_Template", "RegisterFormComponent_mat_error_17_Template", "RegisterFormComponent_mat_error_18_Template", "RegisterFormComponent_Template_button_click_25_listener", "RegisterFormComponent_mat_error_28_Template", "RegisterFormComponent_mat_error_29_Template", "RegisterFormComponent_mat_error_30_Template", "RegisterFormComponent_Template_button_click_37_listener", "RegisterFormComponent_mat_error_40_Template", "RegisterFormComponent_mat_error_41_Template", "RegisterFormComponent_mat_error_50_Template", "RegisterFormComponent_mat_spinner_52_Template", "RegisterFormComponent_span_53_Template", "RegisterFormComponent_Template_a_click_56_listener", "ɵɵadvance", "ɵɵattribute", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\register-form\\register-form.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\register-form\\register-form.component.html"], "sourcesContent": ["/**\n * Register Form Component\n * Handles user registration with name, email, and password\n */\nimport { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';\n\n@Component({\n  selector: 'app-register-form',\n  templateUrl: './register-form.component.html',\n  styleUrls: ['./register-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class RegisterFormComponent implements OnInit {\n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Error message from failed registration attempt\n   */\n  @Input() error: string | null = null;\n  \n  /**\n   * Event emitted when form is submitted\n   */\n  @Output() formSubmit = new EventEmitter<{ name: string; email: string; password: string }>();\n  \n  /**\n   * Event emitted when login link is clicked\n   */\n  @Output() login = new EventEmitter<void>();\n  \n  /**\n   * Registration form group\n   */\n  registerForm!: FormGroup;\n  \n  /**\n   * Flag to toggle password visibility\n   */\n  hidePassword = true;\n  \n  /**\n   * Flag to toggle confirm password visibility\n   */\n  hideConfirmPassword = true;\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the registration form\n   */\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  /**\n   * Initialize registration form with validation\n   */\n  private initForm(): void {\n    this.registerForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [\n        Validators.required, \n        Validators.minLength(8),\n        this.passwordStrengthValidator\n      ]],\n      confirmPassword: ['', Validators.required],\n      termsAccepted: [false, Validators.requiredTrue]\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  /**\n   * Custom validator for password strength\n   * @param control - Form control\n   * @returns Validation errors or null\n   */\n  private passwordStrengthValidator(control: AbstractControl): ValidationErrors | null {\n    const value: string = control.value || '';\n    \n    if (!value) {\n      return null;\n    }\n    \n    const hasUpperCase = /[A-Z]+/.test(value);\n    const hasLowerCase = /[a-z]+/.test(value);\n    const hasNumeric = /[0-9]+/.test(value);\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+/.test(value);\n    \n    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n    \n    return !passwordValid ? { passwordStrength: true } : null;\n  }\n\n  /**\n   * Custom validator for password matching\n   * @param group - Form group\n   * @returns Validation errors or null\n   */\n  private passwordMatchValidator(group: AbstractControl): ValidationErrors | null {\n    const password = group.get('password')?.value;\n    const confirmPassword = group.get('confirmPassword')?.value;\n    \n    return password === confirmPassword ? null : { passwordMismatch: true };\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.registerForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.registerForm.markAllAsTouched();\n      return;\n    }\n    \n    const { name, email, password } = this.registerForm.value;\n    this.formSubmit.emit({ name, email, password });\n  }\n\n  /**\n   * Handle login link click\n   */\n  onLogin(): void {\n    this.login.emit();\n  }\n\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName: string, errorName: string): boolean {\n    const control = this.registerForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n\n  /**\n   * Check if form has error\n   * @param errorName - Name of error\n   * @returns True if form has error\n   */\n  hasFormError(errorName: string): boolean {\n    return this.registerForm.touched && this.registerForm.hasError(errorName);\n  }\n\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility(): void {\n    this.hidePassword = !this.hidePassword;\n  }\n\n  /**\n   * Toggle confirm password visibility\n   */\n  toggleConfirmPasswordVisibility(): void {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n}\n", "<!-- Register form container -->\n<div class=\"register-form-container\">\n  <!-- Error message -->\n  <app-error-message \n    *ngIf=\"error\" \n    [message]=\"error\">\n  </app-error-message>\n\n  <!-- Register form -->\n  <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" class=\"register-form\">\n    <!-- Name field -->\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\n      <mat-label>Full Name</mat-label>\n      <input \n        matInput \n        type=\"text\" \n        formControlName=\"name\" \n        placeholder=\"Enter your full name\"\n        autocomplete=\"name\">\n      <mat-icon matPrefix>person</mat-icon>\n      <mat-error *ngIf=\"hasError('name', 'required')\">\n        Name is required\n      </mat-error>\n      <mat-error *ngIf=\"hasError('name', 'minlength')\">\n        Name must be at least 2 characters\n      </mat-error>\n    </mat-form-field>\n\n    <!-- Email field -->\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\n      <mat-label>Email</mat-label>\n      <input \n        matInput \n        type=\"email\" \n        formControlName=\"email\" \n        placeholder=\"Enter your email\"\n        autocomplete=\"email\">\n      <mat-icon matPrefix>email</mat-icon>\n      <mat-error *ngIf=\"hasError('email', 'required')\">\n        Email is required\n      </mat-error>\n      <mat-error *ngIf=\"hasError('email', 'email')\">\n        Please enter a valid email address\n      </mat-error>\n    </mat-form-field>\n\n    <!-- Password field -->\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\n      <mat-label>Password</mat-label>\n      <input \n        matInput \n        [type]=\"hidePassword ? 'password' : 'text'\" \n        formControlName=\"password\"\n        placeholder=\"Create a password\"\n        autocomplete=\"new-password\">\n      <mat-icon matPrefix>lock</mat-icon>\n      <button \n        mat-icon-button \n        matSuffix \n        type=\"button\"\n        (click)=\"togglePasswordVisibility()\" \n        [attr.aria-label]=\"'Hide password'\" \n        [attr.aria-pressed]=\"hidePassword\">\n        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n      </button>\n      <mat-error *ngIf=\"hasError('password', 'required')\">\n        Password is required\n      </mat-error>\n      <mat-error *ngIf=\"hasError('password', 'minlength')\">\n        Password must be at least 8 characters\n      </mat-error>\n      <mat-error *ngIf=\"hasError('password', 'passwordStrength')\">\n        Password must include uppercase, lowercase, number, and special character\n      </mat-error>\n    </mat-form-field>\n\n    <!-- Confirm Password field -->\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\n      <mat-label>Confirm Password</mat-label>\n      <input \n        matInput \n        [type]=\"hideConfirmPassword ? 'password' : 'text'\" \n        formControlName=\"confirmPassword\"\n        placeholder=\"Confirm your password\"\n        autocomplete=\"new-password\">\n      <mat-icon matPrefix>lock</mat-icon>\n      <button \n        mat-icon-button \n        matSuffix \n        type=\"button\"\n        (click)=\"toggleConfirmPasswordVisibility()\" \n        [attr.aria-label]=\"'Hide confirm password'\" \n        [attr.aria-pressed]=\"hideConfirmPassword\">\n        <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n      </button>\n      <mat-error *ngIf=\"hasError('confirmPassword', 'required')\">\n        Please confirm your password\n      </mat-error>\n      <mat-error *ngIf=\"hasFormError('passwordMismatch')\">\n        Passwords do not match\n      </mat-error>\n    </mat-form-field>\n\n    <!-- Terms and conditions checkbox -->\n    <div class=\"terms-container\">\n      <mat-checkbox formControlName=\"termsAccepted\" color=\"primary\">\n        I agree to the <a href=\"#\" target=\"_blank\">Terms of Service</a> and <a href=\"#\" target=\"_blank\">Privacy Policy</a>\n      </mat-checkbox>\n      <mat-error *ngIf=\"hasError('termsAccepted', 'requiredTrue')\" class=\"terms-error\">\n        You must accept the terms and conditions\n      </mat-error>\n    </div>\n\n    <!-- Submit button -->\n    <button \n      mat-raised-button \n      color=\"primary\" \n      type=\"submit\" \n      class=\"submit-button\"\n      [disabled]=\"loading\">\n      <mat-spinner diameter=\"20\" *ngIf=\"loading\"></mat-spinner>\n      <span *ngIf=\"!loading\">Register</span>\n    </button>\n\n    <!-- Login link -->\n    <div class=\"login-link\">\n      Already have an account?\n      <a (click)=\"onLogin()\">Login</a>\n    </div>\n  </form>\n</div>\n"], "mappings": "AAAA;;;;AAIA,SAAoCA,YAAY,QAAwC,eAAe;AACvG,SAAiCC,UAAU,QAA2C,gBAAgB;;;;;;;;;;;;;ICFpGC,EAAA,CAAAC,SAAA,4BAGoB;;;;IADlBD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,KAAA,CAAiB;;;;;IAefJ,EAAA,CAAAK,cAAA,gBAAgD;IAC9CL,EAAA,CAAAM,MAAA,yBACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAAiD;IAC/CL,EAAA,CAAAM,MAAA,2CACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAaZP,EAAA,CAAAK,cAAA,gBAAiD;IAC/CL,EAAA,CAAAM,MAAA,0BACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAA8C;IAC5CL,EAAA,CAAAM,MAAA,2CACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAsBZP,EAAA,CAAAK,cAAA,gBAAoD;IAClDL,EAAA,CAAAM,MAAA,6BACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAAqD;IACnDL,EAAA,CAAAM,MAAA,+CACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAA4D;IAC1DL,EAAA,CAAAM,MAAA,kFACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAsBZP,EAAA,CAAAK,cAAA,gBAA2D;IACzDL,EAAA,CAAAM,MAAA,qCACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAAoD;IAClDL,EAAA,CAAAM,MAAA,+BACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAQZP,EAAA,CAAAK,cAAA,oBAAiF;IAC/EL,EAAA,CAAAM,MAAA,iDACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAUZP,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAK,cAAA,WAAuB;IAAAL,EAAA,CAAAM,MAAA,eAAQ;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;AD5G5C,OAAM,MAAOC,qBAAqB;EAoChC;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAvCtB;;;IAGS,KAAAC,OAAO,GAAG,KAAK;IAExB;;;IAGS,KAAAP,KAAK,GAAkB,IAAI;IAEpC;;;IAGU,KAAAQ,UAAU,GAAG,IAAId,YAAY,EAAqD;IAE5F;;;IAGU,KAAAe,KAAK,GAAG,IAAIf,YAAY,EAAQ;IAO1C;;;IAGA,KAAAgB,YAAY,GAAG,IAAI;IAEnB;;;IAGA,KAAAC,mBAAmB,GAAG,IAAI;EAMY;EAEtC;;;;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEA;;;EAGQA,QAAQA,CAAA;IACd,IAAI,CAACC,YAAY,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MAChCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACuB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACwB,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACbzB,UAAU,CAACsB,QAAQ,EACnBtB,UAAU,CAACuB,SAAS,CAAC,CAAC,CAAC,EACvB,IAAI,CAACG,yBAAyB,CAC/B,CAAC;MACFC,eAAe,EAAE,CAAC,EAAE,EAAE3B,UAAU,CAACsB,QAAQ,CAAC;MAC1CM,aAAa,EAAE,CAAC,KAAK,EAAE5B,UAAU,CAAC6B,YAAY;KAC/C,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEA;;;;;EAKQL,yBAAyBA,CAACM,OAAwB;IACxD,MAAMC,KAAK,GAAWD,OAAO,CAACC,KAAK,IAAI,EAAE;IAEzC,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI;;IAGb,MAAMC,YAAY,GAAG,QAAQ,CAACC,IAAI,CAACF,KAAK,CAAC;IACzC,MAAMG,YAAY,GAAG,QAAQ,CAACD,IAAI,CAACF,KAAK,CAAC;IACzC,MAAMI,UAAU,GAAG,QAAQ,CAACF,IAAI,CAACF,KAAK,CAAC;IACvC,MAAMK,cAAc,GAAG,wCAAwC,CAACH,IAAI,CAACF,KAAK,CAAC;IAE3E,MAAMM,aAAa,GAAGL,YAAY,IAAIE,YAAY,IAAIC,UAAU,IAAIC,cAAc;IAElF,OAAO,CAACC,aAAa,GAAG;MAAEC,gBAAgB,EAAE;IAAI,CAAE,GAAG,IAAI;EAC3D;EAEA;;;;;EAKQT,sBAAsBA,CAACX,KAAsB;IACnD,MAAMK,QAAQ,GAAGL,KAAK,CAACqB,GAAG,CAAC,UAAU,CAAC,EAAER,KAAK;IAC7C,MAAMN,eAAe,GAAGP,KAAK,CAACqB,GAAG,CAAC,iBAAiB,CAAC,EAAER,KAAK;IAE3D,OAAOR,QAAQ,KAAKE,eAAe,GAAG,IAAI,GAAG;MAAEe,gBAAgB,EAAE;IAAI,CAAE;EACzE;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACxB,YAAY,CAACyB,OAAO,IAAI,IAAI,CAAChC,OAAO,EAAE;MAC7C;MACA,IAAI,CAACO,YAAY,CAAC0B,gBAAgB,EAAE;MACpC;;IAGF,MAAM;MAAExB,IAAI;MAAEG,KAAK;MAAEC;IAAQ,CAAE,GAAG,IAAI,CAACN,YAAY,CAACc,KAAK;IACzD,IAAI,CAACpB,UAAU,CAACiC,IAAI,CAAC;MAAEzB,IAAI;MAAEG,KAAK;MAAEC;IAAQ,CAAE,CAAC;EACjD;EAEA;;;EAGAsB,OAAOA,CAAA;IACL,IAAI,CAACjC,KAAK,CAACgC,IAAI,EAAE;EACnB;EAEA;;;;;;EAMAE,QAAQA,CAACC,WAAmB,EAAEC,SAAiB;IAC7C,MAAMlB,OAAO,GAAG,IAAI,CAACb,YAAY,CAACsB,GAAG,CAACQ,WAAW,CAAC;IAClD,OAAO,CAAC,EAAEjB,OAAO,IAAIA,OAAO,CAACmB,OAAO,IAAInB,OAAO,CAACgB,QAAQ,CAACE,SAAS,CAAC,CAAC;EACtE;EAEA;;;;;EAKAE,YAAYA,CAACF,SAAiB;IAC5B,OAAO,IAAI,CAAC/B,YAAY,CAACgC,OAAO,IAAI,IAAI,CAAChC,YAAY,CAAC6B,QAAQ,CAACE,SAAS,CAAC;EAC3E;EAEA;;;EAGAG,wBAAwBA,CAAA;IACtB,IAAI,CAACtC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGAuC,+BAA+BA,CAAA;IAC7B,IAAI,CAACtC,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;;;uBA1JWP,qBAAqB,EAAAR,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAArBhD,qBAAqB;MAAAiD,SAAA;MAAAC,MAAA;QAAA/C,OAAA;QAAAP,KAAA;MAAA;MAAAuD,OAAA;QAAA/C,UAAA;QAAAC,KAAA;MAAA;MAAA+C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlCjE,EAAA,CAAAK,cAAA,aAAqC;UAEnCL,EAAA,CAAAmE,UAAA,IAAAC,kDAAA,+BAGoB;UAGpBpE,EAAA,CAAAK,cAAA,cAA+E;UAA9CL,EAAA,CAAAqE,UAAA,sBAAAC,wDAAA;YAAA,OAAYJ,GAAA,CAAAxB,QAAA,EAAU;UAAA,EAAC;UAEtD1C,EAAA,CAAAK,cAAA,wBAAwD;UAC3CL,EAAA,CAAAM,MAAA,gBAAS;UAAAN,EAAA,CAAAO,YAAA,EAAY;UAChCP,EAAA,CAAAC,SAAA,eAKsB;UACtBD,EAAA,CAAAK,cAAA,kBAAoB;UAAAL,EAAA,CAAAM,MAAA,aAAM;UAAAN,EAAA,CAAAO,YAAA,EAAW;UACrCP,EAAA,CAAAmE,UAAA,IAAAI,0CAAA,uBAEY;UACZvE,EAAA,CAAAmE,UAAA,KAAAK,2CAAA,uBAEY;UACdxE,EAAA,CAAAO,YAAA,EAAiB;UAGjBP,EAAA,CAAAK,cAAA,yBAAwD;UAC3CL,EAAA,CAAAM,MAAA,aAAK;UAAAN,EAAA,CAAAO,YAAA,EAAY;UAC5BP,EAAA,CAAAC,SAAA,gBAKuB;UACvBD,EAAA,CAAAK,cAAA,mBAAoB;UAAAL,EAAA,CAAAM,MAAA,aAAK;UAAAN,EAAA,CAAAO,YAAA,EAAW;UACpCP,EAAA,CAAAmE,UAAA,KAAAM,2CAAA,uBAEY;UACZzE,EAAA,CAAAmE,UAAA,KAAAO,2CAAA,uBAEY;UACd1E,EAAA,CAAAO,YAAA,EAAiB;UAGjBP,EAAA,CAAAK,cAAA,yBAAwD;UAC3CL,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAO,YAAA,EAAY;UAC/BP,EAAA,CAAAC,SAAA,gBAK8B;UAC9BD,EAAA,CAAAK,cAAA,mBAAoB;UAAAL,EAAA,CAAAM,MAAA,YAAI;UAAAN,EAAA,CAAAO,YAAA,EAAW;UACnCP,EAAA,CAAAK,cAAA,iBAMqC;UAFnCL,EAAA,CAAAqE,UAAA,mBAAAM,wDAAA;YAAA,OAAST,GAAA,CAAAd,wBAAA,EAA0B;UAAA,EAAC;UAGpCpD,EAAA,CAAAK,cAAA,gBAAU;UAAAL,EAAA,CAAAM,MAAA,IAAkD;UAAAN,EAAA,CAAAO,YAAA,EAAW;UAEzEP,EAAA,CAAAmE,UAAA,KAAAS,2CAAA,uBAEY;UACZ5E,EAAA,CAAAmE,UAAA,KAAAU,2CAAA,uBAEY;UACZ7E,EAAA,CAAAmE,UAAA,KAAAW,2CAAA,uBAEY;UACd9E,EAAA,CAAAO,YAAA,EAAiB;UAGjBP,EAAA,CAAAK,cAAA,yBAAwD;UAC3CL,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAO,YAAA,EAAY;UACvCP,EAAA,CAAAC,SAAA,iBAK8B;UAC9BD,EAAA,CAAAK,cAAA,mBAAoB;UAAAL,EAAA,CAAAM,MAAA,YAAI;UAAAN,EAAA,CAAAO,YAAA,EAAW;UACnCP,EAAA,CAAAK,cAAA,iBAM4C;UAF1CL,EAAA,CAAAqE,UAAA,mBAAAU,wDAAA;YAAA,OAASb,GAAA,CAAAb,+BAAA,EAAiC;UAAA,EAAC;UAG3CrD,EAAA,CAAAK,cAAA,gBAAU;UAAAL,EAAA,CAAAM,MAAA,IAAyD;UAAAN,EAAA,CAAAO,YAAA,EAAW;UAEhFP,EAAA,CAAAmE,UAAA,KAAAa,2CAAA,uBAEY;UACZhF,EAAA,CAAAmE,UAAA,KAAAc,2CAAA,uBAEY;UACdjF,EAAA,CAAAO,YAAA,EAAiB;UAGjBP,EAAA,CAAAK,cAAA,eAA6B;UAEzBL,EAAA,CAAAM,MAAA,wBAAe;UAAAN,EAAA,CAAAK,cAAA,aAA4B;UAAAL,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAO,YAAA,EAAI;UAACP,EAAA,CAAAM,MAAA,aAAI;UAAAN,EAAA,CAAAK,cAAA,aAA4B;UAAAL,EAAA,CAAAM,MAAA,sBAAc;UAAAN,EAAA,CAAAO,YAAA,EAAI;UAEpHP,EAAA,CAAAmE,UAAA,KAAAe,2CAAA,wBAEY;UACdlF,EAAA,CAAAO,YAAA,EAAM;UAGNP,EAAA,CAAAK,cAAA,kBAKuB;UACrBL,EAAA,CAAAmE,UAAA,KAAAgB,6CAAA,0BAAyD;UACzDnF,EAAA,CAAAmE,UAAA,KAAAiB,sCAAA,kBAAsC;UACxCpF,EAAA,CAAAO,YAAA,EAAS;UAGTP,EAAA,CAAAK,cAAA,eAAwB;UACtBL,EAAA,CAAAM,MAAA,kCACA;UAAAN,EAAA,CAAAK,cAAA,aAAuB;UAApBL,EAAA,CAAAqE,UAAA,mBAAAgB,mDAAA;YAAA,OAASnB,GAAA,CAAApB,OAAA,EAAS;UAAA,EAAC;UAAC9C,EAAA,CAAAM,MAAA,aAAK;UAAAN,EAAA,CAAAO,YAAA,EAAI;;;UA3HjCP,EAAA,CAAAsF,SAAA,GAAW;UAAXtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAA9D,KAAA,CAAW;UAKRJ,EAAA,CAAAsF,SAAA,GAA0B;UAA1BtF,EAAA,CAAAE,UAAA,cAAAgE,GAAA,CAAAhD,YAAA,CAA0B;UAWhBlB,EAAA,CAAAsF,SAAA,GAAkC;UAAlCtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnB,QAAA,qBAAkC;UAGlC/C,EAAA,CAAAsF,SAAA,GAAmC;UAAnCtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnB,QAAA,sBAAmC;UAenC/C,EAAA,CAAAsF,SAAA,GAAmC;UAAnCtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnB,QAAA,sBAAmC;UAGnC/C,EAAA,CAAAsF,SAAA,GAAgC;UAAhCtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnB,QAAA,mBAAgC;UAU1C/C,EAAA,CAAAsF,SAAA,GAA2C;UAA3CtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAApD,YAAA,uBAA2C;UAU3Cd,EAAA,CAAAsF,SAAA,GAAmC;UAAnCtF,EAAA,CAAAuF,WAAA,+BAAmC,iBAAArB,GAAA,CAAApD,YAAA;UAEzBd,EAAA,CAAAsF,SAAA,GAAkD;UAAlDtF,EAAA,CAAAwF,iBAAA,CAAAtB,GAAA,CAAApD,YAAA,mCAAkD;UAElDd,EAAA,CAAAsF,SAAA,GAAsC;UAAtCtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnB,QAAA,yBAAsC;UAGtC/C,EAAA,CAAAsF,SAAA,GAAuC;UAAvCtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnB,QAAA,0BAAuC;UAGvC/C,EAAA,CAAAsF,SAAA,GAA8C;UAA9CtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnB,QAAA,iCAA8C;UAUxD/C,EAAA,CAAAsF,SAAA,GAAkD;UAAlDtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnD,mBAAA,uBAAkD;UAUlDf,EAAA,CAAAsF,SAAA,GAA2C;UAA3CtF,EAAA,CAAAuF,WAAA,uCAA2C,iBAAArB,GAAA,CAAAnD,mBAAA;UAEjCf,EAAA,CAAAsF,SAAA,GAAyD;UAAzDtF,EAAA,CAAAwF,iBAAA,CAAAtB,GAAA,CAAAnD,mBAAA,mCAAyD;UAEzDf,EAAA,CAAAsF,SAAA,GAA6C;UAA7CtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnB,QAAA,gCAA6C;UAG7C/C,EAAA,CAAAsF,SAAA,GAAsC;UAAtCtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAf,YAAA,qBAAsC;UAUtCnD,EAAA,CAAAsF,SAAA,GAA+C;UAA/CtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAnB,QAAA,kCAA+C;UAW3D/C,EAAA,CAAAsF,SAAA,GAAoB;UAApBtF,EAAA,CAAAE,UAAA,aAAAgE,GAAA,CAAAvD,OAAA,CAAoB;UACQX,EAAA,CAAAsF,SAAA,GAAa;UAAbtF,EAAA,CAAAE,UAAA,SAAAgE,GAAA,CAAAvD,OAAA,CAAa;UAClCX,EAAA,CAAAsF,SAAA,GAAc;UAAdtF,EAAA,CAAAE,UAAA,UAAAgE,GAAA,CAAAvD,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}