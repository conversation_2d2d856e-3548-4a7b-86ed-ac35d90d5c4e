/**
 * Task Create Page Component
 * Page for creating a new task
 */
import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TaskService } from '../../../../core/services/task.service';
import { UserService } from '../../../../core/services/user.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { Task } from '../../../../core/models/task.model';
import { User } from '../../../../core/models/user.model';

@Component({
  selector: 'app-task-create-page',
  templateUrl: './task-create-page.component.html',
  styleUrls: ['./task-create-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskCreatePageComponent implements OnInit, On<PERSON><PERSON>roy {
  /**
   * List of users
   */
  users: User[] = [];
  
  /**
   * Loading state indicator
   */
  loading = false;
  
  /**
   * Subject for unsubscribing from observables
   */
  private destroy$ = new Subject<void>();

  /**
   * Constructor with dependency injection
   * @param taskService - Task service for CRUD operations
   * @param userService - User service for user data
   * @param notificationService - Notification service for displaying messages
   * @param router - Router for navigation
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(
    private taskService: TaskService,
    private userService: UserService,
    private notificationService: NotificationService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Loads users for assignee selection
   */
  ngOnInit(): void {
    this.loadUsers();
  }

  /**
   * Lifecycle hook that is called before component destruction
   * Unsubscribes from observables
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load users for assignee selection
   */
  loadUsers(): void {
    this.userService.getUsers()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (users) => {
          this.users = users;
          this.cdr.markForCheck();
        },
        error: (err) => {
          console.error('Error loading users:', err);
          this.notificationService.error('Failed to load users');
        }
      });
  }

  /**
   * Handle form submission
   * @param taskData - Task data from form
   */
  onFormSubmit(taskData: Partial<Task>): void {
    this.loading = true;
    this.cdr.markForCheck();
    
    this.taskService.createTask(taskData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (createdTask) => {
          this.loading = false;
          this.notificationService.success('Task created successfully');
          this.router.navigate(['/tasks', createdTask.id]);
        },
        error: (err) => {
          this.loading = false;
          this.notificationService.error('Failed to create task');
          console.error('Error creating task:', err);
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Handle form cancellation
   */
  onFormCancel(): void {
    this.router.navigate(['/tasks']);
  }
}
