/**
 * Task filter component styles
 */

/* Filter container */
.task-filter-container {
  padding: 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #eee;
}

/* Filter form */
.filter-form {
  width: 100%;
}

/* Filter row layout */
.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
  
  mat-form-field {
    flex: 1;
    min-width: 150px;
  }
}

/* Search field styling */
.search-field {
  flex: 2;
  min-width: 250px;
}

/* Filter actions container */
.filter-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  
  button {
    white-space: nowrap;
    
    mat-icon {
      font-size: 18px;
      height: 18px;
      width: 18px;
      margin-left: 4px;
    }
  }
}

/* Advanced filters section */
.advanced-filters {
  padding-top: 8px;
  border-top: 1px solid #eee;
  margin-top: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 0;
    
    mat-form-field {
      width: 100%;
    }
  }
  
  .filter-actions {
    margin-top: 8px;
    justify-content: space-between;
    width: 100%;
  }
}
