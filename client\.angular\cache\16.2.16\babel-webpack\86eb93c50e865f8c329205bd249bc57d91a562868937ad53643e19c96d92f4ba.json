{"ast": null, "code": "import * as __Ng<PERSON>li_bootstrap_1 from \"@angular/platform-browser\";\nimport { AppModule } from './app/app.module';\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.error(err));", "map": {"version": 3, "names": ["AppModule", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err", "console", "error"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\main.ts"], "sourcesContent": ["/**\n * Main entry point for the Angular application\n * Bootstraps the AppModule\n */\nimport { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\n\nimport { AppModule } from './app/app.module';\n\nplatformBrowserDynamic().bootstrapModule(AppModule)\n  .catch((err: Error) => console.error(err));\n"], "mappings": ";AAMA,SAASA,SAAS,QAAQ,kBAAkB;AAE5CC,mBAAA,CAAAC,eAAA,EAAwB,CAACC,eAAe,CAACH,SAAS,CAAC,CAChDI,KAAK,CAAEC,GAAU,IAAKC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}