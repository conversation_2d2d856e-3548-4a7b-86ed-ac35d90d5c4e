{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Tasks Page Component\n * Main page for displaying and managing tasks\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';\nexport let TasksPageComponent = class TasksPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param dialog - Dialog service for confirmation dialogs\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(taskService, userService, notificationService, dialog, router, cdr) {\n    this.taskService = taskService;\n    this.userService = userService;\n    this.notificationService = notificationService;\n    this.dialog = dialog;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * List of tasks\n     */\n    this.tasks = [];\n    /**\n     * List of users\n     */\n    this.users = [];\n    /**\n     * Current filter applied to tasks\n     */\n    this.filter = {};\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message if loading failed\n     */\n    this.error = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads tasks and users\n   */\n  ngOnInit() {\n    this.loadTasks();\n    this.loadUsers();\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Load tasks with current filter\n   */\n  loadTasks() {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    this.taskService.getTasks(this.filter).pipe(takeUntil(this.destroy$)).subscribe({\n      next: tasks => {\n        this.tasks = tasks;\n        this.loading = false;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.error = 'Failed to load tasks. Please try again.';\n        this.loading = false;\n        console.error('Error loading tasks:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers() {\n    this.userService.getUsers().pipe(takeUntil(this.destroy$)).subscribe({\n      next: users => {\n        this.users = users;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        console.error('Error loading users:', err);\n      }\n    });\n  }\n  /**\n   * Handle task selection\n   * @param task - Selected task\n   */\n  onTaskSelected(task) {\n    this.router.navigate(['/tasks', task.id]);\n  }\n  /**\n   * Handle task deletion\n   * @param taskId - ID of task to delete\n   */\n  onTaskDeleted(taskId) {\n    const dialogRef = this.dialog.open(ConfirmDialogComponent, {\n      data: {\n        title: 'Delete Task',\n        message: 'Are you sure you want to delete this task? This action cannot be undone.',\n        confirmText: 'Delete',\n        cancelText: 'Cancel',\n        confirmColor: 'warn'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.taskService.deleteTask(taskId).pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            this.tasks = this.tasks.filter(task => task.id !== taskId);\n            this.notificationService.success('Task deleted successfully');\n            this.cdr.markForCheck();\n          },\n          error: err => {\n            this.notificationService.error('Failed to delete task');\n            console.error('Error deleting task:', err);\n          }\n        });\n      }\n    });\n  }\n  /**\n   * Handle task status change\n   * @param event - Object containing taskId and status\n   */\n  onStatusChanged(event) {\n    const {\n      taskId,\n      status\n    } = event;\n    this.taskService.updateTask(taskId, {\n      status\n    }).pipe(takeUntil(this.destroy$)).subscribe({\n      next: updatedTask => {\n        // Update task in list\n        this.tasks = this.tasks.map(task => task.id === taskId ? updatedTask : task);\n        this.notificationService.success('Task status updated');\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.notificationService.error('Failed to update task status');\n        console.error('Error updating task status:', err);\n      }\n    });\n  }\n  /**\n   * Handle filter changes\n   * @param filter - New filter\n   */\n  onFilterChanged(filter) {\n    this.filter = filter;\n    this.loadTasks();\n  }\n  /**\n   * Handle refresh request\n   */\n  onRefresh() {\n    this.loadTasks();\n  }\n};\nTasksPageComponent = __decorate([Component({\n  selector: 'app-tasks-page',\n  templateUrl: './tasks-page.component.html',\n  styleUrls: ['./tasks-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TasksPageComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "Subject", "takeUntil", "ConfirmDialogComponent", "TasksPageComponent", "constructor", "taskService", "userService", "notificationService", "dialog", "router", "cdr", "tasks", "users", "filter", "loading", "error", "destroy$", "ngOnInit", "loadTasks", "loadUsers", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTasks", "pipe", "subscribe", "err", "console", "getUsers", "onTaskSelected", "task", "navigate", "id", "onTaskDeleted", "taskId", "dialogRef", "open", "data", "title", "message", "confirmText", "cancelText", "confirmColor", "afterClosed", "result", "deleteTask", "success", "onStatusChanged", "event", "status", "updateTask", "updatedTask", "map", "onFilterChanged", "onRefresh", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\pages\\tasks-page\\tasks-page.component.ts"], "sourcesContent": ["/**\n * Tasks Page Component\n * Main page for displaying and managing tasks\n */\nimport { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { TaskService } from '../../../../core/services/task.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\nimport { TaskFilter } from '../../../../core/models/task-filter.model';\nimport { MatDialog } from '@angular/material/dialog';\nimport { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';\n\n@Component({\n  selector: 'app-tasks-page',\n  templateUrl: './tasks-page.component.html',\n  styleUrls: ['./tasks-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TasksPageComponent implements OnInit, OnDestroy {\n  /**\n   * List of tasks\n   */\n  tasks: Task[] = [];\n  \n  /**\n   * List of users\n   */\n  users: User[] = [];\n  \n  /**\n   * Current filter applied to tasks\n   */\n  filter: TaskFilter = {};\n  \n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message if loading failed\n   */\n  error: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param dialog - Dialog service for confirmation dialogs\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private taskService: TaskService,\n    private userService: UserService,\n    private notificationService: NotificationService,\n    private dialog: MatDialog,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads tasks and users\n   */\n  ngOnInit(): void {\n    this.loadTasks();\n    this.loadUsers();\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Load tasks with current filter\n   */\n  loadTasks(): void {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    \n    this.taskService.getTasks(this.filter)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (tasks) => {\n          this.tasks = tasks;\n          this.loading = false;\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          this.error = 'Failed to load tasks. Please try again.';\n          this.loading = false;\n          console.error('Error loading tasks:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers(): void {\n    this.userService.getUsers()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (users) => {\n          this.users = users;\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          console.error('Error loading users:', err);\n        }\n      });\n  }\n\n  /**\n   * Handle task selection\n   * @param task - Selected task\n   */\n  onTaskSelected(task: Task): void {\n    this.router.navigate(['/tasks', task.id]);\n  }\n\n  /**\n   * Handle task deletion\n   * @param taskId - ID of task to delete\n   */\n  onTaskDeleted(taskId: string): void {\n    const dialogRef = this.dialog.open(ConfirmDialogComponent, {\n      data: {\n        title: 'Delete Task',\n        message: 'Are you sure you want to delete this task? This action cannot be undone.',\n        confirmText: 'Delete',\n        cancelText: 'Cancel',\n        confirmColor: 'warn'\n      }\n    });\n    \n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.taskService.deleteTask(taskId)\n          .pipe(takeUntil(this.destroy$))\n          .subscribe({\n            next: () => {\n              this.tasks = this.tasks.filter(task => task.id !== taskId);\n              this.notificationService.success('Task deleted successfully');\n              this.cdr.markForCheck();\n            },\n            error: (err) => {\n              this.notificationService.error('Failed to delete task');\n              console.error('Error deleting task:', err);\n            }\n          });\n      }\n    });\n  }\n\n  /**\n   * Handle task status change\n   * @param event - Object containing taskId and status\n   */\n  onStatusChanged(event: { taskId: string; status: string }): void {\n    const { taskId, status } = event;\n    \n    this.taskService.updateTask(taskId, { status })\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (updatedTask) => {\n          // Update task in list\n          this.tasks = this.tasks.map(task => \n            task.id === taskId ? updatedTask : task\n          );\n          this.notificationService.success('Task status updated');\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          this.notificationService.error('Failed to update task status');\n          console.error('Error updating task status:', err);\n        }\n      });\n  }\n\n  /**\n   * Handle filter changes\n   * @param filter - New filter\n   */\n  onFilterChanged(filter: TaskFilter): void {\n    this.filter = filter;\n    this.loadTasks();\n  }\n\n  /**\n   * Handle refresh request\n   */\n  onRefresh(): void {\n    this.loadTasks();\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AAExG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAQ1C,SAASC,sBAAsB,QAAQ,uEAAuE;AAQvG,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EA+B7B;;;;;;;;;EASAC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAiB,EACjBC,MAAc,EACdC,GAAsB;IALtB,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA7Cb;;;IAGA,KAAAC,KAAK,GAAW,EAAE;IAElB;;;IAGA,KAAAC,KAAK,GAAW,EAAE;IAElB;;;IAGA,KAAAC,MAAM,GAAe,EAAE;IAEvB;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIhB,OAAO,EAAQ;EAkBnC;EAEH;;;;EAIAiB,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;EAGAJ,SAASA,CAAA;IACP,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACL,GAAG,CAACa,YAAY,EAAE;IAEvB,IAAI,CAAClB,WAAW,CAACmB,QAAQ,CAAC,IAAI,CAACX,MAAM,CAAC,CACnCY,IAAI,CAACxB,SAAS,CAAC,IAAI,CAACe,QAAQ,CAAC,CAAC,CAC9BU,SAAS,CAAC;MACTL,IAAI,EAAGV,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACG,OAAO,GAAG,KAAK;QACpB,IAAI,CAACJ,GAAG,CAACa,YAAY,EAAE;MACzB,CAAC;MACDR,KAAK,EAAGY,GAAG,IAAI;QACb,IAAI,CAACZ,KAAK,GAAG,yCAAyC;QACtD,IAAI,CAACD,OAAO,GAAG,KAAK;QACpBc,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEY,GAAG,CAAC;QAC1C,IAAI,CAACjB,GAAG,CAACa,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAJ,SAASA,CAAA;IACP,IAAI,CAACb,WAAW,CAACuB,QAAQ,EAAE,CACxBJ,IAAI,CAACxB,SAAS,CAAC,IAAI,CAACe,QAAQ,CAAC,CAAC,CAC9BU,SAAS,CAAC;MACTL,IAAI,EAAGT,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACF,GAAG,CAACa,YAAY,EAAE;MACzB,CAAC;MACDR,KAAK,EAAGY,GAAG,IAAI;QACbC,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEY,GAAG,CAAC;MAC5C;KACD,CAAC;EACN;EAEA;;;;EAIAG,cAAcA,CAACC,IAAU;IACvB,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,QAAQ,EAAED,IAAI,CAACE,EAAE,CAAC,CAAC;EAC3C;EAEA;;;;EAIAC,aAAaA,CAACC,MAAc;IAC1B,MAAMC,SAAS,GAAG,IAAI,CAAC5B,MAAM,CAAC6B,IAAI,CAACnC,sBAAsB,EAAE;MACzDoC,IAAI,EAAE;QACJC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE,0EAA0E;QACnFC,WAAW,EAAE,QAAQ;QACrBC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE;;KAEjB,CAAC;IAEFP,SAAS,CAACQ,WAAW,EAAE,CAAClB,SAAS,CAACmB,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV,IAAI,CAACxC,WAAW,CAACyC,UAAU,CAACX,MAAM,CAAC,CAChCV,IAAI,CAACxB,SAAS,CAAC,IAAI,CAACe,QAAQ,CAAC,CAAC,CAC9BU,SAAS,CAAC;UACTL,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACV,KAAK,GAAG,IAAI,CAACA,KAAK,CAACE,MAAM,CAACkB,IAAI,IAAIA,IAAI,CAACE,EAAE,KAAKE,MAAM,CAAC;YAC1D,IAAI,CAAC5B,mBAAmB,CAACwC,OAAO,CAAC,2BAA2B,CAAC;YAC7D,IAAI,CAACrC,GAAG,CAACa,YAAY,EAAE;UACzB,CAAC;UACDR,KAAK,EAAGY,GAAG,IAAI;YACb,IAAI,CAACpB,mBAAmB,CAACQ,KAAK,CAAC,uBAAuB,CAAC;YACvDa,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEY,GAAG,CAAC;UAC5C;SACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAEA;;;;EAIAqB,eAAeA,CAACC,KAAyC;IACvD,MAAM;MAAEd,MAAM;MAAEe;IAAM,CAAE,GAAGD,KAAK;IAEhC,IAAI,CAAC5C,WAAW,CAAC8C,UAAU,CAAChB,MAAM,EAAE;MAAEe;IAAM,CAAE,CAAC,CAC5CzB,IAAI,CAACxB,SAAS,CAAC,IAAI,CAACe,QAAQ,CAAC,CAAC,CAC9BU,SAAS,CAAC;MACTL,IAAI,EAAG+B,WAAW,IAAI;QACpB;QACA,IAAI,CAACzC,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0C,GAAG,CAACtB,IAAI,IAC9BA,IAAI,CAACE,EAAE,KAAKE,MAAM,GAAGiB,WAAW,GAAGrB,IAAI,CACxC;QACD,IAAI,CAACxB,mBAAmB,CAACwC,OAAO,CAAC,qBAAqB,CAAC;QACvD,IAAI,CAACrC,GAAG,CAACa,YAAY,EAAE;MACzB,CAAC;MACDR,KAAK,EAAGY,GAAG,IAAI;QACb,IAAI,CAACpB,mBAAmB,CAACQ,KAAK,CAAC,8BAA8B,CAAC;QAC9Da,OAAO,CAACb,KAAK,CAAC,6BAA6B,EAAEY,GAAG,CAAC;MACnD;KACD,CAAC;EACN;EAEA;;;;EAIA2B,eAAeA,CAACzC,MAAkB;IAChC,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACK,SAAS,EAAE;EAClB;EAEA;;;EAGAqC,SAASA,CAAA;IACP,IAAI,CAACrC,SAAS,EAAE;EAClB;CACD;AA/LYf,kBAAkB,GAAAqD,UAAA,EAN9B1D,SAAS,CAAC;EACT2D,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,6BAA6B,CAAC;EAC1CC,eAAe,EAAE7D,uBAAuB,CAAC8D;CAC1C,CAAC,C,EACW1D,kBAAkB,CA+L9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}