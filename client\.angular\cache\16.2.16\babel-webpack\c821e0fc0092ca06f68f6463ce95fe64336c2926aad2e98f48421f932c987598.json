{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Footer Component\n * Application footer with copyright and links\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { environment } from '@environments/environment';\nexport let FooterComponent = class FooterComponent {\n  constructor() {\n    /**\n     * Current year for copyright\n     */\n    this.currentYear = new Date().getFullYear();\n    /**\n     * Application version from environment\n     */\n    this.appVersion = environment.version;\n  }\n};\nFooterComponent = __decorate([Component({\n  selector: 'app-footer',\n  templateUrl: './footer.component.html',\n  styleUrls: ['./footer.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], FooterComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "environment", "FooterComponent", "constructor", "currentYear", "Date", "getFullYear", "appVersion", "version", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\components\\footer\\footer.component.ts"], "sourcesContent": ["/**\n * Footer Component\n * Application footer with copyright and links\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { environment } from '@environments/environment';\n\n@Component({\n  selector: 'app-footer',\n  templateUrl: './footer.component.html',\n  styleUrls: ['./footer.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class FooterComponent {\n  /**\n   * Current year for copyright\n   */\n  currentYear = new Date().getFullYear();\n  \n  /**\n   * Application version from environment\n   */\n  appVersion = environment.version;\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAEC,uBAAuB,QAAQ,eAAe;AAClE,SAASC,WAAW,QAAQ,2BAA2B;AAQhD,WAAMC,eAAe,GAArB,MAAMA,eAAe;EAArBC,YAAA;IACL;;;IAGA,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAEtC;;;IAGA,KAAAC,UAAU,GAAGN,WAAW,CAACO,OAAO;EAClC;CAAC;AAVYN,eAAe,GAAAO,UAAA,EAN3BV,SAAS,CAAC;EACTW,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,yBAAyB;EACtCC,SAAS,EAAE,CAAC,yBAAyB,CAAC;EACtCC,eAAe,EAAEb,uBAAuB,CAACc;CAC1C,CAAC,C,EACWZ,eAAe,CAU3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}