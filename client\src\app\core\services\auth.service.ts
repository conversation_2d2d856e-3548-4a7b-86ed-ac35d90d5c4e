/**
 * Authentication service
 * Handles user authentication, registration, password reset, and token management
 */
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { User } from '../models/user.model';
import { AuthResponse } from '../models/auth-response.model';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  /**
   * API endpoint path
   */
  private endpoint = 'auth';
  
  /**
   * BehaviorSubject to track current user
   */
  private currentUserSubject: BehaviorSubject<User | null>;
  
  /**
   * Observable to expose current user to components
   */
  public currentUser$: Observable<User | null>;
  
  /**
   * BehaviorSubject to track authentication status
   */
  private isAuthenticatedSubject: BehaviorSubject<boolean>;
  
  /**
   * Observable to expose authentication status to components
   */
  public isAuthenticated$: Observable<boolean>;

  /**
   * Constructor with dependency injection
   * @param apiService - Base API service for HTTP requests
   * @param router - Router for navigation
   */
  constructor(
    private apiService: ApiService,
    private router: Router
  ) {
    // Initialize from localStorage if available
    const storedUser = localStorage.getItem('currentUser');
    this.currentUserSubject = new BehaviorSubject<User | null>(
      storedUser ? JSON.parse(storedUser) : null
    );
    this.currentUser$ = this.currentUserSubject.asObservable();
    
    this.isAuthenticatedSubject = new BehaviorSubject<boolean>(!!storedUser);
    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  }

  /**
   * Get current user value
   * @returns Current user or null
   */
  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Login user with email and password
   * @param email - User email
   * @param password - User password
   * @returns Observable with authentication response
   */
  login(email: string, password: string): Observable<AuthResponse> {
    return this.apiService.post<AuthResponse>(`${this.endpoint}/login`, { email, password })
      .pipe(
        tap((response: AuthResponse) => this.handleAuthentication(response)),
        catchError((error: unknown) => {
          console.error('Login error:', error);
          return of({ success: false, message: error.error?.message || 'Login failed' } as AuthResponse);
        })
      );
  }

  /**
   * Register new user
   * @param name - User name
   * @param email - User email
   * @param password - User password
   * @returns Observable with authentication response
   */
  register(name: string, email: string, password: string): Observable<AuthResponse> {
    return this.apiService.post<AuthResponse>(`${this.endpoint}/register`, { name, email, password })
      .pipe(
        tap((response: AuthResponse) => this.handleAuthentication(response)),
        catchError((error: unknown) => {
          console.error('Registration error:', error);
          return of({ success: false, message: error.error?.message || 'Registration failed' } as AuthResponse);
        })
      );
  }

  /**
   * Logout user and clear stored data
   */
  logout(): void {
    // Remove user from local storage
    localStorage.removeItem('currentUser');
    localStorage.removeItem('token');
    
    // Reset subjects
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    
    // Navigate to login page
    this.router.navigate(['/auth/login']);
  }

  /**
   * Get current user profile from API
   * @returns Observable with user data
   */
  getProfile(): Observable<User> {
    return this.apiService.get<{ success: boolean, data: User }>(`${this.endpoint}/me`)
      .pipe(
        map((response: { success: boolean, data: User }) => response.data),
        tap((user: User) => {
          // Update stored user data
          this.currentUserSubject.next(user);
          localStorage.setItem('currentUser', JSON.stringify(user));
        })
      );
  }

  /**
   * Check if token is valid
   * @returns Observable boolean indicating if token is valid
   */
  checkToken(): Observable<boolean> {
    const token = localStorage.getItem('token');
    
    if (!token) {
      this.isAuthenticatedSubject.next(false);
      return of(false);
    }
    
    // Verify token with backend
    return this.apiService.get<{ success: boolean }>(`${this.endpoint}/verify-token`)
      .pipe(
        map((response: { success: boolean }) => {
          const isValid = response.success;
          this.isAuthenticatedSubject.next(isValid);
          
          if (!isValid) {
            this.logout();
          }
          
          return isValid;
        }),
        catchError(() => {
          this.logout();
          return of(false);
        })
      );
  }

  /**
   * Request password reset email
   * @param email - User email
   * @returns Observable with success status
   */
  forgotPassword(email: string): Observable<{ success: boolean; message: string }> {
    return this.apiService.post<{ success: boolean; message: string }>(
      `${this.endpoint}/forgot-password`, 
      { email }
    ).pipe(
      catchError((error: any) => {
        console.error('Forgot password error:', error);
        return of({ 
          success: false, 
          message: error.error?.message || 'Failed to send password reset email' 
        });
      })
    );
  }

  /**
   * Reset password with token
   * @param token - Reset token from email
   * @param password - New password
   * @returns Observable with success status
   */
  resetPassword(token: string, password: string): Observable<{ success: boolean; message: string }> {
    return this.apiService.post<{ success: boolean; message: string }>(
      `${this.endpoint}/reset-password`, 
      { token, password }
    ).pipe(
      catchError((error: any) => {
        console.error('Reset password error:', error);
        return of({ 
          success: false, 
          message: error.error?.message || 'Failed to reset password' 
        });
      })
    );
  }

  /**
   * Check if user is authenticated
   * @returns Boolean indicating if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.currentUserSubject.value && !!localStorage.getItem('token');
  }

  /**
   * Handle authentication response
   * @param response - Authentication response from API
   */
  private handleAuthentication(response: AuthResponse): void {
    if (response.success && response.data) {
      // Store user details and token
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));
      localStorage.setItem('token', response.data.token);
      
      // Update subjects
      this.currentUserSubject.next(response.data.user);
      this.isAuthenticatedSubject.next(true);
    }
  }
}
