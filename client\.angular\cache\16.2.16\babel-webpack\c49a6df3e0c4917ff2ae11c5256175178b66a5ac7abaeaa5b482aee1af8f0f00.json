{"ast": null, "code": "import { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class FooterComponent {\n  constructor() {\n    /**\n     * Current year for copyright\n     */\n    this.currentYear = new Date().getFullYear();\n    /**\n     * Application version from environment\n     */\n    this.appVersion = environment.version;\n  }\n  static {\n    this.ɵfac = function FooterComponent_Factory(t) {\n      return new (t || FooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"app-footer\"]],\n      decls: 13,\n      vars: 2,\n      consts: [[1, \"footer\"], [1, \"footer-content\"], [1, \"copyright\"], [1, \"footer-links\"], [\"href\", \"/about\"], [\"href\", \"/privacy\"], [\"href\", \"/terms\"], [1, \"version\"]],\n      template: function FooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"a\", 4);\n          i0.ɵɵtext(6, \"About\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"a\", 5);\n          i0.ɵɵtext(8, \"Privacy Policy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 6);\n          i0.ɵɵtext(10, \"Terms of Service\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \\u00A9 \", ctx.currentYear, \" Task Management Application \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" v\", ctx.appVersion, \" \");\n        }\n      },\n      styles: [\"\\n\\n\\n\\n\\n\\n.footer[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  border-top: 1px solid #e0e0e0;\\n  padding: 16px;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.footer-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.copyright[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n\\n\\n.footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n}\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #303f9f;\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.version[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .footer-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    text-align: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29yZS9jb21wb25lbnRzL2Zvb3Rlci9mb290ZXIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7O0VBQUE7QUFJQSwwQkFBQTtBQUNBO0VBQ0UseUJBQUE7RUFDQSw2QkFBQTtFQUNBLGFBQUE7RUFDQSxpQkFBQTtBQUFGOztBQUdBLDBCQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtBQUFGOztBQUdBLG1CQUFBO0FBQ0E7RUFDRSxXQUFBO0FBQUY7O0FBR0EseUJBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FBQUY7QUFFRTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtFQUNBLDJCQUFBO0FBQUo7QUFFSTtFQUNFLGNBQUE7RUFDQSwwQkFBQTtBQUFOOztBQUtBLGlCQUFBO0FBQ0E7RUFDRSxXQUFBO0VBQ0EsaUJBQUE7QUFGRjs7QUFLQSwyQkFBQTtBQUNBO0VBQ0U7SUFDRSxzQkFBQTtJQUNBLFNBQUE7SUFDQSxrQkFBQTtFQUZGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEZvb3RlciBjb21wb25lbnQgc3R5bGVzXG4gKi9cblxuLyogTWFpbiBmb290ZXIgY29udGFpbmVyICovXG4uZm9vdGVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlMGUwZTA7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xufVxuXG4vKiBGb290ZXIgY29udGVudCBsYXlvdXQgKi9cbi5mb290ZXItY29udGVudCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgbWF4LXdpZHRoOiAxMjAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xufVxuXG4vKiBDb3B5cmlnaHQgdGV4dCAqL1xuLmNvcHlyaWdodCB7XG4gIGNvbG9yOiAjNjY2O1xufVxuXG4vKiBGb290ZXIgbGlua3Mgc2VjdGlvbiAqL1xuLmZvb3Rlci1saW5rcyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMTZweDtcbiAgXG4gIGEge1xuICAgIGNvbG9yOiAjM2Y1MWI1O1xuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICB0cmFuc2l0aW9uOiBjb2xvciAwLjJzIGVhc2U7XG4gICAgXG4gICAgJjpob3ZlciB7XG4gICAgICBjb2xvcjogIzMwM2Y5ZjtcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuICAgIH1cbiAgfVxufVxuXG4vKiBWZXJzaW9uIHRleHQgKi9cbi52ZXJzaW9uIHtcbiAgY29sb3I6ICM5OTk7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xufVxuXG4vKiBSZXNwb25zaXZlIGFkanVzdG1lbnRzICovXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLmZvb3Rlci1jb250ZW50IHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGdhcDogMTJweDtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "FooterComponent", "constructor", "currentYear", "Date", "getFullYear", "appVersion", "version", "selectors", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\components\\footer\\footer.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\components\\footer\\footer.component.html"], "sourcesContent": ["/**\n * Footer Component\n * Application footer with copyright and links\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { environment } from '@environments/environment';\n\n@Component({\n  selector: 'app-footer',\n  templateUrl: './footer.component.html',\n  styleUrls: ['./footer.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class FooterComponent {\n  /**\n   * Current year for copyright\n   */\n  currentYear = new Date().getFullYear();\n  \n  /**\n   * Application version from environment\n   */\n  appVersion = environment.version;\n}\n", "<!-- Application footer -->\n<footer class=\"footer\">\n  <div class=\"footer-content\">\n    <!-- Copyright information -->\n    <div class=\"copyright\">\n      &copy; {{ currentYear }} Task Management Application\n    </div>\n    \n    <!-- Footer links -->\n    <div class=\"footer-links\">\n      <a href=\"/about\">About</a>\n      <a href=\"/privacy\">Privacy Policy</a>\n      <a href=\"/terms\">Terms of Service</a>\n    </div>\n    \n    <!-- Version information -->\n    <div class=\"version\">\n      v{{ appVersion }}\n    </div>\n  </div>\n</footer>\n"], "mappings": "AAKA,SAASA,WAAW,QAAQ,2BAA2B;;AAQvD,OAAM,MAAOC,eAAe;EAN5BC,YAAA;IAOE;;;IAGA,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAEtC;;;IAGA,KAAAC,UAAU,GAAGN,WAAW,CAACO,OAAO;;;;uBATrBN,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAO,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ5BE,EAAA,CAAAC,cAAA,gBAAuB;UAIjBD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,aAA0B;UACPD,EAAA,CAAAE,MAAA,YAAK;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC1BH,EAAA,CAAAC,cAAA,WAAmB;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACrCH,EAAA,CAAAC,cAAA,WAAiB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIvCH,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;;;UAbJH,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,aAAAN,GAAA,CAAAZ,WAAA,kCACF;UAWEa,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,OAAAN,GAAA,CAAAT,UAAA,MACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}