/**
 * Not found handler middleware
 * Handles requests to non-existent routes
 */
import { Request, Response, NextFunction } from 'express';
import { AppError } from '../../domain/common/AppError';

/**
 * Middleware to handle 404 not found errors
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Create a custom 404 error
  const error = new AppError(`Resource not found - ${req.originalUrl}`, 404);
  
  // Pass to error handler middleware
  next(error);
};
