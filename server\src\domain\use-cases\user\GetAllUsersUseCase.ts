/**
 * Get All Users Use Case
 * Handles business logic for retrieving all users with optional filtering
 */
import { IUser, IUserDocument } from '../../entities/User';
import { IUserRepository } from '../../repositories/IUserRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for retrieving all users with optional filtering
 * Implements business rules and validation for user listing
 */
export class GetAllUsersUseCase {
  /**
   * Constructor for GetAllUsersUseCase
   * @param userRepository - Repository for user data access
   */
  constructor(private userRepository: IUserRepository) {}

  /**
   * Executes the use case to retrieve all users with optional filtering
   * @param filter - Optional filter criteria
   * @returns Promise resolving to array of users
   */
  async execute(filter: Partial<IUser> = {}): Promise<IUserDocument[]> {
    try {
      // Get all users with optional filter
      return await this.userRepository.findAll(filter);
    } catch (error) {
      // Wrap all errors
      throw new AppError(`Error retrieving users: ${(error as Error).message}`, 500);
    }
  }
}
