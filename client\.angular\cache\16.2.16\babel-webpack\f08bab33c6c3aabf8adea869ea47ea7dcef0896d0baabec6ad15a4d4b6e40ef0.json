{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Task View Page Component\n * Page for viewing a single task's details\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';\nexport let TaskViewPageComponent = class TaskViewPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param dialog - Dialog service for confirmation dialogs\n   * @param route - Activated route for getting route parameters\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(taskService, userService, notificationService, dialog, route, router, cdr) {\n    this.taskService = taskService;\n    this.userService = userService;\n    this.notificationService = notificationService;\n    this.dialog = dialog;\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Current task being viewed\n     */\n    this.task = null;\n    /**\n     * List of users\n     */\n    this.users = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message if loading failed\n     */\n    this.error = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads task and users\n   */\n  ngOnInit() {\n    this.loadTask();\n    this.loadUsers();\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Load task details\n   */\n  loadTask() {\n    const taskId = this.route.snapshot.paramMap.get('id');\n    if (!taskId) {\n      this.error = 'Task ID not provided';\n      return;\n    }\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    this.taskService.getTaskById(taskId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: task => {\n        this.task = task;\n        this.loading = false;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.error = 'Failed to load task. It may have been deleted or you do not have permission to view it.';\n        this.loading = false;\n        console.error('Error loading task:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers() {\n    this.userService.getUsers().pipe(takeUntil(this.destroy$)).subscribe({\n      next: users => {\n        this.users = users;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        console.error('Error loading users:', err);\n      }\n    });\n  }\n  /**\n   * Handle edit button click\n   */\n  onEdit() {\n    if (this.task) {\n      this.router.navigate(['/tasks', this.task.id, 'edit']);\n    }\n  }\n  /**\n   * Handle delete button click\n   */\n  onDelete() {\n    if (!this.task) return;\n    const dialogRef = this.dialog.open(ConfirmDialogComponent, {\n      data: {\n        title: 'Delete Task',\n        message: 'Are you sure you want to delete this task? This action cannot be undone.',\n        confirmText: 'Delete',\n        cancelText: 'Cancel',\n        confirmColor: 'warn'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && this.task) {\n        this.taskService.deleteTask(this.task.id).pipe(takeUntil(this.destroy$)).subscribe({\n          next: () => {\n            this.notificationService.success('Task deleted successfully');\n            this.router.navigate(['/tasks']);\n          },\n          error: err => {\n            this.notificationService.error('Failed to delete task');\n            console.error('Error deleting task:', err);\n          }\n        });\n      }\n    });\n  }\n  /**\n   * Handle status change\n   * @param status - New status\n   */\n  onStatusChange(status) {\n    if (!this.task) return;\n    this.taskService.updateTask(this.task.id, {\n      status\n    }).pipe(takeUntil(this.destroy$)).subscribe({\n      next: updatedTask => {\n        this.task = updatedTask;\n        this.notificationService.success('Task status updated');\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.notificationService.error('Failed to update task status');\n        console.error('Error updating task status:', err);\n      }\n    });\n  }\n  /**\n   * Handle assignee change\n   * @param assigneeId - New assignee ID\n   */\n  onAssigneeChange(assigneeId) {\n    if (!this.task) return;\n    this.taskService.updateTask(this.task.id, {\n      assignee: assigneeId\n    }).pipe(takeUntil(this.destroy$)).subscribe({\n      next: updatedTask => {\n        this.task = updatedTask;\n        this.notificationService.success('Task assignee updated');\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.notificationService.error('Failed to update task assignee');\n        console.error('Error updating task assignee:', err);\n      }\n    });\n  }\n  /**\n   * Navigate back to tasks list\n   */\n  goBack() {\n    this.router.navigate(['/tasks']);\n  }\n};\nTaskViewPageComponent = __decorate([Component({\n  selector: 'app-task-view-page',\n  templateUrl: './task-view-page.component.html',\n  styleUrls: ['./task-view-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskViewPageComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "Subject", "takeUntil", "ConfirmDialogComponent", "TaskViewPageComponent", "constructor", "taskService", "userService", "notificationService", "dialog", "route", "router", "cdr", "task", "users", "loading", "error", "destroy$", "ngOnInit", "loadTask", "loadUsers", "ngOnDestroy", "next", "complete", "taskId", "snapshot", "paramMap", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTaskById", "pipe", "subscribe", "err", "console", "getUsers", "onEdit", "navigate", "id", "onDelete", "dialogRef", "open", "data", "title", "message", "confirmText", "cancelText", "confirmColor", "afterClosed", "result", "deleteTask", "success", "onStatusChange", "status", "updateTask", "updatedTask", "onAssigneeChange", "assigneeId", "assignee", "goBack", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\pages\\task-view-page\\task-view-page.component.ts"], "sourcesContent": ["/**\n * Task View Page Component\n * Page for viewing a single task's details\n */\nimport { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { TaskService } from '../../../../core/services/task.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\nimport { MatDialog } from '@angular/material/dialog';\nimport { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';\n\n@Component({\n  selector: 'app-task-view-page',\n  templateUrl: './task-view-page.component.html',\n  styleUrls: ['./task-view-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskViewPageComponent implements OnInit, OnDestroy {\n  /**\n   * Current task being viewed\n   */\n  task: Task | null = null;\n  \n  /**\n   * List of users\n   */\n  users: User[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message if loading failed\n   */\n  error: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param dialog - Dialog service for confirmation dialogs\n   * @param route - Activated route for getting route parameters\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private taskService: TaskService,\n    private userService: UserService,\n    private notificationService: NotificationService,\n    private dialog: MatDialog,\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads task and users\n   */\n  ngOnInit(): void {\n    this.loadTask();\n    this.loadUsers();\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Load task details\n   */\n  loadTask(): void {\n    const taskId = this.route.snapshot.paramMap.get('id');\n    if (!taskId) {\n      this.error = 'Task ID not provided';\n      return;\n    }\n    \n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    \n    this.taskService.getTaskById(taskId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (task) => {\n          this.task = task;\n          this.loading = false;\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          this.error = 'Failed to load task. It may have been deleted or you do not have permission to view it.';\n          this.loading = false;\n          console.error('Error loading task:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers(): void {\n    this.userService.getUsers()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (users) => {\n          this.users = users;\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          console.error('Error loading users:', err);\n        }\n      });\n  }\n\n  /**\n   * Handle edit button click\n   */\n  onEdit(): void {\n    if (this.task) {\n      this.router.navigate(['/tasks', this.task.id, 'edit']);\n    }\n  }\n\n  /**\n   * Handle delete button click\n   */\n  onDelete(): void {\n    if (!this.task) return;\n    \n    const dialogRef = this.dialog.open(ConfirmDialogComponent, {\n      data: {\n        title: 'Delete Task',\n        message: 'Are you sure you want to delete this task? This action cannot be undone.',\n        confirmText: 'Delete',\n        cancelText: 'Cancel',\n        confirmColor: 'warn'\n      }\n    });\n    \n    dialogRef.afterClosed().subscribe(result => {\n      if (result && this.task) {\n        this.taskService.deleteTask(this.task.id)\n          .pipe(takeUntil(this.destroy$))\n          .subscribe({\n            next: () => {\n              this.notificationService.success('Task deleted successfully');\n              this.router.navigate(['/tasks']);\n            },\n            error: (err) => {\n              this.notificationService.error('Failed to delete task');\n              console.error('Error deleting task:', err);\n            }\n          });\n      }\n    });\n  }\n\n  /**\n   * Handle status change\n   * @param status - New status\n   */\n  onStatusChange(status: string): void {\n    if (!this.task) return;\n    \n    this.taskService.updateTask(this.task.id, { status })\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (updatedTask) => {\n          this.task = updatedTask;\n          this.notificationService.success('Task status updated');\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          this.notificationService.error('Failed to update task status');\n          console.error('Error updating task status:', err);\n        }\n      });\n  }\n\n  /**\n   * Handle assignee change\n   * @param assigneeId - New assignee ID\n   */\n  onAssigneeChange(assigneeId: string): void {\n    if (!this.task) return;\n    \n    this.taskService.updateTask(this.task.id, { assignee: assigneeId })\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (updatedTask) => {\n          this.task = updatedTask;\n          this.notificationService.success('Task assignee updated');\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          this.notificationService.error('Failed to update task assignee');\n          console.error('Error updating task assignee:', err);\n        }\n      });\n  }\n\n  /**\n   * Navigate back to tasks list\n   */\n  goBack(): void {\n    this.router.navigate(['/tasks']);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AAExG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAO1C,SAASC,sBAAsB,QAAQ,uEAAuE;AAQvG,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EA0BhC;;;;;;;;;;EAUAC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAiB,EACjBC,KAAqB,EACrBC,MAAc,EACdC,GAAsB;IANtB,KAAAN,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA1Cb;;;IAGA,KAAAC,IAAI,GAAgB,IAAI;IAExB;;;IAGA,KAAAC,KAAK,GAAW,EAAE;IAElB;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIhB,OAAO,EAAQ;EAoBnC;EAEH;;;;EAIAiB,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;EAGAJ,QAAQA,CAAA;IACN,MAAMK,MAAM,GAAG,IAAI,CAACd,KAAK,CAACe,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACrD,IAAI,CAACH,MAAM,EAAE;MACX,IAAI,CAACR,KAAK,GAAG,sBAAsB;MACnC;;IAGF,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACJ,GAAG,CAACgB,YAAY,EAAE;IAEvB,IAAI,CAACtB,WAAW,CAACuB,WAAW,CAACL,MAAM,CAAC,CACjCM,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACe,QAAQ,CAAC,CAAC,CAC9Bc,SAAS,CAAC;MACTT,IAAI,EAAGT,IAAI,IAAI;QACb,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACE,OAAO,GAAG,KAAK;QACpB,IAAI,CAACH,GAAG,CAACgB,YAAY,EAAE;MACzB,CAAC;MACDZ,KAAK,EAAGgB,GAAG,IAAI;QACb,IAAI,CAAChB,KAAK,GAAG,yFAAyF;QACtG,IAAI,CAACD,OAAO,GAAG,KAAK;QACpBkB,OAAO,CAACjB,KAAK,CAAC,qBAAqB,EAAEgB,GAAG,CAAC;QACzC,IAAI,CAACpB,GAAG,CAACgB,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAR,SAASA,CAAA;IACP,IAAI,CAACb,WAAW,CAAC2B,QAAQ,EAAE,CACxBJ,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACe,QAAQ,CAAC,CAAC,CAC9Bc,SAAS,CAAC;MACTT,IAAI,EAAGR,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACF,GAAG,CAACgB,YAAY,EAAE;MACzB,CAAC;MACDZ,KAAK,EAAGgB,GAAG,IAAI;QACbC,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEgB,GAAG,CAAC;MAC5C;KACD,CAAC;EACN;EAEA;;;EAGAG,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACtB,IAAI,EAAE;MACb,IAAI,CAACF,MAAM,CAACyB,QAAQ,CAAC,CAAC,QAAQ,EAAE,IAAI,CAACvB,IAAI,CAACwB,EAAE,EAAE,MAAM,CAAC,CAAC;;EAE1D;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACzB,IAAI,EAAE;IAEhB,MAAM0B,SAAS,GAAG,IAAI,CAAC9B,MAAM,CAAC+B,IAAI,CAACrC,sBAAsB,EAAE;MACzDsC,IAAI,EAAE;QACJC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE,0EAA0E;QACnFC,WAAW,EAAE,QAAQ;QACrBC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE;;KAEjB,CAAC;IAEFP,SAAS,CAACQ,WAAW,EAAE,CAAChB,SAAS,CAACiB,MAAM,IAAG;MACzC,IAAIA,MAAM,IAAI,IAAI,CAACnC,IAAI,EAAE;QACvB,IAAI,CAACP,WAAW,CAAC2C,UAAU,CAAC,IAAI,CAACpC,IAAI,CAACwB,EAAE,CAAC,CACtCP,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACe,QAAQ,CAAC,CAAC,CAC9Bc,SAAS,CAAC;UACTT,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACd,mBAAmB,CAAC0C,OAAO,CAAC,2BAA2B,CAAC;YAC7D,IAAI,CAACvC,MAAM,CAACyB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;UAClC,CAAC;UACDpB,KAAK,EAAGgB,GAAG,IAAI;YACb,IAAI,CAACxB,mBAAmB,CAACQ,KAAK,CAAC,uBAAuB,CAAC;YACvDiB,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEgB,GAAG,CAAC;UAC5C;SACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAEA;;;;EAIAmB,cAAcA,CAACC,MAAc;IAC3B,IAAI,CAAC,IAAI,CAACvC,IAAI,EAAE;IAEhB,IAAI,CAACP,WAAW,CAAC+C,UAAU,CAAC,IAAI,CAACxC,IAAI,CAACwB,EAAE,EAAE;MAAEe;IAAM,CAAE,CAAC,CAClDtB,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACe,QAAQ,CAAC,CAAC,CAC9Bc,SAAS,CAAC;MACTT,IAAI,EAAGgC,WAAW,IAAI;QACpB,IAAI,CAACzC,IAAI,GAAGyC,WAAW;QACvB,IAAI,CAAC9C,mBAAmB,CAAC0C,OAAO,CAAC,qBAAqB,CAAC;QACvD,IAAI,CAACtC,GAAG,CAACgB,YAAY,EAAE;MACzB,CAAC;MACDZ,KAAK,EAAGgB,GAAG,IAAI;QACb,IAAI,CAACxB,mBAAmB,CAACQ,KAAK,CAAC,8BAA8B,CAAC;QAC9DiB,OAAO,CAACjB,KAAK,CAAC,6BAA6B,EAAEgB,GAAG,CAAC;MACnD;KACD,CAAC;EACN;EAEA;;;;EAIAuB,gBAAgBA,CAACC,UAAkB;IACjC,IAAI,CAAC,IAAI,CAAC3C,IAAI,EAAE;IAEhB,IAAI,CAACP,WAAW,CAAC+C,UAAU,CAAC,IAAI,CAACxC,IAAI,CAACwB,EAAE,EAAE;MAAEoB,QAAQ,EAAED;IAAU,CAAE,CAAC,CAChE1B,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACe,QAAQ,CAAC,CAAC,CAC9Bc,SAAS,CAAC;MACTT,IAAI,EAAGgC,WAAW,IAAI;QACpB,IAAI,CAACzC,IAAI,GAAGyC,WAAW;QACvB,IAAI,CAAC9C,mBAAmB,CAAC0C,OAAO,CAAC,uBAAuB,CAAC;QACzD,IAAI,CAACtC,GAAG,CAACgB,YAAY,EAAE;MACzB,CAAC;MACDZ,KAAK,EAAGgB,GAAG,IAAI;QACb,IAAI,CAACxB,mBAAmB,CAACQ,KAAK,CAAC,gCAAgC,CAAC;QAChEiB,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEgB,GAAG,CAAC;MACrD;KACD,CAAC;EACN;EAEA;;;EAGA0B,MAAMA,CAAA;IACJ,IAAI,CAAC/C,MAAM,CAACyB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;CACD;AA7MYhC,qBAAqB,GAAAuD,UAAA,EANjC5D,SAAS,CAAC;EACT6D,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC,CAAC;EAC9CC,eAAe,EAAE/D,uBAAuB,CAACgE;CAC1C,CAAC,C,EACW5D,qBAAqB,CA6MjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}