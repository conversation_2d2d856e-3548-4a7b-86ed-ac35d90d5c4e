/**
 * Get User Use Case
 * Handles business logic for retrieving a user by ID
 */
import { IUserDocument } from '../../entities/User';
import { IUserRepository } from '../../repositories/IUserRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for retrieving a user by ID
 * Implements business rules and validation for user retrieval
 */
export class GetUserUseCase {
  /**
   * Constructor for GetUserUseCase
   * @param userRepository - Repository for user data access
   */
  constructor(private userRepository: IUserRepository) {}

  /**
   * Executes the use case to retrieve a user by ID
   * @param userId - ID of the user to retrieve
   * @returns Promise resolving to the found user
   * @throws AppError if user not found
   */
  async execute(userId: string): Promise<IUserDocument> {
    try {
      // Validate user ID
      if (!userId) {
        throw new AppError('User ID is required', 400);
      }

      // Find user by ID
      const user = await this.userRepository.findById(userId);
      
      // Check if user exists
      if (!user) {
        throw new AppError(`User with ID ${userId} not found`, 404);
      }
      
      return user;
    } catch (error) {
      // Rethrow AppErrors, wrap other errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error retrieving user: ${(error as Error).message}`, 500);
    }
  }
}
