/**
 * Create Task Use Case
 * Handles business logic for creating a new task
 */
import { ITask, ITaskDocument } from '../../entities/Task';
import { ITaskRepository } from '../../repositories/ITaskRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for creating a new task
 * Implements business rules and validation for task creation
 */
export class CreateTaskUseCase {
  /**
   * Constructor for CreateTaskUseCase
   * @param taskRepository - Repository for task data access
   */
  constructor(private taskRepository: ITaskRepository) {}

  /**
   * Executes the use case to create a new task
   * @param taskData - Data for the new task
   * @returns Promise resolving to the created task
   */
  async execute(taskData: ITask): Promise<ITaskDocument> {
    try {
      // Validate required fields
      if (!taskData.title) {
        throw new AppError('Task title is required', 400);
      }

      // Validate title length
      if (taskData.title.length > 100) {
        throw new AppError('Task title cannot exceed 100 characters', 400);
      }

      // Validate description length if provided
      if (taskData.description && taskData.description.length > 500) {
        throw new AppError('Task description cannot exceed 500 characters', 400);
      }

      // Create task using repository
      return await this.taskRepository.create(taskData);
    } catch (error) {
      // Rethrow AppErrors, wrap other errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Failed to create task: ${(error as Error).message}`, 500);
    }
  }
}
