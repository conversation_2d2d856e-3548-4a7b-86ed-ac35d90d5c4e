{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/button\";\nexport class ConfirmDialogComponent {\n  /**\n   * Constructor with dependency injection\n   * @param dialogRef - Reference to the dialog\n   * @param data - Data passed to the dialog\n   */\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  /**\n   * Close the dialog with false result (cancel)\n   */\n  onCancel() {\n    this.dialogRef.close(false);\n  }\n  /**\n   * Close the dialog with true result (confirm)\n   */\n  onConfirm() {\n    this.dialogRef.close(true);\n  }\n  static {\n    this.ɵfac = function ConfirmDialogComponent_Factory(t) {\n      return new (t || ConfirmDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ConfirmDialogComponent,\n      selectors: [[\"app-confirm-dialog\"]],\n      decls: 11,\n      vars: 5,\n      consts: [[1, \"confirm-dialog\"], [\"mat-dialog-title\", \"\"], [\"align\", \"end\"], [\"mat-button\", \"\", \"cdkFocusInitial\", \"\", 3, \"click\"], [\"mat-button\", \"\", 3, \"color\", \"click\"]],\n      template: function ConfirmDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-dialog-content\")(4, \"p\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"mat-dialog-actions\", 2)(7, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function ConfirmDialogComponent_Template_button_click_7_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ConfirmDialogComponent_Template_button_click_9_listener() {\n            return ctx.onConfirm();\n          });\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.data.title);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.data.message);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.data.cancelText || \"Cancel\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"color\", ctx.data.confirmColor || \"primary\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.data.confirmText || \"Confirm\", \" \");\n        }\n      },\n      dependencies: [i2.MatButton, i1.MatDialogTitle, i1.MatDialogContent, i1.MatDialogActions],\n      styles: [\"\\n\\n\\n\\n\\n\\n.confirm-dialog[_ngcontent-%COMP%] {\\n  min-width: 300px;\\n  max-width: 500px;\\n}\\n\\n\\n\\nmat-dialog-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 16px;\\n}\\n\\n\\n\\nmat-dialog-content[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\nmat-dialog-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  line-height: 1.5;\\n}\\n\\n\\n\\nmat-dialog-actions[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvY29uZmlybS1kaWFsb2cvY29uZmlybS1kaWFsb2cuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7O0VBQUE7QUFJQSxxQkFBQTtBQUNBO0VBQ0UsZ0JBQUE7RUFDQSxnQkFBQTtBQUFGOztBQUdBLGlCQUFBO0FBQ0E7RUFDRSxnQkFBQTtFQUNBLG1CQUFBO0FBQUY7O0FBR0EsbUJBQUE7QUFDQTtFQUNFLG1CQUFBO0FBQUY7QUFFRTtFQUNFLFNBQUE7RUFDQSxnQkFBQTtBQUFKOztBQUlBLG1CQUFBO0FBQ0E7RUFDRSxjQUFBO0FBREYiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvbmZpcm0gZGlhbG9nIGNvbXBvbmVudCBzdHlsZXNcbiAqL1xuXG4vKiBEaWFsb2cgY29udGFpbmVyICovXG4uY29uZmlybS1kaWFsb2cge1xuICBtaW4td2lkdGg6IDMwMHB4O1xuICBtYXgtd2lkdGg6IDUwMHB4O1xufVxuXG4vKiBEaWFsb2cgdGl0bGUgKi9cbm1hdC1kaWFsb2ctdGl0bGUge1xuICBmb250LXdlaWdodDogNTAwO1xuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xufVxuXG4vKiBEaWFsb2cgY29udGVudCAqL1xubWF0LWRpYWxvZy1jb250ZW50IHtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgXG4gIHAge1xuICAgIG1hcmdpbjogMDtcbiAgICBsaW5lLWhlaWdodDogMS41O1xuICB9XG59XG5cbi8qIERpYWxvZyBhY3Rpb25zICovXG5tYXQtZGlhbG9nLWFjdGlvbnMge1xuICBwYWRkaW5nOiA4cHggMDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "ConfirmDialogComponent", "constructor", "dialogRef", "data", "onCancel", "close", "onConfirm", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "ConfirmDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ConfirmDialogComponent_Template_button_click_7_listener", "ConfirmDialogComponent_Template_button_click_9_listener", "ɵɵadvance", "ɵɵtextInterpolate", "title", "message", "ɵɵtextInterpolate1", "cancelText", "ɵɵproperty", "confirmColor", "confirmText"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\components\\confirm-dialog\\confirm-dialog.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\components\\confirm-dialog\\confirm-dialog.component.html"], "sourcesContent": ["/**\n * Confirm Dialog Component\n * Reusable dialog for confirming user actions\n */\nimport { Component, Inject, ChangeDetectionStrategy } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\n\n/**\n * Interface for dialog data\n */\nexport interface ConfirmDialogData {\n  /**\n   * Dialog title\n   */\n  title: string;\n  \n  /**\n   * Dialog message\n   */\n  message: string;\n  \n  /**\n   * Confirm button text\n   */\n  confirmText?: string;\n  \n  /**\n   * Cancel button text\n   */\n  cancelText?: string;\n  \n  /**\n   * Confirm button color\n   */\n  confirmColor?: 'primary' | 'accent' | 'warn';\n}\n\n@Component({\n  selector: 'app-confirm-dialog',\n  templateUrl: './confirm-dialog.component.html',\n  styleUrls: ['./confirm-dialog.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ConfirmDialogComponent {\n  /**\n   * Constructor with dependency injection\n   * @param dialogRef - Reference to the dialog\n   * @param data - Data passed to the dialog\n   */\n  constructor(\n    public dialogRef: MatDialogRef<ConfirmDialogComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: ConfirmDialogData\n  ) {}\n\n  /**\n   * Close the dialog with false result (cancel)\n   */\n  onCancel(): void {\n    this.dialogRef.close(false);\n  }\n\n  /**\n   * Close the dialog with true result (confirm)\n   */\n  onConfirm(): void {\n    this.dialogRef.close(true);\n  }\n}\n", "<!-- Confirm dialog container -->\n<div class=\"confirm-dialog\">\n  <!-- Dialog header with title -->\n  <h2 mat-dialog-title>{{ data.title }}</h2>\n  \n  <!-- Dialog content with message -->\n  <mat-dialog-content>\n    <p>{{ data.message }}</p>\n  </mat-dialog-content>\n  \n  <!-- Dialog actions with cancel and confirm buttons -->\n  <mat-dialog-actions align=\"end\">\n    <button \n      mat-button \n      (click)=\"onCancel()\" \n      cdkFocusInitial>\n      {{ data.cancelText || 'Cancel' }}\n    </button>\n    <button \n      mat-button \n      [color]=\"data.confirmColor || 'primary'\" \n      (click)=\"onConfirm()\">\n      {{ data.confirmText || 'Confirm' }}\n    </button>\n  </mat-dialog-actions>\n</div>\n"], "mappings": "AAKA,SAASA,eAAe,QAAsB,0BAA0B;;;;AAsCxE,OAAM,MAAOC,sBAAsB;EACjC;;;;;EAKAC,YACSC,SAA+C,EACtBC,IAAuB;IADhD,KAAAD,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;EACnC;EAEH;;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACF,SAAS,CAACG,KAAK,CAAC,KAAK,CAAC;EAC7B;EAEA;;;EAGAC,SAASA,CAAA;IACP,IAAI,CAACJ,SAAS,CAACG,KAAK,CAAC,IAAI,CAAC;EAC5B;;;uBAvBWL,sBAAsB,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAQvBT,eAAe;IAAA;EAAA;;;YARdC,sBAAsB;MAAAW,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1CnCV,EAAA,CAAAY,cAAA,aAA4B;UAELZ,EAAA,CAAAa,MAAA,GAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAG1Cd,EAAA,CAAAY,cAAA,yBAAoB;UACfZ,EAAA,CAAAa,MAAA,GAAkB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI3Bd,EAAA,CAAAY,cAAA,4BAAgC;UAG5BZ,EAAA,CAAAe,UAAA,mBAAAC,wDAAA;YAAA,OAASL,GAAA,CAAAd,QAAA,EAAU;UAAA,EAAC;UAEpBG,EAAA,CAAAa,MAAA,GACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAY,cAAA,gBAGwB;UAAtBZ,EAAA,CAAAe,UAAA,mBAAAE,wDAAA;YAAA,OAASN,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UACrBC,EAAA,CAAAa,MAAA,IACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;;;UApBUd,EAAA,CAAAkB,SAAA,GAAgB;UAAhBlB,EAAA,CAAAmB,iBAAA,CAAAR,GAAA,CAAAf,IAAA,CAAAwB,KAAA,CAAgB;UAIhCpB,EAAA,CAAAkB,SAAA,GAAkB;UAAlBlB,EAAA,CAAAmB,iBAAA,CAAAR,GAAA,CAAAf,IAAA,CAAAyB,OAAA,CAAkB;UASnBrB,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAsB,kBAAA,MAAAX,GAAA,CAAAf,IAAA,CAAA2B,UAAA,kBACF;UAGEvB,EAAA,CAAAkB,SAAA,GAAwC;UAAxClB,EAAA,CAAAwB,UAAA,UAAAb,GAAA,CAAAf,IAAA,CAAA6B,YAAA,cAAwC;UAExCzB,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAsB,kBAAA,MAAAX,GAAA,CAAAf,IAAA,CAAA8B,WAAA,mBACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}