{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Forgot Password Form Component\n * Handles password reset request with email\n */\nimport { Component, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { Validators } from '@angular/forms';\nexport let ForgotPasswordFormComponent = class ForgotPasswordFormComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed request\n     */\n    this.error = null;\n    /**\n     * Success message after successful request\n     */\n    this.success = null;\n    /**\n     * Event emitted when form is submitted\n     */\n    this.formSubmit = new EventEmitter();\n    /**\n     * Event emitted when login link is clicked\n     */\n    this.login = new EventEmitter();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the forgot password form\n   */\n  ngOnInit() {\n    this.initForm();\n  }\n  /**\n   * Initialize forgot password form with validation\n   */\n  initForm() {\n    this.forgotPasswordForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]]\n    });\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.forgotPasswordForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.forgotPasswordForm.markAllAsTouched();\n      return;\n    }\n    const {\n      email\n    } = this.forgotPasswordForm.value;\n    this.formSubmit.emit({\n      email\n    });\n  }\n  /**\n   * Handle login link click\n   */\n  onLogin() {\n    this.login.emit();\n  }\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName, errorName) {\n    const control = this.forgotPasswordForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n};\n__decorate([Input()], ForgotPasswordFormComponent.prototype, \"loading\", void 0);\n__decorate([Input()], ForgotPasswordFormComponent.prototype, \"error\", void 0);\n__decorate([Input()], ForgotPasswordFormComponent.prototype, \"success\", void 0);\n__decorate([Output()], ForgotPasswordFormComponent.prototype, \"formSubmit\", void 0);\n__decorate([Output()], ForgotPasswordFormComponent.prototype, \"login\", void 0);\nForgotPasswordFormComponent = __decorate([Component({\n  selector: 'app-forgot-password-form',\n  templateUrl: './forgot-password-form.component.html',\n  styleUrls: ['./forgot-password-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], ForgotPasswordFormComponent);", "map": {"version": 3, "names": ["Component", "Output", "EventEmitter", "Input", "ChangeDetectionStrategy", "Validators", "ForgotPasswordFormComponent", "constructor", "fb", "loading", "error", "success", "formSubmit", "login", "ngOnInit", "initForm", "forgotPasswordForm", "group", "email", "required", "onSubmit", "invalid", "mark<PERSON>llAsTouched", "value", "emit", "onLogin", "<PERSON><PERSON><PERSON><PERSON>", "controlName", "errorName", "control", "get", "touched", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\forgot-password-form\\forgot-password-form.component.ts"], "sourcesContent": ["/**\n * Forgot Password Form Component\n * Handles password reset request with email\n */\nimport { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\n\n@Component({\n  selector: 'app-forgot-password-form',\n  templateUrl: './forgot-password-form.component.html',\n  styleUrls: ['./forgot-password-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ForgotPasswordFormComponent implements OnInit {\n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Error message from failed request\n   */\n  @Input() error: string | null = null;\n  \n  /**\n   * Success message after successful request\n   */\n  @Input() success: string | null = null;\n  \n  /**\n   * Event emitted when form is submitted\n   */\n  @Output() formSubmit = new EventEmitter<{ email: string }>();\n  \n  /**\n   * Event emitted when login link is clicked\n   */\n  @Output() login = new EventEmitter<void>();\n  \n  /**\n   * Forgot password form group\n   */\n  forgotPasswordForm!: FormGroup;\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the forgot password form\n   */\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  /**\n   * Initialize forgot password form with validation\n   */\n  private initForm(): void {\n    this.forgotPasswordForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]]\n    });\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.forgotPasswordForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.forgotPasswordForm.markAllAsTouched();\n      return;\n    }\n    \n    const { email } = this.forgotPasswordForm.value;\n    this.formSubmit.emit({ email });\n  }\n\n  /**\n   * Handle login link click\n   */\n  onLogin(): void {\n    this.login.emit();\n  }\n\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName: string, errorName: string): boolean {\n    const control = this.forgotPasswordForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAUC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,eAAe;AACvG,SAAiCC,UAAU,QAAQ,gBAAgB;AAQ5D,WAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EA+BtC;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAlCtB;;;IAGS,KAAAC,OAAO,GAAG,KAAK;IAExB;;;IAGS,KAAAC,KAAK,GAAkB,IAAI;IAEpC;;;IAGS,KAAAC,OAAO,GAAkB,IAAI;IAEtC;;;IAGU,KAAAC,UAAU,GAAG,IAAIV,YAAY,EAAqB;IAE5D;;;IAGU,KAAAW,KAAK,GAAG,IAAIX,YAAY,EAAQ;EAWJ;EAEtC;;;;EAIAY,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEA;;;EAGQA,QAAQA,CAAA;IACd,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACb,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACa,KAAK,CAAC;KACpD,CAAC;EACJ;EAEA;;;EAGAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACJ,kBAAkB,CAACK,OAAO,IAAI,IAAI,CAACZ,OAAO,EAAE;MACnD;MACA,IAAI,CAACO,kBAAkB,CAACM,gBAAgB,EAAE;MAC1C;;IAGF,MAAM;MAAEJ;IAAK,CAAE,GAAG,IAAI,CAACF,kBAAkB,CAACO,KAAK;IAC/C,IAAI,CAACX,UAAU,CAACY,IAAI,CAAC;MAAEN;IAAK,CAAE,CAAC;EACjC;EAEA;;;EAGAO,OAAOA,CAAA;IACL,IAAI,CAACZ,KAAK,CAACW,IAAI,EAAE;EACnB;EAEA;;;;;;EAMAE,QAAQA,CAACC,WAAmB,EAAEC,SAAiB;IAC7C,MAAMC,OAAO,GAAG,IAAI,CAACb,kBAAkB,CAACc,GAAG,CAACH,WAAW,CAAC;IACxD,OAAO,CAAC,EAAEE,OAAO,IAAIA,OAAO,CAACE,OAAO,IAAIF,OAAO,CAACH,QAAQ,CAACE,SAAS,CAAC,CAAC;EACtE;CACD;AAjFUI,UAAA,EAAR7B,KAAK,EAAE,C,2DAAiB;AAKhB6B,UAAA,EAAR7B,KAAK,EAAE,C,yDAA6B;AAK5B6B,UAAA,EAAR7B,KAAK,EAAE,C,2DAA+B;AAK7B6B,UAAA,EAAT/B,MAAM,EAAE,C,8DAAoD;AAKnD+B,UAAA,EAAT/B,MAAM,EAAE,C,yDAAkC;AAxBhCK,2BAA2B,GAAA0B,UAAA,EANvChC,SAAS,CAAC;EACTiC,QAAQ,EAAE,0BAA0B;EACpCC,WAAW,EAAE,uCAAuC;EACpDC,SAAS,EAAE,CAAC,uCAAuC,CAAC;EACpDC,eAAe,EAAEhC,uBAAuB,CAACiC;CAC1C,CAAC,C,EACW/B,2BAA2B,CAqFvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}