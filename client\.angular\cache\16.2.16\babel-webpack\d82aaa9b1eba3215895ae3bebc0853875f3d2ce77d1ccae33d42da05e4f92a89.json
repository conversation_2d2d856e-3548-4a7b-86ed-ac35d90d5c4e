{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Base API Service\n * Provides common HTTP functionality for all API services\n */\nimport { Injectable } from '@angular/core';\nimport { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nexport let ApiService = class ApiService {\n  /**\n   * Constructor with dependency injection\n   * @param http - HttpClient for making HTTP requests\n   */\n  constructor(http) {\n    this.http = http;\n    /**\n     * API base URL from environment\n     */\n    this.apiUrl = environment.apiUrl;\n  }\n  /**\n   * Create default headers for API requests\n   * @returns HttpHeaders object with default headers\n   */\n  createDefaultHeaders() {\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n  }\n  /**\n   * Handle HTTP errors\n   * @param error - HTTP error\n   * @returns Observable with error\n   */\n  handleError(error) {\n    let errorMessage = 'An unknown error occurred';\n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = `Error: ${error.error.message}`;\n    } else {\n      // Server-side error\n      errorMessage = error.error?.message || `Error Code: ${error.status}\\nMessage: ${error.message}`;\n    }\n    console.error(errorMessage);\n    return throwError(() => new Error(errorMessage));\n  }\n  /**\n   * Make GET request to API\n   * @param path - API endpoint path\n   * @param params - Optional query parameters\n   * @returns Observable with response data\n   */\n  get(path, params = new HttpParams()) {\n    return this.http.get(`${this.apiUrl}/${path}`, {\n      headers: this.createDefaultHeaders(),\n      params\n    }).pipe(catchError(error => this.handleError(error)));\n  }\n  /**\n   * Make POST request to API\n   * @param path - API endpoint path\n   * @param body - Request body\n   * @returns Observable with response data\n   */\n  post(path, body) {\n    return this.http.post(`${this.apiUrl}/${path}`, body, {\n      headers: this.createDefaultHeaders()\n    }).pipe(catchError(error => this.handleError(error)));\n  }\n  /**\n   * Make PUT request to API\n   * @param path - API endpoint path\n   * @param body - Request body\n   * @returns Observable with response data\n   */\n  put(path, body) {\n    return this.http.put(`${this.apiUrl}/${path}`, body, {\n      headers: this.createDefaultHeaders()\n    }).pipe(catchError(error => this.handleError(error)));\n  }\n  /**\n   * Make PATCH request to API\n   * @param path - API endpoint path\n   * @param body - Request body\n   * @returns Observable with response data\n   */\n  patch(path, body) {\n    return this.http.patch(`${this.apiUrl}/${path}`, body, {\n      headers: this.createDefaultHeaders()\n    }).pipe(catchError(error => this.handleError(error)));\n  }\n  /**\n   * Make DELETE request to API\n   * @param path - API endpoint path\n   * @returns Observable with response data\n   */\n  delete(path) {\n    return this.http.delete(`${this.apiUrl}/${path}`, {\n      headers: this.createDefaultHeaders()\n    }).pipe(catchError(error => this.handleError(error)));\n  }\n};\nApiService = __decorate([Injectable({\n  providedIn: 'root'\n})], ApiService);", "map": {"version": 3, "names": ["Injectable", "HttpHeaders", "HttpParams", "throwError", "catchError", "environment", "ApiService", "constructor", "http", "apiUrl", "createDefaultHeaders", "handleError", "error", "errorMessage", "ErrorEvent", "message", "status", "console", "Error", "get", "path", "params", "headers", "pipe", "post", "body", "put", "patch", "delete", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\services\\api.service.ts"], "sourcesContent": ["/**\n * Base API Service\n * Provides common HTTP functionality for all API services\n */\nimport { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ApiService {\n  /**\n   * API base URL from environment\n   */\n  private apiUrl = environment.apiUrl;\n  \n  /**\n   * Constructor with dependency injection\n   * @param http - HttpClient for making HTTP requests\n   */\n  constructor(private http: HttpClient) {}\n  \n  /**\n   * Create default headers for API requests\n   * @returns HttpHeaders object with default headers\n   */\n  private createDefaultHeaders(): HttpHeaders {\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n  }\n  \n  /**\n   * Handle HTTP errors\n   * @param error - HTTP error\n   * @returns Observable with error\n   */\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'An unknown error occurred';\n    \n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = `Error: ${error.error.message}`;\n    } else {\n      // Server-side error\n      errorMessage = error.error?.message || \n                    `Error Code: ${error.status}\\nMessage: ${error.message}`;\n    }\n    \n    console.error(errorMessage);\n    return throwError(() => new Error(errorMessage));\n  }\n  \n  /**\n   * Make GET request to API\n   * @param path - API endpoint path\n   * @param params - Optional query parameters\n   * @returns Observable with response data\n   */\n  get<T>(path: string, params: HttpParams = new HttpParams()): Observable<T> {\n    return this.http.get<T>(`${this.apiUrl}/${path}`, {\n      headers: this.createDefaultHeaders(),\n      params\n    }).pipe(\n      catchError(error => this.handleError(error))\n    );\n  }\n  \n  /**\n   * Make POST request to API\n   * @param path - API endpoint path\n   * @param body - Request body\n   * @returns Observable with response data\n   */\n  post<T>(path: string, body: any): Observable<T> {\n    return this.http.post<T>(`${this.apiUrl}/${path}`, body, {\n      headers: this.createDefaultHeaders()\n    }).pipe(\n      catchError(error => this.handleError(error))\n    );\n  }\n  \n  /**\n   * Make PUT request to API\n   * @param path - API endpoint path\n   * @param body - Request body\n   * @returns Observable with response data\n   */\n  put<T>(path: string, body: any): Observable<T> {\n    return this.http.put<T>(`${this.apiUrl}/${path}`, body, {\n      headers: this.createDefaultHeaders()\n    }).pipe(\n      catchError(error => this.handleError(error))\n    );\n  }\n  \n  /**\n   * Make PATCH request to API\n   * @param path - API endpoint path\n   * @param body - Request body\n   * @returns Observable with response data\n   */\n  patch<T>(path: string, body: any): Observable<T> {\n    return this.http.patch<T>(`${this.apiUrl}/${path}`, body, {\n      headers: this.createDefaultHeaders()\n    }).pipe(\n      catchError(error => this.handleError(error))\n    );\n  }\n  \n  /**\n   * Make DELETE request to API\n   * @param path - API endpoint path\n   * @returns Observable with response data\n   */\n  delete<T>(path: string): Observable<T> {\n    return this.http.delete<T>(`${this.apiUrl}/${path}`, {\n      headers: this.createDefaultHeaders()\n    }).pipe(\n      catchError(error => this.handleError(error))\n    );\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAAqBC,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAC1E,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,mCAAmC;AAKxD,WAAMC,UAAU,GAAhB,MAAMA,UAAU;EAMrB;;;;EAIAC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IATxB;;;IAGQ,KAAAC,MAAM,GAAGJ,WAAW,CAACI,MAAM;EAMI;EAEvC;;;;EAIQC,oBAAoBA,CAAA;IAC1B,OAAO,IAAIT,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,QAAQ,EAAE;KACX,CAAC;EACJ;EAEA;;;;;EAKQU,WAAWA,CAACC,KAAU;IAC5B,IAAIC,YAAY,GAAG,2BAA2B;IAE9C,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,UAAUD,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;KAC/C,MAAM;MACL;MACAF,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEG,OAAO,IACrB,eAAeH,KAAK,CAACI,MAAM,cAAcJ,KAAK,CAACG,OAAO,EAAE;;IAGxEE,OAAO,CAACL,KAAK,CAACC,YAAY,CAAC;IAC3B,OAAOV,UAAU,CAAC,MAAM,IAAIe,KAAK,CAACL,YAAY,CAAC,CAAC;EAClD;EAEA;;;;;;EAMAM,GAAGA,CAAIC,IAAY,EAAEC,MAAA,GAAqB,IAAInB,UAAU,EAAE;IACxD,OAAO,IAAI,CAACM,IAAI,CAACW,GAAG,CAAI,GAAG,IAAI,CAACV,MAAM,IAAIW,IAAI,EAAE,EAAE;MAChDE,OAAO,EAAE,IAAI,CAACZ,oBAAoB,EAAE;MACpCW;KACD,CAAC,CAACE,IAAI,CACLnB,UAAU,CAACQ,KAAK,IAAI,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC,CAAC,CAC7C;EACH;EAEA;;;;;;EAMAY,IAAIA,CAAIJ,IAAY,EAAEK,IAAS;IAC7B,OAAO,IAAI,CAACjB,IAAI,CAACgB,IAAI,CAAI,GAAG,IAAI,CAACf,MAAM,IAAIW,IAAI,EAAE,EAAEK,IAAI,EAAE;MACvDH,OAAO,EAAE,IAAI,CAACZ,oBAAoB;KACnC,CAAC,CAACa,IAAI,CACLnB,UAAU,CAACQ,KAAK,IAAI,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC,CAAC,CAC7C;EACH;EAEA;;;;;;EAMAc,GAAGA,CAAIN,IAAY,EAAEK,IAAS;IAC5B,OAAO,IAAI,CAACjB,IAAI,CAACkB,GAAG,CAAI,GAAG,IAAI,CAACjB,MAAM,IAAIW,IAAI,EAAE,EAAEK,IAAI,EAAE;MACtDH,OAAO,EAAE,IAAI,CAACZ,oBAAoB;KACnC,CAAC,CAACa,IAAI,CACLnB,UAAU,CAACQ,KAAK,IAAI,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC,CAAC,CAC7C;EACH;EAEA;;;;;;EAMAe,KAAKA,CAAIP,IAAY,EAAEK,IAAS;IAC9B,OAAO,IAAI,CAACjB,IAAI,CAACmB,KAAK,CAAI,GAAG,IAAI,CAAClB,MAAM,IAAIW,IAAI,EAAE,EAAEK,IAAI,EAAE;MACxDH,OAAO,EAAE,IAAI,CAACZ,oBAAoB;KACnC,CAAC,CAACa,IAAI,CACLnB,UAAU,CAACQ,KAAK,IAAI,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC,CAAC,CAC7C;EACH;EAEA;;;;;EAKAgB,MAAMA,CAAIR,IAAY;IACpB,OAAO,IAAI,CAACZ,IAAI,CAACoB,MAAM,CAAI,GAAG,IAAI,CAACnB,MAAM,IAAIW,IAAI,EAAE,EAAE;MACnDE,OAAO,EAAE,IAAI,CAACZ,oBAAoB;KACnC,CAAC,CAACa,IAAI,CACLnB,UAAU,CAACQ,KAAK,IAAI,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC,CAAC,CAC7C;EACH;CACD;AAjHYN,UAAU,GAAAuB,UAAA,EAHtB7B,UAAU,CAAC;EACV8B,UAAU,EAAE;CACb,CAAC,C,EACWxB,UAAU,CAiHtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}