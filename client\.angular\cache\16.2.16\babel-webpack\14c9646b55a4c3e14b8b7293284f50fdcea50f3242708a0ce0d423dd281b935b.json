{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Tasks Module\n * Feature module for task management functionality\n */\nimport { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n// Components\nimport { TaskListComponent } from './components/task-list/task-list.component';\nimport { TaskDetailComponent } from './components/task-detail/task-detail.component';\nimport { TaskFormComponent } from './components/task-form/task-form.component';\nimport { TaskFilterComponent } from './components/task-filter/task-filter.component';\nimport { TaskItemComponent } from './components/task-item/task-item.component';\n// Pages\nimport { TasksPageComponent } from './pages/tasks-page/tasks-page.component';\nimport { TaskCreatePageComponent } from './pages/task-create-page/task-create-page.component';\nimport { TaskEditPageComponent } from './pages/task-edit-page/task-edit-page.component';\nimport { TaskViewPageComponent } from './pages/task-view-page/task-view-page.component';\n/**\n * Routes for the tasks feature module\n */\nconst routes = [{\n  path: '',\n  component: TasksPageComponent\n}, {\n  path: 'create',\n  component: TaskCreatePageComponent\n}, {\n  path: ':id',\n  component: TaskViewPageComponent\n}, {\n  path: ':id/edit',\n  component: TaskEditPageComponent\n}];\nexport let TasksModule = class TasksModule {};\nTasksModule = __decorate([NgModule({\n  declarations: [TaskListComponent, TaskDetailComponent, TaskFormComponent, TaskFilterComponent, TaskItemComponent, TasksPageComponent, TaskCreatePageComponent, TaskEditPageComponent, TaskViewPageComponent],\n  imports: [SharedModule, RouterModule.forChild(routes)]\n})], TasksModule);", "map": {"version": 3, "names": ["NgModule", "RouterModule", "SharedModule", "TaskListComponent", "TaskDetailComponent", "TaskFormComponent", "TaskFilterComponent", "TaskItemComponent", "TasksPageComponent", "TaskCreatePageComponent", "TaskEditPageComponent", "TaskViewPageComponent", "routes", "path", "component", "TasksModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\tasks.module.ts"], "sourcesContent": ["/**\n * Tasks Module\n * Feature module for task management functionality\n */\nimport { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n\n// Components\nimport { TaskListComponent } from './components/task-list/task-list.component';\nimport { TaskDetailComponent } from './components/task-detail/task-detail.component';\nimport { TaskFormComponent } from './components/task-form/task-form.component';\nimport { TaskFilterComponent } from './components/task-filter/task-filter.component';\nimport { TaskItemComponent } from './components/task-item/task-item.component';\n\n// Pages\nimport { TasksPageComponent } from './pages/tasks-page/tasks-page.component';\nimport { TaskCreatePageComponent } from './pages/task-create-page/task-create-page.component';\nimport { TaskEditPageComponent } from './pages/task-edit-page/task-edit-page.component';\nimport { TaskViewPageComponent } from './pages/task-view-page/task-view-page.component';\n\n/**\n * Routes for the tasks feature module\n */\nconst routes: Routes = [\n  {\n    path: '',\n    component: TasksPageComponent\n  },\n  {\n    path: 'create',\n    component: TaskCreatePageComponent\n  },\n  {\n    path: ':id',\n    component: TaskViewPageComponent\n  },\n  {\n    path: ':id/edit',\n    component: TaskEditPageComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    TaskListComponent,\n    TaskDetailComponent,\n    TaskFormComponent,\n    TaskFilterComponent,\n    TaskItemComponent,\n    TasksPageComponent,\n    TaskCreatePageComponent,\n    TaskEditPageComponent,\n    TaskViewPageComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class TasksModule { }\n"], "mappings": ";AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AAEzD;AACA,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,iBAAiB,QAAQ,4CAA4C;AAE9E;AACA,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,uBAAuB,QAAQ,qDAAqD;AAC7F,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,qBAAqB,QAAQ,iDAAiD;AAEvF;;;AAGA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEJ;CACZ,CACF;AAmBM,WAAMK,WAAW,GAAjB,MAAMA,WAAW,GAAI;AAAfA,WAAW,GAAAC,UAAA,EAjBvBhB,QAAQ,CAAC;EACRiB,YAAY,EAAE,CACZd,iBAAiB,EACjBC,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,EACnBC,iBAAiB,EACjBC,kBAAkB,EAClBC,uBAAuB,EACvBC,qBAAqB,EACrBC,qBAAqB,CACtB;EACDO,OAAO,EAAE,CACPhB,YAAY,EACZD,YAAY,CAACkB,QAAQ,CAACP,MAAM,CAAC;CAEhC,CAAC,C,EACWG,WAAW,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}