/**
 * Time Ago Pipe
 * Converts timestamps to relative time format (e.g., "5 minutes ago")
 */
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'timeAgo'
})
export class TimeAgoPipe implements PipeTransform {
  /**
   * Transform method to convert date to relative time
   * @param value - Input date (string, Date, or number)
   * @returns Relative time string
   */
  transform(value: string | Date | number): string {
    if (!value) {
      return '';
    }

    // Convert input to Date object
    const date = value instanceof Date ? value : new Date(value);
    
    // Get current time
    const now = new Date();
    
    // Calculate time difference in seconds
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    // Define time intervals in seconds
    const intervals = {
      year: 31536000,
      month: 2592000,
      week: 604800,
      day: 86400,
      hour: 3600,
      minute: 60,
      second: 1
    };
    
    // Format based on time difference
    if (diffInSeconds < 5) {
      return 'just now';
    } else if (diffInSeconds < intervals.minute) {
      return `${diffInSeconds} seconds ago`;
    } else if (diffInSeconds < intervals.hour) {
      const minutes = Math.floor(diffInSeconds / intervals.minute);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInSeconds < intervals.day) {
      const hours = Math.floor(diffInSeconds / intervals.hour);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInSeconds < intervals.week) {
      const days = Math.floor(diffInSeconds / intervals.day);
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else if (diffInSeconds < intervals.month) {
      const weeks = Math.floor(diffInSeconds / intervals.week);
      return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
    } else if (diffInSeconds < intervals.year) {
      const months = Math.floor(diffInSeconds / intervals.month);
      return `${months} ${months === 1 ? 'month' : 'months'} ago`;
    } else {
      const years = Math.floor(diffInSeconds / intervals.year);
      return `${years} ${years === 1 ? 'year' : 'years'} ago`;
    }
  }
}
