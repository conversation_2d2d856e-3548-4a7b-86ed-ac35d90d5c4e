/**
 * Footer Component
 * Application footer with copyright and links
 */
import { Component, ChangeDetectionStrategy } from '@angular/core';
import { environment } from '@environments/environment';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FooterComponent {
  /**
   * Current year for copyright
   */
  currentYear = new Date().getFullYear();
  
  /**
   * Application version from environment
   */
  appVersion = environment.version;
}
