{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Task Create Page Component\n * Page for creating a new task\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nexport let TaskCreatePageComponent = class TaskCreatePageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(taskService, userService, notificationService, router, cdr) {\n    this.taskService = taskService;\n    this.userService = userService;\n    this.notificationService = notificationService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * List of users\n     */\n    this.users = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads users for assignee selection\n   */\n  ngOnInit() {\n    this.loadUsers();\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers() {\n    this.userService.getUsers().pipe(takeUntil(this.destroy$)).subscribe({\n      next: users => {\n        this.users = users;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        console.error('Error loading users:', err);\n        this.notificationService.error('Failed to load users');\n      }\n    });\n  }\n  /**\n   * Handle form submission\n   * @param taskData - Task data from form\n   */\n  onFormSubmit(taskData) {\n    this.loading = true;\n    this.cdr.markForCheck();\n    this.taskService.createTask(taskData).pipe(takeUntil(this.destroy$)).subscribe({\n      next: createdTask => {\n        this.loading = false;\n        this.notificationService.success('Task created successfully');\n        this.router.navigate(['/tasks', createdTask.id]);\n      },\n      error: err => {\n        this.loading = false;\n        this.notificationService.error('Failed to create task');\n        console.error('Error creating task:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Handle form cancellation\n   */\n  onFormCancel() {\n    this.router.navigate(['/tasks']);\n  }\n};\nTaskCreatePageComponent = __decorate([Component({\n  selector: 'app-task-create-page',\n  templateUrl: './task-create-page.component.html',\n  styleUrls: ['./task-create-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskCreatePageComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "Subject", "takeUntil", "TaskCreatePageComponent", "constructor", "taskService", "userService", "notificationService", "router", "cdr", "users", "loading", "destroy$", "ngOnInit", "loadUsers", "ngOnDestroy", "next", "complete", "getUsers", "pipe", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "err", "console", "onFormSubmit", "taskData", "createTask", "createdTask", "success", "navigate", "id", "onFormCancel", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\pages\\task-create-page\\task-create-page.component.ts"], "sourcesContent": ["/**\n * Task Create Page Component\n * Page for creating a new task\n */\nimport { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { TaskService } from '../../../../core/services/task.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-task-create-page',\n  templateUrl: './task-create-page.component.html',\n  styleUrls: ['./task-create-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskCreatePageComponent implements OnInit, On<PERSON><PERSON>roy {\n  /**\n   * List of users\n   */\n  users: User[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private taskService: TaskService,\n    private userService: UserService,\n    private notificationService: NotificationService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads users for assignee selection\n   */\n  ngOnInit(): void {\n    this.loadUsers();\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers(): void {\n    this.userService.getUsers()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (users) => {\n          this.users = users;\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          console.error('Error loading users:', err);\n          this.notificationService.error('Failed to load users');\n        }\n      });\n  }\n\n  /**\n   * Handle form submission\n   * @param taskData - Task data from form\n   */\n  onFormSubmit(taskData: Partial<Task>): void {\n    this.loading = true;\n    this.cdr.markForCheck();\n    \n    this.taskService.createTask(taskData)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (createdTask) => {\n          this.loading = false;\n          this.notificationService.success('Task created successfully');\n          this.router.navigate(['/tasks', createdTask.id]);\n        },\n        error: (err) => {\n          this.loading = false;\n          this.notificationService.error('Failed to create task');\n          console.error('Error creating task:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Handle form cancellation\n   */\n  onFormCancel(): void {\n    this.router.navigate(['/tasks']);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AAExG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAanC,WAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAgBlC;;;;;;;;EAQAC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,GAAsB;IAJtB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA5Bb;;;IAGA,KAAAC,KAAK,GAAW,EAAE;IAElB;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIX,OAAO,EAAQ;EAgBnC;EAEH;;;;EAIAY,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;IACpB,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;EAC1B;EAEA;;;EAGAH,SAASA,CAAA;IACP,IAAI,CAACR,WAAW,CAACY,QAAQ,EAAE,CACxBC,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTJ,IAAI,EAAGN,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACD,GAAG,CAACY,YAAY,EAAE;MACzB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAAChB,mBAAmB,CAACe,KAAK,CAAC,sBAAsB,CAAC;MACxD;KACD,CAAC;EACN;EAEA;;;;EAIAG,YAAYA,CAACC,QAAuB;IAClC,IAAI,CAACf,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,GAAG,CAACY,YAAY,EAAE;IAEvB,IAAI,CAAChB,WAAW,CAACsB,UAAU,CAACD,QAAQ,CAAC,CAClCP,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTJ,IAAI,EAAGY,WAAW,IAAI;QACpB,IAAI,CAACjB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACJ,mBAAmB,CAACsB,OAAO,CAAC,2BAA2B,CAAC;QAC7D,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,QAAQ,EAAEF,WAAW,CAACG,EAAE,CAAC,CAAC;MAClD,CAAC;MACDT,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAACZ,OAAO,GAAG,KAAK;QACpB,IAAI,CAACJ,mBAAmB,CAACe,KAAK,CAAC,uBAAuB,CAAC;QACvDE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAACd,GAAG,CAACY,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAW,YAAYA,CAAA;IACV,IAAI,CAACxB,MAAM,CAACsB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;CACD;AAlGY3B,uBAAuB,GAAA8B,UAAA,EANnClC,SAAS,CAAC;EACTmC,QAAQ,EAAE,sBAAsB;EAChCC,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,mCAAmC,CAAC;EAChDC,eAAe,EAAErC,uBAAuB,CAACsC;CAC1C,CAAC,C,EACWnC,uBAAuB,CAkGnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}