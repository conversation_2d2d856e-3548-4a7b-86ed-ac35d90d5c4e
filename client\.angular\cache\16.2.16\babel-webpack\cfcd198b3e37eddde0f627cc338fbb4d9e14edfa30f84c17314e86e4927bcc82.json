{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Error Message Component\n * Displays error messages with consistent styling\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\nexport let ErrorMessageComponent = class ErrorMessageComponent {\n  constructor() {\n    /**\n     * Error message to display\n     */\n    this.message = 'An error occurred';\n    /**\n     * Whether to show an icon with the message\n     */\n    this.showIcon = true;\n    /**\n     * Whether to show a retry button\n     */\n    this.showRetry = false;\n    /**\n     * Event handler for retry button click\n     */\n    this.onRetry = () => {};\n  }\n};\n__decorate([Input()], ErrorMessageComponent.prototype, \"message\", void 0);\n__decorate([Input()], ErrorMessageComponent.prototype, \"showIcon\", void 0);\n__decorate([Input()], ErrorMessageComponent.prototype, \"showRetry\", void 0);\n__decorate([Input()], ErrorMessageComponent.prototype, \"onRetry\", void 0);\nErrorMessageComponent = __decorate([Component({\n  selector: 'app-error-message',\n  templateUrl: './error-message.component.html',\n  styleUrls: ['./error-message.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], ErrorMessageComponent);", "map": {"version": 3, "names": ["Component", "Input", "ChangeDetectionStrategy", "ErrorMessageComponent", "constructor", "message", "showIcon", "showRetry", "onRetry", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\components\\error-message\\error-message.component.ts"], "sourcesContent": ["/**\n * Error Message Component\n * Displays error messages with consistent styling\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-error-message',\n  templateUrl: './error-message.component.html',\n  styleUrls: ['./error-message.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ErrorMessageComponent {\n  /**\n   * Error message to display\n   */\n  @Input() message = 'An error occurred';\n  \n  /**\n   * Whether to show an icon with the message\n   */\n  @Input() showIcon = true;\n  \n  /**\n   * Whether to show a retry button\n   */\n  @Input() showRetry = false;\n  \n  /**\n   * Event handler for retry button click\n   */\n  @Input() onRetry: () => void = () => {};\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,eAAe;AAQlE,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAA3BC,YAAA;IACL;;;IAGS,KAAAC,OAAO,GAAG,mBAAmB;IAEtC;;;IAGS,KAAAC,QAAQ,GAAG,IAAI;IAExB;;;IAGS,KAAAC,SAAS,GAAG,KAAK;IAE1B;;;IAGS,KAAAC,OAAO,GAAe,MAAK,CAAE,CAAC;EACzC;CAAC;AAhBUC,UAAA,EAARR,KAAK,EAAE,C,qDAA+B;AAK9BQ,UAAA,EAARR,KAAK,EAAE,C,sDAAiB;AAKhBQ,UAAA,EAARR,KAAK,EAAE,C,uDAAmB;AAKlBQ,UAAA,EAARR,KAAK,EAAE,C,qDAAgC;AAnB7BE,qBAAqB,GAAAM,UAAA,EANjCT,SAAS,CAAC;EACTU,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC,CAAC;EAC7CC,eAAe,EAAEX,uBAAuB,CAACY;CAC1C,CAAC,C,EACWX,qBAAqB,CAoBjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}