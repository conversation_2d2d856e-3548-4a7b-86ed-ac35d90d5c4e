/**
 * Task Summary Component
 * Displays summary cards with task counts by status
 */
import { Component, Input, ChangeDetectionStrategy } from '@angular/core';

@Component({
  selector: 'app-task-summary',
  templateUrl: './task-summary.component.html',
  styleUrls: ['./task-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskSummaryComponent {
  /**
   * Number of tasks with 'todo' status
   */
  @Input() todoCount = 0;
  
  /**
   * Number of tasks with 'in_progress' status
   */
  @Input() inProgressCount = 0;
  
  /**
   * Number of tasks with 'done' status
   */
  @Input() doneCount = 0;
  
  /**
   * Total number of tasks
   */
  @Input() totalCount = 0;
}
