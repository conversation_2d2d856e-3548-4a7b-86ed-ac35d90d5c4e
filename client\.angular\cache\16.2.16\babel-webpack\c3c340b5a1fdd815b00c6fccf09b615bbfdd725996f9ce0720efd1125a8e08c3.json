{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Task Form Component\n * Form for creating and editing tasks\n */\nimport { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { COMMA, ENTER } from '@angular/cdk/keycodes';\nexport let TaskFormComponent = class TaskFormComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - Form builder service\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Input list of users for assignee selection\n     */\n    this.users = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Event emitted when form is submitted\n     */\n    this.formSubmit = new EventEmitter();\n    /**\n     * Event emitted when form is cancelled\n     */\n    this.formCancel = new EventEmitter();\n    /**\n     * Chip input separator keys\n     */\n    this.separatorKeysCodes = [ENTER, COMMA];\n    /**\n     * Available task statuses\n     */\n    this.statuses = [{\n      value: 'todo',\n      label: 'To Do'\n    }, {\n      value: 'in_progress',\n      label: 'In Progress'\n    }, {\n      value: 'review',\n      label: 'Review'\n    }, {\n      value: 'done',\n      label: 'Done'\n    }];\n    /**\n     * Available task priorities\n     */\n    this.priorities = [{\n      value: 'high',\n      label: 'High'\n    }, {\n      value: 'medium',\n      label: 'Medium'\n    }, {\n      value: 'low',\n      label: 'Low'\n    }];\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up the task form\n   */\n  ngOnInit() {\n    this.initForm();\n  }\n  /**\n   * Initialize the form with task data or default values\n   */\n  initForm() {\n    this.taskForm = this.fb.group({\n      title: [this.task?.title || '', [Validators.required, Validators.maxLength(100)]],\n      description: [this.task?.description || '', Validators.maxLength(1000)],\n      status: [this.task?.status || 'todo', Validators.required],\n      priority: [this.task?.priority || 'medium', Validators.required],\n      dueDate: [this.task?.dueDate ? new Date(this.task.dueDate) : null],\n      assignee: [this.task?.assignee || ''],\n      tags: [this.task?.tags || []]\n    });\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.taskForm.invalid || this.loading) {\n      return;\n    }\n    // Get form values\n    const formValues = this.taskForm.value;\n    // Format due date if present\n    if (formValues.dueDate) {\n      formValues.dueDate = new Date(formValues.dueDate).toISOString();\n    }\n    // Emit form data\n    this.formSubmit.emit(formValues);\n  }\n  /**\n   * Handle form cancellation\n   */\n  onCancel() {\n    this.formCancel.emit();\n  }\n  /**\n   * Add a tag\n   * @param event - Chip input event\n   */\n  addTag(event) {\n    const value = (event.value || '').trim();\n    // Add tag\n    if (value) {\n      const currentTags = this.taskForm.get('tags')?.value || [];\n      if (!currentTags.includes(value)) {\n        this.taskForm.get('tags')?.setValue([...currentTags, value]);\n      }\n    }\n    // Clear input\n    event.chipInput.clear();\n  }\n  /**\n   * Remove a tag\n   * @param tag - Tag to remove\n   */\n  removeTag(tag) {\n    const currentTags = this.taskForm.get('tags')?.value || [];\n    const index = currentTags.indexOf(tag);\n    if (index >= 0) {\n      const newTags = [...currentTags];\n      newTags.splice(index, 1);\n      this.taskForm.get('tags')?.setValue(newTags);\n    }\n  }\n  /**\n   * Get error message for a form control\n   * @param controlName - Name of the form control\n   * @returns Error message\n   */\n  getErrorMessage(controlName) {\n    const control = this.taskForm.get(controlName);\n    if (!control || !control.errors) {\n      return '';\n    }\n    if (control.errors['required']) {\n      return 'This field is required';\n    }\n    if (control.errors['maxlength']) {\n      const maxLength = control.errors['maxlength'].requiredLength;\n      return `Maximum length is ${maxLength} characters`;\n    }\n    return 'Invalid value';\n  }\n};\n__decorate([Input()], TaskFormComponent.prototype, \"task\", void 0);\n__decorate([Input()], TaskFormComponent.prototype, \"users\", void 0);\n__decorate([Input()], TaskFormComponent.prototype, \"loading\", void 0);\n__decorate([Output()], TaskFormComponent.prototype, \"formSubmit\", void 0);\n__decorate([Output()], TaskFormComponent.prototype, \"formCancel\", void 0);\nTaskFormComponent = __decorate([Component({\n  selector: 'app-task-form',\n  templateUrl: './task-form.component.html',\n  styleUrls: ['./task-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskFormComponent);", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "ChangeDetectionStrategy", "Validators", "COMMA", "ENTER", "TaskFormComponent", "constructor", "fb", "users", "loading", "formSubmit", "formCancel", "separatorKeysCodes", "statuses", "value", "label", "priorities", "ngOnInit", "initForm", "taskForm", "group", "title", "task", "required", "max<PERSON><PERSON><PERSON>", "description", "status", "priority", "dueDate", "Date", "assignee", "tags", "onSubmit", "invalid", "formValues", "toISOString", "emit", "onCancel", "addTag", "event", "trim", "currentTags", "get", "includes", "setValue", "chipInput", "clear", "removeTag", "tag", "index", "indexOf", "newTags", "splice", "getErrorMessage", "controlName", "control", "errors", "<PERSON><PERSON><PERSON><PERSON>", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\components\\task-form\\task-form.component.ts"], "sourcesContent": ["/**\n * Task Form Component\n * Form for creating and editing tasks\n */\nimport { Component, Input, Output, EventEmitter, OnInit, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\nimport { COMMA, ENTER } from '@angular/cdk/keycodes';\nimport { MatChipInputEvent } from '@angular/material/chips';\n\n@Component({\n  selector: 'app-task-form',\n  templateUrl: './task-form.component.html',\n  styleUrls: ['./task-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskFormComponent implements OnInit {\n  /**\n   * Input task for editing (optional)\n   */\n  @Input() task?: Task;\n  \n  /**\n   * Input list of users for assignee selection\n   */\n  @Input() users: User[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Event emitted when form is submitted\n   */\n  @Output() formSubmit = new EventEmitter<Partial<Task>>();\n  \n  /**\n   * Event emitted when form is cancelled\n   */\n  @Output() formCancel = new EventEmitter<void>();\n  \n  /**\n   * Task form group\n   */\n  taskForm!: FormGroup;\n  \n  /**\n   * Chip input separator keys\n   */\n  readonly separatorKeysCodes = [ENTER, COMMA] as const;\n  \n  /**\n   * Available task statuses\n   */\n  statuses = [\n    { value: 'todo', label: 'To Do' },\n    { value: 'in_progress', label: 'In Progress' },\n    { value: 'review', label: 'Review' },\n    { value: 'done', label: 'Done' }\n  ];\n  \n  /**\n   * Available task priorities\n   */\n  priorities = [\n    { value: 'high', label: 'High' },\n    { value: 'medium', label: 'Medium' },\n    { value: 'low', label: 'Low' }\n  ];\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - Form builder service\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up the task form\n   */\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  /**\n   * Initialize the form with task data or default values\n   */\n  initForm(): void {\n    this.taskForm = this.fb.group({\n      title: [this.task?.title || '', [Validators.required, Validators.maxLength(100)]],\n      description: [this.task?.description || '', Validators.maxLength(1000)],\n      status: [this.task?.status || 'todo', Validators.required],\n      priority: [this.task?.priority || 'medium', Validators.required],\n      dueDate: [this.task?.dueDate ? new Date(this.task.dueDate) : null],\n      assignee: [this.task?.assignee || ''],\n      tags: [this.task?.tags || []]\n    });\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.taskForm.invalid || this.loading) {\n      return;\n    }\n    \n    // Get form values\n    const formValues = this.taskForm.value;\n    \n    // Format due date if present\n    if (formValues.dueDate) {\n      formValues.dueDate = new Date(formValues.dueDate).toISOString();\n    }\n    \n    // Emit form data\n    this.formSubmit.emit(formValues);\n  }\n\n  /**\n   * Handle form cancellation\n   */\n  onCancel(): void {\n    this.formCancel.emit();\n  }\n\n  /**\n   * Add a tag\n   * @param event - Chip input event\n   */\n  addTag(event: MatChipInputEvent): void {\n    const value = (event.value || '').trim();\n    \n    // Add tag\n    if (value) {\n      const currentTags = this.taskForm.get('tags')?.value || [];\n      if (!currentTags.includes(value)) {\n        this.taskForm.get('tags')?.setValue([...currentTags, value]);\n      }\n    }\n    \n    // Clear input\n    event.chipInput!.clear();\n  }\n\n  /**\n   * Remove a tag\n   * @param tag - Tag to remove\n   */\n  removeTag(tag: string): void {\n    const currentTags = this.taskForm.get('tags')?.value || [];\n    const index = currentTags.indexOf(tag);\n    \n    if (index >= 0) {\n      const newTags = [...currentTags];\n      newTags.splice(index, 1);\n      this.taskForm.get('tags')?.setValue(newTags);\n    }\n  }\n\n  /**\n   * Get error message for a form control\n   * @param controlName - Name of the form control\n   * @returns Error message\n   */\n  getErrorMessage(controlName: string): string {\n    const control = this.taskForm.get(controlName);\n    \n    if (!control || !control.errors) {\n      return '';\n    }\n    \n    if (control.errors['required']) {\n      return 'This field is required';\n    }\n    \n    if (control.errors['maxlength']) {\n      const maxLength = control.errors['maxlength'].requiredLength;\n      return `Maximum length is ${maxLength} characters`;\n    }\n    \n    return 'Invalid value';\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAUC,uBAAuB,QAAQ,eAAe;AACvG,SAAiCC,UAAU,QAAQ,gBAAgB;AAGnE,SAASC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AAS7C,WAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAuD5B;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IArDtB;;;IAGS,KAAAC,KAAK,GAAW,EAAE;IAE3B;;;IAGS,KAAAC,OAAO,GAAG,KAAK;IAExB;;;IAGU,KAAAC,UAAU,GAAG,IAAIV,YAAY,EAAiB;IAExD;;;IAGU,KAAAW,UAAU,GAAG,IAAIX,YAAY,EAAQ;IAO/C;;;IAGS,KAAAY,kBAAkB,GAAG,CAACR,KAAK,EAAED,KAAK,CAAU;IAErD;;;IAGA,KAAAU,QAAQ,GAAG,CACT;MAAEC,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAE,EACjC;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAa,CAAE,EAC9C;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAM,CAAE,CACjC;IAED;;;IAGA,KAAAC,UAAU,GAAG,CACX;MAAEF,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAM,CAAE,EAChC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,CAC/B;EAMqC;EAEtC;;;;EAIAE,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEA;;;EAGAA,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACZ,EAAE,CAACa,KAAK,CAAC;MAC5BC,KAAK,EAAE,CAAC,IAAI,CAACC,IAAI,EAAED,KAAK,IAAI,EAAE,EAAE,CAACnB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACsB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACjFC,WAAW,EAAE,CAAC,IAAI,CAACH,IAAI,EAAEG,WAAW,IAAI,EAAE,EAAEvB,UAAU,CAACsB,SAAS,CAAC,IAAI,CAAC,CAAC;MACvEE,MAAM,EAAE,CAAC,IAAI,CAACJ,IAAI,EAAEI,MAAM,IAAI,MAAM,EAAExB,UAAU,CAACqB,QAAQ,CAAC;MAC1DI,QAAQ,EAAE,CAAC,IAAI,CAACL,IAAI,EAAEK,QAAQ,IAAI,QAAQ,EAAEzB,UAAU,CAACqB,QAAQ,CAAC;MAChEK,OAAO,EAAE,CAAC,IAAI,CAACN,IAAI,EAAEM,OAAO,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACP,IAAI,CAACM,OAAO,CAAC,GAAG,IAAI,CAAC;MAClEE,QAAQ,EAAE,CAAC,IAAI,CAACR,IAAI,EAAEQ,QAAQ,IAAI,EAAE,CAAC;MACrCC,IAAI,EAAE,CAAC,IAAI,CAACT,IAAI,EAAES,IAAI,IAAI,EAAE;KAC7B,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACb,QAAQ,CAACc,OAAO,IAAI,IAAI,CAACxB,OAAO,EAAE;MACzC;;IAGF;IACA,MAAMyB,UAAU,GAAG,IAAI,CAACf,QAAQ,CAACL,KAAK;IAEtC;IACA,IAAIoB,UAAU,CAACN,OAAO,EAAE;MACtBM,UAAU,CAACN,OAAO,GAAG,IAAIC,IAAI,CAACK,UAAU,CAACN,OAAO,CAAC,CAACO,WAAW,EAAE;;IAGjE;IACA,IAAI,CAACzB,UAAU,CAAC0B,IAAI,CAACF,UAAU,CAAC;EAClC;EAEA;;;EAGAG,QAAQA,CAAA;IACN,IAAI,CAAC1B,UAAU,CAACyB,IAAI,EAAE;EACxB;EAEA;;;;EAIAE,MAAMA,CAACC,KAAwB;IAC7B,MAAMzB,KAAK,GAAG,CAACyB,KAAK,CAACzB,KAAK,IAAI,EAAE,EAAE0B,IAAI,EAAE;IAExC;IACA,IAAI1B,KAAK,EAAE;MACT,MAAM2B,WAAW,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,MAAM,CAAC,EAAE5B,KAAK,IAAI,EAAE;MAC1D,IAAI,CAAC2B,WAAW,CAACE,QAAQ,CAAC7B,KAAK,CAAC,EAAE;QAChC,IAAI,CAACK,QAAQ,CAACuB,GAAG,CAAC,MAAM,CAAC,EAAEE,QAAQ,CAAC,CAAC,GAAGH,WAAW,EAAE3B,KAAK,CAAC,CAAC;;;IAIhE;IACAyB,KAAK,CAACM,SAAU,CAACC,KAAK,EAAE;EAC1B;EAEA;;;;EAIAC,SAASA,CAACC,GAAW;IACnB,MAAMP,WAAW,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,MAAM,CAAC,EAAE5B,KAAK,IAAI,EAAE;IAC1D,MAAMmC,KAAK,GAAGR,WAAW,CAACS,OAAO,CAACF,GAAG,CAAC;IAEtC,IAAIC,KAAK,IAAI,CAAC,EAAE;MACd,MAAME,OAAO,GAAG,CAAC,GAAGV,WAAW,CAAC;MAChCU,OAAO,CAACC,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACxB,IAAI,CAAC9B,QAAQ,CAACuB,GAAG,CAAC,MAAM,CAAC,EAAEE,QAAQ,CAACO,OAAO,CAAC;;EAEhD;EAEA;;;;;EAKAE,eAAeA,CAACC,WAAmB;IACjC,MAAMC,OAAO,GAAG,IAAI,CAACpC,QAAQ,CAACuB,GAAG,CAACY,WAAW,CAAC;IAE9C,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE;MAC/B,OAAO,EAAE;;IAGX,IAAID,OAAO,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;MAC9B,OAAO,wBAAwB;;IAGjC,IAAID,OAAO,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE;MAC/B,MAAMhC,SAAS,GAAG+B,OAAO,CAACC,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc;MAC5D,OAAO,qBAAqBjC,SAAS,aAAa;;IAGpD,OAAO,eAAe;EACxB;CACD;AApKUkC,UAAA,EAAR5D,KAAK,EAAE,C,8CAAa;AAKZ4D,UAAA,EAAR5D,KAAK,EAAE,C,+CAAoB;AAKnB4D,UAAA,EAAR5D,KAAK,EAAE,C,iDAAiB;AAKf4D,UAAA,EAAT3D,MAAM,EAAE,C,oDAAgD;AAK/C2D,UAAA,EAAT3D,MAAM,EAAE,C,oDAAuC;AAxBrCM,iBAAiB,GAAAqD,UAAA,EAN7B7D,SAAS,CAAC;EACT8D,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,CAAC,4BAA4B,CAAC;EACzCC,eAAe,EAAE7D,uBAAuB,CAAC8D;CAC1C,CAAC,C,EACW1D,iBAAiB,CAwK7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}