{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n// Components\nimport { TaskListComponent } from './components/task-list/task-list.component';\nimport { TaskDetailComponent } from './components/task-detail/task-detail.component';\nimport { TaskFormComponent } from './components/task-form/task-form.component';\nimport { TaskFilterComponent } from './components/task-filter/task-filter.component';\nimport { TaskItemComponent } from './components/task-item/task-item.component';\n// Pages\nimport { TasksPageComponent } from './pages/tasks-page/tasks-page.component';\nimport { TaskCreatePageComponent } from './pages/task-create-page/task-create-page.component';\nimport { TaskEditPageComponent } from './pages/task-edit-page/task-edit-page.component';\nimport { TaskViewPageComponent } from './pages/task-view-page/task-view-page.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n/**\n * Routes for the tasks feature module\n */\nconst routes = [{\n  path: '',\n  component: TasksPageComponent\n}, {\n  path: 'create',\n  component: TaskCreatePageComponent\n}, {\n  path: ':id',\n  component: TaskViewPageComponent\n}, {\n  path: ':id/edit',\n  component: TaskEditPageComponent\n}];\nexport class TasksModule {\n  static {\n    this.ɵfac = function TasksModule_Factory(t) {\n      return new (t || TasksModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TasksModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TasksModule, {\n    declarations: [TaskListComponent, TaskDetailComponent, TaskFormComponent, TaskFilterComponent, TaskItemComponent, TasksPageComponent, TaskCreatePageComponent, TaskEditPageComponent, TaskViewPageComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "TaskListComponent", "TaskDetailComponent", "TaskFormComponent", "TaskFilterComponent", "TaskItemComponent", "TasksPageComponent", "TaskCreatePageComponent", "TaskEditPageComponent", "TaskViewPageComponent", "routes", "path", "component", "TasksModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\tasks.module.ts"], "sourcesContent": ["/**\n * Tasks Module\n * Feature module for task management functionality\n */\nimport { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n\n// Components\nimport { TaskListComponent } from './components/task-list/task-list.component';\nimport { TaskDetailComponent } from './components/task-detail/task-detail.component';\nimport { TaskFormComponent } from './components/task-form/task-form.component';\nimport { TaskFilterComponent } from './components/task-filter/task-filter.component';\nimport { TaskItemComponent } from './components/task-item/task-item.component';\n\n// Pages\nimport { TasksPageComponent } from './pages/tasks-page/tasks-page.component';\nimport { TaskCreatePageComponent } from './pages/task-create-page/task-create-page.component';\nimport { TaskEditPageComponent } from './pages/task-edit-page/task-edit-page.component';\nimport { TaskViewPageComponent } from './pages/task-view-page/task-view-page.component';\n\n/**\n * Routes for the tasks feature module\n */\nconst routes: Routes = [\n  {\n    path: '',\n    component: TasksPageComponent\n  },\n  {\n    path: 'create',\n    component: TaskCreatePageComponent\n  },\n  {\n    path: ':id',\n    component: TaskViewPageComponent\n  },\n  {\n    path: ':id/edit',\n    component: TaskEditPageComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    TaskListComponent,\n    TaskDetailComponent,\n    TaskFormComponent,\n    TaskFilterComponent,\n    TaskItemComponent,\n    TasksPageComponent,\n    TaskCreatePageComponent,\n    TaskEditPageComponent,\n    TaskViewPageComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class TasksModule { }\n"], "mappings": "AAKA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AAEzD;AACA,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,iBAAiB,QAAQ,4CAA4C;AAE9E;AACA,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,uBAAuB,QAAQ,qDAAqD;AAC7F,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,qBAAqB,QAAQ,iDAAiD;;;AAEvF;;;AAGA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEJ;CACZ,CACF;AAmBD,OAAM,MAAOK,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAJpBb,YAAY,EACZD,YAAY,CAACe,QAAQ,CAACJ,MAAM,CAAC;IAAA;EAAA;;;2EAGpBG,WAAW;IAAAE,YAAA,GAfpBd,iBAAiB,EACjBC,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,EACnBC,iBAAiB,EACjBC,kBAAkB,EAClBC,uBAAuB,EACvBC,qBAAqB,EACrBC,qBAAqB;IAAAO,OAAA,GAGrBhB,YAAY,EAAAiB,EAAA,CAAAlB,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}