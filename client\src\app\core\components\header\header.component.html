<!-- Main application header -->
<mat-toolbar color="primary" class="header">
  <!-- Application logo and title -->
  <div class="header-brand">
    <button mat-icon-button aria-label="Toggle sidenav" class="menu-button">
      <mat-icon>menu</mat-icon>
    </button>
    <a routerLink="/" class="brand-link">
      <span class="brand-title">Task Management</span>
    </a>
  </div>

  <!-- Spacer to push content to opposite sides -->
  <span class="spacer"></span>

  <!-- Navigation links -->
  <nav class="nav-links">
    <a mat-button routerLink="/tasks" routerLinkActive="active">
      <mat-icon>task</mat-icon>
      <span>Tasks</span>
    </a>
    <a mat-button routerLink="/tasks/create" routerLinkActive="active">
      <mat-icon>add_task</mat-icon>
      <span>New Task</span>
    </a>
  </nav>

  <!-- User profile menu -->
  <div class="user-menu">
    <!-- Notification bell -->
    <button mat-icon-button aria-label="Show notifications">
      <mat-icon>notifications</mat-icon>
    </button>
    
    <!-- User avatar and dropdown -->
    <div class="user-dropdown" (appClickOutside)="closeUserMenu()">
      <button mat-button [matMenuTriggerFor]="userMenu" class="user-button" (click)="toggleUserMenu()">
        <div class="user-avatar" [style.backgroundImage]="currentUser?.avatarUrl ? 'url(' + currentUser?.avatarUrl + ')' : ''">
          <span *ngIf="!currentUser?.avatarUrl">{{ currentUser?.name?.charAt(0) }}</span>
        </div>
        <span class="user-name">{{ currentUser?.name }}</span>
        <mat-icon>{{ isUserMenuOpen ? 'arrow_drop_up' : 'arrow_drop_down' }}</mat-icon>
      </button>
      
      <!-- User dropdown menu -->
      <mat-menu #userMenu="matMenu">
        <button mat-menu-item (click)="goToProfile()">
          <mat-icon>person</mat-icon>
          <span>Profile</span>
        </button>
        <button mat-menu-item (click)="logout()">
          <mat-icon>exit_to_app</mat-icon>
          <span>Logout</span>
        </button>
      </mat-menu>
    </div>
  </div>
</mat-toolbar>
