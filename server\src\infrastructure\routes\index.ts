/**
 * Main API routes configuration
 * Aggregates all API route modules
 */
import { Router } from 'express';
import taskRoutes from './taskRoutes';
import userRoutes from './userRoutes';
import authRoutes from './authRoutes';

// Create router instance
const router = Router();

/**
 * @swagger
 * tags:
 *   - name: Auth
 *     description: Authentication operations
 *   - name: Users
 *     description: User management operations
 *   - name: Tasks
 *     description: Task management operations
 */

// Register route modules
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/tasks', taskRoutes);

export { router as apiRoutes };
