/**
 * Dashboard Page Component
 * Main dashboard page displaying task summaries and charts
 */
import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TaskService } from '../../../../core/services/task.service';
import { UserService } from '../../../../core/services/user.service';
import { AuthService } from '../../../../core/services/auth.service';
import { Task } from '../../../../core/models/task.model';
import { User } from '../../../../core/models/user.model';

@Component({
  selector: 'app-dashboard-page',
  templateUrl: './dashboard-page.component.html',
  styleUrls: ['./dashboard-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardPageComponent implements OnInit, OnD<PERSON>roy {
  /**
   * Current user
   */
  currentUser: User | null = null;
  
  /**
   * All tasks
   */
  tasks: Task[] = [];
  
  /**
   * Recent tasks (last 5)
   */
  recentTasks: Task[] = [];
  
  /**
   * Loading state indicator
   */
  loading = true;
  
  /**
   * Error message
   */
  error: string | null = null;
  
  /**
   * Task counts by status
   */
  taskStatusCounts = {
    todo: 0,
    inProgress: 0,
    done: 0
  };
  
  /**
   * Task counts by priority
   */
  taskPriorityCounts = {
    high: 0,
    medium: 0,
    low: 0
  };
  
  /**
   * Subject for unsubscribing from observables
   */
  private destroy$ = new Subject<void>();

  /**
   * Constructor with dependency injection
   * @param taskService - Task service
   * @param userService - User service
   * @param authService - Auth service
   * @param cdr - Change detector reference
   */
  constructor(
    private taskService: TaskService,
    private userService: UserService,
    private authService: AuthService,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Loads current user and tasks
   */
  ngOnInit(): void {
    this.loadCurrentUser();
    this.loadTasks();
  }

  /**
   * Lifecycle hook that is called before component destruction
   * Unsubscribes from observables
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load current user data
   */
  private loadCurrentUser(): void {
    this.currentUser = this.authService.currentUserValue;
    
    // If no current user in memory, try to get from API
    if (!this.currentUser) {
      this.authService.getProfile()
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (user) => {
            this.currentUser = user;
            this.cdr.markForCheck();
          },
          error: (err) => {
            console.error('Error loading user profile:', err);
            this.error = 'Failed to load user profile';
            this.cdr.markForCheck();
          }
        });
    }
  }

  /**
   * Load all tasks and calculate statistics
   */
  private loadTasks(): void {
    this.loading = true;
    this.error = null;
    this.cdr.markForCheck();
    
    this.taskService.getTasks()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tasks) => {
          this.tasks = tasks;
          this.recentTasks = [...tasks].sort((a, b) => 
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          ).slice(0, 5);
          
          this.calculateTaskStatistics();
          this.loading = false;
          this.cdr.markForCheck();
        },
        error: (err) => {
          console.error('Error loading tasks:', err);
          this.error = 'Failed to load tasks';
          this.loading = false;
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Calculate task statistics for charts
   */
  private calculateTaskStatistics(): void {
    // Reset counters
    this.taskStatusCounts = {
      todo: 0,
      inProgress: 0,
      done: 0
    };
    
    this.taskPriorityCounts = {
      high: 0,
      medium: 0,
      low: 0
    };
    
    // Count tasks by status and priority
    this.tasks.forEach(task => {
      // Count by status
      if (task.status === 'todo') {
        this.taskStatusCounts.todo++;
      } else if (task.status === 'in_progress') {
        this.taskStatusCounts.inProgress++;
      } else if (task.status === 'done') {
        this.taskStatusCounts.done++;
      }
      
      // Count by priority
      if (task.priority === 'high') {
        this.taskPriorityCounts.high++;
      } else if (task.priority === 'medium') {
        this.taskPriorityCounts.medium++;
      } else if (task.priority === 'low') {
        this.taskPriorityCounts.low++;
      }
    });
  }
}
