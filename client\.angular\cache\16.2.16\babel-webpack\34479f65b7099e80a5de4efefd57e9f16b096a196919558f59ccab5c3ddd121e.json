{"ast": null, "code": "/******/(function () {\n  // webpackBootstrap\n  /******/\n  \"use strict\";\n\n  /******/\n  var __webpack_modules__ = {\n    /***/\"./client-src/modules/logger/SyncBailHookFake.js\":\n    /*!*******************************************************!*\\\n      !*** ./client-src/modules/logger/SyncBailHookFake.js ***!\n      \\*******************************************************/\n    /***/\n    function (module) {\n      /**\n       * Client stub for tapable SyncBailHook\n       */\n      module.exports = function clientTapableSyncBailHook() {\n        return {\n          call: function call() {}\n        };\n      };\n\n      /***/\n    },\n\n    /***/\"./node_modules/webpack/lib/logging/Logger.js\":\n    /*!****************************************************!*\\\n      !*** ./node_modules/webpack/lib/logging/Logger.js ***!\n      \\****************************************************/\n    /***/\n    function (__unused_webpack_module, exports) {\n      /*\n      \tMIT License http://www.opensource.org/licenses/mit-license.php\n      \tAuthor Tobias Koppers @sokra\n      */\n\n      function _toConsumableArray(arr) {\n        return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n      }\n      function _nonIterableSpread() {\n        throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n      }\n      function _unsupportedIterableToArray(o, minLen) {\n        if (!o) return;\n        if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n        var n = Object.prototype.toString.call(o).slice(8, -1);\n        if (n === \"Object\" && o.constructor) n = o.constructor.name;\n        if (n === \"Map\" || n === \"Set\") return Array.from(o);\n        if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n      }\n      function _iterableToArray(iter) {\n        if (typeof (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n          return i;\n        }) !== \"undefined\" && iter[(typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n          return i;\n        }).iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n      }\n      function _arrayWithoutHoles(arr) {\n        if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n      }\n      function _arrayLikeToArray(arr, len) {\n        if (len == null || len > arr.length) len = arr.length;\n        for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n        return arr2;\n      }\n      function _classCallCheck(instance, Constructor) {\n        if (!(instance instanceof Constructor)) {\n          throw new TypeError(\"Cannot call a class as a function\");\n        }\n      }\n      function _defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n        }\n      }\n      function _createClass(Constructor, protoProps, staticProps) {\n        if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) _defineProperties(Constructor, staticProps);\n        Object.defineProperty(Constructor, \"prototype\", {\n          writable: false\n        });\n        return Constructor;\n      }\n      function _toPropertyKey(arg) {\n        var key = _toPrimitive(arg, \"string\");\n        return typeof key === \"symbol\" ? key : String(key);\n      }\n      function _toPrimitive(input, hint) {\n        if (typeof input !== \"object\" || input === null) return input;\n        var prim = input[(typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n          return i;\n        }).toPrimitive];\n        if (prim !== undefined) {\n          var res = prim.call(input, hint || \"default\");\n          if (typeof res !== \"object\") return res;\n          throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n        }\n        return (hint === \"string\" ? String : Number)(input);\n      }\n      var LogType = Object.freeze({\n        error: /** @type {\"error\"} */\"error\",\n        // message, c style arguments\n        warn: /** @type {\"warn\"} */\"warn\",\n        // message, c style arguments\n        info: /** @type {\"info\"} */\"info\",\n        // message, c style arguments\n        log: /** @type {\"log\"} */\"log\",\n        // message, c style arguments\n        debug: /** @type {\"debug\"} */\"debug\",\n        // message, c style arguments\n\n        trace: /** @type {\"trace\"} */\"trace\",\n        // no arguments\n\n        group: /** @type {\"group\"} */\"group\",\n        // [label]\n        groupCollapsed: /** @type {\"groupCollapsed\"} */\"groupCollapsed\",\n        // [label]\n        groupEnd: /** @type {\"groupEnd\"} */\"groupEnd\",\n        // [label]\n\n        profile: /** @type {\"profile\"} */\"profile\",\n        // [profileName]\n        profileEnd: /** @type {\"profileEnd\"} */\"profileEnd\",\n        // [profileName]\n\n        time: /** @type {\"time\"} */\"time\",\n        // name, time as [seconds, nanoseconds]\n\n        clear: /** @type {\"clear\"} */\"clear\",\n        // no arguments\n        status: /** @type {\"status\"} */\"status\" // message, arguments\n      });\n\n      exports.LogType = LogType;\n\n      /** @typedef {typeof LogType[keyof typeof LogType]} LogTypeEnum */\n\n      var LOG_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n        return i;\n      })(\"webpack logger raw log method\");\n      var TIMERS_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n        return i;\n      })(\"webpack logger times\");\n      var TIMERS_AGGREGATES_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n        return i;\n      })(\"webpack logger aggregated times\");\n      var WebpackLogger = /*#__PURE__*/function () {\n        /**\n         * @param {function(LogTypeEnum, any[]=): void} log log function\n         * @param {function(string | function(): string): WebpackLogger} getChildLogger function to create child logger\n         */\n        function WebpackLogger(log, getChildLogger) {\n          _classCallCheck(this, WebpackLogger);\n          this[LOG_SYMBOL] = log;\n          this.getChildLogger = getChildLogger;\n        }\n        _createClass(WebpackLogger, [{\n          key: \"error\",\n          value: function error() {\n            for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n              args[_key] = arguments[_key];\n            }\n            this[LOG_SYMBOL](LogType.error, args);\n          }\n        }, {\n          key: \"warn\",\n          value: function warn() {\n            for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n              args[_key2] = arguments[_key2];\n            }\n            this[LOG_SYMBOL](LogType.warn, args);\n          }\n        }, {\n          key: \"info\",\n          value: function info() {\n            for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n              args[_key3] = arguments[_key3];\n            }\n            this[LOG_SYMBOL](LogType.info, args);\n          }\n        }, {\n          key: \"log\",\n          value: function log() {\n            for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n              args[_key4] = arguments[_key4];\n            }\n            this[LOG_SYMBOL](LogType.log, args);\n          }\n        }, {\n          key: \"debug\",\n          value: function debug() {\n            for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n              args[_key5] = arguments[_key5];\n            }\n            this[LOG_SYMBOL](LogType.debug, args);\n          }\n        }, {\n          key: \"assert\",\n          value: function assert(assertion) {\n            if (!assertion) {\n              for (var _len6 = arguments.length, args = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++) {\n                args[_key6 - 1] = arguments[_key6];\n              }\n              this[LOG_SYMBOL](LogType.error, args);\n            }\n          }\n        }, {\n          key: \"trace\",\n          value: function trace() {\n            this[LOG_SYMBOL](LogType.trace, [\"Trace\"]);\n          }\n        }, {\n          key: \"clear\",\n          value: function clear() {\n            this[LOG_SYMBOL](LogType.clear);\n          }\n        }, {\n          key: \"status\",\n          value: function status() {\n            for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n              args[_key7] = arguments[_key7];\n            }\n            this[LOG_SYMBOL](LogType.status, args);\n          }\n        }, {\n          key: \"group\",\n          value: function group() {\n            for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n              args[_key8] = arguments[_key8];\n            }\n            this[LOG_SYMBOL](LogType.group, args);\n          }\n        }, {\n          key: \"groupCollapsed\",\n          value: function groupCollapsed() {\n            for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n              args[_key9] = arguments[_key9];\n            }\n            this[LOG_SYMBOL](LogType.groupCollapsed, args);\n          }\n        }, {\n          key: \"groupEnd\",\n          value: function groupEnd() {\n            for (var _len10 = arguments.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n              args[_key10] = arguments[_key10];\n            }\n            this[LOG_SYMBOL](LogType.groupEnd, args);\n          }\n        }, {\n          key: \"profile\",\n          value: function profile(label) {\n            this[LOG_SYMBOL](LogType.profile, [label]);\n          }\n        }, {\n          key: \"profileEnd\",\n          value: function profileEnd(label) {\n            this[LOG_SYMBOL](LogType.profileEnd, [label]);\n          }\n        }, {\n          key: \"time\",\n          value: function time(label) {\n            this[TIMERS_SYMBOL] = this[TIMERS_SYMBOL] || new Map();\n            this[TIMERS_SYMBOL].set(label, process.hrtime());\n          }\n        }, {\n          key: \"timeLog\",\n          value: function timeLog(label) {\n            var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n            if (!prev) {\n              throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeLog()\"));\n            }\n            var time = process.hrtime(prev);\n            this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n          }\n        }, {\n          key: \"timeEnd\",\n          value: function timeEnd(label) {\n            var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n            if (!prev) {\n              throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeEnd()\"));\n            }\n            var time = process.hrtime(prev);\n            this[TIMERS_SYMBOL].delete(label);\n            this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n          }\n        }, {\n          key: \"timeAggregate\",\n          value: function timeAggregate(label) {\n            var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n            if (!prev) {\n              throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeAggregate()\"));\n            }\n            var time = process.hrtime(prev);\n            this[TIMERS_SYMBOL].delete(label);\n            this[TIMERS_AGGREGATES_SYMBOL] = this[TIMERS_AGGREGATES_SYMBOL] || new Map();\n            var current = this[TIMERS_AGGREGATES_SYMBOL].get(label);\n            if (current !== undefined) {\n              if (time[1] + current[1] > 1e9) {\n                time[0] += current[0] + 1;\n                time[1] = time[1] - 1e9 + current[1];\n              } else {\n                time[0] += current[0];\n                time[1] += current[1];\n              }\n            }\n            this[TIMERS_AGGREGATES_SYMBOL].set(label, time);\n          }\n        }, {\n          key: \"timeAggregateEnd\",\n          value: function timeAggregateEnd(label) {\n            if (this[TIMERS_AGGREGATES_SYMBOL] === undefined) return;\n            var time = this[TIMERS_AGGREGATES_SYMBOL].get(label);\n            if (time === undefined) return;\n            this[TIMERS_AGGREGATES_SYMBOL].delete(label);\n            this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n          }\n        }]);\n        return WebpackLogger;\n      }();\n      exports.Logger = WebpackLogger;\n\n      /***/\n    },\n\n    /***/\"./node_modules/webpack/lib/logging/createConsoleLogger.js\":\n    /*!*****************************************************************!*\\\n      !*** ./node_modules/webpack/lib/logging/createConsoleLogger.js ***!\n      \\*****************************************************************/\n    /***/\n    function (module, __unused_webpack_exports, __webpack_require__) {\n      /*\n      \tMIT License http://www.opensource.org/licenses/mit-license.php\n      \tAuthor Tobias Koppers @sokra\n      */\n\n      function _toConsumableArray(arr) {\n        return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n      }\n      function _nonIterableSpread() {\n        throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n      }\n      function _unsupportedIterableToArray(o, minLen) {\n        if (!o) return;\n        if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n        var n = Object.prototype.toString.call(o).slice(8, -1);\n        if (n === \"Object\" && o.constructor) n = o.constructor.name;\n        if (n === \"Map\" || n === \"Set\") return Array.from(o);\n        if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n      }\n      function _iterableToArray(iter) {\n        if (typeof (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n          return i;\n        }) !== \"undefined\" && iter[(typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n          return i;\n        }).iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n      }\n      function _arrayWithoutHoles(arr) {\n        if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n      }\n      function _arrayLikeToArray(arr, len) {\n        if (len == null || len > arr.length) len = arr.length;\n        for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n        return arr2;\n      }\n      var _require = __webpack_require__( /*! ./Logger */\"./node_modules/webpack/lib/logging/Logger.js\"),\n        LogType = _require.LogType;\n\n      /** @typedef {import(\"../../declarations/WebpackOptions\").FilterItemTypes} FilterItemTypes */\n      /** @typedef {import(\"../../declarations/WebpackOptions\").FilterTypes} FilterTypes */\n      /** @typedef {import(\"./Logger\").LogTypeEnum} LogTypeEnum */\n\n      /** @typedef {function(string): boolean} FilterFunction */\n\n      /**\n       * @typedef {Object} LoggerConsole\n       * @property {function(): void} clear\n       * @property {function(): void} trace\n       * @property {(...args: any[]) => void} info\n       * @property {(...args: any[]) => void} log\n       * @property {(...args: any[]) => void} warn\n       * @property {(...args: any[]) => void} error\n       * @property {(...args: any[]) => void=} debug\n       * @property {(...args: any[]) => void=} group\n       * @property {(...args: any[]) => void=} groupCollapsed\n       * @property {(...args: any[]) => void=} groupEnd\n       * @property {(...args: any[]) => void=} status\n       * @property {(...args: any[]) => void=} profile\n       * @property {(...args: any[]) => void=} profileEnd\n       * @property {(...args: any[]) => void=} logTime\n       */\n\n      /**\n       * @typedef {Object} LoggerOptions\n       * @property {false|true|\"none\"|\"error\"|\"warn\"|\"info\"|\"log\"|\"verbose\"} level loglevel\n       * @property {FilterTypes|boolean} debug filter for debug logging\n       * @property {LoggerConsole} console the console to log to\n       */\n\n      /**\n       * @param {FilterItemTypes} item an input item\n       * @returns {FilterFunction} filter function\n       */\n      var filterToFunction = function filterToFunction(item) {\n        if (typeof item === \"string\") {\n          var regExp = new RegExp(\"[\\\\\\\\/]\".concat(item.replace(\n          // eslint-disable-next-line no-useless-escape\n          /[-[\\]{}()*+?.\\\\^$|]/g, \"\\\\$&\"), \"([\\\\\\\\/]|$|!|\\\\?)\"));\n          return function (ident) {\n            return regExp.test(ident);\n          };\n        }\n        if (item && typeof item === \"object\" && typeof item.test === \"function\") {\n          return function (ident) {\n            return item.test(ident);\n          };\n        }\n        if (typeof item === \"function\") {\n          return item;\n        }\n        if (typeof item === \"boolean\") {\n          return function () {\n            return item;\n          };\n        }\n      };\n\n      /**\n       * @enum {number}\n       */\n      var LogLevel = {\n        none: 6,\n        false: 6,\n        error: 5,\n        warn: 4,\n        info: 3,\n        log: 2,\n        true: 2,\n        verbose: 1\n      };\n\n      /**\n       * @param {LoggerOptions} options options object\n       * @returns {function(string, LogTypeEnum, any[]): void} logging function\n       */\n      module.exports = function (_ref) {\n        var _ref$level = _ref.level,\n          level = _ref$level === void 0 ? \"info\" : _ref$level,\n          _ref$debug = _ref.debug,\n          debug = _ref$debug === void 0 ? false : _ref$debug,\n          console = _ref.console;\n        var debugFilters = typeof debug === \"boolean\" ? [function () {\n          return debug;\n        }] : /** @type {FilterItemTypes[]} */[].concat(debug).map(filterToFunction);\n        /** @type {number} */\n        var loglevel = LogLevel[\"\".concat(level)] || 0;\n\n        /**\n         * @param {string} name name of the logger\n         * @param {LogTypeEnum} type type of the log entry\n         * @param {any[]} args arguments of the log entry\n         * @returns {void}\n         */\n        var logger = function logger(name, type, args) {\n          var labeledArgs = function labeledArgs() {\n            if (Array.isArray(args)) {\n              if (args.length > 0 && typeof args[0] === \"string\") {\n                return [\"[\".concat(name, \"] \").concat(args[0])].concat(_toConsumableArray(args.slice(1)));\n              } else {\n                return [\"[\".concat(name, \"]\")].concat(_toConsumableArray(args));\n              }\n            } else {\n              return [];\n            }\n          };\n          var debug = debugFilters.some(function (f) {\n            return f(name);\n          });\n          switch (type) {\n            case LogType.debug:\n              if (!debug) return;\n              // eslint-disable-next-line node/no-unsupported-features/node-builtins\n              if (typeof console.debug === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.debug.apply(console, _toConsumableArray(labeledArgs()));\n              } else {\n                console.log.apply(console, _toConsumableArray(labeledArgs()));\n              }\n              break;\n            case LogType.log:\n              if (!debug && loglevel > LogLevel.log) return;\n              console.log.apply(console, _toConsumableArray(labeledArgs()));\n              break;\n            case LogType.info:\n              if (!debug && loglevel > LogLevel.info) return;\n              console.info.apply(console, _toConsumableArray(labeledArgs()));\n              break;\n            case LogType.warn:\n              if (!debug && loglevel > LogLevel.warn) return;\n              console.warn.apply(console, _toConsumableArray(labeledArgs()));\n              break;\n            case LogType.error:\n              if (!debug && loglevel > LogLevel.error) return;\n              console.error.apply(console, _toConsumableArray(labeledArgs()));\n              break;\n            case LogType.trace:\n              if (!debug) return;\n              console.trace();\n              break;\n            case LogType.groupCollapsed:\n              if (!debug && loglevel > LogLevel.log) return;\n              if (!debug && loglevel > LogLevel.verbose) {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                if (typeof console.groupCollapsed === \"function\") {\n                  // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                  console.groupCollapsed.apply(console, _toConsumableArray(labeledArgs()));\n                } else {\n                  console.log.apply(console, _toConsumableArray(labeledArgs()));\n                }\n                break;\n              }\n            // falls through\n            case LogType.group:\n              if (!debug && loglevel > LogLevel.log) return;\n              // eslint-disable-next-line node/no-unsupported-features/node-builtins\n              if (typeof console.group === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.group.apply(console, _toConsumableArray(labeledArgs()));\n              } else {\n                console.log.apply(console, _toConsumableArray(labeledArgs()));\n              }\n              break;\n            case LogType.groupEnd:\n              if (!debug && loglevel > LogLevel.log) return;\n              // eslint-disable-next-line node/no-unsupported-features/node-builtins\n              if (typeof console.groupEnd === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.groupEnd();\n              }\n              break;\n            case LogType.time:\n              {\n                if (!debug && loglevel > LogLevel.log) return;\n                var ms = args[1] * 1000 + args[2] / 1000000;\n                var msg = \"[\".concat(name, \"] \").concat(args[0], \": \").concat(ms, \" ms\");\n                if (typeof console.logTime === \"function\") {\n                  console.logTime(msg);\n                } else {\n                  console.log(msg);\n                }\n                break;\n              }\n            case LogType.profile:\n              // eslint-disable-next-line node/no-unsupported-features/node-builtins\n              if (typeof console.profile === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.profile.apply(console, _toConsumableArray(labeledArgs()));\n              }\n              break;\n            case LogType.profileEnd:\n              // eslint-disable-next-line node/no-unsupported-features/node-builtins\n              if (typeof console.profileEnd === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.profileEnd.apply(console, _toConsumableArray(labeledArgs()));\n              }\n              break;\n            case LogType.clear:\n              if (!debug && loglevel > LogLevel.log) return;\n              // eslint-disable-next-line node/no-unsupported-features/node-builtins\n              if (typeof console.clear === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.clear();\n              }\n              break;\n            case LogType.status:\n              if (!debug && loglevel > LogLevel.info) return;\n              if (typeof console.status === \"function\") {\n                if (args.length === 0) {\n                  console.status();\n                } else {\n                  console.status.apply(console, _toConsumableArray(labeledArgs()));\n                }\n              } else {\n                if (args.length !== 0) {\n                  console.info.apply(console, _toConsumableArray(labeledArgs()));\n                }\n              }\n              break;\n            default:\n              throw new Error(\"Unexpected LogType \".concat(type));\n          }\n        };\n        return logger;\n      };\n\n      /***/\n    },\n\n    /***/\"./node_modules/webpack/lib/logging/runtime.js\":\n    /*!*****************************************************!*\\\n      !*** ./node_modules/webpack/lib/logging/runtime.js ***!\n      \\*****************************************************/\n    /***/\n    function (__unused_webpack_module, exports, __webpack_require__) {\n      /*\n      \tMIT License http://www.opensource.org/licenses/mit-license.php\n      \tAuthor Tobias Koppers @sokra\n      */\n\n      function _extends() {\n        _extends = Object.assign ? Object.assign.bind() : function (target) {\n          for (var i = 1; i < arguments.length; i++) {\n            var source = arguments[i];\n            for (var key in source) {\n              if (Object.prototype.hasOwnProperty.call(source, key)) {\n                target[key] = source[key];\n              }\n            }\n          }\n          return target;\n        };\n        return _extends.apply(this, arguments);\n      }\n      var SyncBailHook = __webpack_require__( /*! tapable/lib/SyncBailHook */\"./client-src/modules/logger/SyncBailHookFake.js\");\n      var _require = __webpack_require__( /*! ./Logger */\"./node_modules/webpack/lib/logging/Logger.js\"),\n        Logger = _require.Logger;\n      var createConsoleLogger = __webpack_require__( /*! ./createConsoleLogger */\"./node_modules/webpack/lib/logging/createConsoleLogger.js\");\n\n      /** @type {createConsoleLogger.LoggerOptions} */\n      var currentDefaultLoggerOptions = {\n        level: \"info\",\n        debug: false,\n        console: console\n      };\n      var currentDefaultLogger = createConsoleLogger(currentDefaultLoggerOptions);\n\n      /**\n       * @param {string} name name of the logger\n       * @returns {Logger} a logger\n       */\n      exports.getLogger = function (name) {\n        return new Logger(function (type, args) {\n          if (exports.hooks.log.call(name, type, args) === undefined) {\n            currentDefaultLogger(name, type, args);\n          }\n        }, function (childName) {\n          return exports.getLogger(\"\".concat(name, \"/\").concat(childName));\n        });\n      };\n\n      /**\n       * @param {createConsoleLogger.LoggerOptions} options new options, merge with old options\n       * @returns {void}\n       */\n      exports.configureDefaultLogger = function (options) {\n        _extends(currentDefaultLoggerOptions, options);\n        currentDefaultLogger = createConsoleLogger(currentDefaultLoggerOptions);\n      };\n      exports.hooks = {\n        log: new SyncBailHook([\"origin\", \"type\", \"args\"])\n      };\n\n      /***/\n    }\n\n    /******/\n  };\n  /************************************************************************/\n  /******/ // The module cache\n  /******/\n  var __webpack_module_cache__ = {};\n  /******/\n  /******/ // The require function\n  /******/\n  function __webpack_require__(moduleId) {\n    /******/ // Check if module is in cache\n    /******/var cachedModule = __webpack_module_cache__[moduleId];\n    /******/\n    if (cachedModule !== undefined) {\n      /******/return cachedModule.exports;\n      /******/\n    }\n    /******/ // Create a new module (and put it into the cache)\n    /******/\n    var module = __webpack_module_cache__[moduleId] = {\n      /******/ // no module.id needed\n      /******/ // no module.loaded needed\n      /******/exports: {}\n      /******/\n    };\n    /******/\n    /******/ // Execute the module function\n    /******/\n    __webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n    /******/\n    /******/ // Return the exports of the module\n    /******/\n    return module.exports;\n    /******/\n  }\n  /******/\n  /************************************************************************/\n  /******/ /* webpack/runtime/define property getters */\n  /******/\n  !function () {\n    /******/ // define getter functions for harmony exports\n    /******/__webpack_require__.d = function (exports, definition) {\n      /******/for (var key in definition) {\n        /******/if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n          /******/Object.defineProperty(exports, key, {\n            enumerable: true,\n            get: definition[key]\n          });\n          /******/\n        }\n        /******/\n      }\n      /******/\n    };\n    /******/\n  }();\n  /******/\n  /******/ /* webpack/runtime/hasOwnProperty shorthand */\n  /******/\n  !function () {\n    /******/__webpack_require__.o = function (obj, prop) {\n      return Object.prototype.hasOwnProperty.call(obj, prop);\n    };\n    /******/\n  }();\n  /******/\n  /******/ /* webpack/runtime/make namespace object */\n  /******/\n  !function () {\n    /******/ // define __esModule on exports\n    /******/__webpack_require__.r = function (exports) {\n      /******/if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n        /******/Object.defineProperty(exports, Symbol.toStringTag, {\n          value: 'Module'\n        });\n        /******/\n      }\n      /******/\n      Object.defineProperty(exports, '__esModule', {\n        value: true\n      });\n      /******/\n    };\n    /******/\n  }();\n  /******/\n  /************************************************************************/\n  var __webpack_exports__ = {};\n  // This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n  !function () {\n    /*!********************************************!*\\\n      !*** ./client-src/modules/logger/index.js ***!\n      \\********************************************/\n    __webpack_require__.r(__webpack_exports__);\n    /* harmony export */\n    __webpack_require__.d(__webpack_exports__, {\n      /* harmony export */\"default\": function () {\n        return (/* reexport default export from named module */webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__\n        );\n      }\n      /* harmony export */\n    });\n    /* harmony import */\n    var webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__( /*! webpack/lib/logging/runtime.js */\"./node_modules/webpack/lib/logging/runtime.js\");\n  }();\n  var __webpack_export_target__ = exports;\n  for (var i in __webpack_exports__) __webpack_export_target__[i] = __webpack_exports__[i];\n  if (__webpack_exports__.__esModule) Object.defineProperty(__webpack_export_target__, \"__esModule\", {\n    value: true\n  });\n  /******/\n})();", "map": {"version": 3, "names": ["__webpack_modules__", "./client-src/modules/logger/SyncBailHookFake.js", "module", "exports", "clientTapableSyncBailHook", "call", "./node_modules/webpack/lib/logging/Logger.js", "__unused_webpack_module", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "Array", "from", "test", "iter", "Symbol", "i", "iterator", "isArray", "len", "length", "arr2", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "LogType", "freeze", "error", "warn", "info", "log", "debug", "trace", "group", "groupCollapsed", "groupEnd", "profile", "profileEnd", "time", "clear", "status", "LOG_SYMBOL", "TIMERS_SYMBOL", "TIMERS_AGGREGATES_SYMBOL", "WebpackLogger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "_len", "arguments", "args", "_key", "_len2", "_key2", "_len3", "_key3", "_len4", "_key4", "_len5", "_key5", "assert", "assertion", "_len6", "_key6", "_len7", "_key7", "_len8", "_key8", "_len9", "_key9", "_len10", "_key10", "label", "Map", "set", "process", "hrtime", "timeLog", "prev", "get", "Error", "concat", "timeEnd", "delete", "timeAggregate", "current", "timeAggregateEnd", "<PERSON><PERSON>", "./node_modules/webpack/lib/logging/createConsoleLogger.js", "__unused_webpack_exports", "__webpack_require__", "_require", "filterToFunction", "item", "regExp", "RegExp", "replace", "ident", "LogLevel", "none", "false", "true", "verbose", "_ref", "_ref$level", "level", "_ref$debug", "console", "debugFilters", "map", "loglevel", "logger", "type", "labeledArgs", "some", "f", "apply", "ms", "msg", "logTime", "./node_modules/webpack/lib/logging/runtime.js", "_extends", "assign", "bind", "source", "hasOwnProperty", "SyncBailHook", "createConsoleLogger", "currentDefaultLoggerOptions", "current<PERSON>efault<PERSON>og<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "hooks", "<PERSON><PERSON><PERSON>", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "__webpack_module_cache__", "moduleId", "cachedModule", "d", "definition", "obj", "prop", "r", "toStringTag", "__webpack_exports__", "default", "webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__", "__webpack_export_target__", "__esModule"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/webpack-dev-server@4.15.1_webpack@5.94.0/node_modules/webpack-dev-server/client/modules/logger/index.js"], "sourcesContent": ["/******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ \"./client-src/modules/logger/SyncBailHookFake.js\":\n/*!*******************************************************!*\\\n  !*** ./client-src/modules/logger/SyncBailHookFake.js ***!\n  \\*******************************************************/\n/***/ (function(module) {\n\n\n\n/**\n * Client stub for tapable SyncBailHook\n */\nmodule.exports = function clientTapableSyncBailHook() {\n  return {\n    call: function call() {}\n  };\n};\n\n/***/ }),\n\n/***/ \"./node_modules/webpack/lib/logging/Logger.js\":\n/*!****************************************************!*\\\n  !*** ./node_modules/webpack/lib/logging/Logger.js ***!\n  \\****************************************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n\n\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; }) !== \"undefined\" && iter[(typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; }).iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[(typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; }).toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nvar LogType = Object.freeze({\n  error: /** @type {\"error\"} */\"error\",\n  // message, c style arguments\n  warn: /** @type {\"warn\"} */\"warn\",\n  // message, c style arguments\n  info: /** @type {\"info\"} */\"info\",\n  // message, c style arguments\n  log: /** @type {\"log\"} */\"log\",\n  // message, c style arguments\n  debug: /** @type {\"debug\"} */\"debug\",\n  // message, c style arguments\n\n  trace: /** @type {\"trace\"} */\"trace\",\n  // no arguments\n\n  group: /** @type {\"group\"} */\"group\",\n  // [label]\n  groupCollapsed: /** @type {\"groupCollapsed\"} */\"groupCollapsed\",\n  // [label]\n  groupEnd: /** @type {\"groupEnd\"} */\"groupEnd\",\n  // [label]\n\n  profile: /** @type {\"profile\"} */\"profile\",\n  // [profileName]\n  profileEnd: /** @type {\"profileEnd\"} */\"profileEnd\",\n  // [profileName]\n\n  time: /** @type {\"time\"} */\"time\",\n  // name, time as [seconds, nanoseconds]\n\n  clear: /** @type {\"clear\"} */\"clear\",\n  // no arguments\n  status: /** @type {\"status\"} */\"status\" // message, arguments\n});\n\nexports.LogType = LogType;\n\n/** @typedef {typeof LogType[keyof typeof LogType]} LogTypeEnum */\n\nvar LOG_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; })(\"webpack logger raw log method\");\nvar TIMERS_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; })(\"webpack logger times\");\nvar TIMERS_AGGREGATES_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; })(\"webpack logger aggregated times\");\nvar WebpackLogger = /*#__PURE__*/function () {\n  /**\n   * @param {function(LogTypeEnum, any[]=): void} log log function\n   * @param {function(string | function(): string): WebpackLogger} getChildLogger function to create child logger\n   */\n  function WebpackLogger(log, getChildLogger) {\n    _classCallCheck(this, WebpackLogger);\n    this[LOG_SYMBOL] = log;\n    this.getChildLogger = getChildLogger;\n  }\n  _createClass(WebpackLogger, [{\n    key: \"error\",\n    value: function error() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      this[LOG_SYMBOL](LogType.error, args);\n    }\n  }, {\n    key: \"warn\",\n    value: function warn() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      this[LOG_SYMBOL](LogType.warn, args);\n    }\n  }, {\n    key: \"info\",\n    value: function info() {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      this[LOG_SYMBOL](LogType.info, args);\n    }\n  }, {\n    key: \"log\",\n    value: function log() {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      this[LOG_SYMBOL](LogType.log, args);\n    }\n  }, {\n    key: \"debug\",\n    value: function debug() {\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        args[_key5] = arguments[_key5];\n      }\n      this[LOG_SYMBOL](LogType.debug, args);\n    }\n  }, {\n    key: \"assert\",\n    value: function assert(assertion) {\n      if (!assertion) {\n        for (var _len6 = arguments.length, args = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++) {\n          args[_key6 - 1] = arguments[_key6];\n        }\n        this[LOG_SYMBOL](LogType.error, args);\n      }\n    }\n  }, {\n    key: \"trace\",\n    value: function trace() {\n      this[LOG_SYMBOL](LogType.trace, [\"Trace\"]);\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this[LOG_SYMBOL](LogType.clear);\n    }\n  }, {\n    key: \"status\",\n    value: function status() {\n      for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        args[_key7] = arguments[_key7];\n      }\n      this[LOG_SYMBOL](LogType.status, args);\n    }\n  }, {\n    key: \"group\",\n    value: function group() {\n      for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n        args[_key8] = arguments[_key8];\n      }\n      this[LOG_SYMBOL](LogType.group, args);\n    }\n  }, {\n    key: \"groupCollapsed\",\n    value: function groupCollapsed() {\n      for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n        args[_key9] = arguments[_key9];\n      }\n      this[LOG_SYMBOL](LogType.groupCollapsed, args);\n    }\n  }, {\n    key: \"groupEnd\",\n    value: function groupEnd() {\n      for (var _len10 = arguments.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n        args[_key10] = arguments[_key10];\n      }\n      this[LOG_SYMBOL](LogType.groupEnd, args);\n    }\n  }, {\n    key: \"profile\",\n    value: function profile(label) {\n      this[LOG_SYMBOL](LogType.profile, [label]);\n    }\n  }, {\n    key: \"profileEnd\",\n    value: function profileEnd(label) {\n      this[LOG_SYMBOL](LogType.profileEnd, [label]);\n    }\n  }, {\n    key: \"time\",\n    value: function time(label) {\n      this[TIMERS_SYMBOL] = this[TIMERS_SYMBOL] || new Map();\n      this[TIMERS_SYMBOL].set(label, process.hrtime());\n    }\n  }, {\n    key: \"timeLog\",\n    value: function timeLog(label) {\n      var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n      if (!prev) {\n        throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeLog()\"));\n      }\n      var time = process.hrtime(prev);\n      this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n    }\n  }, {\n    key: \"timeEnd\",\n    value: function timeEnd(label) {\n      var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n      if (!prev) {\n        throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeEnd()\"));\n      }\n      var time = process.hrtime(prev);\n      this[TIMERS_SYMBOL].delete(label);\n      this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n    }\n  }, {\n    key: \"timeAggregate\",\n    value: function timeAggregate(label) {\n      var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n      if (!prev) {\n        throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeAggregate()\"));\n      }\n      var time = process.hrtime(prev);\n      this[TIMERS_SYMBOL].delete(label);\n      this[TIMERS_AGGREGATES_SYMBOL] = this[TIMERS_AGGREGATES_SYMBOL] || new Map();\n      var current = this[TIMERS_AGGREGATES_SYMBOL].get(label);\n      if (current !== undefined) {\n        if (time[1] + current[1] > 1e9) {\n          time[0] += current[0] + 1;\n          time[1] = time[1] - 1e9 + current[1];\n        } else {\n          time[0] += current[0];\n          time[1] += current[1];\n        }\n      }\n      this[TIMERS_AGGREGATES_SYMBOL].set(label, time);\n    }\n  }, {\n    key: \"timeAggregateEnd\",\n    value: function timeAggregateEnd(label) {\n      if (this[TIMERS_AGGREGATES_SYMBOL] === undefined) return;\n      var time = this[TIMERS_AGGREGATES_SYMBOL].get(label);\n      if (time === undefined) return;\n      this[TIMERS_AGGREGATES_SYMBOL].delete(label);\n      this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n    }\n  }]);\n  return WebpackLogger;\n}();\nexports.Logger = WebpackLogger;\n\n/***/ }),\n\n/***/ \"./node_modules/webpack/lib/logging/createConsoleLogger.js\":\n/*!*****************************************************************!*\\\n  !*** ./node_modules/webpack/lib/logging/createConsoleLogger.js ***!\n  \\*****************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n\n\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; }) !== \"undefined\" && iter[(typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; }).iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nvar _require = __webpack_require__(/*! ./Logger */ \"./node_modules/webpack/lib/logging/Logger.js\"),\n  LogType = _require.LogType;\n\n/** @typedef {import(\"../../declarations/WebpackOptions\").FilterItemTypes} FilterItemTypes */\n/** @typedef {import(\"../../declarations/WebpackOptions\").FilterTypes} FilterTypes */\n/** @typedef {import(\"./Logger\").LogTypeEnum} LogTypeEnum */\n\n/** @typedef {function(string): boolean} FilterFunction */\n\n/**\n * @typedef {Object} LoggerConsole\n * @property {function(): void} clear\n * @property {function(): void} trace\n * @property {(...args: any[]) => void} info\n * @property {(...args: any[]) => void} log\n * @property {(...args: any[]) => void} warn\n * @property {(...args: any[]) => void} error\n * @property {(...args: any[]) => void=} debug\n * @property {(...args: any[]) => void=} group\n * @property {(...args: any[]) => void=} groupCollapsed\n * @property {(...args: any[]) => void=} groupEnd\n * @property {(...args: any[]) => void=} status\n * @property {(...args: any[]) => void=} profile\n * @property {(...args: any[]) => void=} profileEnd\n * @property {(...args: any[]) => void=} logTime\n */\n\n/**\n * @typedef {Object} LoggerOptions\n * @property {false|true|\"none\"|\"error\"|\"warn\"|\"info\"|\"log\"|\"verbose\"} level loglevel\n * @property {FilterTypes|boolean} debug filter for debug logging\n * @property {LoggerConsole} console the console to log to\n */\n\n/**\n * @param {FilterItemTypes} item an input item\n * @returns {FilterFunction} filter function\n */\nvar filterToFunction = function filterToFunction(item) {\n  if (typeof item === \"string\") {\n    var regExp = new RegExp(\"[\\\\\\\\/]\".concat(item.replace(\n    // eslint-disable-next-line no-useless-escape\n    /[-[\\]{}()*+?.\\\\^$|]/g, \"\\\\$&\"), \"([\\\\\\\\/]|$|!|\\\\?)\"));\n    return function (ident) {\n      return regExp.test(ident);\n    };\n  }\n  if (item && typeof item === \"object\" && typeof item.test === \"function\") {\n    return function (ident) {\n      return item.test(ident);\n    };\n  }\n  if (typeof item === \"function\") {\n    return item;\n  }\n  if (typeof item === \"boolean\") {\n    return function () {\n      return item;\n    };\n  }\n};\n\n/**\n * @enum {number}\n */\nvar LogLevel = {\n  none: 6,\n  false: 6,\n  error: 5,\n  warn: 4,\n  info: 3,\n  log: 2,\n  true: 2,\n  verbose: 1\n};\n\n/**\n * @param {LoggerOptions} options options object\n * @returns {function(string, LogTypeEnum, any[]): void} logging function\n */\nmodule.exports = function (_ref) {\n  var _ref$level = _ref.level,\n    level = _ref$level === void 0 ? \"info\" : _ref$level,\n    _ref$debug = _ref.debug,\n    debug = _ref$debug === void 0 ? false : _ref$debug,\n    console = _ref.console;\n  var debugFilters = typeof debug === \"boolean\" ? [function () {\n    return debug;\n  }] : /** @type {FilterItemTypes[]} */[].concat(debug).map(filterToFunction);\n  /** @type {number} */\n  var loglevel = LogLevel[\"\".concat(level)] || 0;\n\n  /**\n   * @param {string} name name of the logger\n   * @param {LogTypeEnum} type type of the log entry\n   * @param {any[]} args arguments of the log entry\n   * @returns {void}\n   */\n  var logger = function logger(name, type, args) {\n    var labeledArgs = function labeledArgs() {\n      if (Array.isArray(args)) {\n        if (args.length > 0 && typeof args[0] === \"string\") {\n          return [\"[\".concat(name, \"] \").concat(args[0])].concat(_toConsumableArray(args.slice(1)));\n        } else {\n          return [\"[\".concat(name, \"]\")].concat(_toConsumableArray(args));\n        }\n      } else {\n        return [];\n      }\n    };\n    var debug = debugFilters.some(function (f) {\n      return f(name);\n    });\n    switch (type) {\n      case LogType.debug:\n        if (!debug) return;\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        if (typeof console.debug === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.debug.apply(console, _toConsumableArray(labeledArgs()));\n        } else {\n          console.log.apply(console, _toConsumableArray(labeledArgs()));\n        }\n        break;\n      case LogType.log:\n        if (!debug && loglevel > LogLevel.log) return;\n        console.log.apply(console, _toConsumableArray(labeledArgs()));\n        break;\n      case LogType.info:\n        if (!debug && loglevel > LogLevel.info) return;\n        console.info.apply(console, _toConsumableArray(labeledArgs()));\n        break;\n      case LogType.warn:\n        if (!debug && loglevel > LogLevel.warn) return;\n        console.warn.apply(console, _toConsumableArray(labeledArgs()));\n        break;\n      case LogType.error:\n        if (!debug && loglevel > LogLevel.error) return;\n        console.error.apply(console, _toConsumableArray(labeledArgs()));\n        break;\n      case LogType.trace:\n        if (!debug) return;\n        console.trace();\n        break;\n      case LogType.groupCollapsed:\n        if (!debug && loglevel > LogLevel.log) return;\n        if (!debug && loglevel > LogLevel.verbose) {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          if (typeof console.groupCollapsed === \"function\") {\n            // eslint-disable-next-line node/no-unsupported-features/node-builtins\n            console.groupCollapsed.apply(console, _toConsumableArray(labeledArgs()));\n          } else {\n            console.log.apply(console, _toConsumableArray(labeledArgs()));\n          }\n          break;\n        }\n      // falls through\n      case LogType.group:\n        if (!debug && loglevel > LogLevel.log) return;\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        if (typeof console.group === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.group.apply(console, _toConsumableArray(labeledArgs()));\n        } else {\n          console.log.apply(console, _toConsumableArray(labeledArgs()));\n        }\n        break;\n      case LogType.groupEnd:\n        if (!debug && loglevel > LogLevel.log) return;\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        if (typeof console.groupEnd === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.groupEnd();\n        }\n        break;\n      case LogType.time:\n        {\n          if (!debug && loglevel > LogLevel.log) return;\n          var ms = args[1] * 1000 + args[2] / 1000000;\n          var msg = \"[\".concat(name, \"] \").concat(args[0], \": \").concat(ms, \" ms\");\n          if (typeof console.logTime === \"function\") {\n            console.logTime(msg);\n          } else {\n            console.log(msg);\n          }\n          break;\n        }\n      case LogType.profile:\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        if (typeof console.profile === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.profile.apply(console, _toConsumableArray(labeledArgs()));\n        }\n        break;\n      case LogType.profileEnd:\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        if (typeof console.profileEnd === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.profileEnd.apply(console, _toConsumableArray(labeledArgs()));\n        }\n        break;\n      case LogType.clear:\n        if (!debug && loglevel > LogLevel.log) return;\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        if (typeof console.clear === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.clear();\n        }\n        break;\n      case LogType.status:\n        if (!debug && loglevel > LogLevel.info) return;\n        if (typeof console.status === \"function\") {\n          if (args.length === 0) {\n            console.status();\n          } else {\n            console.status.apply(console, _toConsumableArray(labeledArgs()));\n          }\n        } else {\n          if (args.length !== 0) {\n            console.info.apply(console, _toConsumableArray(labeledArgs()));\n          }\n        }\n        break;\n      default:\n        throw new Error(\"Unexpected LogType \".concat(type));\n    }\n  };\n  return logger;\n};\n\n/***/ }),\n\n/***/ \"./node_modules/webpack/lib/logging/runtime.js\":\n/*!*****************************************************!*\\\n  !*** ./node_modules/webpack/lib/logging/runtime.js ***!\n  \\*****************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n\n\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nvar SyncBailHook = __webpack_require__(/*! tapable/lib/SyncBailHook */ \"./client-src/modules/logger/SyncBailHookFake.js\");\nvar _require = __webpack_require__(/*! ./Logger */ \"./node_modules/webpack/lib/logging/Logger.js\"),\n  Logger = _require.Logger;\nvar createConsoleLogger = __webpack_require__(/*! ./createConsoleLogger */ \"./node_modules/webpack/lib/logging/createConsoleLogger.js\");\n\n/** @type {createConsoleLogger.LoggerOptions} */\nvar currentDefaultLoggerOptions = {\n  level: \"info\",\n  debug: false,\n  console: console\n};\nvar currentDefaultLogger = createConsoleLogger(currentDefaultLoggerOptions);\n\n/**\n * @param {string} name name of the logger\n * @returns {Logger} a logger\n */\nexports.getLogger = function (name) {\n  return new Logger(function (type, args) {\n    if (exports.hooks.log.call(name, type, args) === undefined) {\n      currentDefaultLogger(name, type, args);\n    }\n  }, function (childName) {\n    return exports.getLogger(\"\".concat(name, \"/\").concat(childName));\n  });\n};\n\n/**\n * @param {createConsoleLogger.LoggerOptions} options new options, merge with old options\n * @returns {void}\n */\nexports.configureDefaultLogger = function (options) {\n  _extends(currentDefaultLoggerOptions, options);\n  currentDefaultLogger = createConsoleLogger(currentDefaultLoggerOptions);\n};\nexports.hooks = {\n  log: new SyncBailHook([\"origin\", \"type\", \"args\"])\n};\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/make namespace object */\n/******/ \t!function() {\n/******/ \t\t// define __esModule on exports\n/******/ \t\t__webpack_require__.r = function(exports) {\n/******/ \t\t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t\t}\n/******/ \t\t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n!function() {\n/*!********************************************!*\\\n  !*** ./client-src/modules/logger/index.js ***!\n  \\********************************************/\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default export from named module */ webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__; }\n/* harmony export */ });\n/* harmony import */ var webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! webpack/lib/logging/runtime.js */ \"./node_modules/webpack/lib/logging/runtime.js\");\n\n}();\nvar __webpack_export_target__ = exports;\nfor(var i in __webpack_exports__) __webpack_export_target__[i] = __webpack_exports__[i];\nif(__webpack_exports__.__esModule) Object.defineProperty(__webpack_export_target__, \"__esModule\", { value: true });\n/******/ })()\n;"], "mappings": "AAAA,QAAS,CAAC,YAAW;EAAE;EACvB;EAAU,YAAY;;EACtB;EAAU,IAAIA,mBAAmB,GAAI;IAErC,KAAM,iDAAiD;IACvD;AACA;AACA;IACA;IAAO,SAAAC,CAASC,MAAM,EAAE;MAIxB;AACA;AACA;MACAA,MAAM,CAACC,OAAO,GAAG,SAASC,yBAAyBA,CAAA,EAAG;QACpD,OAAO;UACLC,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG,CAAC;QACzB,CAAC;MACH,CAAC;;MAED;IAAM,CAAE;;IAER,KAAM,8CAA8C;IACpD;AACA;AACA;IACA;IAAO,SAAAC,CAASC,uBAAuB,EAAEJ,OAAO,EAAE;MAElD;AACA;AACA;AACA;;MAIA,SAASK,kBAAkBA,CAACC,GAAG,EAAE;QAC/B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;MACrH;MACA,SAASA,kBAAkBA,CAAA,EAAG;QAC5B,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;MAC7J;MACA,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;QAC9C,IAAI,CAACD,CAAC,EAAE;QACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;QAC9D,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACU,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,IAAIJ,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACQ,WAAW,EAAEL,CAAC,GAAGH,CAAC,CAACQ,WAAW,CAACC,IAAI;QAC3D,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOO,KAAK,CAACC,IAAI,CAACX,CAAC,CAAC;QACpD,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACS,IAAI,CAACT,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;MAClH;MACA,SAASL,gBAAgBA,CAACiB,IAAI,EAAE;QAC9B,IAAI,QAAQ,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,UAAUC,CAAC,EAAE;UAAE,OAAOA,CAAC;QAAE,CAAC,CAAC,KAAK,WAAW,IAAIF,IAAI,CAAC,CAAC,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,UAAUC,CAAC,EAAE;UAAE,OAAOA,CAAC;QAAE,CAAC,EAAEC,QAAQ,CAAC,IAAI,IAAI,IAAIH,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;MACzP;MACA,SAASlB,kBAAkBA,CAACD,GAAG,EAAE;QAC/B,IAAIgB,KAAK,CAACO,OAAO,CAACvB,GAAG,CAAC,EAAE,OAAOQ,iBAAiB,CAACR,GAAG,CAAC;MACvD;MACA,SAASQ,iBAAiBA,CAACR,GAAG,EAAEwB,GAAG,EAAE;QACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGxB,GAAG,CAACyB,MAAM,EAAED,GAAG,GAAGxB,GAAG,CAACyB,MAAM;QACrD,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEK,IAAI,GAAG,IAAIV,KAAK,CAACQ,GAAG,CAAC,EAAEH,CAAC,GAAGG,GAAG,EAAEH,CAAC,EAAE,EAAEK,IAAI,CAACL,CAAC,CAAC,GAAGrB,GAAG,CAACqB,CAAC,CAAC;QACrE,OAAOK,IAAI;MACb;MACA,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;QAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;UACtC,MAAM,IAAIxB,SAAS,CAAC,mCAAmC,CAAC;QAC1D;MACF;MACA,SAASyB,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;QACxC,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,KAAK,CAACP,MAAM,EAAEJ,CAAC,EAAE,EAAE;UACrC,IAAIY,UAAU,GAAGD,KAAK,CAACX,CAAC,CAAC;UACzBY,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;UACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;UAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;UACrD1B,MAAM,CAAC2B,cAAc,CAACN,MAAM,EAAEO,cAAc,CAACL,UAAU,CAACM,GAAG,CAAC,EAAEN,UAAU,CAAC;QAC3E;MACF;MACA,SAASO,YAAYA,CAACX,WAAW,EAAEY,UAAU,EAAEC,WAAW,EAAE;QAC1D,IAAID,UAAU,EAAEX,iBAAiB,CAACD,WAAW,CAAClB,SAAS,EAAE8B,UAAU,CAAC;QACpE,IAAIC,WAAW,EAAEZ,iBAAiB,CAACD,WAAW,EAAEa,WAAW,CAAC;QAC5DhC,MAAM,CAAC2B,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;UAC9CO,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF,OAAOP,WAAW;MACpB;MACA,SAASS,cAAcA,CAACK,GAAG,EAAE;QAC3B,IAAIJ,GAAG,GAAGK,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;QACrC,OAAO,OAAOJ,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGM,MAAM,CAACN,GAAG,CAAC;MACpD;MACA,SAASK,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;QACjC,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;QAC7D,IAAIE,IAAI,GAAGF,KAAK,CAAC,CAAC,OAAO1B,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,UAAUC,CAAC,EAAE;UAAE,OAAOA,CAAC;QAAE,CAAC,EAAE4B,WAAW,CAAC;QACnG,IAAID,IAAI,KAAKE,SAAS,EAAE;UACtB,IAAIC,GAAG,GAAGH,IAAI,CAACpD,IAAI,CAACkD,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;UAC7C,IAAI,OAAOI,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;UACvC,MAAM,IAAI9C,SAAS,CAAC,8CAA8C,CAAC;QACrE;QACA,OAAO,CAAC0C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;MACrD;MACA,IAAIO,OAAO,GAAG3C,MAAM,CAAC4C,MAAM,CAAC;QAC1BC,KAAK,EAAE,sBAAsB,OAAO;QACpC;QACAC,IAAI,EAAE,qBAAqB,MAAM;QACjC;QACAC,IAAI,EAAE,qBAAqB,MAAM;QACjC;QACAC,GAAG,EAAE,oBAAoB,KAAK;QAC9B;QACAC,KAAK,EAAE,sBAAsB,OAAO;QACpC;;QAEAC,KAAK,EAAE,sBAAsB,OAAO;QACpC;;QAEAC,KAAK,EAAE,sBAAsB,OAAO;QACpC;QACAC,cAAc,EAAE,+BAA+B,gBAAgB;QAC/D;QACAC,QAAQ,EAAE,yBAAyB,UAAU;QAC7C;;QAEAC,OAAO,EAAE,wBAAwB,SAAS;QAC1C;QACAC,UAAU,EAAE,2BAA2B,YAAY;QACnD;;QAEAC,IAAI,EAAE,qBAAqB,MAAM;QACjC;;QAEAC,KAAK,EAAE,sBAAsB,OAAO;QACpC;QACAC,MAAM,EAAE,uBAAuB,QAAQ,CAAC;MAC1C,CAAC,CAAC;;MAEF1E,OAAO,CAAC2D,OAAO,GAAGA,OAAO;;MAEzB;;MAEA,IAAIgB,UAAU,GAAG,CAAC,OAAOjD,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,UAAUC,CAAC,EAAE;QAAE,OAAOA,CAAC;MAAE,CAAC,EAAE,+BAA+B,CAAC;MACvH,IAAIiD,aAAa,GAAG,CAAC,OAAOlD,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,UAAUC,CAAC,EAAE;QAAE,OAAOA,CAAC;MAAE,CAAC,EAAE,sBAAsB,CAAC;MACjH,IAAIkD,wBAAwB,GAAG,CAAC,OAAOnD,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,UAAUC,CAAC,EAAE;QAAE,OAAOA,CAAC;MAAE,CAAC,EAAE,iCAAiC,CAAC;MACvI,IAAImD,aAAa,GAAG,aAAa,YAAY;QAC3C;AACF;AACA;AACA;QACE,SAASA,aAAaA,CAACd,GAAG,EAAEe,cAAc,EAAE;UAC1C9C,eAAe,CAAC,IAAI,EAAE6C,aAAa,CAAC;UACpC,IAAI,CAACH,UAAU,CAAC,GAAGX,GAAG;UACtB,IAAI,CAACe,cAAc,GAAGA,cAAc;QACtC;QACAjC,YAAY,CAACgC,aAAa,EAAE,CAAC;UAC3BjC,GAAG,EAAE,OAAO;UACZmC,KAAK,EAAE,SAASnB,KAAKA,CAAA,EAAG;YACtB,KAAK,IAAIoB,IAAI,GAAGC,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAAC2D,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;cACvFD,IAAI,CAACC,IAAI,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;YAC9B;YACA,IAAI,CAACT,UAAU,CAAC,CAAChB,OAAO,CAACE,KAAK,EAAEsB,IAAI,CAAC;UACvC;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,MAAM;UACXmC,KAAK,EAAE,SAASlB,IAAIA,CAAA,EAAG;YACrB,KAAK,IAAIuB,KAAK,GAAGH,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAAC+D,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;cAC7FH,IAAI,CAACG,KAAK,CAAC,GAAGJ,SAAS,CAACI,KAAK,CAAC;YAChC;YACA,IAAI,CAACX,UAAU,CAAC,CAAChB,OAAO,CAACG,IAAI,EAAEqB,IAAI,CAAC;UACtC;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,MAAM;UACXmC,KAAK,EAAE,SAASjB,IAAIA,CAAA,EAAG;YACrB,KAAK,IAAIwB,KAAK,GAAGL,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAACiE,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;cAC7FL,IAAI,CAACK,KAAK,CAAC,GAAGN,SAAS,CAACM,KAAK,CAAC;YAChC;YACA,IAAI,CAACb,UAAU,CAAC,CAAChB,OAAO,CAACI,IAAI,EAAEoB,IAAI,CAAC;UACtC;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,KAAK;UACVmC,KAAK,EAAE,SAAShB,GAAGA,CAAA,EAAG;YACpB,KAAK,IAAIyB,KAAK,GAAGP,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAACmE,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;cAC7FP,IAAI,CAACO,KAAK,CAAC,GAAGR,SAAS,CAACQ,KAAK,CAAC;YAChC;YACA,IAAI,CAACf,UAAU,CAAC,CAAChB,OAAO,CAACK,GAAG,EAAEmB,IAAI,CAAC;UACrC;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,OAAO;UACZmC,KAAK,EAAE,SAASf,KAAKA,CAAA,EAAG;YACtB,KAAK,IAAI0B,KAAK,GAAGT,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAACqE,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;cAC7FT,IAAI,CAACS,KAAK,CAAC,GAAGV,SAAS,CAACU,KAAK,CAAC;YAChC;YACA,IAAI,CAACjB,UAAU,CAAC,CAAChB,OAAO,CAACM,KAAK,EAAEkB,IAAI,CAAC;UACvC;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,QAAQ;UACbmC,KAAK,EAAE,SAASa,MAAMA,CAACC,SAAS,EAAE;YAChC,IAAI,CAACA,SAAS,EAAE;cACd,KAAK,IAAIC,KAAK,GAAGb,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAACyE,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;gBACjHb,IAAI,CAACa,KAAK,GAAG,CAAC,CAAC,GAAGd,SAAS,CAACc,KAAK,CAAC;cACpC;cACA,IAAI,CAACrB,UAAU,CAAC,CAAChB,OAAO,CAACE,KAAK,EAAEsB,IAAI,CAAC;YACvC;UACF;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,OAAO;UACZmC,KAAK,EAAE,SAASd,KAAKA,CAAA,EAAG;YACtB,IAAI,CAACS,UAAU,CAAC,CAAChB,OAAO,CAACO,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC;UAC5C;QACF,CAAC,EAAE;UACDrB,GAAG,EAAE,OAAO;UACZmC,KAAK,EAAE,SAASP,KAAKA,CAAA,EAAG;YACtB,IAAI,CAACE,UAAU,CAAC,CAAChB,OAAO,CAACc,KAAK,CAAC;UACjC;QACF,CAAC,EAAE;UACD5B,GAAG,EAAE,QAAQ;UACbmC,KAAK,EAAE,SAASN,MAAMA,CAAA,EAAG;YACvB,KAAK,IAAIuB,KAAK,GAAGf,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAAC2E,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;cAC7Ff,IAAI,CAACe,KAAK,CAAC,GAAGhB,SAAS,CAACgB,KAAK,CAAC;YAChC;YACA,IAAI,CAACvB,UAAU,CAAC,CAAChB,OAAO,CAACe,MAAM,EAAES,IAAI,CAAC;UACxC;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,OAAO;UACZmC,KAAK,EAAE,SAASb,KAAKA,CAAA,EAAG;YACtB,KAAK,IAAIgC,KAAK,GAAGjB,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAAC6E,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;cAC7FjB,IAAI,CAACiB,KAAK,CAAC,GAAGlB,SAAS,CAACkB,KAAK,CAAC;YAChC;YACA,IAAI,CAACzB,UAAU,CAAC,CAAChB,OAAO,CAACQ,KAAK,EAAEgB,IAAI,CAAC;UACvC;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,gBAAgB;UACrBmC,KAAK,EAAE,SAASZ,cAAcA,CAAA,EAAG;YAC/B,KAAK,IAAIiC,KAAK,GAAGnB,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAAC+E,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;cAC7FnB,IAAI,CAACmB,KAAK,CAAC,GAAGpB,SAAS,CAACoB,KAAK,CAAC;YAChC;YACA,IAAI,CAAC3B,UAAU,CAAC,CAAChB,OAAO,CAACS,cAAc,EAAEe,IAAI,CAAC;UAChD;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,UAAU;UACfmC,KAAK,EAAE,SAASX,QAAQA,CAAA,EAAG;YACzB,KAAK,IAAIkC,MAAM,GAAGrB,SAAS,CAACnD,MAAM,EAAEoD,IAAI,GAAG,IAAI7D,KAAK,CAACiF,MAAM,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGD,MAAM,EAAEC,MAAM,EAAE,EAAE;cACnGrB,IAAI,CAACqB,MAAM,CAAC,GAAGtB,SAAS,CAACsB,MAAM,CAAC;YAClC;YACA,IAAI,CAAC7B,UAAU,CAAC,CAAChB,OAAO,CAACU,QAAQ,EAAEc,IAAI,CAAC;UAC1C;QACF,CAAC,EAAE;UACDtC,GAAG,EAAE,SAAS;UACdmC,KAAK,EAAE,SAASV,OAAOA,CAACmC,KAAK,EAAE;YAC7B,IAAI,CAAC9B,UAAU,CAAC,CAAChB,OAAO,CAACW,OAAO,EAAE,CAACmC,KAAK,CAAC,CAAC;UAC5C;QACF,CAAC,EAAE;UACD5D,GAAG,EAAE,YAAY;UACjBmC,KAAK,EAAE,SAAST,UAAUA,CAACkC,KAAK,EAAE;YAChC,IAAI,CAAC9B,UAAU,CAAC,CAAChB,OAAO,CAACY,UAAU,EAAE,CAACkC,KAAK,CAAC,CAAC;UAC/C;QACF,CAAC,EAAE;UACD5D,GAAG,EAAE,MAAM;UACXmC,KAAK,EAAE,SAASR,IAAIA,CAACiC,KAAK,EAAE;YAC1B,IAAI,CAAC7B,aAAa,CAAC,GAAG,IAAI,CAACA,aAAa,CAAC,IAAI,IAAI8B,GAAG,CAAC,CAAC;YACtD,IAAI,CAAC9B,aAAa,CAAC,CAAC+B,GAAG,CAACF,KAAK,EAAEG,OAAO,CAACC,MAAM,CAAC,CAAC,CAAC;UAClD;QACF,CAAC,EAAE;UACDhE,GAAG,EAAE,SAAS;UACdmC,KAAK,EAAE,SAAS8B,OAAOA,CAACL,KAAK,EAAE;YAC7B,IAAIM,IAAI,GAAG,IAAI,CAACnC,aAAa,CAAC,IAAI,IAAI,CAACA,aAAa,CAAC,CAACoC,GAAG,CAACP,KAAK,CAAC;YAChE,IAAI,CAACM,IAAI,EAAE;cACT,MAAM,IAAIE,KAAK,CAAC,iBAAiB,CAACC,MAAM,CAACT,KAAK,EAAE,+BAA+B,CAAC,CAAC;YACnF;YACA,IAAIjC,IAAI,GAAGoC,OAAO,CAACC,MAAM,CAACE,IAAI,CAAC;YAC/B,IAAI,CAACpC,UAAU,CAAC,CAAChB,OAAO,CAACa,IAAI,EAAE,CAACiC,KAAK,CAAC,CAACS,MAAM,CAAC7G,kBAAkB,CAACmE,IAAI,CAAC,CAAC,CAAC;UAC1E;QACF,CAAC,EAAE;UACD3B,GAAG,EAAE,SAAS;UACdmC,KAAK,EAAE,SAASmC,OAAOA,CAACV,KAAK,EAAE;YAC7B,IAAIM,IAAI,GAAG,IAAI,CAACnC,aAAa,CAAC,IAAI,IAAI,CAACA,aAAa,CAAC,CAACoC,GAAG,CAACP,KAAK,CAAC;YAChE,IAAI,CAACM,IAAI,EAAE;cACT,MAAM,IAAIE,KAAK,CAAC,iBAAiB,CAACC,MAAM,CAACT,KAAK,EAAE,+BAA+B,CAAC,CAAC;YACnF;YACA,IAAIjC,IAAI,GAAGoC,OAAO,CAACC,MAAM,CAACE,IAAI,CAAC;YAC/B,IAAI,CAACnC,aAAa,CAAC,CAACwC,MAAM,CAACX,KAAK,CAAC;YACjC,IAAI,CAAC9B,UAAU,CAAC,CAAChB,OAAO,CAACa,IAAI,EAAE,CAACiC,KAAK,CAAC,CAACS,MAAM,CAAC7G,kBAAkB,CAACmE,IAAI,CAAC,CAAC,CAAC;UAC1E;QACF,CAAC,EAAE;UACD3B,GAAG,EAAE,eAAe;UACpBmC,KAAK,EAAE,SAASqC,aAAaA,CAACZ,KAAK,EAAE;YACnC,IAAIM,IAAI,GAAG,IAAI,CAACnC,aAAa,CAAC,IAAI,IAAI,CAACA,aAAa,CAAC,CAACoC,GAAG,CAACP,KAAK,CAAC;YAChE,IAAI,CAACM,IAAI,EAAE;cACT,MAAM,IAAIE,KAAK,CAAC,iBAAiB,CAACC,MAAM,CAACT,KAAK,EAAE,qCAAqC,CAAC,CAAC;YACzF;YACA,IAAIjC,IAAI,GAAGoC,OAAO,CAACC,MAAM,CAACE,IAAI,CAAC;YAC/B,IAAI,CAACnC,aAAa,CAAC,CAACwC,MAAM,CAACX,KAAK,CAAC;YACjC,IAAI,CAAC5B,wBAAwB,CAAC,GAAG,IAAI,CAACA,wBAAwB,CAAC,IAAI,IAAI6B,GAAG,CAAC,CAAC;YAC5E,IAAIY,OAAO,GAAG,IAAI,CAACzC,wBAAwB,CAAC,CAACmC,GAAG,CAACP,KAAK,CAAC;YACvD,IAAIa,OAAO,KAAK9D,SAAS,EAAE;cACzB,IAAIgB,IAAI,CAAC,CAAC,CAAC,GAAG8C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;gBAC9B9C,IAAI,CAAC,CAAC,CAAC,IAAI8C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;gBACzB9C,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG8C,OAAO,CAAC,CAAC,CAAC;cACtC,CAAC,MAAM;gBACL9C,IAAI,CAAC,CAAC,CAAC,IAAI8C,OAAO,CAAC,CAAC,CAAC;gBACrB9C,IAAI,CAAC,CAAC,CAAC,IAAI8C,OAAO,CAAC,CAAC,CAAC;cACvB;YACF;YACA,IAAI,CAACzC,wBAAwB,CAAC,CAAC8B,GAAG,CAACF,KAAK,EAAEjC,IAAI,CAAC;UACjD;QACF,CAAC,EAAE;UACD3B,GAAG,EAAE,kBAAkB;UACvBmC,KAAK,EAAE,SAASuC,gBAAgBA,CAACd,KAAK,EAAE;YACtC,IAAI,IAAI,CAAC5B,wBAAwB,CAAC,KAAKrB,SAAS,EAAE;YAClD,IAAIgB,IAAI,GAAG,IAAI,CAACK,wBAAwB,CAAC,CAACmC,GAAG,CAACP,KAAK,CAAC;YACpD,IAAIjC,IAAI,KAAKhB,SAAS,EAAE;YACxB,IAAI,CAACqB,wBAAwB,CAAC,CAACuC,MAAM,CAACX,KAAK,CAAC;YAC5C,IAAI,CAAC9B,UAAU,CAAC,CAAChB,OAAO,CAACa,IAAI,EAAE,CAACiC,KAAK,CAAC,CAACS,MAAM,CAAC7G,kBAAkB,CAACmE,IAAI,CAAC,CAAC,CAAC;UAC1E;QACF,CAAC,CAAC,CAAC;QACH,OAAOM,aAAa;MACtB,CAAC,CAAC,CAAC;MACH9E,OAAO,CAACwH,MAAM,GAAG1C,aAAa;;MAE9B;IAAM,CAAE;;IAER,KAAM,2DAA2D;IACjE;AACA;AACA;IACA;IAAO,SAAA2C,CAAS1H,MAAM,EAAE2H,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;AACA;AACA;AACA;;MAIA,SAAStH,kBAAkBA,CAACC,GAAG,EAAE;QAC/B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;MACrH;MACA,SAASA,kBAAkBA,CAAA,EAAG;QAC5B,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;MAC7J;MACA,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;QAC9C,IAAI,CAACD,CAAC,EAAE;QACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;QAC9D,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAChB,IAAI,CAACU,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,IAAIJ,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACQ,WAAW,EAAEL,CAAC,GAAGH,CAAC,CAACQ,WAAW,CAACC,IAAI;QAC3D,IAAIN,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOO,KAAK,CAACC,IAAI,CAACX,CAAC,CAAC;QACpD,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACS,IAAI,CAACT,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;MAClH;MACA,SAASL,gBAAgBA,CAACiB,IAAI,EAAE;QAC9B,IAAI,QAAQ,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,UAAUC,CAAC,EAAE;UAAE,OAAOA,CAAC;QAAE,CAAC,CAAC,KAAK,WAAW,IAAIF,IAAI,CAAC,CAAC,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,UAAUC,CAAC,EAAE;UAAE,OAAOA,CAAC;QAAE,CAAC,EAAEC,QAAQ,CAAC,IAAI,IAAI,IAAIH,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;MACzP;MACA,SAASlB,kBAAkBA,CAACD,GAAG,EAAE;QAC/B,IAAIgB,KAAK,CAACO,OAAO,CAACvB,GAAG,CAAC,EAAE,OAAOQ,iBAAiB,CAACR,GAAG,CAAC;MACvD;MACA,SAASQ,iBAAiBA,CAACR,GAAG,EAAEwB,GAAG,EAAE;QACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGxB,GAAG,CAACyB,MAAM,EAAED,GAAG,GAAGxB,GAAG,CAACyB,MAAM;QACrD,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEK,IAAI,GAAG,IAAIV,KAAK,CAACQ,GAAG,CAAC,EAAEH,CAAC,GAAGG,GAAG,EAAEH,CAAC,EAAE,EAAEK,IAAI,CAACL,CAAC,CAAC,GAAGrB,GAAG,CAACqB,CAAC,CAAC;QACrE,OAAOK,IAAI;MACb;MACA,IAAI4F,QAAQ,GAAGD,mBAAmB,EAAC,eAAgB,8CAA8C,CAAC;QAChGhE,OAAO,GAAGiE,QAAQ,CAACjE,OAAO;;MAE5B;MACA;MACA;;MAEA;;MAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEA;AACA;AACA;AACA;AACA;AACA;;MAEA;AACA;AACA;AACA;MACA,IAAIkE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;QACrD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC5B,IAAIC,MAAM,GAAG,IAAIC,MAAM,CAAC,SAAS,CAACd,MAAM,CAACY,IAAI,CAACG,OAAO;UACrD;UACA,sBAAsB,EAAE,MAAM,CAAC,EAAE,mBAAmB,CAAC,CAAC;UACtD,OAAO,UAAUC,KAAK,EAAE;YACtB,OAAOH,MAAM,CAACvG,IAAI,CAAC0G,KAAK,CAAC;UAC3B,CAAC;QACH;QACA,IAAIJ,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,CAACtG,IAAI,KAAK,UAAU,EAAE;UACvE,OAAO,UAAU0G,KAAK,EAAE;YACtB,OAAOJ,IAAI,CAACtG,IAAI,CAAC0G,KAAK,CAAC;UACzB,CAAC;QACH;QACA,IAAI,OAAOJ,IAAI,KAAK,UAAU,EAAE;UAC9B,OAAOA,IAAI;QACb;QACA,IAAI,OAAOA,IAAI,KAAK,SAAS,EAAE;UAC7B,OAAO,YAAY;YACjB,OAAOA,IAAI;UACb,CAAC;QACH;MACF,CAAC;;MAED;AACA;AACA;MACA,IAAIK,QAAQ,GAAG;QACbC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRxE,KAAK,EAAE,CAAC;QACRC,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,CAAC;QACNsE,IAAI,EAAE,CAAC;QACPC,OAAO,EAAE;MACX,CAAC;;MAED;AACA;AACA;AACA;MACAxI,MAAM,CAACC,OAAO,GAAG,UAAUwI,IAAI,EAAE;QAC/B,IAAIC,UAAU,GAAGD,IAAI,CAACE,KAAK;UACzBA,KAAK,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,UAAU;UACnDE,UAAU,GAAGH,IAAI,CAACvE,KAAK;UACvBA,KAAK,GAAG0E,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,UAAU;UAClDC,OAAO,GAAGJ,IAAI,CAACI,OAAO;QACxB,IAAIC,YAAY,GAAG,OAAO5E,KAAK,KAAK,SAAS,GAAG,CAAC,YAAY;UAC3D,OAAOA,KAAK;QACd,CAAC,CAAC,GAAG,gCAAgC,EAAE,CAACiD,MAAM,CAACjD,KAAK,CAAC,CAAC6E,GAAG,CAACjB,gBAAgB,CAAC;QAC3E;QACA,IAAIkB,QAAQ,GAAGZ,QAAQ,CAAC,EAAE,CAACjB,MAAM,CAACwB,KAAK,CAAC,CAAC,IAAI,CAAC;;QAE9C;AACF;AACA;AACA;AACA;AACA;QACE,IAAIM,MAAM,GAAG,SAASA,MAAMA,CAAC3H,IAAI,EAAE4H,IAAI,EAAE9D,IAAI,EAAE;UAC7C,IAAI+D,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;YACvC,IAAI5H,KAAK,CAACO,OAAO,CAACsD,IAAI,CAAC,EAAE;cACvB,IAAIA,IAAI,CAACpD,MAAM,GAAG,CAAC,IAAI,OAAOoD,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;gBAClD,OAAO,CAAC,GAAG,CAAC+B,MAAM,CAAC7F,IAAI,EAAE,IAAI,CAAC,CAAC6F,MAAM,CAAC/B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC+B,MAAM,CAAC7G,kBAAkB,CAAC8E,IAAI,CAAChE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3F,CAAC,MAAM;gBACL,OAAO,CAAC,GAAG,CAAC+F,MAAM,CAAC7F,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC6F,MAAM,CAAC7G,kBAAkB,CAAC8E,IAAI,CAAC,CAAC;cACjE;YACF,CAAC,MAAM;cACL,OAAO,EAAE;YACX;UACF,CAAC;UACD,IAAIlB,KAAK,GAAG4E,YAAY,CAACM,IAAI,CAAC,UAAUC,CAAC,EAAE;YACzC,OAAOA,CAAC,CAAC/H,IAAI,CAAC;UAChB,CAAC,CAAC;UACF,QAAQ4H,IAAI;YACV,KAAKtF,OAAO,CAACM,KAAK;cAChB,IAAI,CAACA,KAAK,EAAE;cACZ;cACA,IAAI,OAAO2E,OAAO,CAAC3E,KAAK,KAAK,UAAU,EAAE;gBACvC;gBACA2E,OAAO,CAAC3E,KAAK,CAACoF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cACjE,CAAC,MAAM;gBACLN,OAAO,CAAC5E,GAAG,CAACqF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cAC/D;cACA;YACF,KAAKvF,OAAO,CAACK,GAAG;cACd,IAAI,CAACC,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAG,EAAE;cACvC4E,OAAO,CAAC5E,GAAG,CAACqF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cAC7D;YACF,KAAKvF,OAAO,CAACI,IAAI;cACf,IAAI,CAACE,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACpE,IAAI,EAAE;cACxC6E,OAAO,CAAC7E,IAAI,CAACsF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cAC9D;YACF,KAAKvF,OAAO,CAACG,IAAI;cACf,IAAI,CAACG,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACrE,IAAI,EAAE;cACxC8E,OAAO,CAAC9E,IAAI,CAACuF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cAC9D;YACF,KAAKvF,OAAO,CAACE,KAAK;cAChB,IAAI,CAACI,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACtE,KAAK,EAAE;cACzC+E,OAAO,CAAC/E,KAAK,CAACwF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cAC/D;YACF,KAAKvF,OAAO,CAACO,KAAK;cAChB,IAAI,CAACD,KAAK,EAAE;cACZ2E,OAAO,CAAC1E,KAAK,CAAC,CAAC;cACf;YACF,KAAKP,OAAO,CAACS,cAAc;cACzB,IAAI,CAACH,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAG,EAAE;cACvC,IAAI,CAACC,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACI,OAAO,EAAE;gBACzC;gBACA,IAAI,OAAOK,OAAO,CAACxE,cAAc,KAAK,UAAU,EAAE;kBAChD;kBACAwE,OAAO,CAACxE,cAAc,CAACiF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;gBAC1E,CAAC,MAAM;kBACLN,OAAO,CAAC5E,GAAG,CAACqF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;gBAC/D;gBACA;cACF;YACF;YACA,KAAKvF,OAAO,CAACQ,KAAK;cAChB,IAAI,CAACF,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAG,EAAE;cACvC;cACA,IAAI,OAAO4E,OAAO,CAACzE,KAAK,KAAK,UAAU,EAAE;gBACvC;gBACAyE,OAAO,CAACzE,KAAK,CAACkF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cACjE,CAAC,MAAM;gBACLN,OAAO,CAAC5E,GAAG,CAACqF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cAC/D;cACA;YACF,KAAKvF,OAAO,CAACU,QAAQ;cACnB,IAAI,CAACJ,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAG,EAAE;cACvC;cACA,IAAI,OAAO4E,OAAO,CAACvE,QAAQ,KAAK,UAAU,EAAE;gBAC1C;gBACAuE,OAAO,CAACvE,QAAQ,CAAC,CAAC;cACpB;cACA;YACF,KAAKV,OAAO,CAACa,IAAI;cACf;gBACE,IAAI,CAACP,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAG,EAAE;gBACvC,IAAIsF,EAAE,GAAGnE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO;gBAC3C,IAAIoE,GAAG,GAAG,GAAG,CAACrC,MAAM,CAAC7F,IAAI,EAAE,IAAI,CAAC,CAAC6F,MAAM,CAAC/B,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC+B,MAAM,CAACoC,EAAE,EAAE,KAAK,CAAC;gBACxE,IAAI,OAAOV,OAAO,CAACY,OAAO,KAAK,UAAU,EAAE;kBACzCZ,OAAO,CAACY,OAAO,CAACD,GAAG,CAAC;gBACtB,CAAC,MAAM;kBACLX,OAAO,CAAC5E,GAAG,CAACuF,GAAG,CAAC;gBAClB;gBACA;cACF;YACF,KAAK5F,OAAO,CAACW,OAAO;cAClB;cACA,IAAI,OAAOsE,OAAO,CAACtE,OAAO,KAAK,UAAU,EAAE;gBACzC;gBACAsE,OAAO,CAACtE,OAAO,CAAC+E,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cACnE;cACA;YACF,KAAKvF,OAAO,CAACY,UAAU;cACrB;cACA,IAAI,OAAOqE,OAAO,CAACrE,UAAU,KAAK,UAAU,EAAE;gBAC5C;gBACAqE,OAAO,CAACrE,UAAU,CAAC8E,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;cACtE;cACA;YACF,KAAKvF,OAAO,CAACc,KAAK;cAChB,IAAI,CAACR,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAG,EAAE;cACvC;cACA,IAAI,OAAO4E,OAAO,CAACnE,KAAK,KAAK,UAAU,EAAE;gBACvC;gBACAmE,OAAO,CAACnE,KAAK,CAAC,CAAC;cACjB;cACA;YACF,KAAKd,OAAO,CAACe,MAAM;cACjB,IAAI,CAACT,KAAK,IAAI8E,QAAQ,GAAGZ,QAAQ,CAACpE,IAAI,EAAE;cACxC,IAAI,OAAO6E,OAAO,CAAClE,MAAM,KAAK,UAAU,EAAE;gBACxC,IAAIS,IAAI,CAACpD,MAAM,KAAK,CAAC,EAAE;kBACrB6G,OAAO,CAAClE,MAAM,CAAC,CAAC;gBAClB,CAAC,MAAM;kBACLkE,OAAO,CAAClE,MAAM,CAAC2E,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;gBAClE;cACF,CAAC,MAAM;gBACL,IAAI/D,IAAI,CAACpD,MAAM,KAAK,CAAC,EAAE;kBACrB6G,OAAO,CAAC7E,IAAI,CAACsF,KAAK,CAACT,OAAO,EAAEvI,kBAAkB,CAAC6I,WAAW,CAAC,CAAC,CAAC,CAAC;gBAChE;cACF;cACA;YACF;cACE,MAAM,IAAIjC,KAAK,CAAC,qBAAqB,CAACC,MAAM,CAAC+B,IAAI,CAAC,CAAC;UACvD;QACF,CAAC;QACD,OAAOD,MAAM;MACf,CAAC;;MAED;IAAM,CAAE;;IAER,KAAM,+CAA+C;IACrD;AACA;AACA;IACA;IAAO,SAAAS,CAASrJ,uBAAuB,EAAEJ,OAAO,EAAE2H,mBAAmB,EAAE;MAEvE;AACA;AACA;AACA;;MAIA,SAAS+B,QAAQA,CAAA,EAAG;QAClBA,QAAQ,GAAG1I,MAAM,CAAC2I,MAAM,GAAG3I,MAAM,CAAC2I,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUvH,MAAM,EAAE;UAClE,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,SAAS,CAACnD,MAAM,EAAEJ,CAAC,EAAE,EAAE;YACzC,IAAIkI,MAAM,GAAG3E,SAAS,CAACvD,CAAC,CAAC;YACzB,KAAK,IAAIkB,GAAG,IAAIgH,MAAM,EAAE;cACtB,IAAI7I,MAAM,CAACC,SAAS,CAAC6I,cAAc,CAAC5J,IAAI,CAAC2J,MAAM,EAAEhH,GAAG,CAAC,EAAE;gBACrDR,MAAM,CAACQ,GAAG,CAAC,GAAGgH,MAAM,CAAChH,GAAG,CAAC;cAC3B;YACF;UACF;UACA,OAAOR,MAAM;QACf,CAAC;QACD,OAAOqH,QAAQ,CAACL,KAAK,CAAC,IAAI,EAAEnE,SAAS,CAAC;MACxC;MACA,IAAI6E,YAAY,GAAGpC,mBAAmB,EAAC,+BAAgC,iDAAiD,CAAC;MACzH,IAAIC,QAAQ,GAAGD,mBAAmB,EAAC,eAAgB,8CAA8C,CAAC;QAChGH,MAAM,GAAGI,QAAQ,CAACJ,MAAM;MAC1B,IAAIwC,mBAAmB,GAAGrC,mBAAmB,EAAC,4BAA6B,2DAA2D,CAAC;;MAEvI;MACA,IAAIsC,2BAA2B,GAAG;QAChCvB,KAAK,EAAE,MAAM;QACbzE,KAAK,EAAE,KAAK;QACZ2E,OAAO,EAAEA;MACX,CAAC;MACD,IAAIsB,oBAAoB,GAAGF,mBAAmB,CAACC,2BAA2B,CAAC;;MAE3E;AACA;AACA;AACA;MACAjK,OAAO,CAACmK,SAAS,GAAG,UAAU9I,IAAI,EAAE;QAClC,OAAO,IAAImG,MAAM,CAAC,UAAUyB,IAAI,EAAE9D,IAAI,EAAE;UACtC,IAAInF,OAAO,CAACoK,KAAK,CAACpG,GAAG,CAAC9D,IAAI,CAACmB,IAAI,EAAE4H,IAAI,EAAE9D,IAAI,CAAC,KAAK3B,SAAS,EAAE;YAC1D0G,oBAAoB,CAAC7I,IAAI,EAAE4H,IAAI,EAAE9D,IAAI,CAAC;UACxC;QACF,CAAC,EAAE,UAAUkF,SAAS,EAAE;UACtB,OAAOrK,OAAO,CAACmK,SAAS,CAAC,EAAE,CAACjD,MAAM,CAAC7F,IAAI,EAAE,GAAG,CAAC,CAAC6F,MAAM,CAACmD,SAAS,CAAC,CAAC;QAClE,CAAC,CAAC;MACJ,CAAC;;MAED;AACA;AACA;AACA;MACArK,OAAO,CAACsK,sBAAsB,GAAG,UAAUC,OAAO,EAAE;QAClDb,QAAQ,CAACO,2BAA2B,EAAEM,OAAO,CAAC;QAC9CL,oBAAoB,GAAGF,mBAAmB,CAACC,2BAA2B,CAAC;MACzE,CAAC;MACDjK,OAAO,CAACoK,KAAK,GAAG;QACdpG,GAAG,EAAE,IAAI+F,YAAY,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;MAClD,CAAC;;MAED;IAAM;;IAEN;EAAU,CAAE;EACZ;EACA,SAAU;EACV;EAAU,IAAIS,wBAAwB,GAAG,CAAC,CAAC;EAC3C;EACA,SAAU;EACV;EAAU,SAAS7C,mBAAmBA,CAAC8C,QAAQ,EAAE;IACjD,SAAW;IACX,QAAW,IAAIC,YAAY,GAAGF,wBAAwB,CAACC,QAAQ,CAAC;IAChE;IAAW,IAAIC,YAAY,KAAKlH,SAAS,EAAE;MAC3C,QAAY,OAAOkH,YAAY,CAAC1K,OAAO;MACvC;IAAW;IACX,SAAW;IACX;IAAW,IAAID,MAAM,GAAGyK,wBAAwB,CAACC,QAAQ,CAAC,GAAG;MAC7D,SAAY;MACZ,SAAY;MACZ,QAAYzK,OAAO,EAAE,CAAC;MACtB;IAAW,CAAC;IACZ;IACA,SAAW;IACX;IAAWH,mBAAmB,CAAC4K,QAAQ,CAAC,CAAC1K,MAAM,EAAEA,MAAM,CAACC,OAAO,EAAE2H,mBAAmB,CAAC;IACrF;IACA,SAAW;IACX;IAAW,OAAO5H,MAAM,CAACC,OAAO;IAChC;EAAU;EACV;EACA;EACA,SAAU;EACV;EAAU,CAAC,YAAW;IACtB,SAAW;IACX,QAAW2H,mBAAmB,CAACgD,CAAC,GAAG,UAAS3K,OAAO,EAAE4K,UAAU,EAAE;MACjE,QAAY,KAAI,IAAI/H,GAAG,IAAI+H,UAAU,EAAE;QACvC,QAAa,IAAGjD,mBAAmB,CAAC/G,CAAC,CAACgK,UAAU,EAAE/H,GAAG,CAAC,IAAI,CAAC8E,mBAAmB,CAAC/G,CAAC,CAACZ,OAAO,EAAE6C,GAAG,CAAC,EAAE;UAChG,QAAc7B,MAAM,CAAC2B,cAAc,CAAC3C,OAAO,EAAE6C,GAAG,EAAE;YAAEL,UAAU,EAAE,IAAI;YAAEwE,GAAG,EAAE4D,UAAU,CAAC/H,GAAG;UAAE,CAAC,CAAC;UAC7F;QAAa;QACb;MAAY;MACZ;IAAW,CAAC;IACZ;EAAU,CAAC,CAAC,CAAC;EACb;EACA,SAAU;EACV;EAAU,CAAC,YAAW;IACtB,QAAW8E,mBAAmB,CAAC/G,CAAC,GAAG,UAASiK,GAAG,EAAEC,IAAI,EAAE;MAAE,OAAO9J,MAAM,CAACC,SAAS,CAAC6I,cAAc,CAAC5J,IAAI,CAAC2K,GAAG,EAAEC,IAAI,CAAC;IAAE,CAAC;IAClH;EAAU,CAAC,CAAC,CAAC;EACb;EACA,SAAU;EACV;EAAU,CAAC,YAAW;IACtB,SAAW;IACX,QAAWnD,mBAAmB,CAACoD,CAAC,GAAG,UAAS/K,OAAO,EAAE;MACrD,QAAY,IAAG,OAAO0B,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACsJ,WAAW,EAAE;QACpE,QAAahK,MAAM,CAAC2B,cAAc,CAAC3C,OAAO,EAAE0B,MAAM,CAACsJ,WAAW,EAAE;UAAEhG,KAAK,EAAE;QAAS,CAAC,CAAC;QACpF;MAAY;MACZ;MAAYhE,MAAM,CAAC2B,cAAc,CAAC3C,OAAO,EAAE,YAAY,EAAE;QAAEgF,KAAK,EAAE;MAAK,CAAC,CAAC;MACzE;IAAW,CAAC;IACZ;EAAU,CAAC,CAAC,CAAC;EACb;EACA;EACA,IAAIiG,mBAAmB,GAAG,CAAC,CAAC;EAC5B;EACA,CAAC,YAAW;IACZ;AACA;AACA;IACAtD,mBAAmB,CAACoD,CAAC,CAACE,mBAAmB,CAAC;IAC1C;IAAqBtD,mBAAmB,CAACgD,CAAC,CAACM,mBAAmB,EAAE;MAChE,oBAAuB,SAAS,EAAE,SAAAC,CAAA,EAAW;QAAE,OAAO,gDAAgDC;QAA2D;MAAE;MACnK;IAAqB,CAAC,CAAC;IACvB;IAAqB,IAAIA,2DAA2D,GAAGxD,mBAAmB,EAAC,qCAAsC,+CAA+C,CAAC;EAEjM,CAAC,CAAC,CAAC;EACH,IAAIyD,yBAAyB,GAAGpL,OAAO;EACvC,KAAI,IAAI2B,CAAC,IAAIsJ,mBAAmB,EAAEG,yBAAyB,CAACzJ,CAAC,CAAC,GAAGsJ,mBAAmB,CAACtJ,CAAC,CAAC;EACvF,IAAGsJ,mBAAmB,CAACI,UAAU,EAAErK,MAAM,CAAC2B,cAAc,CAACyI,yBAAyB,EAAE,YAAY,EAAE;IAAEpG,KAAK,EAAE;EAAK,CAAC,CAAC;EAClH;AAAS,CAAC,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}