{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Main application module\n * Imports and configures all necessary modules for the application\n */\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { CoreModule } from './core/core.module';\nimport { SharedModule } from './shared/shared.module';\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent],\n  imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, AppRoutingModule, CoreModule, SharedModule],\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: ErrorInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "BrowserAnimationsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "AppRoutingModule", "AppComponent", "CoreModule", "SharedModule", "AuthInterceptor", "ErrorInterceptor", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\app.module.ts"], "sourcesContent": ["/**\n * Main application module\n * Imports and configures all necessary modules for the application\n */\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { CoreModule } from './core/core.module';\nimport { SharedModule } from './shared/shared.module';\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\n\n@NgModule({\n  declarations: [\n    AppComponent\n  ],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    HttpClientModule,\n    AppRoutingModule,\n    CoreModule,\n    SharedModule\n  ],\n  providers: [\n    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },\n    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAE1E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,gBAAgB,QAAQ,uCAAuC;AAoBjE,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EAlBrBZ,QAAQ,CAAC;EACRa,YAAY,EAAE,CACZP,YAAY,CACb;EACDQ,OAAO,EAAE,CACPb,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBE,gBAAgB,EAChBE,UAAU,EACVC,YAAY,CACb;EACDO,SAAS,EAAE,CACT;IAAEC,OAAO,EAAEZ,iBAAiB;IAAEa,QAAQ,EAAER,eAAe;IAAES,KAAK,EAAE;EAAI,CAAE,EACtE;IAAEF,OAAO,EAAEZ,iBAAiB;IAAEa,QAAQ,EAAEP,gBAAgB;IAAEQ,KAAK,EAAE;EAAI,CAAE,CACxE;EACDC,SAAS,EAAE,CAACb,YAAY;CACzB,CAAC,C,EACWK,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}