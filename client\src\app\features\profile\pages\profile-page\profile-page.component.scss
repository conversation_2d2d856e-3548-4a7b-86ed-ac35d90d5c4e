// Profile page styles
.profile-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

// Header with title and edit button
.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
  
  h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 500;
    color: #333;
  }
}

// Loading container
.loading-container {
  display: flex;
  justify-content: center;
  padding: 2rem;
}

// Profile content
.profile-content {
  padding: 1rem 0;
}

// Profile view mode
.profile-view {
  display: flex;
  flex-direction: row;
  gap: 2rem;
  
  @media (max-width: 600px) {
    flex-direction: column;
  }
}

// Avatar section
.profile-avatar {
  flex: 0 0 150px;
  
  .avatar-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: #3f51b5;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 3rem;
    font-weight: 500;
  }
}

// Profile details
.profile-details {
  flex: 1;
}

// Individual field
.profile-field {
  margin-bottom: 1.5rem;
  
  .field-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.3rem;
  }
  
  .field-value {
    font-size: 1.1rem;
    color: #333;
  }
}

// Form in edit mode
.profile-form {
  max-width: 500px;
  
  .form-field {
    margin-bottom: 1.5rem;
    
    mat-form-field {
      width: 100%;
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }
}
