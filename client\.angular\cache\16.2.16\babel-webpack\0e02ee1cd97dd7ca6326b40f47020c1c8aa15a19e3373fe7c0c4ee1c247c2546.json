{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Guest Guard\n * Protects routes that should only be accessible to non-authenticated users\n * Redirects authenticated users to dashboard\n */\nimport { Injectable } from '@angular/core';\nimport { map, take } from 'rxjs/operators';\nexport let GuestGuard = class GuestGuard {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   */\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  /**\n   * Determines if a route can be activated\n   * @param route - The route being activated\n   * @param state - Router state\n   * @returns Boolean or UrlTree indicating if route can be activated\n   */\n  canActivate(route, state) {\n    return this.authService.isAuthenticated$.pipe(take(1), map(isAuthenticated => {\n      // If not authenticated, allow access\n      if (!isAuthenticated) {\n        return true;\n      }\n      // If authenticated, redirect to dashboard\n      return this.router.createUrlTree(['/dashboard']);\n    }));\n  }\n};\nGuestGuard = __decorate([Injectable({\n  providedIn: 'root'\n})], GuestGuard);", "map": {"version": 3, "names": ["Injectable", "map", "take", "<PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "isAuthenticated$", "pipe", "isAuthenticated", "createUrlTree", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\guards\\guest.guard.ts"], "sourcesContent": ["/**\n * Guest Guard\n * Protects routes that should only be accessible to non-authenticated users\n * Redirects authenticated users to dashboard\n */\nimport { Injectable } from '@angular/core';\nimport { \n  ActivatedRouteSnapshot, \n  RouterStateSnapshot, \n  Router,\n  UrlTree\n} from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GuestGuard {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   */\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  /**\n   * Determines if a route can be activated\n   * @param route - The route being activated\n   * @param state - Router state\n   * @returns Boolean or UrlTree indicating if route can be activated\n   */\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {\n    return this.authService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        // If not authenticated, allow access\n        if (!isAuthenticated) {\n          return true;\n        }\n        \n        // If authenticated, redirect to dashboard\n        return this.router.createUrlTree(['/dashboard']);\n      })\n    );\n  }\n}\n"], "mappings": ";AAAA;;;;;AAKA,SAASA,UAAU,QAAQ,eAAe;AAQ1C,SAASC,GAAG,EAAEC,IAAI,QAAQ,gBAAgB;AAMnC,WAAMC,UAAU,GAAhB,MAAMA,UAAU;EACrB;;;;;EAKAC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEH;;;;;;EAMAC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACJ,WAAW,CAACK,gBAAgB,CAACC,IAAI,CAC3CT,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACW,eAAe,IAAG;MACpB;MACA,IAAI,CAACA,eAAe,EAAE;QACpB,OAAO,IAAI;;MAGb;MACA,OAAO,IAAI,CAACN,MAAM,CAACO,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACH;CACD;AAlCYV,UAAU,GAAAW,UAAA,EAHtBd,UAAU,CAAC;EACVe,UAAU,EAAE;CACb,CAAC,C,EACWZ,UAAU,CAkCtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}