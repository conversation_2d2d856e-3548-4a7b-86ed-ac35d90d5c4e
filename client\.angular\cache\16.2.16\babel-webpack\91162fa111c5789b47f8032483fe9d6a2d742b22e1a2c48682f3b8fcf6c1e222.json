{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Task List Component\n * Displays a list of tasks with filtering and sorting options\n */\nimport { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';\nexport let TaskListComponent = class TaskListComponent {\n  /**\n   * Constructor with dependency injection\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(cdr) {\n    this.cdr = cdr;\n    /**\n     * Input array of tasks to display\n     */\n    this.tasks = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message if loading failed\n     */\n    this.error = null;\n    /**\n     * Current filter applied to tasks\n     */\n    this.filter = {};\n    /**\n     * Event emitted when a task is selected\n     */\n    this.taskSelected = new EventEmitter();\n    /**\n     * Event emitted when a task is deleted\n     */\n    this.taskDeleted = new EventEmitter();\n    /**\n     * Event emitted when a task status is changed\n     */\n    this.statusChanged = new EventEmitter();\n    /**\n     * Event emitted when filter is changed\n     */\n    this.filterChanged = new EventEmitter();\n    /**\n     * Event emitted when refresh is requested\n     */\n    this.refresh = new EventEmitter();\n    /**\n     * Current sort field\n     */\n    this.sortField = 'dueDate';\n    /**\n     * Current sort direction\n     */\n    this.sortDirection = 'asc';\n    /**\n     * Filtered and sorted tasks\n     */\n    this.displayedTasks = [];\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initial processing of tasks\n   */\n  ngOnInit() {\n    this.processTasks();\n  }\n  /**\n   * Lifecycle hook that is called when inputs change\n   * Updates displayed tasks when inputs change\n   */\n  ngOnChanges() {\n    this.processTasks();\n  }\n  /**\n   * Process tasks with current filter and sort settings\n   */\n  processTasks() {\n    // Make a copy to avoid modifying the input array\n    this.displayedTasks = [...this.tasks];\n    // Apply sorting\n    this.sortTasks();\n    // Mark for check since we're using OnPush strategy\n    this.cdr.markForCheck();\n  }\n  /**\n   * Sort tasks based on current sort field and direction\n   */\n  sortTasks() {\n    this.displayedTasks.sort((a, b) => {\n      let comparison = 0;\n      // Sort by the selected field\n      switch (this.sortField) {\n        case 'title':\n          comparison = a.title.localeCompare(b.title);\n          break;\n        case 'priority':\n          const priorityOrder = {\n            high: 0,\n            medium: 1,\n            low: 2\n          };\n          comparison = priorityOrder[a.priority] - priorityOrder[b.priority];\n          break;\n        case 'status':\n          const statusOrder = {\n            todo: 0,\n            in_progress: 1,\n            review: 2,\n            done: 3\n          };\n          comparison = statusOrder[a.status] - statusOrder[b.status];\n          break;\n        case 'dueDate':\n          // Handle null/undefined due dates\n          if (!a.dueDate && !b.dueDate) comparison = 0;else if (!a.dueDate) comparison = 1;else if (!b.dueDate) comparison = -1;else comparison = new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();\n          break;\n        default:\n          comparison = 0;\n      }\n      // Apply sort direction\n      return this.sortDirection === 'asc' ? comparison : -comparison;\n    });\n  }\n  /**\n   * Change the sort field and direction\n   * @param field - Field to sort by\n   */\n  onSort(field) {\n    // If clicking the same field, toggle direction\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      // New field, default to ascending\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    // Re-sort the tasks\n    this.sortTasks();\n  }\n  /**\n   * Handle task selection\n   * @param task - Selected task\n   */\n  onTaskSelect(task) {\n    this.taskSelected.emit(task);\n  }\n  /**\n   * Handle task deletion\n   * @param taskId - ID of task to delete\n   */\n  onTaskDelete(taskId) {\n    this.taskDeleted.emit(taskId);\n  }\n  /**\n   * Handle task status change\n   * @param taskId - ID of task\n   * @param status - New status\n   */\n  onStatusChange(taskId, status) {\n    this.statusChanged.emit({\n      taskId,\n      status\n    });\n  }\n  /**\n   * Handle filter changes\n   * @param filter - New filter\n   */\n  onFilterChange(filter) {\n    this.filterChanged.emit(filter);\n  }\n  /**\n   * Request data refresh\n   */\n  onRefresh() {\n    this.refresh.emit();\n  }\n};\n__decorate([Input()], TaskListComponent.prototype, \"tasks\", void 0);\n__decorate([Input()], TaskListComponent.prototype, \"loading\", void 0);\n__decorate([Input()], TaskListComponent.prototype, \"error\", void 0);\n__decorate([Input()], TaskListComponent.prototype, \"filter\", void 0);\n__decorate([Output()], TaskListComponent.prototype, \"taskSelected\", void 0);\n__decorate([Output()], TaskListComponent.prototype, \"taskDeleted\", void 0);\n__decorate([Output()], TaskListComponent.prototype, \"statusChanged\", void 0);\n__decorate([Output()], TaskListComponent.prototype, \"filterChanged\", void 0);\n__decorate([Output()], TaskListComponent.prototype, \"refresh\", void 0);\nTaskListComponent = __decorate([Component({\n  selector: 'app-task-list',\n  templateUrl: './task-list.component.html',\n  styleUrls: ['./task-list.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskListComponent);", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "ChangeDetectionStrategy", "TaskListComponent", "constructor", "cdr", "tasks", "loading", "error", "filter", "taskSelected", "taskDeleted", "statusChanged", "filterChanged", "refresh", "sortField", "sortDirection", "displayedTasks", "ngOnInit", "processTasks", "ngOnChanges", "sortTasks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "comparison", "title", "localeCompare", "priorityOrder", "high", "medium", "low", "priority", "statusOrder", "todo", "in_progress", "review", "done", "status", "dueDate", "Date", "getTime", "onSort", "field", "onTaskSelect", "task", "emit", "onTaskDelete", "taskId", "onStatusChange", "onFilterChange", "onRefresh", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\components\\task-list\\task-list.component.ts"], "sourcesContent": ["/**\n * Task List Component\n * Displays a list of tasks with filtering and sorting options\n */\nimport { Component, OnInit, Input, Output, EventEmitter, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Task } from '../../../../core/models/task.model';\nimport { TaskFilter } from '../../../../core/models/task-filter.model';\n\n@Component({\n  selector: 'app-task-list',\n  templateUrl: './task-list.component.html',\n  styleUrls: ['./task-list.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskListComponent implements OnInit {\n  /**\n   * Input array of tasks to display\n   */\n  @Input() tasks: Task[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Error message if loading failed\n   */\n  @Input() error: string | null = null;\n  \n  /**\n   * Current filter applied to tasks\n   */\n  @Input() filter: TaskFilter = {};\n  \n  /**\n   * Event emitted when a task is selected\n   */\n  @Output() taskSelected = new EventEmitter<Task>();\n  \n  /**\n   * Event emitted when a task is deleted\n   */\n  @Output() taskDeleted = new EventEmitter<string>();\n  \n  /**\n   * Event emitted when a task status is changed\n   */\n  @Output() statusChanged = new EventEmitter<{ taskId: string; status: string }>();\n  \n  /**\n   * Event emitted when filter is changed\n   */\n  @Output() filterChanged = new EventEmitter<TaskFilter>();\n  \n  /**\n   * Event emitted when refresh is requested\n   */\n  @Output() refresh = new EventEmitter<void>();\n  \n  /**\n   * Current sort field\n   */\n  sortField = 'dueDate';\n  \n  /**\n   * Current sort direction\n   */\n  sortDirection = 'asc';\n  \n  /**\n   * Filtered and sorted tasks\n   */\n  displayedTasks: Task[] = [];\n\n  /**\n   * Constructor with dependency injection\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(private cdr: ChangeDetectorRef) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initial processing of tasks\n   */\n  ngOnInit(): void {\n    this.processTasks();\n  }\n\n  /**\n   * Lifecycle hook that is called when inputs change\n   * Updates displayed tasks when inputs change\n   */\n  ngOnChanges(): void {\n    this.processTasks();\n  }\n\n  /**\n   * Process tasks with current filter and sort settings\n   */\n  processTasks(): void {\n    // Make a copy to avoid modifying the input array\n    this.displayedTasks = [...this.tasks];\n    \n    // Apply sorting\n    this.sortTasks();\n    \n    // Mark for check since we're using OnPush strategy\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Sort tasks based on current sort field and direction\n   */\n  sortTasks(): void {\n    this.displayedTasks.sort((a, b) => {\n      let comparison = 0;\n      \n      // Sort by the selected field\n      switch (this.sortField) {\n        case 'title':\n          comparison = a.title.localeCompare(b.title);\n          break;\n        case 'priority':\n          const priorityOrder = { high: 0, medium: 1, low: 2 };\n          comparison = priorityOrder[a.priority] - priorityOrder[b.priority];\n          break;\n        case 'status':\n          const statusOrder = { todo: 0, in_progress: 1, review: 2, done: 3 };\n          comparison = statusOrder[a.status] - statusOrder[b.status];\n          break;\n        case 'dueDate':\n          // Handle null/undefined due dates\n          if (!a.dueDate && !b.dueDate) comparison = 0;\n          else if (!a.dueDate) comparison = 1;\n          else if (!b.dueDate) comparison = -1;\n          else comparison = new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();\n          break;\n        default:\n          comparison = 0;\n      }\n      \n      // Apply sort direction\n      return this.sortDirection === 'asc' ? comparison : -comparison;\n    });\n  }\n\n  /**\n   * Change the sort field and direction\n   * @param field - Field to sort by\n   */\n  onSort(field: string): void {\n    // If clicking the same field, toggle direction\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      // New field, default to ascending\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    \n    // Re-sort the tasks\n    this.sortTasks();\n  }\n\n  /**\n   * Handle task selection\n   * @param task - Selected task\n   */\n  onTaskSelect(task: Task): void {\n    this.taskSelected.emit(task);\n  }\n\n  /**\n   * Handle task deletion\n   * @param taskId - ID of task to delete\n   */\n  onTaskDelete(taskId: string): void {\n    this.taskDeleted.emit(taskId);\n  }\n\n  /**\n   * Handle task status change\n   * @param taskId - ID of task\n   * @param status - New status\n   */\n  onStatusChange(taskId: string, status: string): void {\n    this.statusChanged.emit({ taskId, status });\n  }\n\n  /**\n   * Handle filter changes\n   * @param filter - New filter\n   */\n  onFilterChange(filter: TaskFilter): void {\n    this.filterChanged.emit(filter);\n  }\n\n  /**\n   * Request data refresh\n   */\n  onRefresh(): void {\n    this.refresh.emit();\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAUC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,uBAAuB,QAA2B,eAAe;AAUnH,WAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EA6D5B;;;;EAIAC,YAAoBC,GAAsB;IAAtB,KAAAA,GAAG,GAAHA,GAAG;IAhEvB;;;IAGS,KAAAC,KAAK,GAAW,EAAE;IAE3B;;;IAGS,KAAAC,OAAO,GAAG,KAAK;IAExB;;;IAGS,KAAAC,KAAK,GAAkB,IAAI;IAEpC;;;IAGS,KAAAC,MAAM,GAAe,EAAE;IAEhC;;;IAGU,KAAAC,YAAY,GAAG,IAAIT,YAAY,EAAQ;IAEjD;;;IAGU,KAAAU,WAAW,GAAG,IAAIV,YAAY,EAAU;IAElD;;;IAGU,KAAAW,aAAa,GAAG,IAAIX,YAAY,EAAsC;IAEhF;;;IAGU,KAAAY,aAAa,GAAG,IAAIZ,YAAY,EAAc;IAExD;;;IAGU,KAAAa,OAAO,GAAG,IAAIb,YAAY,EAAQ;IAE5C;;;IAGA,KAAAc,SAAS,GAAG,SAAS;IAErB;;;IAGA,KAAAC,aAAa,GAAG,KAAK;IAErB;;;IAGA,KAAAC,cAAc,GAAW,EAAE;EAMkB;EAE7C;;;;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACD,YAAY,EAAE;EACrB;EAEA;;;EAGAA,YAAYA,CAAA;IACV;IACA,IAAI,CAACF,cAAc,GAAG,CAAC,GAAG,IAAI,CAACX,KAAK,CAAC;IAErC;IACA,IAAI,CAACe,SAAS,EAAE;IAEhB;IACA,IAAI,CAAChB,GAAG,CAACiB,YAAY,EAAE;EACzB;EAEA;;;EAGAD,SAASA,CAAA;IACP,IAAI,CAACJ,cAAc,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAIC,UAAU,GAAG,CAAC;MAElB;MACA,QAAQ,IAAI,CAACX,SAAS;QACpB,KAAK,OAAO;UACVW,UAAU,GAAGF,CAAC,CAACG,KAAK,CAACC,aAAa,CAACH,CAAC,CAACE,KAAK,CAAC;UAC3C;QACF,KAAK,UAAU;UACb,MAAME,aAAa,GAAG;YAAEC,IAAI,EAAE,CAAC;YAAEC,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAC,CAAE;UACpDN,UAAU,GAAGG,aAAa,CAACL,CAAC,CAACS,QAAQ,CAAC,GAAGJ,aAAa,CAACJ,CAAC,CAACQ,QAAQ,CAAC;UAClE;QACF,KAAK,QAAQ;UACX,MAAMC,WAAW,GAAG;YAAEC,IAAI,EAAE,CAAC;YAAEC,WAAW,EAAE,CAAC;YAAEC,MAAM,EAAE,CAAC;YAAEC,IAAI,EAAE;UAAC,CAAE;UACnEZ,UAAU,GAAGQ,WAAW,CAACV,CAAC,CAACe,MAAM,CAAC,GAAGL,WAAW,CAACT,CAAC,CAACc,MAAM,CAAC;UAC1D;QACF,KAAK,SAAS;UACZ;UACA,IAAI,CAACf,CAAC,CAACgB,OAAO,IAAI,CAACf,CAAC,CAACe,OAAO,EAAEd,UAAU,GAAG,CAAC,CAAC,KACxC,IAAI,CAACF,CAAC,CAACgB,OAAO,EAAEd,UAAU,GAAG,CAAC,CAAC,KAC/B,IAAI,CAACD,CAAC,CAACe,OAAO,EAAEd,UAAU,GAAG,CAAC,CAAC,CAAC,KAChCA,UAAU,GAAG,IAAIe,IAAI,CAACjB,CAAC,CAACgB,OAAO,CAAC,CAACE,OAAO,EAAE,GAAG,IAAID,IAAI,CAAChB,CAAC,CAACe,OAAO,CAAC,CAACE,OAAO,EAAE;UAC/E;QACF;UACEhB,UAAU,GAAG,CAAC;;MAGlB;MACA,OAAO,IAAI,CAACV,aAAa,KAAK,KAAK,GAAGU,UAAU,GAAG,CAACA,UAAU;IAChE,CAAC,CAAC;EACJ;EAEA;;;;EAIAiB,MAAMA,CAACC,KAAa;IAClB;IACA,IAAI,IAAI,CAAC7B,SAAS,KAAK6B,KAAK,EAAE;MAC5B,IAAI,CAAC5B,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;KACnE,MAAM;MACL;MACA,IAAI,CAACD,SAAS,GAAG6B,KAAK;MACtB,IAAI,CAAC5B,aAAa,GAAG,KAAK;;IAG5B;IACA,IAAI,CAACK,SAAS,EAAE;EAClB;EAEA;;;;EAIAwB,YAAYA,CAACC,IAAU;IACrB,IAAI,CAACpC,YAAY,CAACqC,IAAI,CAACD,IAAI,CAAC;EAC9B;EAEA;;;;EAIAE,YAAYA,CAACC,MAAc;IACzB,IAAI,CAACtC,WAAW,CAACoC,IAAI,CAACE,MAAM,CAAC;EAC/B;EAEA;;;;;EAKAC,cAAcA,CAACD,MAAc,EAAEV,MAAc;IAC3C,IAAI,CAAC3B,aAAa,CAACmC,IAAI,CAAC;MAAEE,MAAM;MAAEV;IAAM,CAAE,CAAC;EAC7C;EAEA;;;;EAIAY,cAAcA,CAAC1C,MAAkB;IAC/B,IAAI,CAACI,aAAa,CAACkC,IAAI,CAACtC,MAAM,CAAC;EACjC;EAEA;;;EAGA2C,SAASA,CAAA;IACP,IAAI,CAACtC,OAAO,CAACiC,IAAI,EAAE;EACrB;CACD;AA1LUM,UAAA,EAARtD,KAAK,EAAE,C,+CAAoB;AAKnBsD,UAAA,EAARtD,KAAK,EAAE,C,iDAAiB;AAKhBsD,UAAA,EAARtD,KAAK,EAAE,C,+CAA6B;AAK5BsD,UAAA,EAARtD,KAAK,EAAE,C,gDAAyB;AAKvBsD,UAAA,EAATrD,MAAM,EAAE,C,sDAAyC;AAKxCqD,UAAA,EAATrD,MAAM,EAAE,C,qDAA0C;AAKzCqD,UAAA,EAATrD,MAAM,EAAE,C,uDAAwE;AAKvEqD,UAAA,EAATrD,MAAM,EAAE,C,uDAAgD;AAK/CqD,UAAA,EAATrD,MAAM,EAAE,C,iDAAoC;AA5ClCG,iBAAiB,GAAAkD,UAAA,EAN7BvD,SAAS,CAAC;EACTwD,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,CAAC,4BAA4B,CAAC;EACzCC,eAAe,EAAEvD,uBAAuB,CAACwD;CAC1C,CAAC,C,EACWvD,iBAAiB,CA8L7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}