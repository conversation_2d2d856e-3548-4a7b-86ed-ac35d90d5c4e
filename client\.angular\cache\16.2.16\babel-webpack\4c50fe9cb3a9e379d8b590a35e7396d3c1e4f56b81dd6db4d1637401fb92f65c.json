{"ast": null, "code": "import { AsyncSubject } from '../AsyncSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishLast() {\n  return source => {\n    const subject = new AsyncSubject();\n    return new ConnectableObservable(source, () => subject);\n  };\n}", "map": {"version": 3, "names": ["AsyncSubject", "ConnectableObservable", "publishLast", "source", "subject"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/esm/internal/operators/publishLast.js"], "sourcesContent": ["import { AsyncSubject } from '../AsyncSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishLast() {\n    return (source) => {\n        const subject = new AsyncSubject();\n        return new ConnectableObservable(source, () => subject);\n    };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC1B,OAAQC,MAAM,IAAK;IACf,MAAMC,OAAO,GAAG,IAAIJ,YAAY,CAAC,CAAC;IAClC,OAAO,IAAIC,qBAAqB,CAACE,MAAM,EAAE,MAAMC,OAAO,CAAC;EAC3D,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}