{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Reset Password Form Component\n * Handles password reset with token validation\n */\nimport { Component, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { Validators } from '@angular/forms';\nexport let ResetPasswordFormComponent = class ResetPasswordFormComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed request\n     */\n    this.error = null;\n    /**\n     * Success message after successful request\n     */\n    this.success = null;\n    /**\n     * Reset token from URL\n     */\n    this.token = null;\n    /**\n     * Event emitted when form is submitted\n     */\n    this.formSubmit = new EventEmitter();\n    /**\n     * Event emitted when login link is clicked\n     */\n    this.login = new EventEmitter();\n    /**\n     * Flag to toggle password visibility\n     */\n    this.hidePassword = true;\n    /**\n     * Flag to toggle confirm password visibility\n     */\n    this.hideConfirmPassword = true;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the reset password form\n   */\n  ngOnInit() {\n    this.initForm();\n  }\n  /**\n   * Initialize reset password form with validation\n   */\n  initForm() {\n    this.resetPasswordForm = this.fb.group({\n      password: ['', [Validators.required, Validators.minLength(8), this.passwordStrengthValidator]],\n      confirmPassword: ['', Validators.required]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  /**\n   * Custom validator for password strength\n   * @param control - Form control\n   * @returns Validation errors or null\n   */\n  passwordStrengthValidator(control) {\n    const value = control.value || '';\n    if (!value) {\n      return null;\n    }\n    const hasUpperCase = /[A-Z]+/.test(value);\n    const hasLowerCase = /[a-z]+/.test(value);\n    const hasNumeric = /[0-9]+/.test(value);\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+/.test(value);\n    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n    return !passwordValid ? {\n      passwordStrength: true\n    } : null;\n  }\n  /**\n   * Custom validator for password matching\n   * @param group - Form group\n   * @returns Validation errors or null\n   */\n  passwordMatchValidator(group) {\n    const password = group.get('password')?.value;\n    const confirmPassword = group.get('confirmPassword')?.value;\n    return password === confirmPassword ? null : {\n      passwordMismatch: true\n    };\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.resetPasswordForm.invalid || this.loading || !this.token) {\n      // Mark all fields as touched to trigger validation messages\n      this.resetPasswordForm.markAllAsTouched();\n      return;\n    }\n    const {\n      password\n    } = this.resetPasswordForm.value;\n    this.formSubmit.emit({\n      password,\n      token: this.token\n    });\n  }\n  /**\n   * Handle login link click\n   */\n  onLogin() {\n    this.login.emit();\n  }\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName, errorName) {\n    const control = this.resetPasswordForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n  /**\n   * Check if form has error\n   * @param errorName - Name of error\n   * @returns True if form has error\n   */\n  hasFormError(errorName) {\n    return this.resetPasswordForm.touched && this.resetPasswordForm.hasError(errorName);\n  }\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility() {\n    this.hidePassword = !this.hidePassword;\n  }\n  /**\n   * Toggle confirm password visibility\n   */\n  toggleConfirmPasswordVisibility() {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n};\n__decorate([Input()], ResetPasswordFormComponent.prototype, \"loading\", void 0);\n__decorate([Input()], ResetPasswordFormComponent.prototype, \"error\", void 0);\n__decorate([Input()], ResetPasswordFormComponent.prototype, \"success\", void 0);\n__decorate([Input()], ResetPasswordFormComponent.prototype, \"token\", void 0);\n__decorate([Output()], ResetPasswordFormComponent.prototype, \"formSubmit\", void 0);\n__decorate([Output()], ResetPasswordFormComponent.prototype, \"login\", void 0);\nResetPasswordFormComponent = __decorate([Component({\n  selector: 'app-reset-password-form',\n  templateUrl: './reset-password-form.component.html',\n  styleUrls: ['./reset-password-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], ResetPasswordFormComponent);", "map": {"version": 3, "names": ["Component", "Output", "EventEmitter", "Input", "ChangeDetectionStrategy", "Validators", "ResetPasswordFormComponent", "constructor", "fb", "loading", "error", "success", "token", "formSubmit", "login", "hidePassword", "hideConfirmPassword", "ngOnInit", "initForm", "resetPasswordForm", "group", "password", "required", "<PERSON><PERSON><PERSON><PERSON>", "passwordStrengthValidator", "confirmPassword", "validators", "passwordMatchValidator", "control", "value", "hasUpperCase", "test", "hasLowerCase", "hasNumeric", "hasSpecialChar", "passwordValid", "passwordStrength", "get", "passwordMismatch", "onSubmit", "invalid", "mark<PERSON>llAsTouched", "emit", "onLogin", "<PERSON><PERSON><PERSON><PERSON>", "controlName", "errorName", "touched", "hasFormError", "togglePasswordVisibility", "toggleConfirmPasswordVisibility", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\reset-password-form\\reset-password-form.component.ts"], "sourcesContent": ["/**\n * Reset Password Form Component\n * Handles password reset with token validation\n */\nimport { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';\n\n@Component({\n  selector: 'app-reset-password-form',\n  templateUrl: './reset-password-form.component.html',\n  styleUrls: ['./reset-password-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ResetPasswordFormComponent implements OnInit {\n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Error message from failed request\n   */\n  @Input() error: string | null = null;\n  \n  /**\n   * Success message after successful request\n   */\n  @Input() success: string | null = null;\n  \n  /**\n   * Reset token from URL\n   */\n  @Input() token: string | null = null;\n  \n  /**\n   * Event emitted when form is submitted\n   */\n  @Output() formSubmit = new EventEmitter<{ password: string; token: string }>();\n  \n  /**\n   * Event emitted when login link is clicked\n   */\n  @Output() login = new EventEmitter<void>();\n  \n  /**\n   * Reset password form group\n   */\n  resetPasswordForm!: FormGroup;\n  \n  /**\n   * Flag to toggle password visibility\n   */\n  hidePassword = true;\n  \n  /**\n   * Flag to toggle confirm password visibility\n   */\n  hideConfirmPassword = true;\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the reset password form\n   */\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  /**\n   * Initialize reset password form with validation\n   */\n  private initForm(): void {\n    this.resetPasswordForm = this.fb.group({\n      password: ['', [\n        Validators.required, \n        Validators.minLength(8),\n        this.passwordStrengthValidator\n      ]],\n      confirmPassword: ['', Validators.required]\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  /**\n   * Custom validator for password strength\n   * @param control - Form control\n   * @returns Validation errors or null\n   */\n  private passwordStrengthValidator(control: AbstractControl): ValidationErrors | null {\n    const value: string = control.value || '';\n    \n    if (!value) {\n      return null;\n    }\n    \n    const hasUpperCase = /[A-Z]+/.test(value);\n    const hasLowerCase = /[a-z]+/.test(value);\n    const hasNumeric = /[0-9]+/.test(value);\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+/.test(value);\n    \n    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n    \n    return !passwordValid ? { passwordStrength: true } : null;\n  }\n\n  /**\n   * Custom validator for password matching\n   * @param group - Form group\n   * @returns Validation errors or null\n   */\n  private passwordMatchValidator(group: AbstractControl): ValidationErrors | null {\n    const password = group.get('password')?.value;\n    const confirmPassword = group.get('confirmPassword')?.value;\n    \n    return password === confirmPassword ? null : { passwordMismatch: true };\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.resetPasswordForm.invalid || this.loading || !this.token) {\n      // Mark all fields as touched to trigger validation messages\n      this.resetPasswordForm.markAllAsTouched();\n      return;\n    }\n    \n    const { password } = this.resetPasswordForm.value;\n    this.formSubmit.emit({ password, token: this.token });\n  }\n\n  /**\n   * Handle login link click\n   */\n  onLogin(): void {\n    this.login.emit();\n  }\n\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName: string, errorName: string): boolean {\n    const control = this.resetPasswordForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n\n  /**\n   * Check if form has error\n   * @param errorName - Name of error\n   * @returns True if form has error\n   */\n  hasFormError(errorName: string): boolean {\n    return this.resetPasswordForm.touched && this.resetPasswordForm.hasError(errorName);\n  }\n\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility(): void {\n    this.hidePassword = !this.hidePassword;\n  }\n\n  /**\n   * Toggle confirm password visibility\n   */\n  toggleConfirmPasswordVisibility(): void {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAUC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,eAAe;AACvG,SAAiCC,UAAU,QAA2C,gBAAgB;AAQ/F,WAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EA8CrC;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAjDtB;;;IAGS,KAAAC,OAAO,GAAG,KAAK;IAExB;;;IAGS,KAAAC,KAAK,GAAkB,IAAI;IAEpC;;;IAGS,KAAAC,OAAO,GAAkB,IAAI;IAEtC;;;IAGS,KAAAC,KAAK,GAAkB,IAAI;IAEpC;;;IAGU,KAAAC,UAAU,GAAG,IAAIX,YAAY,EAAuC;IAE9E;;;IAGU,KAAAY,KAAK,GAAG,IAAIZ,YAAY,EAAQ;IAO1C;;;IAGA,KAAAa,YAAY,GAAG,IAAI;IAEnB;;;IAGA,KAAAC,mBAAmB,GAAG,IAAI;EAMY;EAEtC;;;;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEA;;;EAGQA,QAAQA,CAAA;IACd,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACX,EAAE,CAACY,KAAK,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACbhB,UAAU,CAACiB,QAAQ,EACnBjB,UAAU,CAACkB,SAAS,CAAC,CAAC,CAAC,EACvB,IAAI,CAACC,yBAAyB,CAC/B,CAAC;MACFC,eAAe,EAAE,CAAC,EAAE,EAAEpB,UAAU,CAACiB,QAAQ;KAC1C,EAAE;MAAEI,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEA;;;;;EAKQH,yBAAyBA,CAACI,OAAwB;IACxD,MAAMC,KAAK,GAAWD,OAAO,CAACC,KAAK,IAAI,EAAE;IAEzC,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI;;IAGb,MAAMC,YAAY,GAAG,QAAQ,CAACC,IAAI,CAACF,KAAK,CAAC;IACzC,MAAMG,YAAY,GAAG,QAAQ,CAACD,IAAI,CAACF,KAAK,CAAC;IACzC,MAAMI,UAAU,GAAG,QAAQ,CAACF,IAAI,CAACF,KAAK,CAAC;IACvC,MAAMK,cAAc,GAAG,wCAAwC,CAACH,IAAI,CAACF,KAAK,CAAC;IAE3E,MAAMM,aAAa,GAAGL,YAAY,IAAIE,YAAY,IAAIC,UAAU,IAAIC,cAAc;IAElF,OAAO,CAACC,aAAa,GAAG;MAAEC,gBAAgB,EAAE;IAAI,CAAE,GAAG,IAAI;EAC3D;EAEA;;;;;EAKQT,sBAAsBA,CAACP,KAAsB;IACnD,MAAMC,QAAQ,GAAGD,KAAK,CAACiB,GAAG,CAAC,UAAU,CAAC,EAAER,KAAK;IAC7C,MAAMJ,eAAe,GAAGL,KAAK,CAACiB,GAAG,CAAC,iBAAiB,CAAC,EAAER,KAAK;IAE3D,OAAOR,QAAQ,KAAKI,eAAe,GAAG,IAAI,GAAG;MAAEa,gBAAgB,EAAE;IAAI,CAAE;EACzE;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpB,iBAAiB,CAACqB,OAAO,IAAI,IAAI,CAAC/B,OAAO,IAAI,CAAC,IAAI,CAACG,KAAK,EAAE;MACjE;MACA,IAAI,CAACO,iBAAiB,CAACsB,gBAAgB,EAAE;MACzC;;IAGF,MAAM;MAAEpB;IAAQ,CAAE,GAAG,IAAI,CAACF,iBAAiB,CAACU,KAAK;IACjD,IAAI,CAAChB,UAAU,CAAC6B,IAAI,CAAC;MAAErB,QAAQ;MAAET,KAAK,EAAE,IAAI,CAACA;IAAK,CAAE,CAAC;EACvD;EAEA;;;EAGA+B,OAAOA,CAAA;IACL,IAAI,CAAC7B,KAAK,CAAC4B,IAAI,EAAE;EACnB;EAEA;;;;;;EAMAE,QAAQA,CAACC,WAAmB,EAAEC,SAAiB;IAC7C,MAAMlB,OAAO,GAAG,IAAI,CAACT,iBAAiB,CAACkB,GAAG,CAACQ,WAAW,CAAC;IACvD,OAAO,CAAC,EAAEjB,OAAO,IAAIA,OAAO,CAACmB,OAAO,IAAInB,OAAO,CAACgB,QAAQ,CAACE,SAAS,CAAC,CAAC;EACtE;EAEA;;;;;EAKAE,YAAYA,CAACF,SAAiB;IAC5B,OAAO,IAAI,CAAC3B,iBAAiB,CAAC4B,OAAO,IAAI,IAAI,CAAC5B,iBAAiB,CAACyB,QAAQ,CAACE,SAAS,CAAC;EACrF;EAEA;;;EAGAG,wBAAwBA,CAAA;IACtB,IAAI,CAAClC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGAmC,+BAA+BA,CAAA;IAC7B,IAAI,CAAClC,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;CACD;AA9JUmC,UAAA,EAARhD,KAAK,EAAE,C,0DAAiB;AAKhBgD,UAAA,EAARhD,KAAK,EAAE,C,wDAA6B;AAK5BgD,UAAA,EAARhD,KAAK,EAAE,C,0DAA+B;AAK9BgD,UAAA,EAARhD,KAAK,EAAE,C,wDAA6B;AAK3BgD,UAAA,EAATlD,MAAM,EAAE,C,6DAAsE;AAKrEkD,UAAA,EAATlD,MAAM,EAAE,C,wDAAkC;AA7BhCK,0BAA0B,GAAA6C,UAAA,EANtCnD,SAAS,CAAC;EACToD,QAAQ,EAAE,yBAAyB;EACnCC,WAAW,EAAE,sCAAsC;EACnDC,SAAS,EAAE,CAAC,sCAAsC,CAAC;EACnDC,eAAe,EAAEnD,uBAAuB,CAACoD;CAC1C,CAAC,C,EACWlD,0BAA0B,CAkKtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}