/**
 * Task Repository Interface
 * Defines the contract for task data access operations
 */
import { ITask, ITaskDocument } from '../entities/Task';
import mongoose from 'mongoose';

/**
 * Interface for Task Repository
 * Follows repository pattern to abstract data access operations
 */
export interface ITaskRepository {
  /**
   * Creates a new task
   * @param task - Task data to create
   * @returns Promise resolving to created task
   */
  create(task: ITask): Promise<ITaskDocument>;
  
  /**
   * Finds a task by its ID
   * @param id - Task ID
   * @returns Promise resolving to found task or null if not found
   */
  findById(id: string): Promise<ITaskDocument | null>;
  
  /**
   * Finds all tasks with optional filtering
   * @param filter - Optional filter criteria
   * @returns Promise resolving to array of tasks
   */
  findAll(filter?: Partial<ITask>): Promise<ITaskDocument[]>;
  
  /**
   * Updates a task by ID
   * @param id - Task ID
   * @param updateData - Data to update
   * @returns Promise resolving to updated task or null if not found
   */
  update(id: string, updateData: Partial<ITask>): Promise<ITaskDocument | null>;
  
  /**
   * Deletes a task by ID
   * @param id - Task ID
   * @returns Promise resolving to true if deleted, false if not found
   */
  delete(id: string): Promise<boolean>;
  
  /**
   * Finds tasks assigned to a specific user
   * @param userId - User ID
   * @returns Promise resolving to array of tasks
   */
  findByAssignee(userId: mongoose.Types.ObjectId | string): Promise<ITaskDocument[]>;
}
