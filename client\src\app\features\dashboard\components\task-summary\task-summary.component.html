<!-- Task summary cards container -->
<div class="summary-container">
  <!-- Total tasks card -->
  <div class="summary-card total-card mat-elevation-z2">
    <div class="card-content">
      <div class="card-icon">
        <mat-icon>assignment</mat-icon>
      </div>
      <div class="card-data">
        <span class="card-value">{{ totalCount }}</span>
        <span class="card-label">Total Tasks</span>
      </div>
    </div>
  </div>

  <!-- Todo tasks card -->
  <div class="summary-card todo-card mat-elevation-z2">
    <div class="card-content">
      <div class="card-icon">
        <mat-icon>assignment_late</mat-icon>
      </div>
      <div class="card-data">
        <span class="card-value">{{ todoCount }}</span>
        <span class="card-label">To Do</span>
      </div>
    </div>
  </div>

  <!-- In progress tasks card -->
  <div class="summary-card in-progress-card mat-elevation-z2">
    <div class="card-content">
      <div class="card-icon">
        <mat-icon>hourglass_top</mat-icon>
      </div>
      <div class="card-data">
        <span class="card-value">{{ inProgressCount }}</span>
        <span class="card-label">In Progress</span>
      </div>
    </div>
  </div>

  <!-- Done tasks card -->
  <div class="summary-card done-card mat-elevation-z2">
    <div class="card-content">
      <div class="card-icon">
        <mat-icon>task_alt</mat-icon>
      </div>
      <div class="card-data">
        <span class="card-value">{{ doneCount }}</span>
        <span class="card-label">Done</span>
      </div>
    </div>
  </div>
</div>
