{"ast": null, "code": "/**\n * Reset Password Form Component\n * Handles password reset with token validation\n */\nimport { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nimport * as i8 from \"../../../../shared/components/error-message/error-message.component\";\nfunction ResetPasswordFormComponent_app_error_message_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-error-message\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"message\", ctx_r0.error);\n  }\n}\nfunction ResetPasswordFormComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.success);\n  }\n}\nfunction ResetPasswordFormComponent_app_error_message_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-error-message\", 5);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"message\", \"Invalid or expired password reset link. Please request a new one.\");\n  }\n}\nfunction ResetPasswordFormComponent_form_4_mat_error_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordFormComponent_form_4_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 8 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordFormComponent_form_4_mat_error_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must include uppercase, lowercase, number, and special character \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordFormComponent_form_4_mat_error_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please confirm your password \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordFormComponent_form_4_mat_error_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Passwords do not match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordFormComponent_form_4_mat_spinner_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 16);\n  }\n}\nfunction ResetPasswordFormComponent_form_4_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Reset Password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordFormComponent_form_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 7);\n    i0.ɵɵlistener(\"ngSubmit\", function ResetPasswordFormComponent_form_4_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"mat-form-field\", 8)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 9);\n    i0.ɵɵelementStart(5, \"mat-icon\", 10);\n    i0.ɵɵtext(6, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ResetPasswordFormComponent_form_4_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.togglePasswordVisibility());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, ResetPasswordFormComponent_form_4_mat_error_10_Template, 2, 0, \"mat-error\", 12);\n    i0.ɵɵtemplate(11, ResetPasswordFormComponent_form_4_mat_error_11_Template, 2, 0, \"mat-error\", 12);\n    i0.ɵɵtemplate(12, ResetPasswordFormComponent_form_4_mat_error_12_Template, 2, 0, \"mat-error\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"mat-form-field\", 8)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Confirm Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 13);\n    i0.ɵɵelementStart(17, \"mat-icon\", 10);\n    i0.ɵɵtext(18, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ResetPasswordFormComponent_form_4_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.toggleConfirmPasswordVisibility());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, ResetPasswordFormComponent_form_4_mat_error_22_Template, 2, 0, \"mat-error\", 12);\n    i0.ɵɵtemplate(23, ResetPasswordFormComponent_form_4_mat_error_23_Template, 2, 0, \"mat-error\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 14);\n    i0.ɵɵtemplate(25, ResetPasswordFormComponent_form_4_mat_spinner_25_Template, 1, 0, \"mat-spinner\", 15);\n    i0.ɵɵtemplate(26, ResetPasswordFormComponent_form_4_span_26_Template, 2, 0, \"span\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.resetPasswordForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r3.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx_r3.hidePassword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasError(\"password\", \"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasError(\"password\", \"minlength\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasError(\"password\", \"passwordStrength\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r3.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵattribute(\"aria-label\", \"Hide confirm password\")(\"aria-pressed\", ctx_r3.hideConfirmPassword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasError(\"confirmPassword\", \"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasFormError(\"passwordMismatch\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.loading);\n  }\n}\nfunction ResetPasswordFormComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ResetPasswordFormComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onLogin());\n    });\n    i0.ɵɵtext(1, \" Back to Login \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ResetPasswordFormComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed request\n     */\n    this.error = null;\n    /**\n     * Success message after successful request\n     */\n    this.success = null;\n    /**\n     * Reset token from URL\n     */\n    this.token = null;\n    /**\n     * Event emitted when form is submitted\n     */\n    this.formSubmit = new EventEmitter();\n    /**\n     * Event emitted when login link is clicked\n     */\n    this.login = new EventEmitter();\n    /**\n     * Flag to toggle password visibility\n     */\n    this.hidePassword = true;\n    /**\n     * Flag to toggle confirm password visibility\n     */\n    this.hideConfirmPassword = true;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the reset password form\n   */\n  ngOnInit() {\n    this.initForm();\n  }\n  /**\n   * Initialize reset password form with validation\n   */\n  initForm() {\n    this.resetPasswordForm = this.fb.group({\n      password: ['', [Validators.required, Validators.minLength(8), this.passwordStrengthValidator]],\n      confirmPassword: ['', Validators.required]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  /**\n   * Custom validator for password strength\n   * @param control - Form control\n   * @returns Validation errors or null\n   */\n  passwordStrengthValidator(control) {\n    const value = control.value || '';\n    if (!value) {\n      return null;\n    }\n    const hasUpperCase = /[A-Z]+/.test(value);\n    const hasLowerCase = /[a-z]+/.test(value);\n    const hasNumeric = /[0-9]+/.test(value);\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+/.test(value);\n    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n    return !passwordValid ? {\n      passwordStrength: true\n    } : null;\n  }\n  /**\n   * Custom validator for password matching\n   * @param group - Form group\n   * @returns Validation errors or null\n   */\n  passwordMatchValidator(group) {\n    const password = group.get('password')?.value;\n    const confirmPassword = group.get('confirmPassword')?.value;\n    return password === confirmPassword ? null : {\n      passwordMismatch: true\n    };\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.resetPasswordForm.invalid || this.loading || !this.token) {\n      // Mark all fields as touched to trigger validation messages\n      this.resetPasswordForm.markAllAsTouched();\n      return;\n    }\n    const {\n      password\n    } = this.resetPasswordForm.value;\n    this.formSubmit.emit({\n      password,\n      token: this.token\n    });\n  }\n  /**\n   * Handle login link click\n   */\n  onLogin() {\n    this.login.emit();\n  }\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName, errorName) {\n    const control = this.resetPasswordForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n  /**\n   * Check if form has error\n   * @param errorName - Name of error\n   * @returns True if form has error\n   */\n  hasFormError(errorName) {\n    return this.resetPasswordForm.touched && this.resetPasswordForm.hasError(errorName);\n  }\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility() {\n    this.hidePassword = !this.hidePassword;\n  }\n  /**\n   * Toggle confirm password visibility\n   */\n  toggleConfirmPasswordVisibility() {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n  static {\n    this.ɵfac = function ResetPasswordFormComponent_Factory(t) {\n      return new (t || ResetPasswordFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResetPasswordFormComponent,\n      selectors: [[\"app-reset-password-form\"]],\n      inputs: {\n        loading: \"loading\",\n        error: \"error\",\n        success: \"success\",\n        token: \"token\"\n      },\n      outputs: {\n        formSubmit: \"formSubmit\",\n        login: \"login\"\n      },\n      decls: 6,\n      vars: 5,\n      consts: [[1, \"reset-password-form-container\"], [3, \"message\", 4, \"ngIf\"], [\"class\", \"success-message\", 4, \"ngIf\"], [\"class\", \"reset-password-form\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"class\", \"back-to-login-button\", 3, \"click\", 4, \"ngIf\"], [3, \"message\"], [1, \"success-message\"], [1, \"reset-password-form\", 3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Create a new password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"matPrefix\", \"\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm your new password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"diameter\", \"20\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"back-to-login-button\", 3, \"click\"]],\n      template: function ResetPasswordFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ResetPasswordFormComponent_app_error_message_1_Template, 1, 1, \"app-error-message\", 1);\n          i0.ɵɵtemplate(2, ResetPasswordFormComponent_div_2_Template, 5, 1, \"div\", 2);\n          i0.ɵɵtemplate(3, ResetPasswordFormComponent_app_error_message_3_Template, 1, 1, \"app-error-message\", 1);\n          i0.ɵɵtemplate(4, ResetPasswordFormComponent_form_4_Template, 27, 17, \"form\", 3);\n          i0.ɵɵtemplate(5, ResetPasswordFormComponent_button_5_Template, 2, 0, \"button\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.success);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.token && !ctx.success);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.token && !ctx.success);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.success || !ctx.token);\n        }\n      },\n      dependencies: [i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.MatButton, i3.MatIconButton, i4.MatFormField, i4.MatLabel, i4.MatError, i4.MatPrefix, i4.MatSuffix, i5.MatIcon, i6.MatInput, i7.MatProgressSpinner, i8.ErrorMessageComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.reset-password-form-container[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  padding: 24px;\\n  max-width: 400px;\\n  width: 100%;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.reset-password-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n\\n\\n.success-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  background-color: #e8f5e9;\\n  border-radius: 4px;\\n  margin-bottom: 24px;\\n}\\n.success-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin-right: 12px;\\n}\\n.success-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.submit-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1rem;\\n  margin-top: 8px;\\n}\\n.submit-button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.back-to-login-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1rem;\\n  width: 100%;\\n  margin-top: 16px;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .reset-password-form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    box-shadow: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "error", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "success", "ɵɵlistener", "ResetPasswordFormComponent_form_4_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r13", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ResetPasswordFormComponent_form_4_Template_button_click_7_listener", "ctx_r14", "togglePasswordVisibility", "ɵɵtemplate", "ResetPasswordFormComponent_form_4_mat_error_10_Template", "ResetPasswordFormComponent_form_4_mat_error_11_Template", "ResetPasswordFormComponent_form_4_mat_error_12_Template", "ResetPasswordFormComponent_form_4_Template_button_click_19_listener", "ctx_r15", "toggleConfirmPasswordVisibility", "ResetPasswordFormComponent_form_4_mat_error_22_Template", "ResetPasswordFormComponent_form_4_mat_error_23_Template", "ResetPasswordFormComponent_form_4_mat_spinner_25_Template", "ResetPasswordFormComponent_form_4_span_26_Template", "ctx_r3", "resetPasswordForm", "hidePassword", "ɵɵattribute", "<PERSON><PERSON><PERSON><PERSON>", "hideConfirmPassword", "hasFormError", "loading", "ResetPasswordFormComponent_button_5_Template_button_click_0_listener", "_r17", "ctx_r16", "onLogin", "ResetPasswordFormComponent", "constructor", "fb", "token", "formSubmit", "login", "ngOnInit", "initForm", "group", "password", "required", "<PERSON><PERSON><PERSON><PERSON>", "passwordStrengthValidator", "confirmPassword", "validators", "passwordMatchValidator", "control", "value", "hasUpperCase", "test", "hasLowerCase", "hasNumeric", "hasSpecialChar", "passwordValid", "passwordStrength", "get", "passwordMismatch", "invalid", "mark<PERSON>llAsTouched", "emit", "controlName", "errorName", "touched", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ResetPasswordFormComponent_Template", "rf", "ctx", "ResetPasswordFormComponent_app_error_message_1_Template", "ResetPasswordFormComponent_div_2_Template", "ResetPasswordFormComponent_app_error_message_3_Template", "ResetPasswordFormComponent_form_4_Template", "ResetPasswordFormComponent_button_5_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\reset-password-form\\reset-password-form.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\reset-password-form\\reset-password-form.component.html"], "sourcesContent": ["/**\n * Reset Password Form Component\n * Handles password reset with token validation\n */\nimport { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';\n\n@Component({\n  selector: 'app-reset-password-form',\n  templateUrl: './reset-password-form.component.html',\n  styleUrls: ['./reset-password-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ResetPasswordFormComponent implements OnInit {\n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Error message from failed request\n   */\n  @Input() error: string | null = null;\n  \n  /**\n   * Success message after successful request\n   */\n  @Input() success: string | null = null;\n  \n  /**\n   * Reset token from URL\n   */\n  @Input() token: string | null = null;\n  \n  /**\n   * Event emitted when form is submitted\n   */\n  @Output() formSubmit = new EventEmitter<{ password: string; token: string }>();\n  \n  /**\n   * Event emitted when login link is clicked\n   */\n  @Output() login = new EventEmitter<void>();\n  \n  /**\n   * Reset password form group\n   */\n  resetPasswordForm!: FormGroup;\n  \n  /**\n   * Flag to toggle password visibility\n   */\n  hidePassword = true;\n  \n  /**\n   * Flag to toggle confirm password visibility\n   */\n  hideConfirmPassword = true;\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the reset password form\n   */\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  /**\n   * Initialize reset password form with validation\n   */\n  private initForm(): void {\n    this.resetPasswordForm = this.fb.group({\n      password: ['', [\n        Validators.required, \n        Validators.minLength(8),\n        this.passwordStrengthValidator\n      ]],\n      confirmPassword: ['', Validators.required]\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  /**\n   * Custom validator for password strength\n   * @param control - Form control\n   * @returns Validation errors or null\n   */\n  private passwordStrengthValidator(control: AbstractControl): ValidationErrors | null {\n    const value: string = control.value || '';\n    \n    if (!value) {\n      return null;\n    }\n    \n    const hasUpperCase = /[A-Z]+/.test(value);\n    const hasLowerCase = /[a-z]+/.test(value);\n    const hasNumeric = /[0-9]+/.test(value);\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+/.test(value);\n    \n    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n    \n    return !passwordValid ? { passwordStrength: true } : null;\n  }\n\n  /**\n   * Custom validator for password matching\n   * @param group - Form group\n   * @returns Validation errors or null\n   */\n  private passwordMatchValidator(group: AbstractControl): ValidationErrors | null {\n    const password = group.get('password')?.value;\n    const confirmPassword = group.get('confirmPassword')?.value;\n    \n    return password === confirmPassword ? null : { passwordMismatch: true };\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.resetPasswordForm.invalid || this.loading || !this.token) {\n      // Mark all fields as touched to trigger validation messages\n      this.resetPasswordForm.markAllAsTouched();\n      return;\n    }\n    \n    const { password } = this.resetPasswordForm.value;\n    this.formSubmit.emit({ password, token: this.token });\n  }\n\n  /**\n   * Handle login link click\n   */\n  onLogin(): void {\n    this.login.emit();\n  }\n\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName: string, errorName: string): boolean {\n    const control = this.resetPasswordForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n\n  /**\n   * Check if form has error\n   * @param errorName - Name of error\n   * @returns True if form has error\n   */\n  hasFormError(errorName: string): boolean {\n    return this.resetPasswordForm.touched && this.resetPasswordForm.hasError(errorName);\n  }\n\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility(): void {\n    this.hidePassword = !this.hidePassword;\n  }\n\n  /**\n   * Toggle confirm password visibility\n   */\n  toggleConfirmPasswordVisibility(): void {\n    this.hideConfirmPassword = !this.hideConfirmPassword;\n  }\n}\n", "<!-- Reset password form container -->\n<div class=\"reset-password-form-container\">\n  <!-- Error message -->\n  <app-error-message \n    *ngIf=\"error\" \n    [message]=\"error\">\n  </app-error-message>\n\n  <!-- Success message -->\n  <div *ngIf=\"success\" class=\"success-message\">\n    <mat-icon>check_circle</mat-icon>\n    <span>{{ success }}</span>\n  </div>\n\n  <!-- Token error message -->\n  <app-error-message \n    *ngIf=\"!token && !success\" \n    [message]=\"'Invalid or expired password reset link. Please request a new one.'\">\n  </app-error-message>\n\n  <!-- Reset password form -->\n  <form [formGroup]=\"resetPasswordForm\" (ngSubmit)=\"onSubmit()\" class=\"reset-password-form\" *ngIf=\"token && !success\">\n    <!-- Password field -->\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\n      <mat-label>New Password</mat-label>\n      <input \n        matInput \n        [type]=\"hidePassword ? 'password' : 'text'\" \n        formControlName=\"password\"\n        placeholder=\"Create a new password\"\n        autocomplete=\"new-password\">\n      <mat-icon matPrefix>lock</mat-icon>\n      <button \n        mat-icon-button \n        matSuffix \n        type=\"button\"\n        (click)=\"togglePasswordVisibility()\" \n        [attr.aria-label]=\"'Hide password'\" \n        [attr.aria-pressed]=\"hidePassword\">\n        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n      </button>\n      <mat-error *ngIf=\"hasError('password', 'required')\">\n        Password is required\n      </mat-error>\n      <mat-error *ngIf=\"hasError('password', 'minlength')\">\n        Password must be at least 8 characters\n      </mat-error>\n      <mat-error *ngIf=\"hasError('password', 'passwordStrength')\">\n        Password must include uppercase, lowercase, number, and special character\n      </mat-error>\n    </mat-form-field>\n\n    <!-- Confirm Password field -->\n    <mat-form-field appearance=\"outline\" class=\"full-width\">\n      <mat-label>Confirm Password</mat-label>\n      <input \n        matInput \n        [type]=\"hideConfirmPassword ? 'password' : 'text'\" \n        formControlName=\"confirmPassword\"\n        placeholder=\"Confirm your new password\"\n        autocomplete=\"new-password\">\n      <mat-icon matPrefix>lock</mat-icon>\n      <button \n        mat-icon-button \n        matSuffix \n        type=\"button\"\n        (click)=\"toggleConfirmPasswordVisibility()\" \n        [attr.aria-label]=\"'Hide confirm password'\" \n        [attr.aria-pressed]=\"hideConfirmPassword\">\n        <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\n      </button>\n      <mat-error *ngIf=\"hasError('confirmPassword', 'required')\">\n        Please confirm your password\n      </mat-error>\n      <mat-error *ngIf=\"hasFormError('passwordMismatch')\">\n        Passwords do not match\n      </mat-error>\n    </mat-form-field>\n\n    <!-- Submit button -->\n    <button \n      mat-raised-button \n      color=\"primary\" \n      type=\"submit\" \n      class=\"submit-button\"\n      [disabled]=\"loading\">\n      <mat-spinner diameter=\"20\" *ngIf=\"loading\"></mat-spinner>\n      <span *ngIf=\"!loading\">Reset Password</span>\n    </button>\n  </form>\n\n  <!-- Back to login button -->\n  <button \n    *ngIf=\"success || !token\"\n    mat-raised-button \n    color=\"primary\" \n    (click)=\"onLogin()\" \n    class=\"back-to-login-button\">\n    Back to Login\n  </button>\n</div>\n"], "mappings": "AAAA;;;;AAIA,SAAoCA,YAAY,QAAwC,eAAe;AACvG,SAAiCC,UAAU,QAA2C,gBAAgB;;;;;;;;;;;;ICFpGC,EAAA,CAAAC,SAAA,2BAGoB;;;;IADlBD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,KAAA,CAAiB;;;;;IAInBJ,EAAA,CAAAK,cAAA,aAA6C;IACjCL,EAAA,CAAAM,MAAA,mBAAY;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACjCP,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,GAAa;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;IAApBP,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAa;;;;;IAIrBX,EAAA,CAAAC,SAAA,2BAGoB;;;IADlBD,EAAA,CAAAE,UAAA,gFAA+E;;;;;IAwB7EF,EAAA,CAAAK,cAAA,gBAAoD;IAClDL,EAAA,CAAAM,MAAA,6BACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAAqD;IACnDL,EAAA,CAAAM,MAAA,+CACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAA4D;IAC1DL,EAAA,CAAAM,MAAA,kFACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAsBZP,EAAA,CAAAK,cAAA,gBAA2D;IACzDL,EAAA,CAAAM,MAAA,qCACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IACZP,EAAA,CAAAK,cAAA,gBAAoD;IAClDL,EAAA,CAAAM,MAAA,+BACF;IAAAN,EAAA,CAAAO,YAAA,EAAY;;;;;IAUZP,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAK,cAAA,WAAuB;IAAAL,EAAA,CAAAM,MAAA,qBAAc;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;;;IAlEhDP,EAAA,CAAAK,cAAA,cAAoH;IAA9EL,EAAA,CAAAY,UAAA,sBAAAC,oEAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAYjB,EAAA,CAAAkB,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAE3DnB,EAAA,CAAAK,cAAA,wBAAwD;IAC3CL,EAAA,CAAAM,MAAA,mBAAY;IAAAN,EAAA,CAAAO,YAAA,EAAY;IACnCP,EAAA,CAAAC,SAAA,eAK8B;IAC9BD,EAAA,CAAAK,cAAA,mBAAoB;IAAAL,EAAA,CAAAM,MAAA,WAAI;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACnCP,EAAA,CAAAK,cAAA,iBAMqC;IAFnCL,EAAA,CAAAY,UAAA,mBAAAQ,mEAAA;MAAApB,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAArB,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAG,OAAA,CAAAC,wBAAA,EAA0B;IAAA,EAAC;IAGpCtB,EAAA,CAAAK,cAAA,eAAU;IAAAL,EAAA,CAAAM,MAAA,GAAkD;IAAAN,EAAA,CAAAO,YAAA,EAAW;IAEzEP,EAAA,CAAAuB,UAAA,KAAAC,uDAAA,wBAEY;IACZxB,EAAA,CAAAuB,UAAA,KAAAE,uDAAA,wBAEY;IACZzB,EAAA,CAAAuB,UAAA,KAAAG,uDAAA,wBAEY;IACd1B,EAAA,CAAAO,YAAA,EAAiB;IAGjBP,EAAA,CAAAK,cAAA,yBAAwD;IAC3CL,EAAA,CAAAM,MAAA,wBAAgB;IAAAN,EAAA,CAAAO,YAAA,EAAY;IACvCP,EAAA,CAAAC,SAAA,iBAK8B;IAC9BD,EAAA,CAAAK,cAAA,oBAAoB;IAAAL,EAAA,CAAAM,MAAA,YAAI;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACnCP,EAAA,CAAAK,cAAA,kBAM4C;IAF1CL,EAAA,CAAAY,UAAA,mBAAAe,oEAAA;MAAA3B,EAAA,CAAAc,aAAA,CAAAC,IAAA;MAAA,MAAAa,OAAA,GAAA5B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAU,OAAA,CAAAC,+BAAA,EAAiC;IAAA,EAAC;IAG3C7B,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAM,MAAA,IAAyD;IAAAN,EAAA,CAAAO,YAAA,EAAW;IAEhFP,EAAA,CAAAuB,UAAA,KAAAO,uDAAA,wBAEY;IACZ9B,EAAA,CAAAuB,UAAA,KAAAQ,uDAAA,wBAEY;IACd/B,EAAA,CAAAO,YAAA,EAAiB;IAGjBP,EAAA,CAAAK,cAAA,kBAKuB;IACrBL,EAAA,CAAAuB,UAAA,KAAAS,yDAAA,0BAAyD;IACzDhC,EAAA,CAAAuB,UAAA,KAAAU,kDAAA,mBAA4C;IAC9CjC,EAAA,CAAAO,YAAA,EAAS;;;;IAnELP,EAAA,CAAAE,UAAA,cAAAgC,MAAA,CAAAC,iBAAA,CAA+B;IAM/BnC,EAAA,CAAAQ,SAAA,GAA2C;IAA3CR,EAAA,CAAAE,UAAA,SAAAgC,MAAA,CAAAE,YAAA,uBAA2C;IAU3CpC,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAqC,WAAA,+BAAmC,iBAAAH,MAAA,CAAAE,YAAA;IAEzBpC,EAAA,CAAAQ,SAAA,GAAkD;IAAlDR,EAAA,CAAAS,iBAAA,CAAAyB,MAAA,CAAAE,YAAA,mCAAkD;IAElDpC,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAE,UAAA,SAAAgC,MAAA,CAAAI,QAAA,yBAAsC;IAGtCtC,EAAA,CAAAQ,SAAA,GAAuC;IAAvCR,EAAA,CAAAE,UAAA,SAAAgC,MAAA,CAAAI,QAAA,0BAAuC;IAGvCtC,EAAA,CAAAQ,SAAA,GAA8C;IAA9CR,EAAA,CAAAE,UAAA,SAAAgC,MAAA,CAAAI,QAAA,iCAA8C;IAUxDtC,EAAA,CAAAQ,SAAA,GAAkD;IAAlDR,EAAA,CAAAE,UAAA,SAAAgC,MAAA,CAAAK,mBAAA,uBAAkD;IAUlDvC,EAAA,CAAAQ,SAAA,GAA2C;IAA3CR,EAAA,CAAAqC,WAAA,uCAA2C,iBAAAH,MAAA,CAAAK,mBAAA;IAEjCvC,EAAA,CAAAQ,SAAA,GAAyD;IAAzDR,EAAA,CAAAS,iBAAA,CAAAyB,MAAA,CAAAK,mBAAA,mCAAyD;IAEzDvC,EAAA,CAAAQ,SAAA,GAA6C;IAA7CR,EAAA,CAAAE,UAAA,SAAAgC,MAAA,CAAAI,QAAA,gCAA6C;IAG7CtC,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAE,UAAA,SAAAgC,MAAA,CAAAM,YAAA,qBAAsC;IAWlDxC,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAE,UAAA,aAAAgC,MAAA,CAAAO,OAAA,CAAoB;IACQzC,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAAE,UAAA,SAAAgC,MAAA,CAAAO,OAAA,CAAa;IAClCzC,EAAA,CAAAQ,SAAA,GAAc;IAAdR,EAAA,CAAAE,UAAA,UAAAgC,MAAA,CAAAO,OAAA,CAAc;;;;;;IAKzBzC,EAAA,CAAAK,cAAA,iBAK+B;IAD7BL,EAAA,CAAAY,UAAA,mBAAA8B,qEAAA;MAAA1C,EAAA,CAAAc,aAAA,CAAA6B,IAAA;MAAA,MAAAC,OAAA,GAAA5C,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA0B,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAEnB7C,EAAA,CAAAM,MAAA,sBACF;IAAAN,EAAA,CAAAO,YAAA,EAAS;;;ADtFX,OAAM,MAAOuC,0BAA0B;EA8CrC;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAjDtB;;;IAGS,KAAAP,OAAO,GAAG,KAAK;IAExB;;;IAGS,KAAArC,KAAK,GAAkB,IAAI;IAEpC;;;IAGS,KAAAO,OAAO,GAAkB,IAAI;IAEtC;;;IAGS,KAAAsC,KAAK,GAAkB,IAAI;IAEpC;;;IAGU,KAAAC,UAAU,GAAG,IAAIpD,YAAY,EAAuC;IAE9E;;;IAGU,KAAAqD,KAAK,GAAG,IAAIrD,YAAY,EAAQ;IAO1C;;;IAGA,KAAAsC,YAAY,GAAG,IAAI;IAEnB;;;IAGA,KAAAG,mBAAmB,GAAG,IAAI;EAMY;EAEtC;;;;EAIAa,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEA;;;EAGQA,QAAQA,CAAA;IACd,IAAI,CAAClB,iBAAiB,GAAG,IAAI,CAACa,EAAE,CAACM,KAAK,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACbxD,UAAU,CAACyD,QAAQ,EACnBzD,UAAU,CAAC0D,SAAS,CAAC,CAAC,CAAC,EACvB,IAAI,CAACC,yBAAyB,CAC/B,CAAC;MACFC,eAAe,EAAE,CAAC,EAAE,EAAE5D,UAAU,CAACyD,QAAQ;KAC1C,EAAE;MAAEI,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEA;;;;;EAKQH,yBAAyBA,CAACI,OAAwB;IACxD,MAAMC,KAAK,GAAWD,OAAO,CAACC,KAAK,IAAI,EAAE;IAEzC,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI;;IAGb,MAAMC,YAAY,GAAG,QAAQ,CAACC,IAAI,CAACF,KAAK,CAAC;IACzC,MAAMG,YAAY,GAAG,QAAQ,CAACD,IAAI,CAACF,KAAK,CAAC;IACzC,MAAMI,UAAU,GAAG,QAAQ,CAACF,IAAI,CAACF,KAAK,CAAC;IACvC,MAAMK,cAAc,GAAG,wCAAwC,CAACH,IAAI,CAACF,KAAK,CAAC;IAE3E,MAAMM,aAAa,GAAGL,YAAY,IAAIE,YAAY,IAAIC,UAAU,IAAIC,cAAc;IAElF,OAAO,CAACC,aAAa,GAAG;MAAEC,gBAAgB,EAAE;IAAI,CAAE,GAAG,IAAI;EAC3D;EAEA;;;;;EAKQT,sBAAsBA,CAACP,KAAsB;IACnD,MAAMC,QAAQ,GAAGD,KAAK,CAACiB,GAAG,CAAC,UAAU,CAAC,EAAER,KAAK;IAC7C,MAAMJ,eAAe,GAAGL,KAAK,CAACiB,GAAG,CAAC,iBAAiB,CAAC,EAAER,KAAK;IAE3D,OAAOR,QAAQ,KAAKI,eAAe,GAAG,IAAI,GAAG;MAAEa,gBAAgB,EAAE;IAAI,CAAE;EACzE;EAEA;;;EAGArD,QAAQA,CAAA;IACN,IAAI,IAAI,CAACgB,iBAAiB,CAACsC,OAAO,IAAI,IAAI,CAAChC,OAAO,IAAI,CAAC,IAAI,CAACQ,KAAK,EAAE;MACjE;MACA,IAAI,CAACd,iBAAiB,CAACuC,gBAAgB,EAAE;MACzC;;IAGF,MAAM;MAAEnB;IAAQ,CAAE,GAAG,IAAI,CAACpB,iBAAiB,CAAC4B,KAAK;IACjD,IAAI,CAACb,UAAU,CAACyB,IAAI,CAAC;MAAEpB,QAAQ;MAAEN,KAAK,EAAE,IAAI,CAACA;IAAK,CAAE,CAAC;EACvD;EAEA;;;EAGAJ,OAAOA,CAAA;IACL,IAAI,CAACM,KAAK,CAACwB,IAAI,EAAE;EACnB;EAEA;;;;;;EAMArC,QAAQA,CAACsC,WAAmB,EAAEC,SAAiB;IAC7C,MAAMf,OAAO,GAAG,IAAI,CAAC3B,iBAAiB,CAACoC,GAAG,CAACK,WAAW,CAAC;IACvD,OAAO,CAAC,EAAEd,OAAO,IAAIA,OAAO,CAACgB,OAAO,IAAIhB,OAAO,CAACxB,QAAQ,CAACuC,SAAS,CAAC,CAAC;EACtE;EAEA;;;;;EAKArC,YAAYA,CAACqC,SAAiB;IAC5B,OAAO,IAAI,CAAC1C,iBAAiB,CAAC2C,OAAO,IAAI,IAAI,CAAC3C,iBAAiB,CAACG,QAAQ,CAACuC,SAAS,CAAC;EACrF;EAEA;;;EAGAvD,wBAAwBA,CAAA;IACtB,IAAI,CAACc,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGAP,+BAA+BA,CAAA;IAC7B,IAAI,CAACU,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;;;uBAjKWO,0BAA0B,EAAA9C,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA1BnC,0BAA0B;MAAAoC,SAAA;MAAAC,MAAA;QAAA1C,OAAA;QAAArC,KAAA;QAAAO,OAAA;QAAAsC,KAAA;MAAA;MAAAmC,OAAA;QAAAlC,UAAA;QAAAC,KAAA;MAAA;MAAAkC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZvC1F,EAAA,CAAAK,cAAA,aAA2C;UAEzCL,EAAA,CAAAuB,UAAA,IAAAqE,uDAAA,+BAGoB;UAGpB5F,EAAA,CAAAuB,UAAA,IAAAsE,yCAAA,iBAGM;UAGN7F,EAAA,CAAAuB,UAAA,IAAAuE,uDAAA,+BAGoB;UAGpB9F,EAAA,CAAAuB,UAAA,IAAAwE,0CAAA,oBAoEO;UAGP/F,EAAA,CAAAuB,UAAA,IAAAyE,4CAAA,oBAOS;UACXhG,EAAA,CAAAO,YAAA,EAAM;;;UAhGDP,EAAA,CAAAQ,SAAA,GAAW;UAAXR,EAAA,CAAAE,UAAA,SAAAyF,GAAA,CAAAvF,KAAA,CAAW;UAKRJ,EAAA,CAAAQ,SAAA,GAAa;UAAbR,EAAA,CAAAE,UAAA,SAAAyF,GAAA,CAAAhF,OAAA,CAAa;UAOhBX,EAAA,CAAAQ,SAAA,GAAwB;UAAxBR,EAAA,CAAAE,UAAA,UAAAyF,GAAA,CAAA1C,KAAA,KAAA0C,GAAA,CAAAhF,OAAA,CAAwB;UAKgEX,EAAA,CAAAQ,SAAA,GAAuB;UAAvBR,EAAA,CAAAE,UAAA,SAAAyF,GAAA,CAAA1C,KAAA,KAAA0C,GAAA,CAAAhF,OAAA,CAAuB;UAwE/GX,EAAA,CAAAQ,SAAA,GAAuB;UAAvBR,EAAA,CAAAE,UAAA,SAAAyF,GAAA,CAAAhF,OAAA,KAAAgF,GAAA,CAAA1C,KAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}