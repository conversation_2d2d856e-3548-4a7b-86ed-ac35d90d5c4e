/**
 * Task summary component styles
 */

/* Container for summary cards */
.summary-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 16px;
}

/* Base summary card styles */
.summary-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
}

/* Card content layout */
.card-content {
  display: flex;
  align-items: center;
}

/* Card icon styles */
.card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 16px;
  
  mat-icon {
    font-size: 24px;
    height: 24px;
    width: 24px;
    color: #fff;
  }
}

/* Card data styles */
.card-data {
  display: flex;
  flex-direction: column;
}

/* Card value (number) */
.card-value {
  font-size: 1.8rem;
  font-weight: 500;
  line-height: 1.2;
}

/* Card label */
.card-label {
  font-size: 0.9rem;
  color: #666;
}

/* Total card specific styles */
.total-card {
  .card-icon {
    background-color: #3f51b5;
  }
  
  .card-value {
    color: #3f51b5;
  }
}

/* Todo card specific styles */
.todo-card {
  .card-icon {
    background-color: #ff9800;
  }
  
  .card-value {
    color: #ff9800;
  }
}

/* In progress card specific styles */
.in-progress-card {
  .card-icon {
    background-color: #2196f3;
  }
  
  .card-value {
    color: #2196f3;
  }
}

/* Done card specific styles */
.done-card {
  .card-icon {
    background-color: #4caf50;
  }
  
  .card-value {
    color: #4caf50;
  }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .summary-container {
    grid-template-columns: 1fr 1fr;
  }
  
  .card-value {
    font-size: 1.5rem;
  }
}
