{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nexport class NotFoundPageComponent {\n  /**\n   * Constructor\n   * @param router - Router service for navigation\n   */\n  constructor(router) {\n    this.router = router;\n  }\n  /**\n   * Navigates back to the home page\n   */\n  goToHome() {\n    this.router.navigate(['/']);\n  }\n  static {\n    this.ɵfac = function NotFoundPageComponent_Factory(t) {\n      return new (t || NotFoundPageComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotFoundPageComponent,\n      selectors: [[\"app-not-found-page\"]],\n      decls: 12,\n      vars: 0,\n      consts: [[1, \"not-found-container\"], [1, \"not-found-content\"], [1, \"error-code\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function NotFoundPageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"404\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"h1\");\n          i0.ɵɵtext(5, \"Page Not Found\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\");\n          i0.ɵɵtext(7, \"The page you are looking for does not exist or has been moved.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function NotFoundPageComponent_Template_button_click_8_listener() {\n            return ctx.goToHome();\n          });\n          i0.ɵɵelementStart(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Return to Home \");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [i2.MatButton, i3.MatIcon],\n      styles: [\".not-found-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: calc(100vh - 200px);\\n  padding: 2rem;\\n}\\n\\n.not-found-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 500px;\\n}\\n.not-found-content[_ngcontent-%COMP%]   .error-code[_ngcontent-%COMP%] {\\n  font-size: 8rem;\\n  font-weight: 700;\\n  color: #3f51b5;\\n  line-height: 1;\\n  margin-bottom: 1rem;\\n  opacity: 0.8;\\n}\\n.not-found-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 1rem;\\n  color: #333;\\n}\\n.not-found-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #666;\\n  margin-bottom: 2rem;\\n}\\n.not-found-content[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvbm90LWZvdW5kL3BhZ2VzL25vdC1mb3VuZC1wYWdlL25vdC1mb3VuZC1wYWdlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSwrQkFBQTtFQUNBLGFBQUE7QUFBRjs7QUFHQTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7QUFBRjtBQUVFO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7QUFBSjtBQUdFO0VBQ0UsZUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtBQURKO0FBSUU7RUFDRSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtBQUZKO0FBTUk7RUFDRSxvQkFBQTtBQUpOIiwic291cmNlc0NvbnRlbnQiOlsiLy8gTm90IEZvdW5kIHBhZ2Ugc3R5bGVzXG4ubm90LWZvdW5kLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gMjAwcHgpO1xuICBwYWRkaW5nOiAycmVtO1xufVxuXG4ubm90LWZvdW5kLWNvbnRlbnQge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1heC13aWR0aDogNTAwcHg7XG4gIFxuICAuZXJyb3ItY29kZSB7XG4gICAgZm9udC1zaXplOiA4cmVtO1xuICAgIGZvbnQtd2VpZ2h0OiA3MDA7XG4gICAgY29sb3I6ICMzZjUxYjU7XG4gICAgbGluZS1oZWlnaHQ6IDE7XG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgICBvcGFjaXR5OiAwLjg7XG4gIH1cbiAgXG4gIGgxIHtcbiAgICBmb250LXNpemU6IDJyZW07XG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgICBjb2xvcjogIzMzMztcbiAgfVxuICBcbiAgcCB7XG4gICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgY29sb3I6ICM2NjY7XG4gICAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgfVxuICBcbiAgYnV0dG9uIHtcbiAgICBtYXQtaWNvbiB7XG4gICAgICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcbiAgICB9XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NotFoundPageComponent", "constructor", "router", "goToHome", "navigate", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "NotFoundPageComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "NotFoundPageComponent_Template_button_click_8_listener"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\not-found\\pages\\not-found-page\\not-found-page.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\not-found\\pages\\not-found-page\\not-found-page.component.html"], "sourcesContent": ["/**\n * Not Found Page component\n * Displays a 404 error page when a route is not found\n */\nimport { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-not-found-page',\n  templateUrl: './not-found-page.component.html',\n  styleUrls: ['./not-found-page.component.scss']\n})\nexport class NotFoundPageComponent {\n  /**\n   * Constructor\n   * @param router - Router service for navigation\n   */\n  constructor(private router: Router) {}\n\n  /**\n   * Navigates back to the home page\n   */\n  goToHome(): void {\n    this.router.navigate(['/']);\n  }\n}\n", "<div class=\"not-found-container\">\n  <div class=\"not-found-content\">\n    <div class=\"error-code\">404</div>\n    <h1>Page Not Found</h1>\n    <p>The page you are looking for does not exist or has been moved.</p>\n    <button mat-raised-button color=\"primary\" (click)=\"goToHome()\">\n      <mat-icon>home</mat-icon>\n      Return to Home\n    </button>\n  </div>\n</div>\n"], "mappings": ";;;;AAYA,OAAM,MAAOA,qBAAqB;EAChC;;;;EAIAC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErC;;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;;;uBAZWJ,qBAAqB,EAAAK,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArBR,qBAAqB;MAAAS,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlCV,EAAA,CAAAY,cAAA,aAAiC;UAELZ,EAAA,CAAAa,MAAA,UAAG;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACjCd,EAAA,CAAAY,cAAA,SAAI;UAAAZ,EAAA,CAAAa,MAAA,qBAAc;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvBd,EAAA,CAAAY,cAAA,QAAG;UAAAZ,EAAA,CAAAa,MAAA,qEAA8D;UAAAb,EAAA,CAAAc,YAAA,EAAI;UACrEd,EAAA,CAAAY,cAAA,gBAA+D;UAArBZ,EAAA,CAAAe,UAAA,mBAAAC,uDAAA;YAAA,OAASL,GAAA,CAAAb,QAAA,EAAU;UAAA,EAAC;UAC5DE,EAAA,CAAAY,cAAA,eAAU;UAAAZ,EAAA,CAAAa,MAAA,YAAI;UAAAb,EAAA,CAAAc,YAAA,EAAW;UACzBd,EAAA,CAAAa,MAAA,wBACF;UAAAb,EAAA,CAAAc,YAAA,EAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}