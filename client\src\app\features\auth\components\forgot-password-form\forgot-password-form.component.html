<!-- Forgot password form container -->
<div class="forgot-password-form-container">
  <!-- Error message -->
  <app-error-message 
    *ngIf="error" 
    [message]="error">
  </app-error-message>

  <!-- Success message -->
  <div *ngIf="success" class="success-message">
    <mat-icon>check_circle</mat-icon>
    <span>{{ success }}</span>
  </div>

  <!-- Forgot password form -->
  <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()" class="forgot-password-form" *ngIf="!success">
    <!-- Email field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Email</mat-label>
      <input 
        matInput 
        type="email" 
        formControlName="email" 
        placeholder="Enter your email"
        autocomplete="email">
      <mat-icon matPrefix>email</mat-icon>
      <mat-error *ngIf="hasError('email', 'required')">
        Email is required
      </mat-error>
      <mat-error *ngIf="hasError('email', 'email')">
        Please enter a valid email address
      </mat-error>
    </mat-form-field>

    <!-- Submit button -->
    <button 
      mat-raised-button 
      color="primary" 
      type="submit" 
      class="submit-button"
      [disabled]="loading">
      <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
      <span *ngIf="!loading">Reset Password</span>
    </button>

    <!-- Login link -->
    <div class="login-link">
      Remember your password?
      <a href="javascript:void(0)" (click)="onLogin()" (keydown.enter)="onLogin()" (keydown.space)="onLogin()" tabindex="0" role="button">Back to Login</a>
    </div>
  </form>

  <!-- Back to login button (shown after success) -->
  <button 
    *ngIf="success"
    mat-raised-button 
    color="primary" 
    (click)="onLogin()" 
    class="back-to-login-button">
    Back to Login
  </button>
</div>
