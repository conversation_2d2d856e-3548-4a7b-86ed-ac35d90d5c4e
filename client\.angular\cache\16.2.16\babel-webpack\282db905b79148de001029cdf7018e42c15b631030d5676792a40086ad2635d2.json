{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class UserService {\n  /**\n   * Constructor with dependency injection\n   * @param apiService - Base API service for HTTP requests\n   */\n  constructor(apiService) {\n    this.apiService = apiService;\n    /**\n     * API endpoint path\n     */\n    this.endpoint = 'users';\n  }\n  /**\n   * Get all users\n   * @returns Observable with array of users\n   */\n  getUsers() {\n    return this.apiService.get(this.endpoint).pipe(map(response => response.data));\n  }\n  /**\n   * Get a user by ID\n   * @param id - User ID\n   * @returns Observable with user data\n   */\n  getUser(id) {\n    return this.apiService.get(`${this.endpoint}/${id}`).pipe(map(response => response.data));\n  }\n  /**\n   * Update user profile\n   * @param id - User ID\n   * @param userData - Updated user data\n   * @returns Observable with updated user\n   */\n  updateUser(id, userData) {\n    return this.apiService.put(`${this.endpoint}/${id}`, userData).pipe(map(response => response.data));\n  }\n  /**\n   * Update user password\n   * @param id - User ID\n   * @param currentPassword - Current password\n   * @param newPassword - New password\n   * @returns Observable with success status\n   */\n  updatePassword(id, currentPassword, newPassword) {\n    return this.apiService.put(`${this.endpoint}/${id}/password`, {\n      currentPassword,\n      newPassword\n    }).pipe(map(response => response.success));\n  }\n  static {\n    this.ɵfac = function UserService_Factory(t) {\n      return new (t || UserService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "UserService", "constructor", "apiService", "endpoint", "getUsers", "get", "pipe", "response", "data", "getUser", "id", "updateUser", "userData", "put", "updatePassword", "currentPassword", "newPassword", "success", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\services\\user.service.ts"], "sourcesContent": ["/**\n * User Service\n * Handles API communication for user-related operations\n */\nimport { Injectable } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { User } from '../models/user.model';\nimport { ApiResponse } from '../models/api-response.model';\nimport { ApiService } from './api.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserService {\n  /**\n   * API endpoint path\n   */\n  private endpoint = 'users';\n\n  /**\n   * Constructor with dependency injection\n   * @param apiService - Base API service for HTTP requests\n   */\n  constructor(private apiService: ApiService) {}\n\n  /**\n   * Get all users\n   * @returns Observable with array of users\n   */\n  getUsers(): Observable<User[]> {\n    return this.apiService.get<ApiResponse<User[]>>(this.endpoint)\n      .pipe(\n        map((response: ApiResponse<User[]>) => response.data)\n      );\n  }\n\n  /**\n   * Get a user by ID\n   * @param id - User ID\n   * @returns Observable with user data\n   */\n  getUser(id: string): Observable<User> {\n    return this.apiService.get<ApiResponse<User>>(`${this.endpoint}/${id}`)\n      .pipe(\n        map((response: ApiResponse<User>) => response.data)\n      );\n  }\n\n  /**\n   * Update user profile\n   * @param id - User ID\n   * @param userData - Updated user data\n   * @returns Observable with updated user\n   */\n  updateUser(id: string, userData: Partial<User>): Observable<User> {\n    return this.apiService.put<ApiResponse<User>>(`${this.endpoint}/${id}`, userData)\n      .pipe(\n        map((response: ApiResponse<User>) => response.data)\n      );\n  }\n\n  /**\n   * Update user password\n   * @param id - User ID\n   * @param currentPassword - Current password\n   * @param newPassword - New password\n   * @returns Observable with success status\n   */\n  updatePassword(id: string, currentPassword: string, newPassword: string): Observable<boolean> {\n    return this.apiService.put<ApiResponse<{}>>(`${this.endpoint}/${id}/password`, {\n      currentPassword,\n      newPassword\n    }).pipe(\n      map((response: ApiResponse<{}>) => response.success)\n    );\n  }\n}\n"], "mappings": "AAMA,SAASA,GAAG,QAAQ,gBAAgB;;;AAQpC,OAAM,MAAOC,WAAW;EAMtB;;;;EAIAC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAT9B;;;IAGQ,KAAAC,QAAQ,GAAG,OAAO;EAMmB;EAE7C;;;;EAIAC,QAAQA,CAAA;IACN,OAAO,IAAI,CAACF,UAAU,CAACG,GAAG,CAAsB,IAAI,CAACF,QAAQ,CAAC,CAC3DG,IAAI,CACHP,GAAG,CAAEQ,QAA6B,IAAKA,QAAQ,CAACC,IAAI,CAAC,CACtD;EACL;EAEA;;;;;EAKAC,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACR,UAAU,CAACG,GAAG,CAAoB,GAAG,IAAI,CAACF,QAAQ,IAAIO,EAAE,EAAE,CAAC,CACpEJ,IAAI,CACHP,GAAG,CAAEQ,QAA2B,IAAKA,QAAQ,CAACC,IAAI,CAAC,CACpD;EACL;EAEA;;;;;;EAMAG,UAAUA,CAACD,EAAU,EAAEE,QAAuB;IAC5C,OAAO,IAAI,CAACV,UAAU,CAACW,GAAG,CAAoB,GAAG,IAAI,CAACV,QAAQ,IAAIO,EAAE,EAAE,EAAEE,QAAQ,CAAC,CAC9EN,IAAI,CACHP,GAAG,CAAEQ,QAA2B,IAAKA,QAAQ,CAACC,IAAI,CAAC,CACpD;EACL;EAEA;;;;;;;EAOAM,cAAcA,CAACJ,EAAU,EAAEK,eAAuB,EAAEC,WAAmB;IACrE,OAAO,IAAI,CAACd,UAAU,CAACW,GAAG,CAAkB,GAAG,IAAI,CAACV,QAAQ,IAAIO,EAAE,WAAW,EAAE;MAC7EK,eAAe;MACfC;KACD,CAAC,CAACV,IAAI,CACLP,GAAG,CAAEQ,QAAyB,IAAKA,QAAQ,CAACU,OAAO,CAAC,CACrD;EACH;;;uBA9DWjB,WAAW,EAAAkB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXrB,WAAW;MAAAsB,OAAA,EAAXtB,WAAW,CAAAuB,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}