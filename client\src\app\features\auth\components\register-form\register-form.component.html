<!-- Register form container -->
<div class="register-form-container">
  <!-- Error message -->
  <app-error-message 
    *ngIf="error" 
    [message]="error">
  </app-error-message>

  <!-- Register form -->
  <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
    <!-- Name field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Full Name</mat-label>
      <input 
        matInput 
        type="text" 
        formControlName="name" 
        placeholder="Enter your full name"
        autocomplete="name">
      <mat-icon matPrefix>person</mat-icon>
      <mat-error *ngIf="hasError('name', 'required')">
        Name is required
      </mat-error>
      <mat-error *ngIf="hasError('name', 'minlength')">
        Name must be at least 2 characters
      </mat-error>
    </mat-form-field>

    <!-- Email field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Email</mat-label>
      <input 
        matInput 
        type="email" 
        formControlName="email" 
        placeholder="Enter your email"
        autocomplete="email">
      <mat-icon matPrefix>email</mat-icon>
      <mat-error *ngIf="hasError('email', 'required')">
        Email is required
      </mat-error>
      <mat-error *ngIf="hasError('email', 'email')">
        Please enter a valid email address
      </mat-error>
    </mat-form-field>

    <!-- Password field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Password</mat-label>
      <input 
        matInput 
        [type]="hidePassword ? 'password' : 'text'" 
        formControlName="password"
        placeholder="Create a password"
        autocomplete="new-password">
      <mat-icon matPrefix>lock</mat-icon>
      <button 
        mat-icon-button 
        matSuffix 
        type="button"
        (click)="togglePasswordVisibility()" 
        [attr.aria-label]="'Hide password'" 
        [attr.aria-pressed]="hidePassword">
        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
      </button>
      <mat-error *ngIf="hasError('password', 'required')">
        Password is required
      </mat-error>
      <mat-error *ngIf="hasError('password', 'minlength')">
        Password must be at least 8 characters
      </mat-error>
      <mat-error *ngIf="hasError('password', 'passwordStrength')">
        Password must include uppercase, lowercase, number, and special character
      </mat-error>
    </mat-form-field>

    <!-- Confirm Password field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Confirm Password</mat-label>
      <input 
        matInput 
        [type]="hideConfirmPassword ? 'password' : 'text'" 
        formControlName="confirmPassword"
        placeholder="Confirm your password"
        autocomplete="new-password">
      <mat-icon matPrefix>lock</mat-icon>
      <button 
        mat-icon-button 
        matSuffix 
        type="button"
        (click)="toggleConfirmPasswordVisibility()" 
        [attr.aria-label]="'Hide confirm password'" 
        [attr.aria-pressed]="hideConfirmPassword">
        <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
      </button>
      <mat-error *ngIf="hasError('confirmPassword', 'required')">
        Please confirm your password
      </mat-error>
      <mat-error *ngIf="hasFormError('passwordMismatch')">
        Passwords do not match
      </mat-error>
    </mat-form-field>

    <!-- Terms and conditions checkbox -->
    <div class="terms-container">
      <mat-checkbox formControlName="termsAccepted" color="primary">
        I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
      </mat-checkbox>
      <mat-error *ngIf="hasError('termsAccepted', 'requiredTrue')" class="terms-error">
        You must accept the terms and conditions
      </mat-error>
    </div>

    <!-- Submit button -->
    <button 
      mat-raised-button 
      color="primary" 
      type="submit" 
      class="submit-button"
      [disabled]="loading">
      <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
      <span *ngIf="!loading">Register</span>
    </button>

    <!-- Login link -->
    <div class="login-link">
      Already have an account?
      <a href="javascript:void(0)" (click)="onLogin()" (keydown.enter)="onLogin()" (keydown.space)="onLogin()" tabindex="0" role="button">Login</a>
    </div>
  </form>
</div>
