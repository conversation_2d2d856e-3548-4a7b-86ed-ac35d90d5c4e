/**
 * Task List Component
 * Displays a list of tasks with filtering and sorting options
 */
import { Component, OnInit, Input, Output, EventEmitter, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Task } from '../../../../core/models/task.model';
import { TaskFilter } from '../../../../core/models/task-filter.model';

@Component({
  selector: 'app-task-list',
  templateUrl: './task-list.component.html',
  styleUrls: ['./task-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskListComponent implements OnInit {
  /**
   * Input array of tasks to display
   */
  @Input() tasks: Task[] = [];
  
  /**
   * Loading state indicator
   */
  @Input() loading = false;
  
  /**
   * Error message if loading failed
   */
  @Input() error: string | null = null;
  
  /**
   * Current filter applied to tasks
   */
  @Input() filter: TaskFilter = {};
  
  /**
   * Event emitted when a task is selected
   */
  @Output() taskSelected = new EventEmitter<Task>();
  
  /**
   * Event emitted when a task is deleted
   */
  @Output() taskDeleted = new EventEmitter<string>();
  
  /**
   * Event emitted when a task status is changed
   */
  @Output() statusChanged = new EventEmitter<{ taskId: string; status: string }>();
  
  /**
   * Event emitted when filter is changed
   */
  @Output() filterChanged = new EventEmitter<TaskFilter>();
  
  /**
   * Event emitted when refresh is requested
   */
  @Output() refresh = new EventEmitter<void>();
  
  /**
   * Current sort field
   */
  sortField = 'dueDate';
  
  /**
   * Current sort direction
   */
  sortDirection = 'asc';
  
  /**
   * Filtered and sorted tasks
   */
  displayedTasks: Task[] = [];

  /**
   * Constructor with dependency injection
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(private cdr: ChangeDetectorRef) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Initial processing of tasks
   */
  ngOnInit(): void {
    this.processTasks();
  }

  /**
   * Lifecycle hook that is called when inputs change
   * Updates displayed tasks when inputs change
   */
  ngOnChanges(): void {
    this.processTasks();
  }

  /**
   * Process tasks with current filter and sort settings
   */
  processTasks(): void {
    // Make a copy to avoid modifying the input array
    this.displayedTasks = [...this.tasks];
    
    // Apply sorting
    this.sortTasks();
    
    // Mark for check since we're using OnPush strategy
    this.cdr.markForCheck();
  }

  /**
   * Sort tasks based on current sort field and direction
   */
  sortTasks(): void {
    this.displayedTasks.sort((a, b) => {
      let comparison = 0;
      
      // Sort by the selected field
      switch (this.sortField) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'priority':
          const priorityOrder = { high: 0, medium: 1, low: 2 };
          comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
          break;
        case 'status':
          const statusOrder = { todo: 0, in_progress: 1, review: 2, done: 3 };
          comparison = statusOrder[a.status] - statusOrder[b.status];
          break;
        case 'dueDate':
          // Handle null/undefined due dates
          if (!a.dueDate && !b.dueDate) comparison = 0;
          else if (!a.dueDate) comparison = 1;
          else if (!b.dueDate) comparison = -1;
          else comparison = new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
          break;
        default:
          comparison = 0;
      }
      
      // Apply sort direction
      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
  }

  /**
   * Change the sort field and direction
   * @param field - Field to sort by
   */
  onSort(field: string): void {
    // If clicking the same field, toggle direction
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      // New field, default to ascending
      this.sortField = field;
      this.sortDirection = 'asc';
    }
    
    // Re-sort the tasks
    this.sortTasks();
  }

  /**
   * Handle task selection
   * @param task - Selected task
   */
  onTaskSelect(task: Task): void {
    this.taskSelected.emit(task);
  }

  /**
   * Handle task deletion
   * @param taskId - ID of task to delete
   */
  onTaskDelete(taskId: string): void {
    this.taskDeleted.emit(taskId);
  }

  /**
   * Handle task status change
   * @param taskId - ID of task
   * @param status - New status
   */
  onStatusChange(taskId: string, status: string): void {
    this.statusChanged.emit({ taskId, status });
  }

  /**
   * Handle filter changes
   * @param filter - New filter
   */
  onFilterChange(filter: TaskFilter): void {
    this.filterChanged.emit(filter);
  }

  /**
   * Request data refresh
   */
  onRefresh(): void {
    this.refresh.emit();
  }
}
