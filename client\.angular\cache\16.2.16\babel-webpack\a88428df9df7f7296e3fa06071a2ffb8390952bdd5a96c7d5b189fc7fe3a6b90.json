{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n// Define routes for the not-found feature\nconst routes = [{\n  path: '',\n  loadChildren: () => import('./pages/not-found-page/not-found-page.module').then(m => m.NotFoundPageModule)\n}];\nexport class NotFoundModule {\n  static {\n    this.ɵfac = function NotFoundModule_Factory(t) {\n      return new (t || NotFoundModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: NotFoundModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(NotFoundModule, {\n    imports: [CommonModule, SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "SharedModule", "routes", "path", "loadChildren", "then", "m", "NotFoundPageModule", "NotFoundModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\not-found\\not-found.module.ts"], "sourcesContent": ["/**\n * Not Found module for 404 page\n * Contains components for displaying 404 error page\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n\n// Define routes for the not-found feature\nconst routes: Routes = [\n  {\n    path: '',\n    loadChildren: () => import('./pages/not-found-page/not-found-page.module').then(m => m.NotFoundPageModule)\n  }\n];\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class NotFoundModule { }\n"], "mappings": "AAKA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;;;AAEzD;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,kBAAkB;CAC1G,CACF;AAUD,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBALvBT,YAAY,EACZE,YAAY,EACZD,YAAY,CAACS,QAAQ,CAACP,MAAM,CAAC;IAAA;EAAA;;;2EAGpBM,cAAc;IAAAE,OAAA,GALvBX,YAAY,EACZE,YAAY,EAAAU,EAAA,CAAAX,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}