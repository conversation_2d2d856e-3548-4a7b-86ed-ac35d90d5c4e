/**
 * Forgot Password Form Component
 * Handles password reset request with email
 */
import { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-forgot-password-form',
  templateUrl: './forgot-password-form.component.html',
  styleUrls: ['./forgot-password-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ForgotPasswordFormComponent implements OnInit {
  /**
   * Loading state indicator
   */
  @Input() loading = false;
  
  /**
   * Error message from failed request
   */
  @Input() error: string | null = null;
  
  /**
   * Success message after successful request
   */
  @Input() success: string | null = null;
  
  /**
   * Event emitted when form is submitted
   */
  @Output() formSubmit = new EventEmitter<{ email: string }>();
  
  /**
   * Event emitted when login link is clicked
   */
  @Output() login = new EventEmitter<void>();
  
  /**
   * Forgot password form group
   */
  forgotPasswordForm!: FormGroup;

  /**
   * Constructor with dependency injection
   * @param fb - FormBuilder for reactive forms
   */
  constructor(private fb: FormBuilder) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Initializes the forgot password form
   */
  ngOnInit(): void {
    this.initForm();
  }

  /**
   * Initialize forgot password form with validation
   */
  private initForm(): void {
    this.forgotPasswordForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  /**
   * Handle form submission
   */
  onSubmit(): void {
    if (this.forgotPasswordForm.invalid || this.loading) {
      // Mark all fields as touched to trigger validation messages
      this.forgotPasswordForm.markAllAsTouched();
      return;
    }
    
    const { email } = this.forgotPasswordForm.value;
    this.formSubmit.emit({ email });
  }

  /**
   * Handle login link click
   */
  onLogin(): void {
    this.login.emit();
  }

  /**
   * Check if form control has error
   * @param controlName - Name of form control
   * @param errorName - Name of error
   * @returns True if control has error
   */
  hasError(controlName: string, errorName: string): boolean {
    const control = this.forgotPasswordForm.get(controlName);
    return !!(control && control.touched && control.hasError(errorName));
  }
}
