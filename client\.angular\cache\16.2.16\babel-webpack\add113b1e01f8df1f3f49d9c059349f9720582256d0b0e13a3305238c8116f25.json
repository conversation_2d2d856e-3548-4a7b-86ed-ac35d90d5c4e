{"ast": null, "code": "/**\n * Development environment configuration\n * Contains settings specific to the development environment\n */\nexport const environment = {\n  /**\n   * Production flag\n   */\n  production: false,\n  /**\n   * API URL for backend services\n   */\n  apiUrl: 'http://localhost:3000/api',\n  /**\n   * Enable debug tools and logging\n   */\n  enableDebug: true,\n  /**\n   * Version of the application\n   */\n  version: '0.1.0'\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "enableDebug", "version"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\environments\\environment.ts"], "sourcesContent": ["/**\n * Development environment configuration\n * Contains settings specific to the development environment\n */\nexport const environment = {\n  /**\n   * Production flag\n   */\n  production: false,\n  \n  /**\n   * API URL for backend services\n   */\n  apiUrl: 'http://localhost:3000/api',\n  \n  /**\n   * Enable debug tools and logging\n   */\n  enableDebug: true,\n  \n  /**\n   * Version of the application\n   */\n  version: '0.1.0'\n};\n"], "mappings": "AAAA;;;;AAIA,OAAO,MAAMA,WAAW,GAAG;EACzB;;;EAGAC,UAAU,EAAE,KAAK;EAEjB;;;EAGAC,MAAM,EAAE,2BAA2B;EAEnC;;;EAGAC,WAAW,EAAE,IAAI;EAEjB;;;EAGAC,OAAO,EAAE;CACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}