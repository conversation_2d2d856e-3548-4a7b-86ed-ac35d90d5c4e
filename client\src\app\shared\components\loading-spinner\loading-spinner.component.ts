/**
 * Loading Spinner Component
 * Displays a loading indicator for async operations
 */
import { Component, Input, ChangeDetectionStrategy } from '@angular/core';

@Component({
  selector: 'app-loading-spinner',
  templateUrl: './loading-spinner.component.html',
  styleUrls: ['./loading-spinner.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoadingSpinnerComponent {
  /**
   * Diameter of the spinner in pixels
   */
  @Input() diameter = 40;
  
  /**
   * Optional message to display below the spinner
   */
  @Input() message = 'Loading...';
  
  /**
   * Whether to show the loading message
   */
  @Input() showMessage = true;
  
  /**
   * Color of the spinner
   */
  @Input() color: 'primary' | 'accent' | 'warn' = 'primary';
}
