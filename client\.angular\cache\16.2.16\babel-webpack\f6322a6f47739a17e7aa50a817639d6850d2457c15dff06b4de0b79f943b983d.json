{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function isAsyncIterable(obj) {\n  return Symbol.asyncIterator && isFunction(obj === null || obj === void 0 ? void 0 : obj[Symbol.asyncIterator]);\n}", "map": {"version": 3, "names": ["isFunction", "isAsyncIterable", "obj", "Symbol", "asyncIterator"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/esm/internal/util/isAsyncIterable.js"], "sourcesContent": ["import { isFunction } from './isFunction';\nexport function isAsyncIterable(obj) {\n    return Symbol.asyncIterator && isFunction(obj === null || obj === void 0 ? void 0 : obj[Symbol.asyncIterator]);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,eAAeA,CAACC,GAAG,EAAE;EACjC,OAAOC,MAAM,CAACC,aAAa,IAAIJ,UAAU,CAACE,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACC,MAAM,CAACC,aAAa,CAAC,CAAC;AAClH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}