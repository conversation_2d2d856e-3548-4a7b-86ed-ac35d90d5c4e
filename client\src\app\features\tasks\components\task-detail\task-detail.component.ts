/**
 * Task Detail Component
 * Displays detailed information about a task
 */
import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { Task } from '../../../../core/models/task.model';
import { User } from '../../../../core/models/user.model';

@Component({
  selector: 'app-task-detail',
  templateUrl: './task-detail.component.html',
  styleUrls: ['./task-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskDetailComponent {
  /**
   * Input task to display
   */
  @Input() task!: Task;
  
  /**
   * Input list of users for assignee selection
   */
  @Input() users: User[] = [];
  
  /**
   * Loading state indicator
   */
  @Input() loading = false;
  
  /**
   * Event emitted when edit button is clicked
   */
  @Output() edit = new EventEmitter<void>();
  
  /**
   * Event emitted when delete button is clicked
   */
  @Output() delete = new EventEmitter<void>();
  
  /**
   * Event emitted when status is changed
   */
  @Output() statusChange = new EventEmitter<string>();
  
  /**
   * Event emitted when assignee is changed
   */
  @Output() assigneeChange = new EventEmitter<string>();
  
  /**
   * Available task statuses
   */
  statuses = [
    { value: 'todo', label: 'To Do' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'review', label: 'Review' },
    { value: 'done', label: 'Done' }
  ];

  /**
   * Get CSS class for priority
   * @returns CSS class name
   */
  getPriorityClass(): string {
    return `priority-${this.task.priority}`;
  }

  /**
   * Get CSS class for status
   * @returns CSS class name
   */
  getStatusClass(): string {
    return `status-${this.task.status}`;
  }

  /**
   * Get formatted status label
   * @returns Status label
   */
  getStatusLabel(): string {
    const status = this.statuses.find(s => s.value === this.task.status);
    return status ? status.label : this.task.status;
  }

  /**
   * Handle status change
   * @param status - New status
   */
  onStatusChange(status: string): void {
    this.statusChange.emit(status);
  }

  /**
   * Handle assignee change
   * @param assigneeId - New assignee ID
   */
  onAssigneeChange(assigneeId: string): void {
    this.assigneeChange.emit(assigneeId);
  }

  /**
   * Handle edit button click
   */
  onEdit(): void {
    this.edit.emit();
  }

  /**
   * Handle delete button click
   */
  onDelete(): void {
    this.delete.emit();
  }

  /**
   * Check if task is overdue
   * @returns True if task is overdue
   */
  isOverdue(): boolean {
    if (!this.task.dueDate || this.task.status === 'done') {
      return false;
    }
    
    const dueDate = new Date(this.task.dueDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return dueDate < today;
  }

  /**
   * Get assignee name
   * @returns Assignee name or 'Unassigned'
   */
  getAssigneeName(): string {
    if (!this.task.assignee) {
      return 'Unassigned';
    }
    
    const user = this.users.find(u => u.id === this.task.assignee);
    return user ? user.name : 'Unknown User';
  }

  /**
   * Get creator name
   * @returns Creator name or 'Unknown'
   */
  getCreatorName(): string {
    if (!this.task.creator) {
      return 'Unknown';
    }
    
    const user = this.users.find(u => u.id === this.task.creator);
    return user ? user.name : 'Unknown User';
  }
}
