{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Loading Spinner Component\n * Displays a loading indicator for async operations\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\nexport let LoadingSpinnerComponent = class LoadingSpinnerComponent {\n  constructor() {\n    /**\n     * Diameter of the spinner in pixels\n     */\n    this.diameter = 40;\n    /**\n     * Optional message to display below the spinner\n     */\n    this.message = 'Loading...';\n    /**\n     * Whether to show the loading message\n     */\n    this.showMessage = true;\n    /**\n     * Color of the spinner\n     */\n    this.color = 'primary';\n  }\n};\n__decorate([Input()], LoadingSpinnerComponent.prototype, \"diameter\", void 0);\n__decorate([Input()], LoadingSpinnerComponent.prototype, \"message\", void 0);\n__decorate([Input()], LoadingSpinnerComponent.prototype, \"showMessage\", void 0);\n__decorate([Input()], LoadingSpinnerComponent.prototype, \"color\", void 0);\nLoadingSpinnerComponent = __decorate([Component({\n  selector: 'app-loading-spinner',\n  templateUrl: './loading-spinner.component.html',\n  styleUrls: ['./loading-spinner.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], LoadingSpinnerComponent);", "map": {"version": 3, "names": ["Component", "Input", "ChangeDetectionStrategy", "LoadingSpinnerComponent", "constructor", "diameter", "message", "showMessage", "color", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\components\\loading-spinner\\loading-spinner.component.ts"], "sourcesContent": ["/**\n * Loading Spinner Component\n * Displays a loading indicator for async operations\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-loading-spinner',\n  templateUrl: './loading-spinner.component.html',\n  styleUrls: ['./loading-spinner.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class LoadingSpinnerComponent {\n  /**\n   * Diameter of the spinner in pixels\n   */\n  @Input() diameter = 40;\n  \n  /**\n   * Optional message to display below the spinner\n   */\n  @Input() message = 'Loading...';\n  \n  /**\n   * Whether to show the loading message\n   */\n  @Input() showMessage = true;\n  \n  /**\n   * Color of the spinner\n   */\n  @Input() color: 'primary' | 'accent' | 'warn' = 'primary';\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,eAAe;AAQlE,WAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAA7BC,YAAA;IACL;;;IAGS,KAAAC,QAAQ,GAAG,EAAE;IAEtB;;;IAGS,KAAAC,OAAO,GAAG,YAAY;IAE/B;;;IAGS,KAAAC,WAAW,GAAG,IAAI;IAE3B;;;IAGS,KAAAC,KAAK,GAAkC,SAAS;EAC3D;CAAC;AAhBUC,UAAA,EAARR,KAAK,EAAE,C,wDAAe;AAKdQ,UAAA,EAARR,KAAK,EAAE,C,uDAAwB;AAKvBQ,UAAA,EAARR,KAAK,EAAE,C,2DAAoB;AAKnBQ,UAAA,EAARR,KAAK,EAAE,C,qDAAkD;AAnB/CE,uBAAuB,GAAAM,UAAA,EANnCT,SAAS,CAAC;EACTU,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC,CAAC;EAC/CC,eAAe,EAAEX,uBAAuB,CAACY;CAC1C,CAAC,C,EACWX,uBAAuB,CAoBnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}