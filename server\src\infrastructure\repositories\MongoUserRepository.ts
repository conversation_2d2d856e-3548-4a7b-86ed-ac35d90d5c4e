/**
 * MongoDB implementation of User Repository
 * Implements the IUserRepository interface using Mongoose
 */
import mongoose from 'mongoose';
import { IUser, IUserDocument, User } from '../../domain/entities/User';
import { IUserRepository } from '../../domain/repositories/IUserRepository';
import { AppError } from '../../domain/common/AppError';

/**
 * MongoDB implementation of User Repository
 * Handles all data access operations for users
 */
export class MongoUserRepository implements IUserRepository {
  /**
   * Creates a new user in the database
   * @param userData - User data to create
   * @returns Promise resolving to created user
   */
  async create(userData: IUser): Promise<IUserDocument> {
    try {
      // Check if email already exists
      const existingUser = await User.findOne({ email: userData.email });
      if (existingUser) {
        throw new AppError('User with this email already exists', 400);
      }
      
      // Create new user using Mongoose model
      const user = new User(userData);
      return await user.save();
    } catch (error: any) {
      // Handle validation errors
      if (error.name === 'ValidationError') {
        throw new AppError(`Validation error: ${error.message}`, 400);
      }
      // Only rethrow AppErrors, convert others to internal server errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Failed to create user: ${error.message}`, 500);
    }
  }

  /**
   * Finds a user by ID
   * @param id - User ID
   * @returns Promise resolving to found user or null if not found
   */
  async findById(id: string): Promise<IUserDocument | null> {
    try {
      // Validate ID format
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new AppError('Invalid user ID format', 400);
      }
      
      // Find user by ID
      return await User.findById(id);
    } catch (error: any) {
      // Only rethrow AppErrors, convert others to internal server errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error finding user: ${error.message}`, 500);
    }
  }

  /**
   * Finds a user by email
   * @param email - User email
   * @returns Promise resolving to found user or null if not found
   */
  async findByEmail(email: string): Promise<IUserDocument | null> {
    try {
      // Find user by email (case insensitive)
      return await User.findOne({ email: email.toLowerCase() }).select('+password');
    } catch (error: any) {
      throw new AppError(`Error finding user by email: ${error.message}`, 500);
    }
  }

  /**
   * Finds all users with optional filtering
   * @param filter - Optional filter criteria
   * @returns Promise resolving to array of users
   */
  async findAll(filter: Partial<IUser> = {}): Promise<IUserDocument[]> {
    try {
      // Find users with optional filter
      return await User.find(filter).sort({ name: 1 }); // Sort by name ascending
    } catch (error: any) {
      throw new AppError(`Error fetching users: ${error.message}`, 500);
    }
  }

  /**
   * Updates a user by ID
   * @param id - User ID
   * @param updateData - Data to update
   * @returns Promise resolving to updated user or null if not found
   */
  async update(id: string, updateData: Partial<IUser>): Promise<IUserDocument | null> {
    try {
      // Validate ID format
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new AppError('Invalid user ID format', 400);
      }
      
      // Check if trying to update email to one that already exists
      if (updateData.email) {
        const existingUser = await User.findOne({ 
          email: updateData.email,
          _id: { $ne: id } // Exclude current user from check
        });
        
        if (existingUser) {
          throw new AppError('Email already in use', 400);
        }
      }
      
      // Update user with new data and return updated document
      return await User.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true } // Return updated doc and run validators
      );
    } catch (error: any) {
      // Handle validation errors
      if (error.name === 'ValidationError') {
        throw new AppError(`Validation error: ${error.message}`, 400);
      }
      // Only rethrow AppErrors, convert others to internal server errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error updating user: ${error.message}`, 500);
    }
  }

  /**
   * Deletes a user by ID
   * @param id - User ID
   * @returns Promise resolving to true if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    try {
      // Validate ID format
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new AppError('Invalid user ID format', 400);
      }
      
      // Delete user and check if it existed
      const result = await User.findByIdAndDelete(id);
      return result !== null;
    } catch (error: any) {
      // Only rethrow AppErrors, convert others to internal server errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error deleting user: ${error.message}`, 500);
    }
  }
}
