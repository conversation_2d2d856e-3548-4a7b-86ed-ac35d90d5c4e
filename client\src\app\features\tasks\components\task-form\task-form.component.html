<!-- Task form container -->
<div class="task-form-container">
  <!-- Loading state -->
  <div *ngIf="loading" class="task-form-loading">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Task form -->
  <form [formGroup]="taskForm" (ngSubmit)="onSubmit()" class="task-form" *ngIf="!loading">
    <!-- Title field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Title</mat-label>
      <input matInput formControlName="title" placeholder="Task title" required>
      <mat-error *ngIf="taskForm.get('title')?.invalid">
        {{ getErrorMessage('title') }}
      </mat-error>
    </mat-form-field>

    <!-- Description field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Description</mat-label>
      <textarea 
        matInput 
        formControlName="description" 
        placeholder="Task description" 
        rows="4">
      </textarea>
      <mat-error *ngIf="taskForm.get('description')?.invalid">
        {{ getErrorMessage('description') }}
      </mat-error>
    </mat-form-field>

    <!-- Status and priority fields -->
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Status</mat-label>
        <mat-select formControlName="status" required>
          <mat-option *ngFor="let status of statuses" [value]="status.value">
            {{ status.label }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="taskForm.get('status')?.invalid">
          {{ getErrorMessage('status') }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Priority</mat-label>
        <mat-select formControlName="priority" required>
          <mat-option *ngFor="let priority of priorities" [value]="priority.value">
            {{ priority.label }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="taskForm.get('priority')?.invalid">
          {{ getErrorMessage('priority') }}
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Due date and assignee fields -->
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Due Date</mat-label>
        <input matInput [matDatepicker]="picker" formControlName="dueDate">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Assignee</mat-label>
        <mat-select formControlName="assignee">
          <mat-option [value]="''">Unassigned</mat-option>
          <mat-option *ngFor="let user of users" [value]="user.id">
            {{ user.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <!-- Tags field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Tags</mat-label>
      <mat-chip-grid #chipGrid aria-label="Tag selection">
        <mat-chip-row
          *ngFor="let tag of taskForm.get('tags')?.value"
          (removed)="removeTag(tag)">
          {{tag}}
          <button matChipRemove aria-label="remove {{tag}}">
            <mat-icon>cancel</mat-icon>
          </button>
        </mat-chip-row>
        <input
          placeholder="Add tag..."
          [matChipInputFor]="chipGrid"
          [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
          (matChipInputTokenEnd)="addTag($event)">
      </mat-chip-grid>
    </mat-form-field>

    <!-- Form actions -->
    <div class="form-actions">
      <button 
        mat-button 
        type="button" 
        (click)="onCancel()" 
        [disabled]="loading">
        Cancel
      </button>
      <button 
        mat-raised-button 
        color="primary" 
        type="submit" 
        [disabled]="taskForm.invalid || loading">
        {{ task ? 'Update Task' : 'Create Task' }}
      </button>
    </div>
  </form>
</div>
