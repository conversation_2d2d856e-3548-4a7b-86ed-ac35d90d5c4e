/**
 * Task Edit Page Component
 * Page for editing an existing task
 */
import { Component, OnInit, <PERSON><PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TaskService } from '../../../../core/services/task.service';
import { UserService } from '../../../../core/services/user.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { Task } from '../../../../core/models/task.model';
import { User } from '../../../../core/models/user.model';

@Component({
  selector: 'app-task-edit-page',
  templateUrl: './task-edit-page.component.html',
  styleUrls: ['./task-edit-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskEditPageComponent implements OnInit, OnD<PERSON>roy {
  /**
   * Current task being edited
   */
  task: Task | null = null;
  
  /**
   * List of users
   */
  users: User[] = [];
  
  /**
   * Loading state indicator
   */
  loading = false;
  
  /**
   * Error message if loading failed
   */
  error: string | null = null;
  
  /**
   * Subject for unsubscribing from observables
   */
  private destroy$ = new Subject<void>();

  /**
   * Constructor with dependency injection
   * @param taskService - Task service for CRUD operations
   * @param userService - User service for user data
   * @param notificationService - Notification service for displaying messages
   * @param route - Activated route for getting route parameters
   * @param router - Router for navigation
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(
    private taskService: TaskService,
    private userService: UserService,
    private notificationService: NotificationService,
    private route: ActivatedRoute,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Loads task and users
   */
  ngOnInit(): void {
    this.loadTask();
    this.loadUsers();
  }

  /**
   * Lifecycle hook that is called before component destruction
   * Unsubscribes from observables
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load task details
   */
  loadTask(): void {
    const taskId = this.route.snapshot.paramMap.get('id');
    if (!taskId) {
      this.error = 'Task ID not provided';
      return;
    }
    
    this.loading = true;
    this.error = null;
    this.cdr.markForCheck();
    
    this.taskService.getTaskById(taskId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (task: Task) => {
          this.task = task;
          this.loading = false;
          this.cdr.markForCheck();
        },
        error: (err: any) => {
          this.error = 'Failed to load task. It may have been deleted or you do not have permission to edit it.';
          this.loading = false;
          console.error('Error loading task:', err);
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Load users for assignee selection
   */
  loadUsers(): void {
    this.userService.getUsers()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (users: User[]) => {
          this.users = users;
          this.cdr.markForCheck();
        },
        error: (err: any) => {
          console.error('Error loading users:', err);
        }
      });
  }

  /**
   * Handle form submission
   * @param taskData - Updated task data from form
   */
  onFormSubmit(taskData: Partial<Task>): void {
    if (!this.task) return;
    
    this.loading = true;
    this.cdr.markForCheck();
    
    this.taskService.updateTask(this.task.id, taskData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedTask: Task) => {
          this.loading = false;
          this.notificationService.success('Task updated successfully');
          this.router.navigate(['/tasks', updatedTask.id]);
        },
        error: (err: any) => {
          this.loading = false;
          this.notificationService.error('Failed to update task');
          console.error('Error updating task:', err);
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Handle form cancellation
   */
  onFormCancel(): void {
    if (this.task) {
      this.router.navigate(['/tasks', this.task.id]);
    } else {
      this.router.navigate(['/tasks']);
    }
  }
}
