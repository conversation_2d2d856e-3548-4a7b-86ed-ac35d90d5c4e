{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Authentication service\n * Handles user authentication, registration, password reset, and token management\n */\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject, of } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nexport let AuthService = class AuthService {\n  /**\n   * Constructor with dependency injection\n   * @param apiService - Base API service for HTTP requests\n   * @param router - Router for navigation\n   */\n  constructor(apiService, router) {\n    this.apiService = apiService;\n    this.router = router;\n    /**\n     * API endpoint path\n     */\n    this.endpoint = 'auth';\n    // Initialize from localStorage if available\n    const storedUser = localStorage.getItem('currentUser');\n    this.currentUserSubject = new BehaviorSubject(storedUser ? JSON.parse(storedUser) : null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticatedSubject = new BehaviorSubject(!!storedUser);\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n  }\n  /**\n   * Get current user value\n   * @returns Current user or null\n   */\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  /**\n   * Login user with email and password\n   * @param email - User email\n   * @param password - User password\n   * @returns Observable with authentication response\n   */\n  login(email, password) {\n    return this.apiService.post(`${this.endpoint}/login`, {\n      email,\n      password\n    }).pipe(tap(response => this.handleAuthentication(response)), catchError(error => {\n      console.error('Login error:', error);\n      return of({\n        success: false,\n        message: error.error?.message || 'Login failed'\n      });\n    }));\n  }\n  /**\n   * Register new user\n   * @param name - User name\n   * @param email - User email\n   * @param password - User password\n   * @returns Observable with authentication response\n   */\n  register(name, email, password) {\n    return this.apiService.post(`${this.endpoint}/register`, {\n      name,\n      email,\n      password\n    }).pipe(tap(response => this.handleAuthentication(response)), catchError(error => {\n      console.error('Registration error:', error);\n      return of({\n        success: false,\n        message: error.error?.message || 'Registration failed'\n      });\n    }));\n  }\n  /**\n   * Logout user and clear stored data\n   */\n  logout() {\n    // Remove user from local storage\n    localStorage.removeItem('currentUser');\n    localStorage.removeItem('token');\n    // Reset subjects\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    // Navigate to login page\n    this.router.navigate(['/auth/login']);\n  }\n  /**\n   * Get current user profile from API\n   * @returns Observable with user data\n   */\n  getProfile() {\n    return this.apiService.get(`${this.endpoint}/me`).pipe(map(response => response.data), tap(user => {\n      // Update stored user data\n      this.currentUserSubject.next(user);\n      localStorage.setItem('currentUser', JSON.stringify(user));\n    }));\n  }\n  /**\n   * Check if token is valid\n   * @returns Observable boolean indicating if token is valid\n   */\n  checkToken() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.isAuthenticatedSubject.next(false);\n      return of(false);\n    }\n    // Verify token with backend\n    return this.apiService.get(`${this.endpoint}/verify-token`).pipe(map(response => {\n      const isValid = response.success;\n      this.isAuthenticatedSubject.next(isValid);\n      if (!isValid) {\n        this.logout();\n      }\n      return isValid;\n    }), catchError(() => {\n      this.logout();\n      return of(false);\n    }));\n  }\n  /**\n   * Request password reset email\n   * @param email - User email\n   * @returns Observable with success status\n   */\n  forgotPassword(email) {\n    return this.apiService.post(`${this.endpoint}/forgot-password`, {\n      email\n    }).pipe(catchError(error => {\n      console.error('Forgot password error:', error);\n      return of({\n        success: false,\n        message: error.error?.message || 'Failed to send password reset email'\n      });\n    }));\n  }\n  /**\n   * Reset password with token\n   * @param token - Reset token from email\n   * @param password - New password\n   * @returns Observable with success status\n   */\n  resetPassword(token, password) {\n    return this.apiService.post(`${this.endpoint}/reset-password`, {\n      token,\n      password\n    }).pipe(catchError(error => {\n      console.error('Reset password error:', error);\n      return of({\n        success: false,\n        message: error.error?.message || 'Failed to reset password'\n      });\n    }));\n  }\n  /**\n   * Check if user is authenticated\n   * @returns Boolean indicating if user is authenticated\n   */\n  isAuthenticated() {\n    return !!this.currentUserSubject.value && !!localStorage.getItem('token');\n  }\n  /**\n   * Handle authentication response\n   * @param response - Authentication response from API\n   */\n  handleAuthentication(response) {\n    if (response.success && response.data) {\n      // Store user details and token\n      localStorage.setItem('currentUser', JSON.stringify(response.data.user));\n      localStorage.setItem('token', response.data.token);\n      // Update subjects\n      this.currentUserSubject.next(response.data.user);\n      this.isAuthenticatedSubject.next(true);\n    }\n  }\n};\nAuthService = __decorate([Injectable({\n  providedIn: 'root'\n})], AuthService);", "map": {"version": 3, "names": ["Injectable", "BehaviorSubject", "of", "map", "catchError", "tap", "AuthService", "constructor", "apiService", "router", "endpoint", "storedUser", "localStorage", "getItem", "currentUserSubject", "JSON", "parse", "currentUser$", "asObservable", "isAuthenticatedSubject", "isAuthenticated$", "currentUserValue", "value", "login", "email", "password", "post", "pipe", "response", "handleAuthentication", "error", "console", "success", "message", "register", "name", "logout", "removeItem", "next", "navigate", "getProfile", "get", "data", "user", "setItem", "stringify", "checkToken", "token", "<PERSON><PERSON><PERSON><PERSON>", "forgotPassword", "resetPassword", "isAuthenticated", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["/**\n * Authentication service\n * Handles user authentication, registration, password reset, and token management\n */\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { User } from '../models/user.model';\nimport { AuthResponse } from '../models/auth-response.model';\nimport { ApiService } from './api.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  /**\n   * API endpoint path\n   */\n  private endpoint = 'auth';\n  \n  /**\n   * BehaviorSubject to track current user\n   */\n  private currentUserSubject: BehaviorSubject<User | null>;\n  \n  /**\n   * Observable to expose current user to components\n   */\n  public currentUser$: Observable<User | null>;\n  \n  /**\n   * BehaviorSubject to track authentication status\n   */\n  private isAuthenticatedSubject: BehaviorSubject<boolean>;\n  \n  /**\n   * Observable to expose authentication status to components\n   */\n  public isAuthenticated$: Observable<boolean>;\n\n  /**\n   * Constructor with dependency injection\n   * @param apiService - Base API service for HTTP requests\n   * @param router - Router for navigation\n   */\n  constructor(\n    private apiService: ApiService,\n    private router: Router\n  ) {\n    // Initialize from localStorage if available\n    const storedUser = localStorage.getItem('currentUser');\n    this.currentUserSubject = new BehaviorSubject<User | null>(\n      storedUser ? JSON.parse(storedUser) : null\n    );\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    \n    this.isAuthenticatedSubject = new BehaviorSubject<boolean>(!!storedUser);\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n  }\n\n  /**\n   * Get current user value\n   * @returns Current user or null\n   */\n  public get currentUserValue(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  /**\n   * Login user with email and password\n   * @param email - User email\n   * @param password - User password\n   * @returns Observable with authentication response\n   */\n  login(email: string, password: string): Observable<AuthResponse> {\n    return this.apiService.post<AuthResponse>(`${this.endpoint}/login`, { email, password })\n      .pipe(\n        tap((response: AuthResponse) => this.handleAuthentication(response)),\n        catchError((error: any) => {\n          console.error('Login error:', error);\n          return of({ success: false, message: error.error?.message || 'Login failed' } as AuthResponse);\n        })\n      );\n  }\n\n  /**\n   * Register new user\n   * @param name - User name\n   * @param email - User email\n   * @param password - User password\n   * @returns Observable with authentication response\n   */\n  register(name: string, email: string, password: string): Observable<AuthResponse> {\n    return this.apiService.post<AuthResponse>(`${this.endpoint}/register`, { name, email, password })\n      .pipe(\n        tap((response: AuthResponse) => this.handleAuthentication(response)),\n        catchError((error: any) => {\n          console.error('Registration error:', error);\n          return of({ success: false, message: error.error?.message || 'Registration failed' } as AuthResponse);\n        })\n      );\n  }\n\n  /**\n   * Logout user and clear stored data\n   */\n  logout(): void {\n    // Remove user from local storage\n    localStorage.removeItem('currentUser');\n    localStorage.removeItem('token');\n    \n    // Reset subjects\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    \n    // Navigate to login page\n    this.router.navigate(['/auth/login']);\n  }\n\n  /**\n   * Get current user profile from API\n   * @returns Observable with user data\n   */\n  getProfile(): Observable<User> {\n    return this.apiService.get<{ success: boolean, data: User }>(`${this.endpoint}/me`)\n      .pipe(\n        map((response: { success: boolean, data: User }) => response.data),\n        tap((user: User) => {\n          // Update stored user data\n          this.currentUserSubject.next(user);\n          localStorage.setItem('currentUser', JSON.stringify(user));\n        })\n      );\n  }\n\n  /**\n   * Check if token is valid\n   * @returns Observable boolean indicating if token is valid\n   */\n  checkToken(): Observable<boolean> {\n    const token = localStorage.getItem('token');\n    \n    if (!token) {\n      this.isAuthenticatedSubject.next(false);\n      return of(false);\n    }\n    \n    // Verify token with backend\n    return this.apiService.get<{ success: boolean }>(`${this.endpoint}/verify-token`)\n      .pipe(\n        map((response: { success: boolean }) => {\n          const isValid = response.success;\n          this.isAuthenticatedSubject.next(isValid);\n          \n          if (!isValid) {\n            this.logout();\n          }\n          \n          return isValid;\n        }),\n        catchError(() => {\n          this.logout();\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * Request password reset email\n   * @param email - User email\n   * @returns Observable with success status\n   */\n  forgotPassword(email: string): Observable<{ success: boolean; message: string }> {\n    return this.apiService.post<{ success: boolean; message: string }>(\n      `${this.endpoint}/forgot-password`, \n      { email }\n    ).pipe(\n      catchError((error: any) => {\n        console.error('Forgot password error:', error);\n        return of({ \n          success: false, \n          message: error.error?.message || 'Failed to send password reset email' \n        });\n      })\n    );\n  }\n\n  /**\n   * Reset password with token\n   * @param token - Reset token from email\n   * @param password - New password\n   * @returns Observable with success status\n   */\n  resetPassword(token: string, password: string): Observable<{ success: boolean; message: string }> {\n    return this.apiService.post<{ success: boolean; message: string }>(\n      `${this.endpoint}/reset-password`, \n      { token, password }\n    ).pipe(\n      catchError((error: any) => {\n        console.error('Reset password error:', error);\n        return of({ \n          success: false, \n          message: error.error?.message || 'Failed to reset password' \n        });\n      })\n    );\n  }\n\n  /**\n   * Check if user is authenticated\n   * @returns Boolean indicating if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return !!this.currentUserSubject.value && !!localStorage.getItem('token');\n  }\n\n  /**\n   * Handle authentication response\n   * @param response - Authentication response from API\n   */\n  private handleAuthentication(response: AuthResponse): void {\n    if (response.success && response.data) {\n      // Store user details and token\n      localStorage.setItem('currentUser', JSON.stringify(response.data.user));\n      localStorage.setItem('token', response.data.token);\n      \n      // Update subjects\n      this.currentUserSubject.next(response.data.user);\n      this.isAuthenticatedSubject.next(true);\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,EAAcC,EAAE,QAAQ,MAAM;AACtD,SAASC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAS9C,WAAMC,WAAW,GAAjB,MAAMA,WAAW;EA0BtB;;;;;EAKAC,YACUC,UAAsB,EACtBC,MAAc;IADd,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IAhChB;;;IAGQ,KAAAC,QAAQ,GAAG,MAAM;IA+BvB;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACtD,IAAI,CAACC,kBAAkB,GAAG,IAAIb,eAAe,CAC3CU,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,IAAI,CAC3C;IACD,IAAI,CAACM,YAAY,GAAG,IAAI,CAACH,kBAAkB,CAACI,YAAY,EAAE;IAE1D,IAAI,CAACC,sBAAsB,GAAG,IAAIlB,eAAe,CAAU,CAAC,CAACU,UAAU,CAAC;IACxE,IAAI,CAACS,gBAAgB,GAAG,IAAI,CAACD,sBAAsB,CAACD,YAAY,EAAE;EACpE;EAEA;;;;EAIA,IAAWG,gBAAgBA,CAAA;IACzB,OAAO,IAAI,CAACP,kBAAkB,CAACQ,KAAK;EACtC;EAEA;;;;;;EAMAC,KAAKA,CAACC,KAAa,EAAEC,QAAgB;IACnC,OAAO,IAAI,CAACjB,UAAU,CAACkB,IAAI,CAAe,GAAG,IAAI,CAAChB,QAAQ,QAAQ,EAAE;MAAEc,KAAK;MAAEC;IAAQ,CAAE,CAAC,CACrFE,IAAI,CACHtB,GAAG,CAAEuB,QAAsB,IAAK,IAAI,CAACC,oBAAoB,CAACD,QAAQ,CAAC,CAAC,EACpExB,UAAU,CAAE0B,KAAU,IAAI;MACxBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAO5B,EAAE,CAAC;QAAE8B,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAEH,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI;MAAc,CAAkB,CAAC;IAChG,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOAC,QAAQA,CAACC,IAAY,EAAEX,KAAa,EAAEC,QAAgB;IACpD,OAAO,IAAI,CAACjB,UAAU,CAACkB,IAAI,CAAe,GAAG,IAAI,CAAChB,QAAQ,WAAW,EAAE;MAAEyB,IAAI;MAAEX,KAAK;MAAEC;IAAQ,CAAE,CAAC,CAC9FE,IAAI,CACHtB,GAAG,CAAEuB,QAAsB,IAAK,IAAI,CAACC,oBAAoB,CAACD,QAAQ,CAAC,CAAC,EACpExB,UAAU,CAAE0B,KAAU,IAAI;MACxBC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO5B,EAAE,CAAC;QAAE8B,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAEH,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI;MAAqB,CAAkB,CAAC;IACvG,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAG,MAAMA,CAAA;IACJ;IACAxB,YAAY,CAACyB,UAAU,CAAC,aAAa,CAAC;IACtCzB,YAAY,CAACyB,UAAU,CAAC,OAAO,CAAC;IAEhC;IACA,IAAI,CAACvB,kBAAkB,CAACwB,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACnB,sBAAsB,CAACmB,IAAI,CAAC,KAAK,CAAC;IAEvC;IACA,IAAI,CAAC7B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEA;;;;EAIAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAAChC,UAAU,CAACiC,GAAG,CAAmC,GAAG,IAAI,CAAC/B,QAAQ,KAAK,CAAC,CAChFiB,IAAI,CACHxB,GAAG,CAAEyB,QAA0C,IAAKA,QAAQ,CAACc,IAAI,CAAC,EAClErC,GAAG,CAAEsC,IAAU,IAAI;MACjB;MACA,IAAI,CAAC7B,kBAAkB,CAACwB,IAAI,CAACK,IAAI,CAAC;MAClC/B,YAAY,CAACgC,OAAO,CAAC,aAAa,EAAE7B,IAAI,CAAC8B,SAAS,CAACF,IAAI,CAAC,CAAC;IAC3D,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIAG,UAAUA,CAAA;IACR,MAAMC,KAAK,GAAGnC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI,CAACkC,KAAK,EAAE;MACV,IAAI,CAAC5B,sBAAsB,CAACmB,IAAI,CAAC,KAAK,CAAC;MACvC,OAAOpC,EAAE,CAAC,KAAK,CAAC;;IAGlB;IACA,OAAO,IAAI,CAACM,UAAU,CAACiC,GAAG,CAAuB,GAAG,IAAI,CAAC/B,QAAQ,eAAe,CAAC,CAC9EiB,IAAI,CACHxB,GAAG,CAAEyB,QAA8B,IAAI;MACrC,MAAMoB,OAAO,GAAGpB,QAAQ,CAACI,OAAO;MAChC,IAAI,CAACb,sBAAsB,CAACmB,IAAI,CAACU,OAAO,CAAC;MAEzC,IAAI,CAACA,OAAO,EAAE;QACZ,IAAI,CAACZ,MAAM,EAAE;;MAGf,OAAOY,OAAO;IAChB,CAAC,CAAC,EACF5C,UAAU,CAAC,MAAK;MACd,IAAI,CAACgC,MAAM,EAAE;MACb,OAAOlC,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKA+C,cAAcA,CAACzB,KAAa;IAC1B,OAAO,IAAI,CAAChB,UAAU,CAACkB,IAAI,CACzB,GAAG,IAAI,CAAChB,QAAQ,kBAAkB,EAClC;MAAEc;IAAK,CAAE,CACV,CAACG,IAAI,CACJvB,UAAU,CAAE0B,KAAU,IAAI;MACxBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO5B,EAAE,CAAC;QACR8B,OAAO,EAAE,KAAK;QACdC,OAAO,EAAEH,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI;OAClC,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;;;;;;EAMAiB,aAAaA,CAACH,KAAa,EAAEtB,QAAgB;IAC3C,OAAO,IAAI,CAACjB,UAAU,CAACkB,IAAI,CACzB,GAAG,IAAI,CAAChB,QAAQ,iBAAiB,EACjC;MAAEqC,KAAK;MAAEtB;IAAQ,CAAE,CACpB,CAACE,IAAI,CACJvB,UAAU,CAAE0B,KAAU,IAAI;MACxBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO5B,EAAE,CAAC;QACR8B,OAAO,EAAE,KAAK;QACdC,OAAO,EAAEH,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI;OAClC,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;;;;EAIAkB,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACrC,kBAAkB,CAACQ,KAAK,IAAI,CAAC,CAACV,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3E;EAEA;;;;EAIQgB,oBAAoBA,CAACD,QAAsB;IACjD,IAAIA,QAAQ,CAACI,OAAO,IAAIJ,QAAQ,CAACc,IAAI,EAAE;MACrC;MACA9B,YAAY,CAACgC,OAAO,CAAC,aAAa,EAAE7B,IAAI,CAAC8B,SAAS,CAACjB,QAAQ,CAACc,IAAI,CAACC,IAAI,CAAC,CAAC;MACvE/B,YAAY,CAACgC,OAAO,CAAC,OAAO,EAAEhB,QAAQ,CAACc,IAAI,CAACK,KAAK,CAAC;MAElD;MACA,IAAI,CAACjC,kBAAkB,CAACwB,IAAI,CAACV,QAAQ,CAACc,IAAI,CAACC,IAAI,CAAC;MAChD,IAAI,CAACxB,sBAAsB,CAACmB,IAAI,CAAC,IAAI,CAAC;;EAE1C;CACD;AAzNYhC,WAAW,GAAA8C,UAAA,EAHvBpD,UAAU,CAAC;EACVqD,UAAU,EAAE;CACb,CAAC,C,EACW/C,WAAW,CAyNvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}