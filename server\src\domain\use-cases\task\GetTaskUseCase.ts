/**
 * Get Task Use Case
 * Handles business logic for retrieving a task by ID
 */
import { ITaskDocument } from '../../entities/Task';
import { ITaskRepository } from '../../repositories/ITaskRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for retrieving a task by ID
 * Implements business rules and validation for task retrieval
 */
export class GetTaskUseCase {
  /**
   * Constructor for GetTaskUseCase
   * @param taskRepository - Repository for task data access
   */
  constructor(private taskRepository: ITaskRepository) {}

  /**
   * Executes the use case to retrieve a task by ID
   * @param taskId - ID of the task to retrieve
   * @returns Promise resolving to the found task
   * @throws AppError if task not found
   */
  async execute(taskId: string): Promise<ITaskDocument> {
    try {
      // Validate task ID
      if (!taskId) {
        throw new AppError('Task ID is required', 400);
      }

      // Find task by ID
      const task = await this.taskRepository.findById(taskId);
      
      // Check if task exists
      if (!task) {
        throw new AppError(`Task with ID ${taskId} not found`, 404);
      }
      
      return task;
    } catch (error) {
      // Rethrow AppErrors, wrap other errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error retrieving task: ${(error as Error).message}`, 500);
    }
  }
}
