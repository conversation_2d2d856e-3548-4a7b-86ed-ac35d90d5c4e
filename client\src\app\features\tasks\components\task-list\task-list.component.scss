/**
 * Task list component styles
 */

/* Container for the entire task list */
.task-list-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header section with title and actions */
.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
  
  h2 {
    margin: 0;
    font-weight: 500;
    color: #333;
  }
}

/* Action buttons container */
.task-list-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Loading state container */
.task-list-loading {
  padding: 32px;
  display: flex;
  justify-content: center;
}

/* Empty state container */
.task-list-empty {
  padding: 48px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: #666;
  
  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
    color: #ccc;
  }
  
  p {
    margin-bottom: 16px;
    font-size: 1.1rem;
  }
}

/* Task list table */
.task-list {
  width: 100%;
}

/* Header row for the task list table */
.task-list-header-row {
  display: flex;
  background-color: #f5f5f5;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  
  .task-column {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    
    &:hover {
      background-color: #eee;
    }
    
    mat-icon {
      font-size: 18px;
      height: 18px;
      width: 18px;
      margin-left: 4px;
    }
  }
}

/* Column widths */
.task-status-column {
  width: 120px;
  flex-shrink: 0;
}

.task-title-column {
  flex: 1;
}

.task-priority-column {
  width: 100px;
  flex-shrink: 0;
}

.task-date-column {
  width: 120px;
  flex-shrink: 0;
}

.task-actions-column {
  width: 100px;
  flex-shrink: 0;
  cursor: default;
  
  &:hover {
    background-color: transparent;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .task-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .task-list-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .task-priority-column,
  .task-date-column {
    display: none;
  }
  
  .task-status-column {
    width: 80px;
  }
  
  .task-actions-column {
    width: 80px;
  }
}
