<!-- Task edit page container -->
<div class="task-edit-page-container">
  <!-- Page header -->
  <div class="page-header">
    <h1>Edit Task</h1>
    <button mat-button (click)="onFormCancel()" class="back-button">
      <mat-icon>arrow_back</mat-icon>
      Back to Task
    </button>
  </div>
  
  <!-- Error message -->
  <app-error-message 
    *ngIf="error" 
    [message]="error" 
    [showRetry]="true"
    [onRetry]="loadTask.bind(this)">
  </app-error-message>
  
  <!-- Task form component -->
  <app-task-form
    *ngIf="!error && task"
    [task]="task"
    [users]="users"
    [loading]="loading"
    (formSubmit)="onFormSubmit($event)"
    (formCancel)="onFormCancel()">
  </app-task-form>
</div>
