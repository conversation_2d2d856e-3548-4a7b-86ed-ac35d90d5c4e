/**
 * Task Form Component
 * Form for creating and editing tasks
 */
import { Component, Input, Output, EventEmitter, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Task } from '../../../../core/models/task.model';
import { User } from '../../../../core/models/user.model';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';

@Component({
  selector: 'app-task-form',
  templateUrl: './task-form.component.html',
  styleUrls: ['./task-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskFormComponent implements OnInit {
  /**
   * Input task for editing (optional)
   */
  @Input() task?: Task;
  
  /**
   * Input list of users for assignee selection
   */
  @Input() users: User[] = [];
  
  /**
   * Loading state indicator
   */
  @Input() loading = false;
  
  /**
   * Event emitted when form is submitted
   */
  @Output() formSubmit = new EventEmitter<Partial<Task>>();
  
  /**
   * Event emitted when form is cancelled
   */
  @Output() formCancel = new EventEmitter<void>();
  
  /**
   * Task form group
   */
  taskForm!: FormGroup;
  
  /**
   * Chip input separator keys
   */
  readonly separatorKeysCodes = [ENTER, COMMA] as const;
  
  /**
   * Available task statuses
   */
  statuses = [
    { value: 'todo', label: 'To Do' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'review', label: 'Review' },
    { value: 'done', label: 'Done' }
  ];
  
  /**
   * Available task priorities
   */
  priorities = [
    { value: 'high', label: 'High' },
    { value: 'medium', label: 'Medium' },
    { value: 'low', label: 'Low' }
  ];

  /**
   * Constructor with dependency injection
   * @param fb - Form builder service
   */
  constructor(private fb: FormBuilder) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Sets up the task form
   */
  ngOnInit(): void {
    this.initForm();
  }

  /**
   * Initialize the form with task data or default values
   */
  initForm(): void {
    this.taskForm = this.fb.group({
      title: [this.task?.title || '', [Validators.required, Validators.maxLength(100)]],
      description: [this.task?.description || '', Validators.maxLength(1000)],
      status: [this.task?.status || 'todo', Validators.required],
      priority: [this.task?.priority || 'medium', Validators.required],
      dueDate: [this.task?.dueDate ? new Date(this.task.dueDate) : null],
      assignee: [this.task?.assignee || ''],
      tags: [this.task?.tags || []]
    });
  }

  /**
   * Handle form submission
   */
  onSubmit(): void {
    if (this.taskForm.invalid || this.loading) {
      return;
    }
    
    // Get form values
    const formValues = this.taskForm.value;
    
    // Format due date if present
    if (formValues.dueDate) {
      formValues.dueDate = new Date(formValues.dueDate).toISOString();
    }
    
    // Emit form data
    this.formSubmit.emit(formValues);
  }

  /**
   * Handle form cancellation
   */
  onCancel(): void {
    this.formCancel.emit();
  }

  /**
   * Add a tag
   * @param event - Chip input event
   */
  addTag(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    
    // Add tag
    if (value) {
      const currentTags = this.taskForm.get('tags')?.value || [];
      if (!currentTags.includes(value)) {
        this.taskForm.get('tags')?.setValue([...currentTags, value]);
      }
    }
    
    // Clear input
    event.chipInput!.clear();
  }

  /**
   * Remove a tag
   * @param tag - Tag to remove
   */
  removeTag(tag: string): void {
    const currentTags = this.taskForm.get('tags')?.value || [];
    const index = currentTags.indexOf(tag);
    
    if (index >= 0) {
      const newTags = [...currentTags];
      newTags.splice(index, 1);
      this.taskForm.get('tags')?.setValue(newTags);
    }
  }

  /**
   * Get error message for a form control
   * @param controlName - Name of the form control
   * @returns Error message
   */
  getErrorMessage(controlName: string): string {
    const control = this.taskForm.get(controlName);
    
    if (!control || !control.errors) {
      return '';
    }
    
    if (control.errors['required']) {
      return 'This field is required';
    }
    
    if (control.errors['maxlength']) {
      const maxLength = control.errors['maxlength'].requiredLength;
      return `Maximum length is ${maxLength} characters`;
    }
    
    return 'Invalid value';
  }
}
