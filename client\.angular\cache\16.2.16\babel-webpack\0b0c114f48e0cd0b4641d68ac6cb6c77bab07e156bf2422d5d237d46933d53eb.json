{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Task Edit Page Component\n * Page for editing an existing task\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nexport let TaskEditPageComponent = class TaskEditPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param route - Activated route for getting route parameters\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(taskService, userService, notificationService, route, router, cdr) {\n    this.taskService = taskService;\n    this.userService = userService;\n    this.notificationService = notificationService;\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Current task being edited\n     */\n    this.task = null;\n    /**\n     * List of users\n     */\n    this.users = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message if loading failed\n     */\n    this.error = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads task and users\n   */\n  ngOnInit() {\n    this.loadTask();\n    this.loadUsers();\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Load task details\n   */\n  loadTask() {\n    const taskId = this.route.snapshot.paramMap.get('id');\n    if (!taskId) {\n      this.error = 'Task ID not provided';\n      return;\n    }\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    this.taskService.getTaskById(taskId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: task => {\n        this.task = task;\n        this.loading = false;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.error = 'Failed to load task. It may have been deleted or you do not have permission to edit it.';\n        this.loading = false;\n        console.error('Error loading task:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers() {\n    this.userService.getUsers().pipe(takeUntil(this.destroy$)).subscribe({\n      next: users => {\n        this.users = users;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        console.error('Error loading users:', err);\n      }\n    });\n  }\n  /**\n   * Handle form submission\n   * @param taskData - Updated task data from form\n   */\n  onFormSubmit(taskData) {\n    if (!this.task) return;\n    this.loading = true;\n    this.cdr.markForCheck();\n    this.taskService.updateTask(this.task.id, taskData).pipe(takeUntil(this.destroy$)).subscribe({\n      next: updatedTask => {\n        this.loading = false;\n        this.notificationService.success('Task updated successfully');\n        this.router.navigate(['/tasks', updatedTask.id]);\n      },\n      error: err => {\n        this.loading = false;\n        this.notificationService.error('Failed to update task');\n        console.error('Error updating task:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Handle form cancellation\n   */\n  onFormCancel() {\n    if (this.task) {\n      this.router.navigate(['/tasks', this.task.id]);\n    } else {\n      this.router.navigate(['/tasks']);\n    }\n  }\n};\nTaskEditPageComponent = __decorate([Component({\n  selector: 'app-task-edit-page',\n  templateUrl: './task-edit-page.component.html',\n  styleUrls: ['./task-edit-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskEditPageComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "Subject", "takeUntil", "TaskEditPageComponent", "constructor", "taskService", "userService", "notificationService", "route", "router", "cdr", "task", "users", "loading", "error", "destroy$", "ngOnInit", "loadTask", "loadUsers", "ngOnDestroy", "next", "complete", "taskId", "snapshot", "paramMap", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTaskById", "pipe", "subscribe", "err", "console", "getUsers", "onFormSubmit", "taskData", "updateTask", "id", "updatedTask", "success", "navigate", "onFormCancel", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\pages\\task-edit-page\\task-edit-page.component.ts"], "sourcesContent": ["/**\n * Task Edit Page Component\n * Page for editing an existing task\n */\nimport { Component, OnInit, <PERSON><PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { TaskService } from '../../../../core/services/task.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-task-edit-page',\n  templateUrl: './task-edit-page.component.html',\n  styleUrls: ['./task-edit-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskEditPageComponent implements OnInit, OnD<PERSON>roy {\n  /**\n   * Current task being edited\n   */\n  task: Task | null = null;\n  \n  /**\n   * List of users\n   */\n  users: User[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message if loading failed\n   */\n  error: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param route - Activated route for getting route parameters\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private taskService: TaskService,\n    private userService: UserService,\n    private notificationService: NotificationService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads task and users\n   */\n  ngOnInit(): void {\n    this.loadTask();\n    this.loadUsers();\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Load task details\n   */\n  loadTask(): void {\n    const taskId = this.route.snapshot.paramMap.get('id');\n    if (!taskId) {\n      this.error = 'Task ID not provided';\n      return;\n    }\n    \n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    \n    this.taskService.getTaskById(taskId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (task: Task) => {\n          this.task = task;\n          this.loading = false;\n          this.cdr.markForCheck();\n        },\n        error: (err: any) => {\n          this.error = 'Failed to load task. It may have been deleted or you do not have permission to edit it.';\n          this.loading = false;\n          console.error('Error loading task:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers(): void {\n    this.userService.getUsers()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (users: User[]) => {\n          this.users = users;\n          this.cdr.markForCheck();\n        },\n        error: (err: any) => {\n          console.error('Error loading users:', err);\n        }\n      });\n  }\n\n  /**\n   * Handle form submission\n   * @param taskData - Updated task data from form\n   */\n  onFormSubmit(taskData: Partial<Task>): void {\n    if (!this.task) return;\n    \n    this.loading = true;\n    this.cdr.markForCheck();\n    \n    this.taskService.updateTask(this.task.id, taskData)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (updatedTask: Task) => {\n          this.loading = false;\n          this.notificationService.success('Task updated successfully');\n          this.router.navigate(['/tasks', updatedTask.id]);\n        },\n        error: (err: any) => {\n          this.loading = false;\n          this.notificationService.error('Failed to update task');\n          console.error('Error updating task:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Handle form cancellation\n   */\n  onFormCancel(): void {\n    if (this.task) {\n      this.router.navigate(['/tasks', this.task.id]);\n    } else {\n      this.router.navigate(['/tasks']);\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AAExG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAanC,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EA0BhC;;;;;;;;;EASAC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,mBAAwC,EACxCC,KAAqB,EACrBC,MAAc,EACdC,GAAsB;IALtB,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IAxCb;;;IAGA,KAAAC,IAAI,GAAgB,IAAI;IAExB;;;IAGA,KAAAC,KAAK,GAAW,EAAE;IAElB;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAId,OAAO,EAAQ;EAkBnC;EAEH;;;;EAIAe,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;EAGAJ,QAAQA,CAAA;IACN,MAAMK,MAAM,GAAG,IAAI,CAACd,KAAK,CAACe,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACrD,IAAI,CAACH,MAAM,EAAE;MACX,IAAI,CAACR,KAAK,GAAG,sBAAsB;MACnC;;IAGF,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACJ,GAAG,CAACgB,YAAY,EAAE;IAEvB,IAAI,CAACrB,WAAW,CAACsB,WAAW,CAACL,MAAM,CAAC,CACjCM,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAACa,QAAQ,CAAC,CAAC,CAC9Bc,SAAS,CAAC;MACTT,IAAI,EAAGT,IAAU,IAAI;QACnB,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACE,OAAO,GAAG,KAAK;QACpB,IAAI,CAACH,GAAG,CAACgB,YAAY,EAAE;MACzB,CAAC;MACDZ,KAAK,EAAGgB,GAAQ,IAAI;QAClB,IAAI,CAAChB,KAAK,GAAG,yFAAyF;QACtG,IAAI,CAACD,OAAO,GAAG,KAAK;QACpBkB,OAAO,CAACjB,KAAK,CAAC,qBAAqB,EAAEgB,GAAG,CAAC;QACzC,IAAI,CAACpB,GAAG,CAACgB,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAR,SAASA,CAAA;IACP,IAAI,CAACZ,WAAW,CAAC0B,QAAQ,EAAE,CACxBJ,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAACa,QAAQ,CAAC,CAAC,CAC9Bc,SAAS,CAAC;MACTT,IAAI,EAAGR,KAAa,IAAI;QACtB,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACF,GAAG,CAACgB,YAAY,EAAE;MACzB,CAAC;MACDZ,KAAK,EAAGgB,GAAQ,IAAI;QAClBC,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEgB,GAAG,CAAC;MAC5C;KACD,CAAC;EACN;EAEA;;;;EAIAG,YAAYA,CAACC,QAAuB;IAClC,IAAI,CAAC,IAAI,CAACvB,IAAI,EAAE;IAEhB,IAAI,CAACE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,GAAG,CAACgB,YAAY,EAAE;IAEvB,IAAI,CAACrB,WAAW,CAAC8B,UAAU,CAAC,IAAI,CAACxB,IAAI,CAACyB,EAAE,EAAEF,QAAQ,CAAC,CAChDN,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAACa,QAAQ,CAAC,CAAC,CAC9Bc,SAAS,CAAC;MACTT,IAAI,EAAGiB,WAAiB,IAAI;QAC1B,IAAI,CAACxB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACN,mBAAmB,CAAC+B,OAAO,CAAC,2BAA2B,CAAC;QAC7D,IAAI,CAAC7B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,QAAQ,EAAEF,WAAW,CAACD,EAAE,CAAC,CAAC;MAClD,CAAC;MACDtB,KAAK,EAAGgB,GAAQ,IAAI;QAClB,IAAI,CAACjB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACN,mBAAmB,CAACO,KAAK,CAAC,uBAAuB,CAAC;QACvDiB,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEgB,GAAG,CAAC;QAC1C,IAAI,CAACpB,GAAG,CAACgB,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAc,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC7B,IAAI,EAAE;MACb,IAAI,CAACF,MAAM,CAAC8B,QAAQ,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC5B,IAAI,CAACyB,EAAE,CAAC,CAAC;KAC/C,MAAM;MACL,IAAI,CAAC3B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;EAEpC;CACD;AAnJYpC,qBAAqB,GAAAsC,UAAA,EANjC1C,SAAS,CAAC;EACT2C,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC,CAAC;EAC9CC,eAAe,EAAE7C,uBAAuB,CAAC8C;CAC1C,CAAC,C,EACW3C,qBAAqB,CAmJjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}