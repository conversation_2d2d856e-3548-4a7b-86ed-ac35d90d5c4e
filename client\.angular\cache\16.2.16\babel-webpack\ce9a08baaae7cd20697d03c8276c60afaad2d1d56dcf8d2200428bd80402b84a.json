{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Not Found Page module\n * Contains the component for displaying 404 error page\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../../../shared/shared.module';\nimport { NotFoundPageComponent } from './not-found-page.component';\n// Define routes for the not-found page\nconst routes = [{\n  path: '',\n  component: NotFoundPageComponent\n}];\nexport let NotFoundPageModule = class NotFoundPageModule {};\nNotFoundPageModule = __decorate([NgModule({\n  declarations: [NotFoundPageComponent],\n  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n})], NotFoundPageModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "SharedModule", "NotFoundPageComponent", "routes", "path", "component", "NotFoundPageModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\not-found\\pages\\not-found-page\\not-found-page.module.ts"], "sourcesContent": ["/**\n * Not Found Page module\n * Contains the component for displaying 404 error page\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../../../shared/shared.module';\nimport { NotFoundPageComponent } from './not-found-page.component';\n\n// Define routes for the not-found page\nconst routes: Routes = [\n  {\n    path: '',\n    component: NotFoundPageComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    NotFoundPageComponent\n  ],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class NotFoundPageModule { }\n"], "mappings": ";AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,qBAAqB,QAAQ,4BAA4B;AAElE;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAYM,WAAMI,kBAAkB,GAAxB,MAAMA,kBAAkB,GAAI;AAAtBA,kBAAkB,GAAAC,UAAA,EAV9BT,QAAQ,CAAC;EACRU,YAAY,EAAE,CACZN,qBAAqB,CACtB;EACDO,OAAO,EAAE,CACPV,YAAY,EACZE,YAAY,EACZD,YAAY,CAACU,QAAQ,CAACP,MAAM,CAAC;CAEhC,CAAC,C,EACWG,kBAAkB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}