/**
 * Custom application error class
 * Extends the standard Error class with additional properties
 */
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  /**
   * Creates a new AppError instance
   * @param message - Error message
   * @param statusCode - HTTP status code (default: 500)
   * @param isOperational - Whether the error is operational (expected) or programming error (default: true)
   */
  constructor(message: string, statusCode = 500, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
    
    // Set prototype explicitly for proper instanceof checks
    Object.setPrototypeOf(this, AppError.prototype);
  }
}
