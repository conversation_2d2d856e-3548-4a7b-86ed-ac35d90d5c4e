{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { Directive, Output } from '@angular/core';\nimport { EventEmitter } from '@angular/core';\nexport let LazyLoadDirective = class LazyLoadDirective {\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    /**\n     * Event emitted when element enters viewport\n     */\n    this.appLazyLoad = new EventEmitter();\n    /**\n     * Intersection observer instance\n     */\n    this.observer = null;\n  }\n  /**\n   * Initialize the intersection observer\n   */\n  ngOnInit() {\n    // Create observer with options\n    this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {\n      root: null,\n      rootMargin: '0px',\n      threshold: 0.1 // Trigger when 10% of element is visible\n    });\n    // Start observing the element\n    this.observer.observe(this.elementRef.nativeElement);\n  }\n  /**\n   * Clean up observer when directive is destroyed\n   */\n  ngOnDestroy() {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  handleIntersection(entries) {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n};\n__decorate([Output()], LazyLoadDirective.prototype, \"appLazyLoad\", void 0);\nLazyLoadDirective = __decorate([Directive({\n  selector: '[appLazyLoad]'\n})], LazyLoadDirective);", "map": {"version": 3, "names": ["Directive", "Output", "EventEmitter", "LazyLoadDirective", "constructor", "elementRef", "appLazyLoad", "observer", "ngOnInit", "IntersectionObserver", "handleIntersection", "bind", "root", "rootMargin", "threshold", "observe", "nativeElement", "ngOnDestroy", "disconnect", "entries", "for<PERSON>ach", "entry", "isIntersecting", "emit", "unobserve", "target", "__decorate", "selector"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\directives\\lazy-load.directive.ts"], "sourcesContent": ["/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { Directive, Output } from '@angular/core';\nimport { EventEmitter } from '@angular/core';\n\n@Directive({\n  selector: '[appLazyLoad]'\n})\nexport class LazyLoadDirective {\n  /**\n   * Event emitted when element enters viewport\n   */\n  @Output() appLazyLoad = new EventEmitter<void>();\n  \n  /**\n   * Intersection observer instance\n   */\n  private observer: IntersectionObserver | null = null;\n\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(private elementRef: any) {}\n\n  /**\n   * Initialize the intersection observer\n   */\n  ngOnInit(): void {\n    // Create observer with options\n    this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {\n      root: null, // Use viewport as root\n      rootMargin: '0px',\n      threshold: 0.1 // Trigger when 10% of element is visible\n    });\n    \n    // Start observing the element\n    this.observer.observe(this.elementRef.nativeElement);\n  }\n\n  /**\n   * Clean up observer when directive is destroyed\n   */\n  ngOnDestroy(): void {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  private handleIntersection(entries: IntersectionObserverEntry[]): void {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        \n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;;AAKA,SAASA,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,YAAY,QAAQ,eAAe;AAKrC,WAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAW5B;;;;EAIAC,YAAoBC,UAAe;IAAf,KAAAA,UAAU,GAAVA,UAAU;IAd9B;;;IAGU,KAAAC,WAAW,GAAG,IAAIJ,YAAY,EAAQ;IAEhD;;;IAGQ,KAAAK,QAAQ,GAAgC,IAAI;EAMd;EAEtC;;;EAGAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,QAAQ,GAAG,IAAIE,oBAAoB,CAAC,IAAI,CAACC,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;MAC3EC,IAAI,EAAE,IAAI;MACVC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,GAAG,CAAC;KAChB,CAAC;IAEF;IACA,IAAI,CAACP,QAAQ,CAACQ,OAAO,CAAC,IAAI,CAACV,UAAU,CAACW,aAAa,CAAC;EACtD;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACV,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACW,UAAU,EAAE;MAC1B,IAAI,CAACX,QAAQ,GAAG,IAAI;;EAExB;EAEA;;;;EAIQG,kBAAkBA,CAACS,OAAoC;IAC7DA,OAAO,CAACC,OAAO,CAACC,KAAK,IAAG;MACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxB;QACA,IAAI,CAAChB,WAAW,CAACiB,IAAI,EAAE;QAEvB;QACA,IAAI,IAAI,CAAChB,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,CAACiB,SAAS,CAACH,KAAK,CAACI,MAAM,CAAC;;;IAG3C,CAAC,CAAC;EACJ;CACD;AAvDWC,UAAA,EAATzB,MAAM,EAAE,C,qDAAwC;AAJtCE,iBAAiB,GAAAuB,UAAA,EAH7B1B,SAAS,CAAC;EACT2B,QAAQ,EAAE;CACX,CAAC,C,EACWxB,iBAAiB,CA2D7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}