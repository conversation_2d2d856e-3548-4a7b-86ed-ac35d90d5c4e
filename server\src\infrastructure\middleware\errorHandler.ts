/**
 * Global error handling middleware
 * Processes all errors and sends appropriate responses
 */
import { Request, Response, NextFunction } from 'express';
import { AppError } from '../../domain/common/AppError';

/**
 * Custom error handler middleware
 * Formats error responses based on error type and environment
 * @param err - Error object
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const errorHandler = (
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Default error values
  let statusCode = 500;
  let message = 'Internal Server Error';
  let stack = undefined;
  
  // Check if error is our custom AppError
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    message = err.message;
  } else {
    // Handle standard errors
    message = err.message || 'Something went wrong';
  }
  
  // Include stack trace in development mode
  if (process.env.NODE_ENV === 'development') {
    stack = err.stack;
  }
  
  // Log error for debugging
  console.error(`[Error] ${statusCode} - ${message}`, stack || '');
  
  // Send error response
  res.status(statusCode).json({
    success: false,
    error: {
      message,
      ...(stack && { stack }),
    },
  });
};
