/**
 * Tasks Module
 * Feature module for task management functionality
 */
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';

// Components
import { TaskListComponent } from './components/task-list/task-list.component';
import { TaskDetailComponent } from './components/task-detail/task-detail.component';
import { TaskFormComponent } from './components/task-form/task-form.component';
import { TaskFilterComponent } from './components/task-filter/task-filter.component';
import { TaskItemComponent } from './components/task-item/task-item.component';

// Pages
import { TasksPageComponent } from './pages/tasks-page/tasks-page.component';
import { TaskCreatePageComponent } from './pages/task-create-page/task-create-page.component';
import { TaskEditPageComponent } from './pages/task-edit-page/task-edit-page.component';
import { TaskViewPageComponent } from './pages/task-view-page/task-view-page.component';

/**
 * Routes for the tasks feature module
 */
const routes: Routes = [
  {
    path: '',
    component: TasksPageComponent
  },
  {
    path: 'create',
    component: TaskCreatePageComponent
  },
  {
    path: ':id',
    component: TaskViewPageComponent
  },
  {
    path: ':id/edit',
    component: TaskEditPageComponent
  }
];

@NgModule({
  declarations: [
    TaskListComponent,
    TaskDetailComponent,
    TaskFormComponent,
    TaskFilterComponent,
    TaskItemComponent,
    TasksPageComponent,
    TaskCreatePageComponent,
    TaskEditPageComponent,
    TaskViewPageComponent
  ],
  imports: [
    SharedModule,
    RouterModule.forChild(routes)
  ]
})
export class TasksModule { }
