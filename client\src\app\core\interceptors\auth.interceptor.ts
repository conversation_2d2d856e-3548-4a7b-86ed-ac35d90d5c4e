/**
 * Authentication Interceptor
 * Adds authentication token to outgoing HTTP requests
 */
import { Injectable } from '@angular/core';
import {
  HttpRe<PERSON>,
  <PERSON>ttpHandler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@environments/environment';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  /**
   * Intercepts HTTP requests and adds authentication token
   * @param request - The outgoing HTTP request
   * @param next - The next interceptor in the chain
   * @returns Observable of HTTP event
   */
  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    // Get token from local storage
    const token = localStorage.getItem('token');
    
    // Only add token to API requests
    if (token && request.url.startsWith(environment.apiUrl)) {
      // Clone the request and add the authorization header
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });
    }
    
    // Pass the modified request to the next handler
    return next.handle(request);
  }
}
