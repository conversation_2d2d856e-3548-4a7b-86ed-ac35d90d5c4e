{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { Directive, EventEmitter, Output } from '@angular/core';\nexport let LazyLoadDirective = class LazyLoadDirective {\n  /**\n   * Constructor with dependency injection\n   * @param el - Reference to the host element\n   */\n  constructor(el) {\n    this.el = el;\n    /**\n     * Event emitted when element enters viewport\n     */\n    this.appLazyLoad = new EventEmitter();\n    /**\n     * Intersection observer instance\n     */\n    this.observer = null;\n  }\n  /**\n   * Initialize the intersection observer when directive is initialized\n   */\n  ngOnInit() {\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {\n      // Create observer with options\n      this.observer = new IntersectionObserver(entries => {\n        this.handleIntersection(entries);\n      }, {\n        root: null,\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      // Start observing the element\n      if (this.el && this.el.nativeElement) {\n        this.observer.observe(this.el.nativeElement);\n      }\n    }\n  }\n  /**\n   * Clean up observer when directive is destroyed to prevent memory leaks\n   */\n  ngOnDestroy() {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  handleIntersection(entries) {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n};\n__decorate([Output()], LazyLoadDirective.prototype, \"appLazyLoad\", void 0);\nLazyLoadDirective = __decorate([Directive({\n  selector: '[appLazyLoad]'\n})], LazyLoadDirective);", "map": {"version": 3, "names": ["Directive", "EventEmitter", "Output", "LazyLoadDirective", "constructor", "el", "appLazyLoad", "observer", "ngOnInit", "window", "IntersectionObserver", "entries", "handleIntersection", "root", "rootMargin", "threshold", "nativeElement", "observe", "ngOnDestroy", "disconnect", "for<PERSON>ach", "entry", "isIntersecting", "emit", "unobserve", "target", "__decorate", "selector"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\directives\\lazy-load.directive.ts"], "sourcesContent": ["/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { Directive, ElementRef, EventEmitter, Output } from '@angular/core';\n\n@Directive({\n  selector: '[appLazyLoad]'\n})\nexport class LazyLoadDirective {\n  /**\n   * Event emitted when element enters viewport\n   */\n  @Output() appLazyLoad = new EventEmitter<void>();\n  \n  /**\n   * Intersection observer instance\n   */\n  private observer: any = null;\n\n  /**\n   * Constructor with dependency injection\n   * @param el - Reference to the host element\n   */\n  constructor(private el: ElementRef) {}\n\n  /**\n   * Initialize the intersection observer when directive is initialized\n   */\n  ngOnInit(): void {\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {\n      // Create observer with options\n      this.observer = new IntersectionObserver((entries) => {\n        this.handleIntersection(entries);\n      }, {\n        root: null, // Use viewport as root\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      \n      // Start observing the element\n      if (this.el && this.el.nativeElement) {\n        this.observer.observe(this.el.nativeElement);\n      }\n    }\n  }\n\n  /**\n   * Clean up observer when directive is destroyed to prevent memory leaks\n   */\n  ngOnDestroy(): void {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  private handleIntersection(entries: IntersectionObserverEntry[]): void {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        \n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;;AAKA,SAASA,SAAS,EAAcC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AAKpE,WAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAW5B;;;;EAIAC,YAAoBC,EAAc;IAAd,KAAAA,EAAE,GAAFA,EAAE;IAdtB;;;IAGU,KAAAC,WAAW,GAAG,IAAIL,YAAY,EAAQ;IAEhD;;;IAGQ,KAAAM,QAAQ,GAAQ,IAAI;EAMS;EAErC;;;EAGAC,QAAQA,CAAA;IACN,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,sBAAsB,IAAIA,MAAM,EAAE;MACrE;MACA,IAAI,CAACF,QAAQ,GAAG,IAAIG,oBAAoB,CAAEC,OAAO,IAAI;QACnD,IAAI,CAACC,kBAAkB,CAACD,OAAO,CAAC;MAClC,CAAC,EAAE;QACDE,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,GAAG,CAAC;OAChB,CAAC;MAEF;MACA,IAAI,IAAI,CAACV,EAAE,IAAI,IAAI,CAACA,EAAE,CAACW,aAAa,EAAE;QACpC,IAAI,CAACT,QAAQ,CAACU,OAAO,CAAC,IAAI,CAACZ,EAAE,CAACW,aAAa,CAAC;;;EAGlD;EAEA;;;EAGAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACX,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACY,UAAU,EAAE;MAC1B,IAAI,CAACZ,QAAQ,GAAG,IAAI;;EAExB;EAEA;;;;EAIQK,kBAAkBA,CAACD,OAAoC;IAC7DA,OAAO,CAACS,OAAO,CAACC,KAAK,IAAG;MACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxB;QACA,IAAI,CAAChB,WAAW,CAACiB,IAAI,EAAE;QAEvB;QACA,IAAI,IAAI,CAAChB,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,CAACiB,SAAS,CAACH,KAAK,CAACI,MAAM,CAAC;;;IAG3C,CAAC,CAAC;EACJ;CACD;AA7DWC,UAAA,EAATxB,MAAM,EAAE,C,qDAAwC;AAJtCC,iBAAiB,GAAAuB,UAAA,EAH7B1B,SAAS,CAAC;EACT2B,QAAQ,EAAE;CACX,CAAC,C,EACWxB,iBAAiB,CAiE7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}