/**
 * Dashboard page component styles
 */

/* Container for the entire dashboard */
.dashboard-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Dashboard header with title and actions */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

/* Dashboard title */
.dashboard-title {
  font-size: 2rem;
  font-weight: 500;
  margin: 0;
  color: #333;
}

/* Loading container */
.loading-container {
  display: flex;
  justify-content: center;
  padding: 48px 0;
}

/* Dashboard content layout */
.dashboard-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

/* Welcome card */
.welcome-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  
  h2 {
    margin-top: 0;
    color: var(--primary-color);
  }
  
  p {
    color: #666;
    margin-bottom: 0;
  }
}

/* Summary cards section */
.summary-cards {
  margin-bottom: 16px;
}

/* Charts section */
.charts-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

/* Chart card */
.chart-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  
  h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #333;
  }
}

/* Recent tasks section */
.recent-tasks-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  
  h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #333;
  }
}

/* Responsive layout for larger screens */
@media (min-width: 768px) {
  .charts-section {
    grid-template-columns: 1fr 1fr;
  }
}

/* Responsive layout adjustments for smaller screens */
@media (max-width: 600px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .dashboard-title {
    font-size: 1.5rem;
  }
}
