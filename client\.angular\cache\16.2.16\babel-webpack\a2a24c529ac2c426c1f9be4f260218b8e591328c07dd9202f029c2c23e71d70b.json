{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\nimport { GuestGuard } from './core/guards/guest.guard';\nimport { CustomPreloadingStrategy } from './core/strategies/custom-preloading.strategy';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n/**\n * Application routes configuration\n * Implements lazy loading for better performance\n */\nconst routes = [{\n  path: '',\n  redirectTo: 'dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule),\n  canActivate: [GuestGuard]\n}, {\n  path: 'dashboard',\n  loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'tasks',\n  loadChildren: () => import('./features/tasks/tasks.module').then(m => m.TasksModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'profile',\n  loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule),\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  loadChildren: () => import('./features/not-found/not-found.module').then(m => m.NotFoundModule)\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, {\n        initialNavigation: 'enabledBlocking',\n        scrollPositionRestoration: 'enabled',\n        relativeLinkResolution: 'legacy',\n        // Preload all modules for better UX after initial load\n        preloadingStrategy: CustomPreloadingStrategy\n      }), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CustomPreloadingStrategy", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "AuthModule", "canActivate", "DashboardModule", "TasksModule", "ProfileModule", "NotFoundModule", "AppRoutingModule", "forRoot", "initialNavigation", "scrollPositionRestoration", "relativeLinkResolution", "preloadingStrategy", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\app-routing.module.ts"], "sourcesContent": ["/**\n * Main routing module for the application\n * Defines routes and lazy loading strategy\n */\nimport { NgModule } from '@angular/core';\nimport { RouterModule, Routes, PreloadAllModules } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\nimport { GuestGuard } from './core/guards/guest.guard';\nimport { CustomPreloadingStrategy } from './core/strategies/custom-preloading.strategy';\n\n/**\n * Application routes configuration\n * Implements lazy loading for better performance\n */\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: 'dashboard',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule),\n    canActivate: [GuestGuard]\n  },\n  {\n    path: 'dashboard',\n    loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'tasks',\n    loadChildren: () => import('./features/tasks/tasks.module').then(m => m.TasksModule),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: '**',\n    loadChildren: () => import('./features/not-found/not-found.module').then(m => m.NotFoundModule)\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes, {\n    initialNavigation: 'enabledBlocking', // For SSR\n    scrollPositionRestoration: 'enabled', // Scroll to top on navigation\n    relativeLinkResolution: 'legacy',\n    // Preload all modules for better UX after initial load\n    preloadingStrategy: CustomPreloadingStrategy\n  })],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AAKA,SAASA,YAAY,QAAmC,iBAAiB;AACzE,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,wBAAwB,QAAQ,8CAA8C;;;AAEvF;;;;AAIA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,WAAW;EACvBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC;EACjFC,WAAW,EAAE,CAACV,UAAU;CACzB,EACD;EACEG,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,eAAe,CAAC;EAChGD,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,WAAW,CAAC;EACpFF,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,aAAa,CAAC;EAC1FH,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,IAAI;EACVG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,cAAc;CAC/F,CACF;AAYD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBATjBjB,YAAY,CAACkB,OAAO,CAACd,MAAM,EAAE;QACrCe,iBAAiB,EAAE,iBAAiB;QACpCC,yBAAyB,EAAE,SAAS;QACpCC,sBAAsB,EAAE,QAAQ;QAChC;QACAC,kBAAkB,EAAEnB;OACrB,CAAC,EACQH,YAAY;IAAA;EAAA;;;2EAEXiB,gBAAgB;IAAAM,OAAA,GAAAC,EAAA,CAAAxB,YAAA;IAAAyB,OAAA,GAFjBzB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}