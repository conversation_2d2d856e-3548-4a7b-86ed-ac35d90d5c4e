<!-- Dashboard page container -->
<div class="dashboard-container">
  <!-- Page header -->
  <header class="dashboard-header">
    <h1 class="dashboard-title">Dashboard</h1>
    <div class="dashboard-actions">
      <button mat-raised-button color="primary" routerLink="/tasks/create">
        <mat-icon>add</mat-icon>
        New Task
      </button>
    </div>
  </header>

  <!-- Loading spinner -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Error message -->
  <app-error-message 
    *ngIf="error" 
    [message]="error">
  </app-error-message>

  <!-- Dashboard content -->
  <div *ngIf="!loading && !error" class="dashboard-content">
    <!-- Welcome message -->
    <div class="welcome-card mat-elevation-z2">
      <h2>Welcome, {{ currentUser?.name || 'User' }}!</h2>
      <p>Here's an overview of your tasks</p>
    </div>

    <!-- Task summary cards -->
    <div class="summary-cards">
      <app-task-summary 
        [todoCount]="taskStatusCounts.todo"
        [inProgressCount]="taskStatusCounts.inProgress"
        [doneCount]="taskStatusCounts.done"
        [totalCount]="tasks.length">
      </app-task-summary>
    </div>

    <!-- Charts section -->
    <div class="charts-section">
      <div class="chart-card mat-elevation-z2">
        <h3>Task Status</h3>
        <app-task-status-chart 
          [todoCount]="taskStatusCounts.todo"
          [inProgressCount]="taskStatusCounts.inProgress"
          [doneCount]="taskStatusCounts.done">
        </app-task-status-chart>
      </div>

      <div class="chart-card mat-elevation-z2">
        <h3>Task Priority</h3>
        <app-task-priority-chart
          [highCount]="taskPriorityCounts.high"
          [mediumCount]="taskPriorityCounts.medium"
          [lowCount]="taskPriorityCounts.low">
        </app-task-priority-chart>
      </div>
    </div>

    <!-- Recent tasks section -->
    <div class="recent-tasks-section mat-elevation-z2">
      <h3>Recent Tasks</h3>
      <app-recent-tasks 
        [tasks]="recentTasks">
      </app-recent-tasks>
    </div>
  </div>
</div>
