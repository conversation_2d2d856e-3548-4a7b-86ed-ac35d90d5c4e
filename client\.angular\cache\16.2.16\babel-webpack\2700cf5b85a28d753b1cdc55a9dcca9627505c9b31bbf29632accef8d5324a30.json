{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n// Components\nimport { LoginFormComponent } from './components/login-form/login-form.component';\nimport { RegisterFormComponent } from './components/register-form/register-form.component';\nimport { ForgotPasswordFormComponent } from './components/forgot-password-form/forgot-password-form.component';\nimport { ResetPasswordFormComponent } from './components/reset-password-form/reset-password-form.component';\n// Pages\nimport { LoginPageComponent } from './pages/login-page/login-page.component';\nimport { RegisterPageComponent } from './pages/register-page/register-page.component';\nimport { ForgotPasswordPageComponent } from './pages/forgot-password-page/forgot-password-page.component';\nimport { ResetPasswordPageComponent } from './pages/reset-password-page/reset-password-page.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n/**\n * Routes for the auth feature module\n */\nconst routes = [{\n  path: 'login',\n  component: LoginPageComponent\n}, {\n  path: 'register',\n  component: RegisterPageComponent\n}, {\n  path: 'forgot-password',\n  component: ForgotPasswordPageComponent\n}, {\n  path: 'reset-password',\n  component: ResetPasswordPageComponent\n}, {\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}];\nexport class AuthModule {\n  static {\n    this.ɵfac = function AuthModule_Factory(t) {\n      return new (t || AuthModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [LoginFormComponent, RegisterFormComponent, ForgotPasswordFormComponent, ResetPasswordFormComponent, LoginPageComponent, RegisterPageComponent, ForgotPasswordPageComponent, ResetPasswordPageComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "LoginFormComponent", "RegisterFormComponent", "ForgotPasswordFormComponent", "ResetPasswordFormComponent", "LoginPageComponent", "RegisterPageComponent", "ForgotPasswordPageComponent", "ResetPasswordPageComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AuthModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\auth.module.ts"], "sourcesContent": ["/**\n * Auth Module\n * Feature module for authentication functionality\n */\nimport { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n\n// Components\nimport { LoginFormComponent } from './components/login-form/login-form.component';\nimport { RegisterFormComponent } from './components/register-form/register-form.component';\nimport { ForgotPasswordFormComponent } from './components/forgot-password-form/forgot-password-form.component';\nimport { ResetPasswordFormComponent } from './components/reset-password-form/reset-password-form.component';\n\n// Pages\nimport { LoginPageComponent } from './pages/login-page/login-page.component';\nimport { RegisterPageComponent } from './pages/register-page/register-page.component';\nimport { ForgotPasswordPageComponent } from './pages/forgot-password-page/forgot-password-page.component';\nimport { ResetPasswordPageComponent } from './pages/reset-password-page/reset-password-page.component';\n\n/**\n * Routes for the auth feature module\n */\nconst routes: Routes = [\n  {\n    path: 'login',\n    component: LoginPageComponent\n  },\n  {\n    path: 'register',\n    component: RegisterPageComponent\n  },\n  {\n    path: 'forgot-password',\n    component: ForgotPasswordPageComponent\n  },\n  {\n    path: 'reset-password',\n    component: ResetPasswordPageComponent\n  },\n  {\n    path: '',\n    redirectTo: 'login',\n    pathMatch: 'full'\n  }\n];\n\n@NgModule({\n  declarations: [\n    LoginFormComponent,\n    RegisterFormComponent,\n    ForgotPasswordFormComponent,\n    ResetPasswordFormComponent,\n    LoginPageComponent,\n    RegisterPageComponent,\n    ForgotPasswordPageComponent,\n    ResetPasswordPageComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class AuthModule { }\n"], "mappings": "AAKA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AAEzD;AACA,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,2BAA2B,QAAQ,kEAAkE;AAC9G,SAASC,0BAA0B,QAAQ,gEAAgE;AAE3G;AACA,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,2BAA2B,QAAQ,6DAA6D;AACzG,SAASC,0BAA0B,QAAQ,2DAA2D;;;AAEtG;;;AAGA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,EAAE;EACRE,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,CACF;AAkBD,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAJnBd,YAAY,EACZD,YAAY,CAACgB,QAAQ,CAACN,MAAM,CAAC;IAAA;EAAA;;;2EAGpBK,UAAU;IAAAE,YAAA,GAdnBf,kBAAkB,EAClBC,qBAAqB,EACrBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,kBAAkB,EAClBC,qBAAqB,EACrBC,2BAA2B,EAC3BC,0BAA0B;IAAAS,OAAA,GAG1BjB,YAAY,EAAAkB,EAAA,CAAAnB,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}