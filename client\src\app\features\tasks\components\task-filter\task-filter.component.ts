/**
 * Task Filter Component
 * Provides filtering options for the task list
 */
import { Component, Input, Output, EventEmitter, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { TaskFilter } from '../../../../core/models/task-filter.model';

@Component({
  selector: 'app-task-filter',
  templateUrl: './task-filter.component.html',
  styleUrls: ['./task-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskFilterComponent implements OnInit {
  /**
   * Current filter applied to tasks
   */
  @Input() filter: TaskFilter = {};
  
  /**
   * Event emitted when filter is changed
   */
  @Output() filterChanged = new EventEmitter<TaskFilter>();
  
  /**
   * Filter form group
   */
  filterForm!: FormGroup;
  
  /**
   * Whether advanced filters are shown
   */
  showAdvancedFilters = false;
  
  /**
   * Available task statuses
   */
  statuses = [
    { value: '', label: 'All Statuses' },
    { value: 'todo', label: 'To Do' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'review', label: 'Review' },
    { value: 'done', label: 'Done' }
  ];
  
  /**
   * Available task priorities
   */
  priorities = [
    { value: '', label: 'All Priorities' },
    { value: 'high', label: 'High' },
    { value: 'medium', label: 'Medium' },
    { value: 'low', label: 'Low' }
  ];

  /**
   * Constructor with dependency injection
   * @param fb - Form builder service
   */
  constructor(private fb: FormBuilder) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Sets up the filter form
   */
  ngOnInit(): void {
    this.initFilterForm();
  }

  /**
   * Initialize the filter form with current filter values
   */
  initFilterForm(): void {
    this.filterForm = this.fb.group({
      search: [this.filter.search || ''],
      status: [this.filter.status || ''],
      priority: [this.filter.priority || ''],
      assigneeId: [this.filter.assigneeId || ''],
      tags: [this.filter.tags || []],
      dueDateFrom: [this.filter.dueDateFrom || null],
      dueDateTo: [this.filter.dueDateTo || null]
    });
    
    // Subscribe to form value changes
    this.filterForm.valueChanges
      .pipe(
        debounceTime(300), // Wait for 300ms pause in events
        distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr))
      )
      .subscribe(values => {
        // Clean empty values
        const filter: TaskFilter = {};
        
        Object.keys(values).forEach(key => {
          const value = values[key];
          if (value !== null && value !== '' && !(Array.isArray(value) && value.length === 0)) {
            filter[key] = value;
          }
        });
        
        this.filterChanged.emit(filter);
      });
  }

  /**
   * Toggle advanced filters visibility
   */
  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.filterForm.reset({
      search: '',
      status: '',
      priority: '',
      assigneeId: '',
      tags: [],
      dueDateFrom: null,
      dueDateTo: null
    });
  }
}
