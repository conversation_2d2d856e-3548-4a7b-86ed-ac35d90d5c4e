/**
 * User Repository Interface
 * Defines the contract for user data access operations
 */
import { IUser, IUserDocument } from '../entities/User';

/**
 * Interface for User Repository
 * Follows repository pattern to abstract data access operations
 */
export interface IUserRepository {
  /**
   * Creates a new user
   * @param userData - User data to create
   * @returns Promise resolving to created user
   */
  create(userData: IUser): Promise<IUserDocument>;
  
  /**
   * Finds a user by ID
   * @param id - User ID
   * @returns Promise resolving to found user or null if not found
   */
  findById(id: string): Promise<IUserDocument | null>;
  
  /**
   * Finds a user by email
   * @param email - User email
   * @returns Promise resolving to found user or null if not found
   */
  findByEmail(email: string): Promise<IUserDocument | null>;
  
  /**
   * Finds all users with optional filtering
   * @param filter - Optional filter criteria
   * @returns Promise resolving to array of users
   */
  findAll(filter?: Partial<IUser>): Promise<IUserDocument[]>;
  
  /**
   * Updates a user by ID
   * @param id - User ID
   * @param updateData - Data to update
   * @returns Promise resolving to updated user or null if not found
   */
  update(id: string, updateData: Partial<IUser>): Promise<IUserDocument | null>;
  
  /**
   * Deletes a user by ID
   * @param id - User ID
   * @returns Promise resolving to true if deleted, false if not found
   */
  delete(id: string): Promise<boolean>;
}
