/**
 * Task Controller
 * Handles HTTP requests for task-related operations
 */
import { Request, Response, NextFunction } from 'express';
import { MongoTaskRepository } from '../repositories/MongoTaskRepository';
import { CreateTaskUseCase } from '../../domain/use-cases/task/CreateTaskUseCase';
import { GetTaskUseCase } from '../../domain/use-cases/task/GetTaskUseCase';
import { GetAllTasksUseCase } from '../../domain/use-cases/task/GetAllTasksUseCase';
import { UpdateTaskUseCase } from '../../domain/use-cases/task/UpdateTaskUseCase';
import { DeleteTaskUseCase } from '../../domain/use-cases/task/DeleteTaskUseCase';
import { TaskStatus, TaskPriority } from '../../domain/entities/Task';
import mongoose from 'mongoose';

// Create repository instance
const taskRepository = new MongoTaskRepository();

/**
 * @swagger
 * /api/tasks:
 *   post:
 *     summary: Create a new task
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [todo, in-progress, done]
 *               assigneeId:
 *                 type: string
 *               dueDate:
 *                 type: string
 *                 format: date-time
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high]
 *     responses:
 *       201:
 *         description: Task created successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const createTask = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const createTaskUseCase = new CreateTaskUseCase(taskRepository);
    
    // Convert assigneeId string to ObjectId if present
    if (req.body.assigneeId && typeof req.body.assigneeId === 'string') {
      req.body.assigneeId = new mongoose.Types.ObjectId(req.body.assigneeId);
    }
    
    // Execute use case
    const task = await createTaskUseCase.execute(req.body);
    
    // Send response
    res.status(201).json({
      success: true,
      data: task,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/tasks/{id}:
 *   get:
 *     summary: Get a task by ID
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Task retrieved successfully
 *       404:
 *         description: Task not found
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const getTask = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const getTaskUseCase = new GetTaskUseCase(taskRepository);
    
    // Execute use case
    const task = await getTaskUseCase.execute(req.params.id);
    
    // Send response
    res.status(200).json({
      success: true,
      data: task,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/tasks:
 *   get:
 *     summary: Get all tasks with optional filtering
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [todo, in-progress, done]
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, medium, high]
 *       - in: query
 *         name: assigneeId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Tasks retrieved successfully
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const getAllTasks = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const getAllTasksUseCase = new GetAllTasksUseCase(taskRepository);
    
    // Build filter from query parameters
    const filter: any = {};
    
    if (req.query.status && Object.values(TaskStatus).includes(req.query.status as TaskStatus)) {
      filter.status = req.query.status;
    }
    
    if (req.query.priority && Object.values(TaskPriority).includes(req.query.priority as TaskPriority)) {
      filter.priority = req.query.priority;
    }
    
    if (req.query.assigneeId) {
      filter.assigneeId = new mongoose.Types.ObjectId(req.query.assigneeId as string);
    }
    
    // Execute use case
    const tasks = await getAllTasksUseCase.execute(filter);
    
    // Send response
    res.status(200).json({
      success: true,
      count: tasks.length,
      data: tasks,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/tasks/{id}:
 *   put:
 *     summary: Update a task
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [todo, in-progress, done]
 *               assigneeId:
 *                 type: string
 *               dueDate:
 *                 type: string
 *                 format: date-time
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high]
 *     responses:
 *       200:
 *         description: Task updated successfully
 *       400:
 *         description: Invalid request data
 *       404:
 *         description: Task not found
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const updateTask = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const updateTaskUseCase = new UpdateTaskUseCase(taskRepository);
    
    // Convert assigneeId string to ObjectId if present
    if (req.body.assigneeId && typeof req.body.assigneeId === 'string') {
      req.body.assigneeId = new mongoose.Types.ObjectId(req.body.assigneeId);
    }
    
    // Execute use case
    const task = await updateTaskUseCase.execute(req.params.id, req.body);
    
    // Send response
    res.status(200).json({
      success: true,
      data: task,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/tasks/{id}:
 *   delete:
 *     summary: Delete a task
 *     tags: [Tasks]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Task deleted successfully
 *       404:
 *         description: Task not found
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const deleteTask = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const deleteTaskUseCase = new DeleteTaskUseCase(taskRepository);
    
    // Execute use case
    await deleteTaskUseCase.execute(req.params.id);
    
    // Send response
    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    next(error);
  }
};
