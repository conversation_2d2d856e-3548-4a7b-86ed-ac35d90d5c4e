/**
 * Register Form Component
 * Handles user registration with name, email, and password
 */
import { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';

@Component({
  selector: 'app-register-form',
  templateUrl: './register-form.component.html',
  styleUrls: ['./register-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RegisterFormComponent implements OnInit {
  /**
   * Loading state indicator
   */
  @Input() loading = false;
  
  /**
   * Error message from failed registration attempt
   */
  @Input() error: string | null = null;
  
  /**
   * Event emitted when form is submitted
   */
  @Output() formSubmit = new EventEmitter<{ name: string; email: string; password: string }>();
  
  /**
   * Event emitted when login link is clicked
   */
  @Output() login = new EventEmitter<void>();
  
  /**
   * Registration form group
   */
  registerForm!: FormGroup;
  
  /**
   * Flag to toggle password visibility
   */
  hidePassword = true;
  
  /**
   * Flag to toggle confirm password visibility
   */
  hideConfirmPassword = true;

  /**
   * Constructor with dependency injection
   * @param fb - FormBuilder for reactive forms
   */
  constructor(private fb: FormBuilder) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Initializes the registration form
   */
  ngOnInit(): void {
    this.initForm();
  }

  /**
   * Initialize registration form with validation
   */
  private initForm(): void {
    this.registerForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [
        Validators.required, 
        Validators.minLength(8),
        this.passwordStrengthValidator
      ]],
      confirmPassword: ['', Validators.required],
      termsAccepted: [false, Validators.requiredTrue]
    }, { validators: this.passwordMatchValidator });
  }

  /**
   * Custom validator for password strength
   * @param control - Form control
   * @returns Validation errors or null
   */
  private passwordStrengthValidator(control: AbstractControl): ValidationErrors | null {
    const value: string = control.value || '';
    
    if (!value) {
      return null;
    }
    
    const hasUpperCase = /[A-Z]+/.test(value);
    const hasLowerCase = /[a-z]+/.test(value);
    const hasNumeric = /[0-9]+/.test(value);
    const hasSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]+/.test(value);
    
    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;
    
    return !passwordValid ? { passwordStrength: true } : null;
  }

  /**
   * Custom validator for password matching
   * @param group - Form group
   * @returns Validation errors or null
   */
  private passwordMatchValidator(group: AbstractControl): ValidationErrors | null {
    const password = group.get('password')?.value;
    const confirmPassword = group.get('confirmPassword')?.value;
    
    return password === confirmPassword ? null : { passwordMismatch: true };
  }

  /**
   * Handle form submission
   */
  onSubmit(): void {
    if (this.registerForm.invalid || this.loading) {
      // Mark all fields as touched to trigger validation messages
      this.registerForm.markAllAsTouched();
      return;
    }
    
    const { name, email, password } = this.registerForm.value;
    this.formSubmit.emit({ name, email, password });
  }

  /**
   * Handle login link click
   */
  onLogin(): void {
    this.login.emit();
  }

  /**
   * Check if form control has error
   * @param controlName - Name of form control
   * @param errorName - Name of error
   * @returns True if control has error
   */
  hasError(controlName: string, errorName: string): boolean {
    const control = this.registerForm.get(controlName);
    return !!(control && control.touched && control.hasError(errorName));
  }

  /**
   * Check if form has error
   * @param errorName - Name of error
   * @returns True if form has error
   */
  hasFormError(errorName: string): boolean {
    return this.registerForm.touched && this.registerForm.hasError(errorName);
  }

  /**
   * Toggle password visibility
   */
  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  /**
   * Toggle confirm password visibility
   */
  toggleConfirmPasswordVisibility(): void {
    this.hideConfirmPassword = !this.hideConfirmPassword;
  }
}
