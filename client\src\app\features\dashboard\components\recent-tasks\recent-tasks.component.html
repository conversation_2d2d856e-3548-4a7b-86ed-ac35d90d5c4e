<!-- Recent tasks table -->
<div class="recent-tasks-container">
  <!-- Empty state message -->
  <div *ngIf="tasks.length === 0" class="empty-state">
    <mat-icon>assignment</mat-icon>
    <p>No tasks available</p>
  </div>

  <!-- Tasks table -->
  <table mat-table [dataSource]="tasks" class="tasks-table" *ngIf="tasks.length > 0">
    <!-- Title Column -->
    <ng-container matColumnDef="title">
      <th mat-header-cell *matHeaderCellDef>Task</th>
      <td mat-cell *matCellDef="let task">{{ task.title }}</td>
    </ng-container>

    <!-- Priority Column -->
    <ng-container matColumnDef="priority">
      <th mat-header-cell *matHeaderCellDef>Priority</th>
      <td mat-cell *matCellDef="let task">
        <span class="priority-badge" [ngClass]="getPriorityClass(task.priority)">
          {{ task.priority | titlecase }}
        </span>
      </td>
    </ng-container>

    <!-- Status Column -->
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>Status</th>
      <td mat-cell *matCellDef="let task">
        <span class="status-badge" [ngClass]="getStatusClass(task.status)">
          {{ formatStatus(task.status) }}
        </span>
      </td>
    </ng-container>

    <!-- Updated Date Column -->
    <ng-container matColumnDef="updatedAt">
      <th mat-header-cell *matHeaderCellDef>Updated</th>
      <td mat-cell *matCellDef="let task">
        {{ task.updatedAt | date:'short' }}
      </td>
    </ng-container>

    <!-- Actions Column -->
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef></th>
      <td mat-cell *matCellDef="let task">
        <button 
          mat-icon-button 
          color="primary" 
          (click)="viewTask(task._id)"
          aria-label="View task details">
          <mat-icon>visibility</mat-icon>
        </button>
      </td>
    </ng-container>

    <!-- Table Header and Rows -->
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr 
      mat-row 
      *matRowDef="let row; columns: displayedColumns;"
      (click)="viewTask(row._id)"
      class="task-row">
    </tr>
  </table>

  <!-- View all tasks link -->
  <div class="view-all-container" *ngIf="tasks.length > 0">
    <a mat-button color="primary" routerLink="/tasks">
      View All Tasks
      <mat-icon>arrow_forward</mat-icon>
    </a>
  </div>
</div>
