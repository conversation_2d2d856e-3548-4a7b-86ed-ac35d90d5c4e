/**
 * Recent Tasks Component
 * Displays a list of recently updated tasks
 */
import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { Task } from '../../../../core/models/task.model';

@Component({
  selector: 'app-recent-tasks',
  templateUrl: './recent-tasks.component.html',
  styleUrls: ['./recent-tasks.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RecentTasksComponent {
  /**
   * List of recent tasks
   */
  @Input() tasks: Task[] = [];
  
  /**
   * Columns to display in the task table
   */
  displayedColumns: string[] = ['title', 'priority', 'status', 'updatedAt', 'actions'];
  
  /**
   * Constructor with dependency injection
   * @param router - Router for navigation
   */
  constructor() {}
  
  /**
   * Navigate to task detail page
   * @param taskId - ID of the task to view
   */
  viewTask(taskId: string): void {
    // Navigation would be handled by parent component or router service
    console.log('Navigate to task:', taskId);
  }
  
  /**
   * Get CSS class for task priority
   * @param priority - Task priority
   * @returns CSS class name
   */
  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'high':
        return 'priority-high';
      case 'medium':
        return 'priority-medium';
      case 'low':
        return 'priority-low';
      default:
        return '';
    }
  }
  
  /**
   * Get CSS class for task status
   * @param status - Task status
   * @returns CSS class name
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'todo':
        return 'status-todo';
      case 'in_progress':
        return 'status-in-progress';
      case 'done':
        return 'status-done';
      default:
        return '';
    }
  }
  
  /**
   * Format task status for display
   * @param status - Task status
   * @returns Formatted status string
   */
  formatStatus(status: string): string {
    switch (status) {
      case 'todo':
        return 'To Do';
      case 'in_progress':
        return 'In Progress';
      case 'done':
        return 'Done';
      default:
        return status;
    }
  }
}
