/**
 * Base API Service
 * Provides common HTTP functionality for all API services
 */
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders as HttpHeadersClass, HttpParams as HttpParamsClass } from '@angular/common/http';
import type { Observable } from 'rxjs';
import { throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  /**
   * API base URL from environment
   */
  private apiUrl = environment.apiUrl;
  
  /**
   * Constructor with dependency injection
   * @param http - HttpClient for making HTTP requests
   */
  constructor(private http: HttpClient) {}
  
  /**
   * Create default headers for API requests
   * @returns HttpHeaders object with default headers
   */
  private createDefaultHeaders(): HttpHeadersClass {
    return new HttpHeadersClass({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });
  }
  
  /**
   * Handle HTTP errors
   * @param error - HTTP error
   * @returns Observable with error
   */
  private handleError(error: unknown): Observable<never> {
    // Type guard for error object
    const err = error as { 
      error?: { message?: string, instanceof?: unknown }, 
      status?: number, 
      message?: string 
    };
    let errorMessage = 'An unknown error occurred';
    
    if (err.error && 'instanceof' in err.error && err.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${(err.error as ErrorEvent).message}`;
    } else {
      // Server-side error
      errorMessage = err.error?.message || 
                    `Error Code: ${err.status || 'unknown'}\nMessage: ${err.message || 'Unknown error'}`;
    }
    
    // Log error message to help with debugging
    // console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
  
  /**
   * Make GET request to API
   * @param path - API endpoint path
   * @param params - Optional query parameters
   * @returns Observable with response data
   */
  get<T>(path: string, params: HttpParamsClass = new HttpParamsClass()): Observable<T> {
    return this.http.get<T>(`${this.apiUrl}/${path}`, {
      headers: this.createDefaultHeaders(),
      params
    }).pipe(
      catchError((error: unknown) => this.handleError(error))
    );
  }
  
  /**
   * Make POST request to API
   * @param path - API endpoint path
   * @param body - Request body
   * @returns Observable with response data
   */
  post<T>(path: string, body: unknown): Observable<T> {
    return this.http.post<T>(`${this.apiUrl}/${path}`, body, {
      headers: this.createDefaultHeaders()
    }).pipe(
      catchError((error: unknown) => this.handleError(error))
    );
  }
  
  /**
   * Make PUT request to API
   * @param path - API endpoint path
   * @param body - Request body
   * @returns Observable with response data
   */
  put<T>(path: string, body: unknown): Observable<T> {
    return this.http.put<T>(`${this.apiUrl}/${path}`, body, {
      headers: this.createDefaultHeaders()
    }).pipe(
      catchError((error: unknown) => this.handleError(error))
    );
  }
  
  /**
   * Make PATCH request to API
   * @param path - API endpoint path
   * @param body - Request body
   * @returns Observable with response data
   */
  patch<T>(path: string, body: unknown): Observable<T> {
    return this.http.patch<T>(`${this.apiUrl}/${path}`, body, {
      headers: this.createDefaultHeaders()
    }).pipe(
      catchError((error: unknown) => this.handleError(error))
    );
  }
  
  /**
   * Make DELETE request to API
   * @param path - API endpoint path
   * @returns Observable with response data
   */
  delete<T>(path: string): Observable<T> {
    return this.http.delete<T>(`${this.apiUrl}/${path}`, {
      headers: this.createDefaultHeaders()
    }).pipe(
      catchError((error: unknown) => this.handleError(error))
    );
  }
}
