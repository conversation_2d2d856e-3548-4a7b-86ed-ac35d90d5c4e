/**
 * Task status chart component styles
 */

/* Chart container */
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 250px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Chart visualization */
.chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Chart bars container */
.chart-bars {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  width: 100%;
  height: 180px;
  margin-bottom: 16px;
}

/* Individual bar container */
.chart-bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
  max-width: 80px;
}

/* Chart bar */
.chart-bar {
  width: 100%;
  border-radius: 4px 4px 0 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 8px;
  transition: height 0.3s ease;
  min-height: 20px;
}

/* Bar colors */
.todo-bar {
  background-color: rgba(255, 152, 0, 0.7);
}

.in-progress-bar {
  background-color: rgba(33, 150, 243, 0.7);
}

.done-bar {
  background-color: rgba(76, 175, 80, 0.7);
}

/* Chart value */
.chart-value {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Chart label */
.chart-label {
  margin-top: 8px;
  font-size: 0.8rem;
  text-align: center;
  color: #666;
}

/* Chart legend */
.chart-legend {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 16px;
}

/* Legend item */
.legend-item {
  display: flex;
  align-items: center;
  margin: 0 8px 8px;
}

/* Legend color box */
.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  margin-right: 4px;
}

/* Legend color variants */
.todo-color {
  background-color: rgba(255, 152, 0, 0.7);
}

.in-progress-color {
  background-color: rgba(33, 150, 243, 0.7);
}

.done-color {
  background-color: rgba(76, 175, 80, 0.7);
}

/* Legend label */
.legend-label {
  font-size: 0.8rem;
  color: #666;
}

/* Empty chart state */
.empty-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #9e9e9e;
  
  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 1rem;
    margin: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .chart-bars {
    height: 150px;
  }
  
  .chart-bar-container {
    width: 28%;
  }
  
  .chart-value {
    font-size: 0.8rem;
  }
  
  .chart-label {
    font-size: 0.7rem;
  }
}
