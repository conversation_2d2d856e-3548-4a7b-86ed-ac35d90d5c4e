/**
 * Lazy Load Directive
 * Defers loading of elements until they are in viewport
 * Improves performance by reducing initial load time
 */
import { Directive, ElementRef, EventEmitter, Output } from '@angular/core';

@Directive({
  selector: '[appLazyLoad]'
})
export class LazyLoadDirective {
  /**
   * Event emitted when element enters viewport
   */
  @Output() appLazyLoad = new EventEmitter();
  
  /**
   * Intersection observer instance
   */
  private observer: any = null;

  /**
   * Constructor with dependency injection
   * @param elementRef - Reference to the host element
   */
  constructor(private elementRef: any) {}

  /**
   * Initialize the intersection observer when directive is initialized
   */
  ngOnInit(): void {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      // Create observer with options
      this.observer = new IntersectionObserver((entries) => {
        this.handleIntersection(entries);
      }, {
        root: null, // Use viewport as root
        rootMargin: '0px',
        threshold: 0.1 // Trigger when 10% of element is visible
      });
      
      // Start observing the element
      if (this.elementRef && this.elementRef.nativeElement) {
        this.observer.observe(this.elementRef.nativeElement);
      }
    }
  }

  /**
   * Clean up observer when directive is destroyed to prevent memory leaks
   */
  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }

  /**
   * Handle intersection events
   * @param entries - Intersection observer entries
   */
  private handleIntersection(entries: any[]): void {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // Element is now visible in viewport
        this.appLazyLoad.emit();
        
        // Stop observing once triggered
        if (this.observer) {
          this.observer.unobserve(entry.target);
        }
      }
    });
  }
}
