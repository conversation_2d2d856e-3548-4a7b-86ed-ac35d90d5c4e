{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/icon\";\nexport class TaskSummaryComponent {\n  constructor() {\n    /**\n     * Number of tasks with 'todo' status\n     */\n    this.todoCount = 0;\n    /**\n     * Number of tasks with 'in_progress' status\n     */\n    this.inProgressCount = 0;\n    /**\n     * Number of tasks with 'done' status\n     */\n    this.doneCount = 0;\n    /**\n     * Total number of tasks\n     */\n    this.totalCount = 0;\n  }\n  static {\n    this.ɵfac = function TaskSummaryComponent_Factory(t) {\n      return new (t || TaskSummaryComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskSummaryComponent,\n      selectors: [[\"app-task-summary\"]],\n      inputs: {\n        todoCount: \"todoCount\",\n        inProgressCount: \"inProgressCount\",\n        doneCount: \"doneCount\",\n        totalCount: \"totalCount\"\n      },\n      decls: 41,\n      vars: 4,\n      consts: [[1, \"summary-container\"], [1, \"summary-card\", \"total-card\", \"mat-elevation-z2\"], [1, \"card-content\"], [1, \"card-icon\"], [1, \"card-data\"], [1, \"card-value\"], [1, \"card-label\"], [1, \"summary-card\", \"todo-card\", \"mat-elevation-z2\"], [1, \"summary-card\", \"in-progress-card\", \"mat-elevation-z2\"], [1, \"summary-card\", \"done-card\", \"mat-elevation-z2\"]],\n      template: function TaskSummaryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"assignment\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"span\", 5);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"span\", 6);\n          i0.ɵɵtext(10, \"Total Tasks\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 2)(13, \"div\", 3)(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"assignment_late\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 4)(17, \"span\", 5);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 6);\n          i0.ɵɵtext(20, \"To Do\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 8)(22, \"div\", 2)(23, \"div\", 3)(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"hourglass_top\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 4)(27, \"span\", 5);\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 6);\n          i0.ɵɵtext(30, \"In Progress\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 2)(33, \"div\", 3)(34, \"mat-icon\");\n          i0.ɵɵtext(35, \"task_alt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 4)(37, \"span\", 5);\n          i0.ɵɵtext(38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 6);\n          i0.ɵɵtext(40, \"Done\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.totalCount);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.todoCount);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.inProgressCount);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.doneCount);\n        }\n      },\n      dependencies: [i1.MatIcon],\n      styles: [\"\\n\\n\\n\\n\\n\\n.summary-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));\\n  gap: 16px;\\n}\\n\\n\\n\\n.summary-card[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 8px;\\n  padding: 16px;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n.summary-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n\\n.card-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  margin-right: 16px;\\n}\\n.card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  height: 24px;\\n  width: 24px;\\n  color: #fff;\\n}\\n\\n\\n\\n.card-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n\\n\\n.card-value[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 500;\\n  line-height: 1.2;\\n}\\n\\n\\n\\n.card-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n\\n\\n.total-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  background-color: #3f51b5;\\n}\\n.total-card[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n}\\n\\n\\n\\n.todo-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n}\\n.todo-card[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n\\n\\n\\n.in-progress-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  background-color: #2196f3;\\n}\\n.in-progress-card[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n}\\n\\n\\n\\n.done-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n.done-card[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n\\n\\n@media (max-width: 600px) {\\n  .summary-container[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n  .card-value[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["TaskSummaryComponent", "constructor", "todoCount", "inProgressCount", "doneCount", "totalCount", "selectors", "inputs", "decls", "vars", "consts", "template", "TaskSummaryComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\task-summary\\task-summary.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\task-summary\\task-summary.component.html"], "sourcesContent": ["/**\n * Task Summary Component\n * Displays summary cards with task counts by status\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-task-summary',\n  templateUrl: './task-summary.component.html',\n  styleUrls: ['./task-summary.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskSummaryComponent {\n  /**\n   * Number of tasks with 'todo' status\n   */\n  @Input() todoCount = 0;\n  \n  /**\n   * Number of tasks with 'in_progress' status\n   */\n  @Input() inProgressCount = 0;\n  \n  /**\n   * Number of tasks with 'done' status\n   */\n  @Input() doneCount = 0;\n  \n  /**\n   * Total number of tasks\n   */\n  @Input() totalCount = 0;\n}\n", "<!-- Task summary cards container -->\n<div class=\"summary-container\">\n  <!-- Total tasks card -->\n  <div class=\"summary-card total-card mat-elevation-z2\">\n    <div class=\"card-content\">\n      <div class=\"card-icon\">\n        <mat-icon>assignment</mat-icon>\n      </div>\n      <div class=\"card-data\">\n        <span class=\"card-value\">{{ totalCount }}</span>\n        <span class=\"card-label\">Total Tasks</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Todo tasks card -->\n  <div class=\"summary-card todo-card mat-elevation-z2\">\n    <div class=\"card-content\">\n      <div class=\"card-icon\">\n        <mat-icon>assignment_late</mat-icon>\n      </div>\n      <div class=\"card-data\">\n        <span class=\"card-value\">{{ todoCount }}</span>\n        <span class=\"card-label\">To Do</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- In progress tasks card -->\n  <div class=\"summary-card in-progress-card mat-elevation-z2\">\n    <div class=\"card-content\">\n      <div class=\"card-icon\">\n        <mat-icon>hourglass_top</mat-icon>\n      </div>\n      <div class=\"card-data\">\n        <span class=\"card-value\">{{ inProgressCount }}</span>\n        <span class=\"card-label\">In Progress</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Done tasks card -->\n  <div class=\"summary-card done-card mat-elevation-z2\">\n    <div class=\"card-content\">\n      <div class=\"card-icon\">\n        <mat-icon>task_alt</mat-icon>\n      </div>\n      <div class=\"card-data\">\n        <span class=\"card-value\">{{ doneCount }}</span>\n        <span class=\"card-label\">Done</span>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAYA,OAAM,MAAOA,oBAAoB;EANjCC,YAAA;IAOE;;;IAGS,KAAAC,SAAS,GAAG,CAAC;IAEtB;;;IAGS,KAAAC,eAAe,GAAG,CAAC;IAE5B;;;IAGS,KAAAC,SAAS,GAAG,CAAC;IAEtB;;;IAGS,KAAAC,UAAU,GAAG,CAAC;;;;uBAnBZL,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAM,SAAA;MAAAC,MAAA;QAAAL,SAAA;QAAAC,eAAA;QAAAC,SAAA;QAAAC,UAAA;MAAA;MAAAG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXjCE,EAAA,CAAAC,cAAA,aAA+B;UAKbD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEjCH,EAAA,CAAAC,cAAA,aAAuB;UACID,EAAA,CAAAE,MAAA,GAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChDH,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAMjDH,EAAA,CAAAC,cAAA,cAAqD;UAGrCD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEtCH,EAAA,CAAAC,cAAA,cAAuB;UACID,EAAA,CAAAE,MAAA,IAAe;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/CH,EAAA,CAAAC,cAAA,eAAyB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAM3CH,EAAA,CAAAC,cAAA,cAA4D;UAG5CD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEpCH,EAAA,CAAAC,cAAA,cAAuB;UACID,EAAA,CAAAE,MAAA,IAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrDH,EAAA,CAAAC,cAAA,eAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAMjDH,EAAA,CAAAC,cAAA,cAAqD;UAGrCD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE/BH,EAAA,CAAAC,cAAA,cAAuB;UACID,EAAA,CAAAE,MAAA,IAAe;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/CH,EAAA,CAAAC,cAAA,eAAyB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;UAxCXH,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAK,iBAAA,CAAAN,GAAA,CAAAT,UAAA,CAAgB;UAahBU,EAAA,CAAAI,SAAA,IAAe;UAAfJ,EAAA,CAAAK,iBAAA,CAAAN,GAAA,CAAAZ,SAAA,CAAe;UAafa,EAAA,CAAAI,SAAA,IAAqB;UAArBJ,EAAA,CAAAK,iBAAA,CAAAN,GAAA,CAAAX,eAAA,CAAqB;UAarBY,EAAA,CAAAI,SAAA,IAAe;UAAfJ,EAAA,CAAAK,iBAAA,CAAAN,GAAA,CAAAV,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}