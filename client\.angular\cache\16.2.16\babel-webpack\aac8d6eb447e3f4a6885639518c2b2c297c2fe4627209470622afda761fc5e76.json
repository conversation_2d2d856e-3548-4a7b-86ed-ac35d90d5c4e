{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/task.service\";\nimport * as i2 from \"../../../../core/services/user.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/progress-spinner\";\nimport * as i9 from \"../../../../shared/components/error-message/error-message.component\";\nimport * as i10 from \"../../components/task-summary/task-summary.component\";\nimport * as i11 from \"../../components/recent-tasks/recent-tasks.component\";\nimport * as i12 from \"../../components/task-status-chart/task-status-chart.component\";\nimport * as i13 from \"../../components/task-priority-chart/task-priority-chart.component\";\nfunction DashboardPageComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"mat-spinner\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardPageComponent_app_error_message_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-error-message\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"message\", ctx_r1.error);\n  }\n}\nfunction DashboardPageComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Here's an overview of your tasks\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 13);\n    i0.ɵɵelement(7, \"app-task-summary\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"div\", 16)(10, \"h3\");\n    i0.ɵɵtext(11, \"Task Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"app-task-status-chart\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 16)(14, \"h3\");\n    i0.ɵɵtext(15, \"Task Priority\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"app-task-priority-chart\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 19)(18, \"h3\");\n    i0.ɵɵtext(19, \"Recent Tasks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"app-recent-tasks\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Welcome, \", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.name) || \"User\", \"!\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"todoCount\", ctx_r2.taskStatusCounts.todo)(\"inProgressCount\", ctx_r2.taskStatusCounts.inProgress)(\"doneCount\", ctx_r2.taskStatusCounts.done)(\"totalCount\", ctx_r2.tasks.length);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"todoCount\", ctx_r2.taskStatusCounts.todo)(\"inProgressCount\", ctx_r2.taskStatusCounts.inProgress)(\"doneCount\", ctx_r2.taskStatusCounts.done);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"highCount\", ctx_r2.taskPriorityCounts.high)(\"mediumCount\", ctx_r2.taskPriorityCounts.medium)(\"lowCount\", ctx_r2.taskPriorityCounts.low);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"tasks\", ctx_r2.recentTasks);\n  }\n}\nexport class DashboardPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service\n   * @param userService - User service\n   * @param authService - Auth service\n   * @param cdr - Change detector reference\n   */\n  constructor(taskService, userService, authService, cdr) {\n    this.taskService = taskService;\n    this.userService = userService;\n    this.authService = authService;\n    this.cdr = cdr;\n    /**\n     * Current user\n     */\n    this.currentUser = null;\n    /**\n     * All tasks\n     */\n    this.tasks = [];\n    /**\n     * Recent tasks (last 5)\n     */\n    this.recentTasks = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = true;\n    /**\n     * Error message\n     */\n    this.error = null;\n    /**\n     * Task counts by status\n     */\n    this.taskStatusCounts = {\n      todo: 0,\n      inProgress: 0,\n      done: 0\n    };\n    /**\n     * Task counts by priority\n     */\n    this.taskPriorityCounts = {\n      high: 0,\n      medium: 0,\n      low: 0\n    };\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads current user and tasks\n   */\n  ngOnInit() {\n    this.loadCurrentUser();\n    this.loadTasks();\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Load current user data\n   */\n  loadCurrentUser() {\n    this.currentUser = this.authService.currentUserValue;\n    // If no current user in memory, try to get from API\n    if (!this.currentUser) {\n      this.authService.getProfile().pipe(takeUntil(this.destroy$)).subscribe({\n        next: user => {\n          this.currentUser = user;\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          console.error('Error loading user profile:', err);\n          this.error = 'Failed to load user profile';\n          this.cdr.markForCheck();\n        }\n      });\n    }\n  }\n  /**\n   * Load all tasks and calculate statistics\n   */\n  loadTasks() {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    this.taskService.getTasks().pipe(takeUntil(this.destroy$)).subscribe({\n      next: tasks => {\n        this.tasks = tasks;\n        this.recentTasks = [...tasks].sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()).slice(0, 5);\n        this.calculateTaskStatistics();\n        this.loading = false;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        console.error('Error loading tasks:', err);\n        this.error = 'Failed to load tasks';\n        this.loading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Calculate task statistics for charts\n   */\n  calculateTaskStatistics() {\n    // Reset counters\n    this.taskStatusCounts = {\n      todo: 0,\n      inProgress: 0,\n      done: 0\n    };\n    this.taskPriorityCounts = {\n      high: 0,\n      medium: 0,\n      low: 0\n    };\n    // Count tasks by status and priority\n    this.tasks.forEach(task => {\n      // Count by status\n      if (task.status === 'todo') {\n        this.taskStatusCounts.todo++;\n      } else if (task.status === 'in_progress') {\n        this.taskStatusCounts.inProgress++;\n      } else if (task.status === 'done') {\n        this.taskStatusCounts.done++;\n      }\n      // Count by priority\n      if (task.priority === 'high') {\n        this.taskPriorityCounts.high++;\n      } else if (task.priority === 'medium') {\n        this.taskPriorityCounts.medium++;\n      } else if (task.priority === 'low') {\n        this.taskPriorityCounts.low++;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function DashboardPageComponent_Factory(t) {\n      return new (t || DashboardPageComponent)(i0.ɵɵdirectiveInject(i1.TaskService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardPageComponent,\n      selectors: [[\"app-dashboard-page\"]],\n      decls: 12,\n      vars: 3,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"dashboard-title\"], [1, \"dashboard-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/tasks/create\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [3, \"message\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [3, \"message\"], [1, \"dashboard-content\"], [1, \"welcome-card\", \"mat-elevation-z2\"], [1, \"summary-cards\"], [3, \"todoCount\", \"inProgressCount\", \"doneCount\", \"totalCount\"], [1, \"charts-section\"], [1, \"chart-card\", \"mat-elevation-z2\"], [3, \"todoCount\", \"inProgressCount\", \"doneCount\"], [3, \"highCount\", \"mediumCount\", \"lowCount\"], [1, \"recent-tasks-section\", \"mat-elevation-z2\"], [3, \"tasks\"]],\n      template: function DashboardPageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"button\", 4)(6, \"mat-icon\");\n          i0.ɵɵtext(7, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(8, \" New Task \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(9, DashboardPageComponent_div_9_Template, 2, 0, \"div\", 5);\n          i0.ɵɵtemplate(10, DashboardPageComponent_app_error_message_10_Template, 1, 1, \"app-error-message\", 6);\n          i0.ɵɵtemplate(11, DashboardPageComponent_div_11_Template, 21, 12, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n        }\n      },\n      dependencies: [i4.NgIf, i5.RouterLink, i6.MatButton, i7.MatIcon, i8.MatProgressSpinner, i9.ErrorMessageComponent, i10.TaskSummaryComponent, i11.RecentTasksComponent, i12.TaskStatusChartComponent, i13.TaskPriorityChartComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.dashboard-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n\\n\\n\\n.dashboard-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 500;\\n  margin: 0;\\n  color: #333;\\n}\\n\\n\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 48px 0;\\n}\\n\\n\\n\\n.dashboard-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 24px;\\n}\\n\\n\\n\\n.welcome-card[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 8px;\\n  padding: 24px;\\n}\\n.welcome-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  color: var(--primary-color);\\n}\\n.welcome-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n\\n\\n.summary-cards[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n\\n\\n.charts-section[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 24px;\\n  margin-bottom: 24px;\\n}\\n\\n\\n\\n.chart-card[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 8px;\\n  padding: 24px;\\n}\\n.chart-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 16px;\\n  color: #333;\\n}\\n\\n\\n\\n.recent-tasks-section[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 8px;\\n  padding: 24px;\\n}\\n.recent-tasks-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 16px;\\n  color: #333;\\n}\\n\\n\\n\\n@media (min-width: 768px) {\\n  .charts-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n}\\n\\n\\n@media (max-width: 600px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .dashboard-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "error", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "currentUser", "name", "taskStatusCounts", "todo", "inProgress", "done", "tasks", "length", "taskPriorityCounts", "high", "medium", "low", "recentTasks", "DashboardPageComponent", "constructor", "taskService", "userService", "authService", "cdr", "loading", "destroy$", "ngOnInit", "loadCurrentUser", "loadTasks", "ngOnDestroy", "next", "complete", "currentUserValue", "getProfile", "pipe", "subscribe", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "console", "getTasks", "sort", "a", "b", "Date", "updatedAt", "getTime", "slice", "calculateTaskStatistics", "for<PERSON>ach", "task", "status", "priority", "ɵɵdirectiveInject", "i1", "TaskService", "i2", "UserService", "i3", "AuthService", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "DashboardPageComponent_Template", "rf", "ctx", "ɵɵtemplate", "DashboardPageComponent_div_9_Template", "DashboardPageComponent_app_error_message_10_Template", "DashboardPageComponent_div_11_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\pages\\dashboard-page\\dashboard-page.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\pages\\dashboard-page\\dashboard-page.component.html"], "sourcesContent": ["/**\n * Dashboard Page Component\n * Main dashboard page displaying task summaries and charts\n */\nimport { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { TaskService } from '../../../../core/services/task.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-dashboard-page',\n  templateUrl: './dashboard-page.component.html',\n  styleUrls: ['./dashboard-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class DashboardPageComponent implements OnInit, OnD<PERSON>roy {\n  /**\n   * Current user\n   */\n  currentUser: User | null = null;\n  \n  /**\n   * All tasks\n   */\n  tasks: Task[] = [];\n  \n  /**\n   * Recent tasks (last 5)\n   */\n  recentTasks: Task[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  loading = true;\n  \n  /**\n   * Error message\n   */\n  error: string | null = null;\n  \n  /**\n   * Task counts by status\n   */\n  taskStatusCounts = {\n    todo: 0,\n    inProgress: 0,\n    done: 0\n  };\n  \n  /**\n   * Task counts by priority\n   */\n  taskPriorityCounts = {\n    high: 0,\n    medium: 0,\n    low: 0\n  };\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service\n   * @param userService - User service\n   * @param authService - Auth service\n   * @param cdr - Change detector reference\n   */\n  constructor(\n    private taskService: TaskService,\n    private userService: UserService,\n    private authService: AuthService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads current user and tasks\n   */\n  ngOnInit(): void {\n    this.loadCurrentUser();\n    this.loadTasks();\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Load current user data\n   */\n  private loadCurrentUser(): void {\n    this.currentUser = this.authService.currentUserValue;\n    \n    // If no current user in memory, try to get from API\n    if (!this.currentUser) {\n      this.authService.getProfile()\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: (user) => {\n            this.currentUser = user;\n            this.cdr.markForCheck();\n          },\n          error: (err) => {\n            console.error('Error loading user profile:', err);\n            this.error = 'Failed to load user profile';\n            this.cdr.markForCheck();\n          }\n        });\n    }\n  }\n\n  /**\n   * Load all tasks and calculate statistics\n   */\n  private loadTasks(): void {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    \n    this.taskService.getTasks()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (tasks) => {\n          this.tasks = tasks;\n          this.recentTasks = [...tasks].sort((a, b) => \n            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()\n          ).slice(0, 5);\n          \n          this.calculateTaskStatistics();\n          this.loading = false;\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          console.error('Error loading tasks:', err);\n          this.error = 'Failed to load tasks';\n          this.loading = false;\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Calculate task statistics for charts\n   */\n  private calculateTaskStatistics(): void {\n    // Reset counters\n    this.taskStatusCounts = {\n      todo: 0,\n      inProgress: 0,\n      done: 0\n    };\n    \n    this.taskPriorityCounts = {\n      high: 0,\n      medium: 0,\n      low: 0\n    };\n    \n    // Count tasks by status and priority\n    this.tasks.forEach(task => {\n      // Count by status\n      if (task.status === 'todo') {\n        this.taskStatusCounts.todo++;\n      } else if (task.status === 'in_progress') {\n        this.taskStatusCounts.inProgress++;\n      } else if (task.status === 'done') {\n        this.taskStatusCounts.done++;\n      }\n      \n      // Count by priority\n      if (task.priority === 'high') {\n        this.taskPriorityCounts.high++;\n      } else if (task.priority === 'medium') {\n        this.taskPriorityCounts.medium++;\n      } else if (task.priority === 'low') {\n        this.taskPriorityCounts.low++;\n      }\n    });\n  }\n}\n", "<!-- Dashboard page container -->\n<div class=\"dashboard-container\">\n  <!-- Page header -->\n  <header class=\"dashboard-header\">\n    <h1 class=\"dashboard-title\">Dashboard</h1>\n    <div class=\"dashboard-actions\">\n      <button mat-raised-button color=\"primary\" routerLink=\"/tasks/create\">\n        <mat-icon>add</mat-icon>\n        New Task\n      </button>\n    </div>\n  </header>\n\n  <!-- Loading spinner -->\n  <div *ngIf=\"loading\" class=\"loading-container\">\n    <mat-spinner diameter=\"40\"></mat-spinner>\n  </div>\n\n  <!-- Error message -->\n  <app-error-message \n    *ngIf=\"error\" \n    [message]=\"error\">\n  </app-error-message>\n\n  <!-- Dashboard content -->\n  <div *ngIf=\"!loading && !error\" class=\"dashboard-content\">\n    <!-- Welcome message -->\n    <div class=\"welcome-card mat-elevation-z2\">\n      <h2>Welcome, {{ currentUser?.name || 'User' }}!</h2>\n      <p>Here's an overview of your tasks</p>\n    </div>\n\n    <!-- Task summary cards -->\n    <div class=\"summary-cards\">\n      <app-task-summary \n        [todoCount]=\"taskStatusCounts.todo\"\n        [inProgressCount]=\"taskStatusCounts.inProgress\"\n        [doneCount]=\"taskStatusCounts.done\"\n        [totalCount]=\"tasks.length\">\n      </app-task-summary>\n    </div>\n\n    <!-- Charts section -->\n    <div class=\"charts-section\">\n      <div class=\"chart-card mat-elevation-z2\">\n        <h3>Task Status</h3>\n        <app-task-status-chart \n          [todoCount]=\"taskStatusCounts.todo\"\n          [inProgressCount]=\"taskStatusCounts.inProgress\"\n          [doneCount]=\"taskStatusCounts.done\">\n        </app-task-status-chart>\n      </div>\n\n      <div class=\"chart-card mat-elevation-z2\">\n        <h3>Task Priority</h3>\n        <app-task-priority-chart\n          [highCount]=\"taskPriorityCounts.high\"\n          [mediumCount]=\"taskPriorityCounts.medium\"\n          [lowCount]=\"taskPriorityCounts.low\">\n        </app-task-priority-chart>\n      </div>\n    </div>\n\n    <!-- Recent tasks section -->\n    <div class=\"recent-tasks-section mat-elevation-z2\">\n      <h3>Recent Tasks</h3>\n      <app-recent-tasks \n        [tasks]=\"recentTasks\">\n      </app-recent-tasks>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAKA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;ICQxCC,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,qBAAyC;IAC3CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAE,SAAA,4BAGoB;;;;IADlBF,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAAC,KAAA,CAAiB;;;;;IAInBN,EAAA,CAAAC,cAAA,cAA0D;IAGlDD,EAAA,CAAAO,MAAA,GAA2C;IAAAP,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAO,MAAA,uCAAgC;IAAAP,EAAA,CAAAG,YAAA,EAAI;IAIzCH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,SAAA,2BAKmB;IACrBF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA4B;IAEpBD,EAAA,CAAAO,MAAA,mBAAW;IAAAP,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAE,SAAA,iCAIwB;IAC1BF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAyC;IACnCD,EAAA,CAAAO,MAAA,qBAAa;IAAAP,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAE,SAAA,mCAI0B;IAC5BF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAAmD;IAC7CD,EAAA,CAAAO,MAAA,oBAAY;IAAAP,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAE,SAAA,4BAEmB;IACrBF,EAAA,CAAAG,YAAA,EAAM;;;;IAzCAH,EAAA,CAAAQ,SAAA,GAA2C;IAA3CR,EAAA,CAAAS,kBAAA,eAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,IAAA,iBAA2C;IAO7CZ,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAI,UAAA,cAAAM,MAAA,CAAAG,gBAAA,CAAAC,IAAA,CAAmC,oBAAAJ,MAAA,CAAAG,gBAAA,CAAAE,UAAA,eAAAL,MAAA,CAAAG,gBAAA,CAAAG,IAAA,gBAAAN,MAAA,CAAAO,KAAA,CAAAC,MAAA;IAYjClB,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAI,UAAA,cAAAM,MAAA,CAAAG,gBAAA,CAAAC,IAAA,CAAmC,oBAAAJ,MAAA,CAAAG,gBAAA,CAAAE,UAAA,eAAAL,MAAA,CAAAG,gBAAA,CAAAG,IAAA;IASnChB,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAI,UAAA,cAAAM,MAAA,CAAAS,kBAAA,CAAAC,IAAA,CAAqC,gBAAAV,MAAA,CAAAS,kBAAA,CAAAE,MAAA,cAAAX,MAAA,CAAAS,kBAAA,CAAAG,GAAA;IAWvCtB,EAAA,CAAAQ,SAAA,GAAqB;IAArBR,EAAA,CAAAI,UAAA,UAAAM,MAAA,CAAAa,WAAA,CAAqB;;;ADhD7B,OAAM,MAAOC,sBAAsB;EAiDjC;;;;;;;EAOAC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,WAAwB,EACxBC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,GAAG,GAAHA,GAAG;IA3Db;;;IAGA,KAAAlB,WAAW,GAAgB,IAAI;IAE/B;;;IAGA,KAAAM,KAAK,GAAW,EAAE;IAElB;;;IAGA,KAAAM,WAAW,GAAW,EAAE;IAExB;;;IAGA,KAAAO,OAAO,GAAG,IAAI;IAEd;;;IAGA,KAAAxB,KAAK,GAAkB,IAAI;IAE3B;;;IAGA,KAAAO,gBAAgB,GAAG;MACjBC,IAAI,EAAE,CAAC;MACPC,UAAU,EAAE,CAAC;MACbC,IAAI,EAAE;KACP;IAED;;;IAGA,KAAAG,kBAAkB,GAAG;MACnBC,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,CAAC;MACTC,GAAG,EAAE;KACN;IAED;;;IAGQ,KAAAS,QAAQ,GAAG,IAAIjC,OAAO,EAAQ;EAcnC;EAEH;;;;EAIAkC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;EAGQJ,eAAeA,CAAA;IACrB,IAAI,CAACtB,WAAW,GAAG,IAAI,CAACiB,WAAW,CAACU,gBAAgB;IAEpD;IACA,IAAI,CAAC,IAAI,CAAC3B,WAAW,EAAE;MACrB,IAAI,CAACiB,WAAW,CAACW,UAAU,EAAE,CAC1BC,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACgC,QAAQ,CAAC,CAAC,CAC9BU,SAAS,CAAC;QACTL,IAAI,EAAGM,IAAI,IAAI;UACb,IAAI,CAAC/B,WAAW,GAAG+B,IAAI;UACvB,IAAI,CAACb,GAAG,CAACc,YAAY,EAAE;QACzB,CAAC;QACDrC,KAAK,EAAGsC,GAAG,IAAI;UACbC,OAAO,CAACvC,KAAK,CAAC,6BAA6B,EAAEsC,GAAG,CAAC;UACjD,IAAI,CAACtC,KAAK,GAAG,6BAA6B;UAC1C,IAAI,CAACuB,GAAG,CAACc,YAAY,EAAE;QACzB;OACD,CAAC;;EAER;EAEA;;;EAGQT,SAASA,CAAA;IACf,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACxB,KAAK,GAAG,IAAI;IACjB,IAAI,CAACuB,GAAG,CAACc,YAAY,EAAE;IAEvB,IAAI,CAACjB,WAAW,CAACoB,QAAQ,EAAE,CACxBN,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACgC,QAAQ,CAAC,CAAC,CAC9BU,SAAS,CAAC;MACTL,IAAI,EAAGnB,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACM,WAAW,GAAG,CAAC,GAAGN,KAAK,CAAC,CAAC8B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACtC,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,CAACC,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE,CAClE,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEb,IAAI,CAACC,uBAAuB,EAAE;QAC9B,IAAI,CAACxB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACD,GAAG,CAACc,YAAY,EAAE;MACzB,CAAC;MACDrC,KAAK,EAAGsC,GAAG,IAAI;QACbC,OAAO,CAACvC,KAAK,CAAC,sBAAsB,EAAEsC,GAAG,CAAC;QAC1C,IAAI,CAACtC,KAAK,GAAG,sBAAsB;QACnC,IAAI,CAACwB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACD,GAAG,CAACc,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGQW,uBAAuBA,CAAA;IAC7B;IACA,IAAI,CAACzC,gBAAgB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,UAAU,EAAE,CAAC;MACbC,IAAI,EAAE;KACP;IAED,IAAI,CAACG,kBAAkB,GAAG;MACxBC,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,CAAC;MACTC,GAAG,EAAE;KACN;IAED;IACA,IAAI,CAACL,KAAK,CAACsC,OAAO,CAACC,IAAI,IAAG;MACxB;MACA,IAAIA,IAAI,CAACC,MAAM,KAAK,MAAM,EAAE;QAC1B,IAAI,CAAC5C,gBAAgB,CAACC,IAAI,EAAE;OAC7B,MAAM,IAAI0C,IAAI,CAACC,MAAM,KAAK,aAAa,EAAE;QACxC,IAAI,CAAC5C,gBAAgB,CAACE,UAAU,EAAE;OACnC,MAAM,IAAIyC,IAAI,CAACC,MAAM,KAAK,MAAM,EAAE;QACjC,IAAI,CAAC5C,gBAAgB,CAACG,IAAI,EAAE;;MAG9B;MACA,IAAIwC,IAAI,CAACE,QAAQ,KAAK,MAAM,EAAE;QAC5B,IAAI,CAACvC,kBAAkB,CAACC,IAAI,EAAE;OAC/B,MAAM,IAAIoC,IAAI,CAACE,QAAQ,KAAK,QAAQ,EAAE;QACrC,IAAI,CAACvC,kBAAkB,CAACE,MAAM,EAAE;OACjC,MAAM,IAAImC,IAAI,CAACE,QAAQ,KAAK,KAAK,EAAE;QAClC,IAAI,CAACvC,kBAAkB,CAACG,GAAG,EAAE;;IAEjC,CAAC,CAAC;EACJ;;;uBA5KWE,sBAAsB,EAAAxB,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/D,EAAA,CAAA2D,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjE,EAAA,CAAA2D,iBAAA,CAAA3D,EAAA,CAAAkE,iBAAA;IAAA;EAAA;;;YAAtB1C,sBAAsB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBnCzE,EAAA,CAAAC,cAAA,aAAiC;UAGDD,EAAA,CAAAO,MAAA,gBAAS;UAAAP,EAAA,CAAAG,YAAA,EAAK;UAC1CH,EAAA,CAAAC,cAAA,aAA+B;UAEjBD,EAAA,CAAAO,MAAA,UAAG;UAAAP,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAO,MAAA,iBACF;UAAAP,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAA2E,UAAA,IAAAC,qCAAA,iBAEM;UAGN5E,EAAA,CAAA2E,UAAA,KAAAE,oDAAA,+BAGoB;UAGpB7E,EAAA,CAAA2E,UAAA,KAAAG,sCAAA,mBA6CM;UACR9E,EAAA,CAAAG,YAAA,EAAM;;;UAzDEH,EAAA,CAAAQ,SAAA,GAAa;UAAbR,EAAA,CAAAI,UAAA,SAAAsE,GAAA,CAAA5C,OAAA,CAAa;UAMhB9B,EAAA,CAAAQ,SAAA,GAAW;UAAXR,EAAA,CAAAI,UAAA,SAAAsE,GAAA,CAAApE,KAAA,CAAW;UAKRN,EAAA,CAAAQ,SAAA,GAAwB;UAAxBR,EAAA,CAAAI,UAAA,UAAAsE,GAAA,CAAA5C,OAAA,KAAA4C,GAAA,CAAApE,KAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}