/**
 * User Controller
 * Handles HTTP requests for user-related operations
 */
import { Request, Response, NextFunction } from 'express';
import { MongoUserRepository } from '../repositories/MongoUserRepository';
import { CreateUserUseCase } from '../../domain/use-cases/user/CreateUserUseCase';
import { GetUserUseCase } from '../../domain/use-cases/user/GetUserUseCase';
import { GetAllUsersUseCase } from '../../domain/use-cases/user/GetAllUsersUseCase';
import { UpdateUserUseCase } from '../../domain/use-cases/user/UpdateUserUseCase';
import { DeleteUserUseCase } from '../../domain/use-cases/user/DeleteUserUseCase';
import { UserRole } from '../../domain/entities/User';

// Create repository instance
const userRepository = new MongoUserRepository();

/**
 * @swagger
 * /api/users:
 *   post:
 *     summary: Create a new user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 format: password
 *               role:
 *                 type: string
 *                 enum: [user, admin]
 *     responses:
 *       201:
 *         description: User created successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const createUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const createUserUseCase = new CreateUserUseCase(userRepository);
    
    // Execute use case
    const user = await createUserUseCase.execute(req.body);
    
    // Send response
    res.status(201).json({
      success: true,
      data: user,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/users/{id}:
 *   get:
 *     summary: Get a user by ID
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *       404:
 *         description: User not found
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const getUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const getUserUseCase = new GetUserUseCase(userRepository);
    
    // Execute use case
    const user = await getUserUseCase.execute(req.params.id);
    
    // Send response
    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/users:
 *   get:
 *     summary: Get all users with optional filtering
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [user, admin]
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const getAllUsers = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const getAllUsersUseCase = new GetAllUsersUseCase(userRepository);
    
    // Build filter from query parameters
    const filter: any = {};
    
    if (req.query.role && Object.values(UserRole).includes(req.query.role as UserRole)) {
      filter.role = req.query.role;
    }
    
    // Execute use case
    const users = await getAllUsersUseCase.execute(filter);
    
    // Send response
    res.status(200).json({
      success: true,
      count: users.length,
      data: users,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/users/{id}:
 *   put:
 *     summary: Update a user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 format: password
 *               role:
 *                 type: string
 *                 enum: [user, admin]
 *     responses:
 *       200:
 *         description: User updated successfully
 *       400:
 *         description: Invalid request data
 *       404:
 *         description: User not found
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const updateUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const updateUserUseCase = new UpdateUserUseCase(userRepository);
    
    // Execute use case
    const user = await updateUserUseCase.execute(req.params.id, req.body);
    
    // Send response
    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/users/{id}:
 *   delete:
 *     summary: Delete a user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User deleted successfully
 *       404:
 *         description: User not found
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const deleteUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const deleteUserUseCase = new DeleteUserUseCase(userRepository);
    
    // Execute use case
    await deleteUserUseCase.execute(req.params.id);
    
    // Send response
    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    next(error);
  }
};
