{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Task Priority Chart Component\n * Displays a chart showing task distribution by priority\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\nexport let TaskPriorityChartComponent = class TaskPriorityChartComponent {\n  constructor() {\n    /**\n     * Number of tasks with 'high' priority\n     */\n    this.highCount = 0;\n    /**\n     * Number of tasks with 'medium' priority\n     */\n    this.mediumCount = 0;\n    /**\n     * Number of tasks with 'low' priority\n     */\n    this.lowCount = 0;\n    /**\n     * Chart data for rendering\n     */\n    this.chartData = [];\n    /**\n     * Chart view dimensions\n     */\n    this.view = [300, 200];\n    /**\n     * Chart color scheme\n     */\n    this.colorScheme = {\n      domain: ['#f44336', '#ff9800', '#4caf50']\n    };\n    /**\n     * Flag to show/hide chart labels\n     */\n    this.showLabels = true;\n    /**\n     * Flag to enable/disable chart animations\n     */\n    this.animations = true;\n    /**\n     * Flag to show/hide legend\n     */\n    this.showLegend = true;\n    /**\n     * Flag to enable/disable gradient fills\n     */\n    this.gradient = false;\n  }\n  /**\n   * Lifecycle hook that is called when input properties change\n   * Updates chart data when counts change\n   * @param changes - Input changes\n   */\n  ngOnChanges(changes) {\n    this.updateChartData();\n  }\n  /**\n   * Update chart data based on current counts\n   */\n  updateChartData() {\n    this.chartData = [{\n      name: 'High',\n      value: this.highCount\n    }, {\n      name: 'Medium',\n      value: this.mediumCount\n    }, {\n      name: 'Low',\n      value: this.lowCount\n    }];\n  }\n  /**\n   * Calculate the height percentage for a bar based on its count\n   * @param count - The count value for the bar\n   * @returns Percentage height (0-100)\n   */\n  getBarHeight(count) {\n    const total = this.highCount + this.mediumCount + this.lowCount;\n    if (total === 0) return 0;\n    // Calculate percentage with a minimum height of 5% for visibility\n    const percentage = count / total * 100;\n    return Math.max(percentage, 5);\n  }\n};\n__decorate([Input()], TaskPriorityChartComponent.prototype, \"highCount\", void 0);\n__decorate([Input()], TaskPriorityChartComponent.prototype, \"mediumCount\", void 0);\n__decorate([Input()], TaskPriorityChartComponent.prototype, \"lowCount\", void 0);\nTaskPriorityChartComponent = __decorate([Component({\n  selector: 'app-task-priority-chart',\n  templateUrl: './task-priority-chart.component.html',\n  styleUrls: ['./task-priority-chart.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskPriorityChartComponent);", "map": {"version": 3, "names": ["Component", "Input", "ChangeDetectionStrategy", "TaskPriorityChartComponent", "constructor", "highCount", "mediumCount", "lowCount", "chartData", "view", "colorScheme", "domain", "showLabels", "animations", "showLegend", "gradient", "ngOnChanges", "changes", "updateChartData", "name", "value", "getBarHeight", "count", "total", "percentage", "Math", "max", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\task-priority-chart\\task-priority-chart.component.ts"], "sourcesContent": ["/**\n * Task Priority Chart Component\n * Displays a chart showing task distribution by priority\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-task-priority-chart',\n  templateUrl: './task-priority-chart.component.html',\n  styleUrls: ['./task-priority-chart.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskPriorityChartComponent {\n  /**\n   * Number of tasks with 'high' priority\n   */\n  @Input() highCount = 0;\n  \n  /**\n   * Number of tasks with 'medium' priority\n   */\n  @Input() mediumCount = 0;\n  \n  /**\n   * Number of tasks with 'low' priority\n   */\n  @Input() lowCount = 0;\n  \n  /**\n   * Chart data for rendering\n   */\n  chartData: any[] = [];\n  \n  /**\n   * Chart view dimensions\n   */\n  view: [number, number] = [300, 200];\n  \n  /**\n   * Chart color scheme\n   */\n  colorScheme = {\n    domain: ['#f44336', '#ff9800', '#4caf50']\n  };\n  \n  /**\n   * Flag to show/hide chart labels\n   */\n  showLabels = true;\n  \n  /**\n   * Flag to enable/disable chart animations\n   */\n  animations = true;\n  \n  /**\n   * Flag to show/hide legend\n   */\n  showLegend = true;\n  \n  /**\n   * Flag to enable/disable gradient fills\n   */\n  gradient = false;\n  \n  /**\n   * Lifecycle hook that is called when input properties change\n   * Updates chart data when counts change\n   * @param changes - Input changes\n   */\n  ngOnChanges(changes: any): void {\n    this.updateChartData();\n  }\n  \n  /**\n   * Update chart data based on current counts\n   */\n  private updateChartData(): void {\n    this.chartData = [\n      {\n        name: 'High',\n        value: this.highCount\n      },\n      {\n        name: 'Medium',\n        value: this.mediumCount\n      },\n      {\n        name: 'Low',\n        value: this.lowCount\n      }\n    ];\n  }\n  \n  /**\n   * Calculate the height percentage for a bar based on its count\n   * @param count - The count value for the bar\n   * @returns Percentage height (0-100)\n   */\n  getBarHeight(count: number): number {\n    const total = this.highCount + this.mediumCount + this.lowCount;\n    if (total === 0) return 0;\n    \n    // Calculate percentage with a minimum height of 5% for visibility\n    const percentage = (count / total) * 100;\n    return Math.max(percentage, 5);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,eAAe;AAQlE,WAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAAhCC,YAAA;IACL;;;IAGS,KAAAC,SAAS,GAAG,CAAC;IAEtB;;;IAGS,KAAAC,WAAW,GAAG,CAAC;IAExB;;;IAGS,KAAAC,QAAQ,GAAG,CAAC;IAErB;;;IAGA,KAAAC,SAAS,GAAU,EAAE;IAErB;;;IAGA,KAAAC,IAAI,GAAqB,CAAC,GAAG,EAAE,GAAG,CAAC;IAEnC;;;IAGA,KAAAC,WAAW,GAAG;MACZC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;KACzC;IAED;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,QAAQ,GAAG,KAAK;EA4ClB;EA1CE;;;;;EAKAC,WAAWA,CAACC,OAAY;IACtB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGQA,eAAeA,CAAA;IACrB,IAAI,CAACV,SAAS,GAAG,CACf;MACEW,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI,CAACf;KACb,EACD;MACEc,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,IAAI,CAACd;KACb,EACD;MACEa,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,IAAI,CAACb;KACb,CACF;EACH;EAEA;;;;;EAKAc,YAAYA,CAACC,KAAa;IACxB,MAAMC,KAAK,GAAG,IAAI,CAAClB,SAAS,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,QAAQ;IAC/D,IAAIgB,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzB;IACA,MAAMC,UAAU,GAAIF,KAAK,GAAGC,KAAK,GAAI,GAAG;IACxC,OAAOE,IAAI,CAACC,GAAG,CAACF,UAAU,EAAE,CAAC,CAAC;EAChC;CACD;AA3FUG,UAAA,EAAR1B,KAAK,EAAE,C,4DAAe;AAKd0B,UAAA,EAAR1B,KAAK,EAAE,C,8DAAiB;AAKhB0B,UAAA,EAAR1B,KAAK,EAAE,C,2DAAc;AAdXE,0BAA0B,GAAAwB,UAAA,EANtC3B,SAAS,CAAC;EACT4B,QAAQ,EAAE,yBAAyB;EACnCC,WAAW,EAAE,sCAAsC;EACnDC,SAAS,EAAE,CAAC,sCAAsC,CAAC;EACnDC,eAAe,EAAE7B,uBAAuB,CAAC8B;CAC1C,CAAC,C,EACW7B,0BAA0B,CA+FtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}