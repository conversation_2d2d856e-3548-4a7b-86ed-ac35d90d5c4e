# Task Management Application

## Recent Updates

- Fixed Angular Material module imports in CoreModule
- Created missing feature modules (profile.module and not-found.module)
- Added global styles.scss file
- Fixed property naming issues (assignee vs assigneeId)
- Added tslib dependency for TypeScript helpers



A full-stack task management application built with the MEAN stack (MongoDB, Express.js, Angular, Node.js) using TypeScript. This application follows clean architecture principles and implements SOLID design patterns.

## Features

- **User Authentication**: Secure login, registration, and password reset functionality
- **Task Management**: Create, read, update, and delete tasks
- **Task Assignment**: Assign tasks to different users
- **Task Filtering**: Filter tasks by status, priority, and assignee
- **Dashboard**: Overview of task statistics and important metrics
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

### Backend
- **Node.js** with **Express.js** framework
- **TypeScript** for type safety
- **MongoDB** for database
- **Jest** for testing
- **Clean Architecture** for code organization

### Frontend
- **Angular 16** with TypeScript
- **Angular Material** for UI components
- **RxJS** for reactive programming
- **Feature Modules** for better code organization
- **Lazy Loading** for improved performance

### DevOps
- **GitHub Actions** for CI/CD
- **pnpm** for package management

## Project Structure

```
├── client/                 # Angular frontend application
│   ├── src/
│   │   ├── app/
│   │   │   ├── core/       # Core modules, services, and models
│   │   │   ├── features/   # Feature modules (tasks, auth, dashboard)
│   │   │   └── shared/     # Shared components, directives, and pipes
│   │   └── environments/   # Environment configuration
│   └── ...
├── server/                 # Node.js backend application
│   ├── src/
│   │   ├── domain/        # Domain entities and business rules
│   │   ├── infrastructure/ # Database and external services
│   │   └── ...
│   └── ...
└── ...
```

## Recent Updates

- Refactored Angular API services to use a centralized base ApiService
- Fixed TypeScript lint errors and improved type safety
- Implemented proper error handling in API communication
- Added explicit type annotations in RxJS operators
- Updated authentication system with token validation

## Getting Started

### Prerequisites

- Node.js (v16+)
- pnpm (v7+)
- MongoDB

### Installation

1. Clone the repository
   ```bash
   git clone https://github.com/your-username/task-management-application.git
   cd task-management-application
   ```

2. Install dependencies for both client and server
   ```bash
   # Install server dependencies
   cd server
   pnpm install
   
   # Install client dependencies
   cd ../client
   pnpm install
   ```

3. Set up environment variables
   ```bash
   # In the server directory
   cp .env.example .env
   # Edit .env with your MongoDB connection string and JWT secret
   ```

4. Start the development servers
   ```bash
   # Start the backend server
   cd server
   pnpm dev
   
   # In another terminal, start the frontend server
   cd client
   pnpm start
   ```

5. Open your browser and navigate to `http://localhost:4200`

   The backend API will be available at `http://localhost:8000/api`
   Swagger documentation is available at `http://localhost:8000/api-docs`

## Testing

```bash
# Run backend tests
cd server
pnpm test

# Run frontend tests
cd client
pnpm test
```

## Deployment

The application is configured with GitHub Actions for CI/CD. Push to the main branch will trigger:

1. Linting and testing
2. Building the application
3. Deployment to the hosting platform

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
