/**
 * Custom Preloading Strategy
 * Allows selective preloading of Angular modules based on data in route configuration
 */
import { Injectable } from '@angular/core';
import { of } from 'rxjs';
import type { PreloadingStrategy, Route } from '@angular/router';
import type { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CustomPreloadingStrategy implements PreloadingStrategy {
  /**
   * Determines which routes to preload based on data.preload property
   * @param route - Route configuration
   * @param load - Function to load the module
   * @returns Observable that completes when preloading is done
   */
  preload(route: Route, load: () => Observable<unknown>): Observable<unknown> {
    // Check if the route has data and preload flag is set to true
    if (route.data && route.data['preload']) {
      // Log which route is being preloaded
      // Log which route is being preloaded for debugging
      // eslint-disable-next-line no-console
      console.log('Preloaded:', route.path);
      
      // Return the loading Observable
      return load();
    } else {
      // Skip preloading
      return of(null);
    }
  }
}
