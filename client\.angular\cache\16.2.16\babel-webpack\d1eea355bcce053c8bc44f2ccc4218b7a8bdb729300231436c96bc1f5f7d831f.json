{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Error Interceptor\n * Handles HTTP errors globally and provides consistent error handling\n */\nimport { Injectable } from '@angular/core';\nimport { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nexport let ErrorInterceptor = class ErrorInterceptor {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying errors\n   * @param router - Router for navigation\n   */\n  constructor(authService, notificationService, router) {\n    this.authService = authService;\n    this.notificationService = notificationService;\n    this.router = router;\n  }\n  /**\n   * Intercepts HTTP responses and handles errors\n   * @param request - The outgoing HTTP request\n   * @param next - The next interceptor in the chain\n   * @returns Observable of HTTP event\n   */\n  intercept(request, next) {\n    return next.handle(request).pipe(catchError(error => {\n      let errorMessage = 'An unknown error occurred';\n      // Handle different types of errors\n      if (error.error instanceof ErrorEvent) {\n        // Client-side error\n        errorMessage = `Error: ${error.error.message}`;\n      } else {\n        // Server-side error\n        switch (error.status) {\n          case 400:\n            // Bad request\n            errorMessage = error.error?.message || 'Bad request';\n            break;\n          case 401:\n            // Unauthorized\n            errorMessage = 'You are not authorized to access this resource';\n            // Logout user if token is invalid\n            this.authService.logout();\n            this.router.navigate(['/auth/login']);\n            break;\n          case 403:\n            // Forbidden\n            errorMessage = 'You do not have permission to access this resource';\n            break;\n          case 404:\n            // Not found\n            errorMessage = 'Resource not found';\n            break;\n          case 500:\n            // Server error\n            errorMessage = 'Server error. Please try again later';\n            break;\n          default:\n            // Other errors\n            errorMessage = error.error?.message || `Error ${error.status}: ${error.statusText}`;\n            break;\n        }\n      }\n      // Show error notification\n      this.notificationService.error(errorMessage);\n      // Pass the error along\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n};\nErrorInterceptor = __decorate([Injectable()], ErrorInterceptor);", "map": {"version": 3, "names": ["Injectable", "throwError", "catchError", "ErrorInterceptor", "constructor", "authService", "notificationService", "router", "intercept", "request", "next", "handle", "pipe", "error", "errorMessage", "ErrorEvent", "message", "status", "logout", "navigate", "statusText", "Error", "__decorate"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\interceptors\\error.interceptor.ts"], "sourcesContent": ["/**\n * Error Interceptor\n * Handles HTTP errors globally and provides consistent error handling\n */\nimport { Injectable } from '@angular/core';\nimport {\n  HttpRequest,\n  HttpHandler,\n  HttpEvent,\n  HttpInterceptor,\n  HttpErrorResponse\n} from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\nimport { NotificationService } from '../services/notification.service';\n\n@Injectable()\nexport class ErrorInterceptor implements HttpInterceptor {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying errors\n   * @param router - Router for navigation\n   */\n  constructor(\n    private authService: AuthService,\n    private notificationService: NotificationService,\n    private router: Router\n  ) {}\n\n  /**\n   * Intercepts HTTP responses and handles errors\n   * @param request - The outgoing HTTP request\n   * @param next - The next interceptor in the chain\n   * @returns Observable of HTTP event\n   */\n  intercept(request: HttpRequest<unknown>, next: HttpHand<PERSON>): Observable<HttpEvent<unknown>> {\n    return next.handle(request).pipe(\n      catchError((error: HttpErrorResponse) => {\n        let errorMessage = 'An unknown error occurred';\n        \n        // Handle different types of errors\n        if (error.error instanceof ErrorEvent) {\n          // Client-side error\n          errorMessage = `Error: ${error.error.message}`;\n        } else {\n          // Server-side error\n          switch (error.status) {\n            case 400:\n              // Bad request\n              errorMessage = error.error?.message || 'Bad request';\n              break;\n              \n            case 401:\n              // Unauthorized\n              errorMessage = 'You are not authorized to access this resource';\n              // Logout user if token is invalid\n              this.authService.logout();\n              this.router.navigate(['/auth/login']);\n              break;\n              \n            case 403:\n              // Forbidden\n              errorMessage = 'You do not have permission to access this resource';\n              break;\n              \n            case 404:\n              // Not found\n              errorMessage = 'Resource not found';\n              break;\n              \n            case 500:\n              // Server error\n              errorMessage = 'Server error. Please try again later';\n              break;\n              \n            default:\n              // Other errors\n              errorMessage = error.error?.message || `Error ${error.status}: ${error.statusText}`;\n              break;\n          }\n        }\n        \n        // Show error notification\n        this.notificationService.error(errorMessage);\n        \n        // Pass the error along\n        return throwError(() => new Error(errorMessage));\n      })\n    );\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,UAAU,QAAQ,eAAe;AAQ1C,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;AAMpC,WAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAC3B;;;;;;EAMAC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;EACb;EAEH;;;;;;EAMAC,SAASA,CAACC,OAA6B,EAAEC,IAAiB;IACxD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC9BV,UAAU,CAAEW,KAAwB,IAAI;MACtC,IAAIC,YAAY,GAAG,2BAA2B;MAE9C;MACA,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;QACrC;QACAD,YAAY,GAAG,UAAUD,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;OAC/C,MAAM;QACL;QACA,QAAQH,KAAK,CAACI,MAAM;UAClB,KAAK,GAAG;YACN;YACAH,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,aAAa;YACpD;UAEF,KAAK,GAAG;YACN;YACAF,YAAY,GAAG,gDAAgD;YAC/D;YACA,IAAI,CAACT,WAAW,CAACa,MAAM,EAAE;YACzB,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;YACrC;UAEF,KAAK,GAAG;YACN;YACAL,YAAY,GAAG,oDAAoD;YACnE;UAEF,KAAK,GAAG;YACN;YACAA,YAAY,GAAG,oBAAoB;YACnC;UAEF,KAAK,GAAG;YACN;YACAA,YAAY,GAAG,sCAAsC;YACrD;UAEF;YACE;YACAA,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAI,SAASH,KAAK,CAACI,MAAM,KAAKJ,KAAK,CAACO,UAAU,EAAE;YACnF;;;MAIN;MACA,IAAI,CAACd,mBAAmB,CAACO,KAAK,CAACC,YAAY,CAAC;MAE5C;MACA,OAAOb,UAAU,CAAC,MAAM,IAAIoB,KAAK,CAACP,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACH;CACD;AA1EYX,gBAAgB,GAAAmB,UAAA,EAD5BtB,UAAU,EAAE,C,EACAG,gBAAgB,CA0E5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}