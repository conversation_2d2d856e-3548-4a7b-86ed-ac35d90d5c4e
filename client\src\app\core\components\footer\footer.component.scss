/**
 * Footer component styles
 */

/* Main footer container */
.footer {
  background-color: #f5f5f5;
  border-top: 1px solid #e0e0e0;
  padding: 16px;
  font-size: 0.9rem;
}

/* Footer content layout */
.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

/* Copyright text */
.copyright {
  color: #666;
}

/* Footer links section */
.footer-links {
  display: flex;
  gap: 16px;
  
  a {
    color: #3f51b5;
    text-decoration: none;
    transition: color 0.2s ease;
    
    &:hover {
      color: #303f9f;
      text-decoration: underline;
    }
  }
}

/* Version text */
.version {
  color: #999;
  font-size: 0.8rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}
