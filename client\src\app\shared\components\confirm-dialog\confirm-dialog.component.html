<!-- Confirm dialog container -->
<div class="confirm-dialog">
  <!-- Dialog header with title -->
  <h2 mat-dialog-title>{{ data.title }}</h2>
  
  <!-- Dialog content with message -->
  <mat-dialog-content>
    <p>{{ data.message }}</p>
  </mat-dialog-content>
  
  <!-- Dialog actions with cancel and confirm buttons -->
  <mat-dialog-actions align="end">
    <button 
      mat-button 
      (click)="onCancel()" 
      cdkFocusInitial>
      {{ data.cancelText || 'Cancel' }}
    </button>
    <button 
      mat-button 
      [color]="data.confirmColor || 'primary'" 
      (click)="onConfirm()">
      {{ data.confirmText || 'Confirm' }}
    </button>
  </mat-dialog-actions>
</div>
