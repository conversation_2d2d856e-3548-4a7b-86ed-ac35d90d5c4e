/**
 * Main application styles
 */

/* Container for the entire application */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* Main content area with flex layout */
.main-content {
  display: flex;
  flex: 1;
  position: relative;
  margin-top: 64px; /* Height of the header */
}

/* Container for router outlet content */
.content-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  max-width: 100%;
  transition: padding 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .content-container {
    padding: 16px;
  }
}

/* Performance optimizations */
* {
  will-change: transform, opacity;
  contain: layout style paint;
}

/* Accessibility improvements */
:focus {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}
