/**
 * User model interface
 * Represents a user in the system
 */
export interface User {
  /**
   * Unique identifier for the user
   */
  id: string;
  
  /**
   * User's full name
   */
  name: string;
  
  /**
   * User's email address (used for login)
   */
  email: string;
  
  /**
   * User's role in the system
   */
  role: 'admin' | 'user';
  
  /**
   * URL to user's profile image
   */
  avatarUrl?: string;
  
  /**
   * Date when the user was created
   */
  createdAt: Date;
  
  /**
   * Date when the user was last updated
   */
  updatedAt: Date;
}
