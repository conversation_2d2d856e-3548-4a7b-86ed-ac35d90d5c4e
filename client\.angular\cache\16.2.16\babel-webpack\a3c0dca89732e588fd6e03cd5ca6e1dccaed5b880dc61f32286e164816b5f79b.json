{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nfunction ErrorMessageComponent_mat_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 4);\n    i0.ɵɵtext(1, \"error_outline\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorMessageComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ErrorMessageComponent_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRetry());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Retry \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ErrorMessageComponent {\n  constructor() {\n    /**\n     * Error message to display\n     */\n    this.message = 'An error occurred';\n    /**\n     * Whether to show an icon with the message\n     */\n    this.showIcon = true;\n    /**\n     * Whether to show a retry button\n     */\n    this.showRetry = false;\n    /**\n     * Event handler for retry button click\n     */\n    this.onRetry = () => {};\n  }\n  static {\n    this.ɵfac = function ErrorMessageComponent_Factory(t) {\n      return new (t || ErrorMessageComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ErrorMessageComponent,\n      selectors: [[\"app-error-message\"]],\n      inputs: {\n        message: \"message\",\n        showIcon: \"showIcon\",\n        showRetry: \"showRetry\",\n        onRetry: \"onRetry\"\n      },\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"error-container\"], [\"class\", \"error-icon\", 4, \"ngIf\"], [1, \"error-message\"], [\"mat-button\", \"\", \"color\", \"primary\", \"class\", \"retry-button\", 3, \"click\", 4, \"ngIf\"], [1, \"error-icon\"], [\"mat-button\", \"\", \"color\", \"primary\", 1, \"retry-button\", 3, \"click\"]],\n      template: function ErrorMessageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ErrorMessageComponent_mat_icon_1_Template, 2, 0, \"mat-icon\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ErrorMessageComponent_button_4_Template, 4, 0, \"button\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showIcon);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showRetry);\n        }\n      },\n      dependencies: [i1.NgIf, i2.MatButton, i3.MatIcon],\n      styles: [\"\\n\\n\\n\\n\\n\\n.error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 16px;\\n  background-color: #ffebee;\\n  border-radius: 4px;\\n  border-left: 4px solid #f44336;\\n  margin: 16px 0;\\n}\\n\\n\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  font-size: 32px;\\n  height: 32px;\\n  width: 32px;\\n  margin-bottom: 8px;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  text-align: center;\\n  margin-bottom: 12px;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.retry-button[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n}\\n.retry-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvZXJyb3ItbWVzc2FnZS9lcnJvci1tZXNzYWdlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOztFQUFBO0FBSUEsb0JBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSw4QkFBQTtFQUNBLGNBQUE7QUFBRjs7QUFHQSxlQUFBO0FBQ0E7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7RUFDQSxXQUFBO0VBQ0Esa0JBQUE7QUFBRjs7QUFHQSx1QkFBQTtBQUNBO0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQUFGOztBQUdBLGlCQUFBO0FBQ0E7RUFDRSxlQUFBO0FBQUY7QUFFRTtFQUNFLGlCQUFBO0FBQUoiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEVycm9yIG1lc3NhZ2UgY29tcG9uZW50IHN0eWxlc1xuICovXG5cbi8qIEVycm9yIGNvbnRhaW5lciAqL1xuLmVycm9yLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmViZWU7XG4gIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjZjQ0MzM2O1xuICBtYXJnaW46IDE2cHggMDtcbn1cblxuLyogRXJyb3IgaWNvbiAqL1xuLmVycm9yLWljb24ge1xuICBjb2xvcjogI2Y0NDMzNjtcbiAgZm9udC1zaXplOiAzMnB4O1xuICBoZWlnaHQ6IDMycHg7XG4gIHdpZHRoOiAzMnB4O1xuICBtYXJnaW4tYm90dG9tOiA4cHg7XG59XG5cbi8qIEVycm9yIG1lc3NhZ2UgdGV4dCAqL1xuLmVycm9yLW1lc3NhZ2Uge1xuICBjb2xvcjogI2QzMmYyZjtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAxMnB4O1xuICBsaW5lLWhlaWdodDogMS41O1xufVxuXG4vKiBSZXRyeSBidXR0b24gKi9cbi5yZXRyeS1idXR0b24ge1xuICBtYXJnaW4tdG9wOiA4cHg7XG4gIFxuICBtYXQtaWNvbiB7XG4gICAgbWFyZ2luLXJpZ2h0OiA0cHg7XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ErrorMessageComponent_button_4_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ErrorMessageComponent", "constructor", "message", "showIcon", "showRetry", "selectors", "inputs", "decls", "vars", "consts", "template", "ErrorMessageComponent_Template", "rf", "ctx", "ɵɵtemplate", "ErrorMessageComponent_mat_icon_1_Template", "ErrorMessageComponent_button_4_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\components\\error-message\\error-message.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\components\\error-message\\error-message.component.html"], "sourcesContent": ["/**\n * Error Message Component\n * Displays error messages with consistent styling\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-error-message',\n  templateUrl: './error-message.component.html',\n  styleUrls: ['./error-message.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ErrorMessageComponent {\n  /**\n   * Error message to display\n   */\n  @Input() message = 'An error occurred';\n  \n  /**\n   * Whether to show an icon with the message\n   */\n  @Input() showIcon = true;\n  \n  /**\n   * Whether to show a retry button\n   */\n  @Input() showRetry = false;\n  \n  /**\n   * Event handler for retry button click\n   */\n  @Input() onRetry: () => void = () => {};\n}\n", "<!-- Error message container -->\n<div class=\"error-container\">\n  <!-- Error icon -->\n  <mat-icon *ngIf=\"showIcon\" class=\"error-icon\">error_outline</mat-icon>\n  \n  <!-- Error message text -->\n  <div class=\"error-message\">{{ message }}</div>\n  \n  <!-- Optional retry button -->\n  <button \n    *ngIf=\"showRetry\" \n    mat-button \n    color=\"primary\" \n    class=\"retry-button\"\n    (click)=\"onRetry()\">\n    <mat-icon>refresh</mat-icon>\n    Retry\n  </button>\n</div>\n"], "mappings": ";;;;;;ICGEA,EAAA,CAAAC,cAAA,kBAA8C;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IAMtEH,EAAA,CAAAC,cAAA,gBAKsB;IAApBD,EAAA,CAAAI,UAAA,mBAAAC,gEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IACnBX,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADLX,OAAM,MAAOS,qBAAqB;EANlCC,YAAA;IAOE;;;IAGS,KAAAC,OAAO,GAAG,mBAAmB;IAEtC;;;IAGS,KAAAC,QAAQ,GAAG,IAAI;IAExB;;;IAGS,KAAAC,SAAS,GAAG,KAAK;IAE1B;;;IAGS,KAAAL,OAAO,GAAe,MAAK,CAAE,CAAC;;;;uBAnB5BC,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAK,SAAA;MAAAC,MAAA;QAAAJ,OAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAL,OAAA;MAAA;MAAAQ,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlCxB,EAAA,CAAAC,cAAA,aAA6B;UAE3BD,EAAA,CAAA0B,UAAA,IAAAC,yCAAA,sBAAsE;UAGtE3B,EAAA,CAAAC,cAAA,aAA2B;UAAAD,EAAA,CAAAE,MAAA,GAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAG9CH,EAAA,CAAA0B,UAAA,IAAAE,uCAAA,oBAQS;UACX5B,EAAA,CAAAG,YAAA,EAAM;;;UAfOH,EAAA,CAAA6B,SAAA,GAAc;UAAd7B,EAAA,CAAA8B,UAAA,SAAAL,GAAA,CAAAV,QAAA,CAAc;UAGEf,EAAA,CAAA6B,SAAA,GAAa;UAAb7B,EAAA,CAAA+B,iBAAA,CAAAN,GAAA,CAAAX,OAAA,CAAa;UAIrCd,EAAA,CAAA6B,SAAA,GAAe;UAAf7B,EAAA,CAAA8B,UAAA,SAAAL,GAAA,CAAAT,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}