{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Task Filter Component\n * Provides filtering options for the task list\n */\nimport { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nexport let TaskFilterComponent = class TaskFilterComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - Form builder service\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Current filter applied to tasks\n     */\n    this.filter = {};\n    /**\n     * Event emitted when filter is changed\n     */\n    this.filterChanged = new EventEmitter();\n    /**\n     * Whether advanced filters are shown\n     */\n    this.showAdvancedFilters = false;\n    /**\n     * Available task statuses\n     */\n    this.statuses = [{\n      value: '',\n      label: 'All Statuses'\n    }, {\n      value: 'todo',\n      label: 'To Do'\n    }, {\n      value: 'in_progress',\n      label: 'In Progress'\n    }, {\n      value: 'review',\n      label: 'Review'\n    }, {\n      value: 'done',\n      label: 'Done'\n    }];\n    /**\n     * Available task priorities\n     */\n    this.priorities = [{\n      value: '',\n      label: 'All Priorities'\n    }, {\n      value: 'high',\n      label: 'High'\n    }, {\n      value: 'medium',\n      label: 'Medium'\n    }, {\n      value: 'low',\n      label: 'Low'\n    }];\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up the filter form\n   */\n  ngOnInit() {\n    this.initFilterForm();\n  }\n  /**\n   * Initialize the filter form with current filter values\n   */\n  initFilterForm() {\n    this.filterForm = this.fb.group({\n      search: [this.filter.search || ''],\n      status: [this.filter.status || ''],\n      priority: [this.filter.priority || ''],\n      assigneeId: [this.filter.assigneeId || ''],\n      tags: [this.filter.tags || []],\n      dueDateFrom: [this.filter.dueDateFrom || null],\n      dueDateTo: [this.filter.dueDateTo || null]\n    });\n    // Subscribe to form value changes\n    this.filterForm.valueChanges.pipe(debounceTime(300),\n    // Wait for 300ms pause in events\n    distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr))).subscribe(values => {\n      // Clean empty values\n      const filter = {};\n      Object.keys(values).forEach(key => {\n        const value = values[key];\n        if (value !== null && value !== '' && !(Array.isArray(value) && value.length === 0)) {\n          filter[key] = value;\n        }\n      });\n      this.filterChanged.emit(filter);\n    });\n  }\n  /**\n   * Toggle advanced filters visibility\n   */\n  toggleAdvancedFilters() {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n  /**\n   * Clear all filters\n   */\n  clearFilters() {\n    this.filterForm.reset({\n      search: '',\n      status: '',\n      priority: '',\n      assigneeId: '',\n      tags: [],\n      dueDateFrom: null,\n      dueDateTo: null\n    });\n  }\n};\n__decorate([Input()], TaskFilterComponent.prototype, \"filter\", void 0);\n__decorate([Output()], TaskFilterComponent.prototype, \"filterChanged\", void 0);\nTaskFilterComponent = __decorate([Component({\n  selector: 'app-task-filter',\n  templateUrl: './task-filter.component.html',\n  styleUrls: ['./task-filter.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskFilterComponent);", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "ChangeDetectionStrategy", "debounceTime", "distinctUntilChanged", "TaskFilterComponent", "constructor", "fb", "filter", "filterChanged", "showAdvancedFilters", "statuses", "value", "label", "priorities", "ngOnInit", "initFilterForm", "filterForm", "group", "search", "status", "priority", "assigneeId", "tags", "dueDateFrom", "dueDateTo", "valueChanges", "pipe", "prev", "curr", "JSON", "stringify", "subscribe", "values", "Object", "keys", "for<PERSON>ach", "key", "Array", "isArray", "length", "emit", "toggleAdvancedFilters", "clearFilters", "reset", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\components\\task-filter\\task-filter.component.ts"], "sourcesContent": ["/**\n * Task Filter Component\n * Provides filtering options for the task list\n */\nimport { Component, Input, Output, EventEmitter, OnInit, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { TaskFilter } from '../../../../core/models/task-filter.model';\n\n@Component({\n  selector: 'app-task-filter',\n  templateUrl: './task-filter.component.html',\n  styleUrls: ['./task-filter.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskFilterComponent implements OnInit {\n  /**\n   * Current filter applied to tasks\n   */\n  @Input() filter: TaskFilter = {};\n  \n  /**\n   * Event emitted when filter is changed\n   */\n  @Output() filterChanged = new EventEmitter<TaskFilter>();\n  \n  /**\n   * Filter form group\n   */\n  filterForm!: FormGroup;\n  \n  /**\n   * Whether advanced filters are shown\n   */\n  showAdvancedFilters = false;\n  \n  /**\n   * Available task statuses\n   */\n  statuses = [\n    { value: '', label: 'All Statuses' },\n    { value: 'todo', label: 'To Do' },\n    { value: 'in_progress', label: 'In Progress' },\n    { value: 'review', label: 'Review' },\n    { value: 'done', label: 'Done' }\n  ];\n  \n  /**\n   * Available task priorities\n   */\n  priorities = [\n    { value: '', label: 'All Priorities' },\n    { value: 'high', label: 'High' },\n    { value: 'medium', label: 'Medium' },\n    { value: 'low', label: 'Low' }\n  ];\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - Form builder service\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up the filter form\n   */\n  ngOnInit(): void {\n    this.initFilterForm();\n  }\n\n  /**\n   * Initialize the filter form with current filter values\n   */\n  initFilterForm(): void {\n    this.filterForm = this.fb.group({\n      search: [this.filter.search || ''],\n      status: [this.filter.status || ''],\n      priority: [this.filter.priority || ''],\n      assigneeId: [this.filter.assigneeId || ''],\n      tags: [this.filter.tags || []],\n      dueDateFrom: [this.filter.dueDateFrom || null],\n      dueDateTo: [this.filter.dueDateTo || null]\n    });\n    \n    // Subscribe to form value changes\n    this.filterForm.valueChanges\n      .pipe(\n        debounceTime(300), // Wait for 300ms pause in events\n        distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr))\n      )\n      .subscribe(values => {\n        // Clean empty values\n        const filter: TaskFilter = {};\n        \n        Object.keys(values).forEach(key => {\n          const value = values[key];\n          if (value !== null && value !== '' && !(Array.isArray(value) && value.length === 0)) {\n            filter[key] = value;\n          }\n        });\n        \n        this.filterChanged.emit(filter);\n      });\n  }\n\n  /**\n   * Toggle advanced filters visibility\n   */\n  toggleAdvancedFilters(): void {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n\n  /**\n   * Clear all filters\n   */\n  clearFilters(): void {\n    this.filterForm.reset({\n      search: '',\n      status: '',\n      priority: '',\n      assigneeId: '',\n      tags: [],\n      dueDateFrom: null,\n      dueDateTo: null\n    });\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAUC,uBAAuB,QAAQ,eAAe;AAEvG,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,gBAAgB;AAS5D,WAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EA0C9B;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IA7CtB;;;IAGS,KAAAC,MAAM,GAAe,EAAE;IAEhC;;;IAGU,KAAAC,aAAa,GAAG,IAAIR,YAAY,EAAc;IAOxD;;;IAGA,KAAAS,mBAAmB,GAAG,KAAK;IAE3B;;;IAGA,KAAAC,QAAQ,GAAG,CACT;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAc,CAAE,EACpC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAE,EACjC;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAa,CAAE,EAC9C;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAM,CAAE,CACjC;IAED;;;IAGA,KAAAC,UAAU,GAAG,CACX;MAAEF,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACtC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAM,CAAE,EAChC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,CAC/B;EAMqC;EAEtC;;;;EAIAE,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA;;;EAGAA,cAAcA,CAAA;IACZ,IAAI,CAACC,UAAU,GAAG,IAAI,CAACV,EAAE,CAACW,KAAK,CAAC;MAC9BC,MAAM,EAAE,CAAC,IAAI,CAACX,MAAM,CAACW,MAAM,IAAI,EAAE,CAAC;MAClCC,MAAM,EAAE,CAAC,IAAI,CAACZ,MAAM,CAACY,MAAM,IAAI,EAAE,CAAC;MAClCC,QAAQ,EAAE,CAAC,IAAI,CAACb,MAAM,CAACa,QAAQ,IAAI,EAAE,CAAC;MACtCC,UAAU,EAAE,CAAC,IAAI,CAACd,MAAM,CAACc,UAAU,IAAI,EAAE,CAAC;MAC1CC,IAAI,EAAE,CAAC,IAAI,CAACf,MAAM,CAACe,IAAI,IAAI,EAAE,CAAC;MAC9BC,WAAW,EAAE,CAAC,IAAI,CAAChB,MAAM,CAACgB,WAAW,IAAI,IAAI,CAAC;MAC9CC,SAAS,EAAE,CAAC,IAAI,CAACjB,MAAM,CAACiB,SAAS,IAAI,IAAI;KAC1C,CAAC;IAEF;IACA,IAAI,CAACR,UAAU,CAACS,YAAY,CACzBC,IAAI,CACHxB,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oBAAoB,CAAC,CAACwB,IAAI,EAAEC,IAAI,KAAKC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,KAAKE,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC,CACpF,CACAG,SAAS,CAACC,MAAM,IAAG;MAClB;MACA,MAAMzB,MAAM,GAAe,EAAE;MAE7B0B,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAACC,GAAG,IAAG;QAChC,MAAMzB,KAAK,GAAGqB,MAAM,CAACI,GAAG,CAAC;QACzB,IAAIzB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,IAAI,EAAE0B,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,IAAIA,KAAK,CAAC4B,MAAM,KAAK,CAAC,CAAC,EAAE;UACnFhC,MAAM,CAAC6B,GAAG,CAAC,GAAGzB,KAAK;;MAEvB,CAAC,CAAC;MAEF,IAAI,CAACH,aAAa,CAACgC,IAAI,CAACjC,MAAM,CAAC;IACjC,CAAC,CAAC;EACN;EAEA;;;EAGAkC,qBAAqBA,CAAA;IACnB,IAAI,CAAChC,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA;;;EAGAiC,YAAYA,CAAA;IACV,IAAI,CAAC1B,UAAU,CAAC2B,KAAK,CAAC;MACpBzB,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE;KACZ,CAAC;EACJ;CACD;AA5GUoB,UAAA,EAAR9C,KAAK,EAAE,C,kDAAyB;AAKvB8C,UAAA,EAAT7C,MAAM,EAAE,C,yDAAgD;AAT9CK,mBAAmB,GAAAwC,UAAA,EAN/B/C,SAAS,CAAC;EACTgD,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,8BAA8B,CAAC;EAC3CC,eAAe,EAAE/C,uBAAuB,CAACgD;CAC1C,CAAC,C,EACW7C,mBAAmB,CAgH/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}