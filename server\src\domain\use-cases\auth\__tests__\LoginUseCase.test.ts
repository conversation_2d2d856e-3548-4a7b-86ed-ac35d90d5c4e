/**
 * Tests for LoginUseCase
 * Verifies the business logic for user authentication
 */
import { LoginUseCase, LoginCredentials } from '../LoginUseCase';
import { IUserRepository } from '../../../repositories/IUserRepository';
import { UserRole } from '../../../entities/User';
import { AppError } from '../../../common/AppError';

// Mock user repository
const mockUserRepository: jest.Mocked<IUserRepository> = {
  create: jest.fn(),
  findById: jest.fn(),
  findByEmail: jest.fn(),
  findAll: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

// Mock user document
const mockUserDocument = {
  id: '123456789012',
  name: 'Test User',
  email: '<EMAIL>',
  password: 'hashedPassword123',
  role: UserRole.USER,
  comparePassword: jest.fn(),
  generateAuthToken: jest.fn(),
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Mock login credentials
const mockCredentials: LoginCredentials = {
  email: '<EMAIL>',
  password: 'password123',
};

describe('LoginUseCase', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Test successful login
  it('should authenticate a user successfully', async () => {
    // Arrange
    mockUserRepository.findByEmail.mockResolvedValue(mockUserDocument as any);
    mockUserDocument.comparePassword.mockResolvedValue(true);
    mockUserDocument.generateAuthToken.mockReturnValue('mock-token');
    
    const loginUseCase = new LoginUseCase(mockUserRepository);

    // Act
    const result = await loginUseCase.execute(mockCredentials);

    // Assert
    expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(mockCredentials.email);
    expect(mockUserDocument.comparePassword).toHaveBeenCalledWith(mockCredentials.password);
    expect(mockUserDocument.generateAuthToken).toHaveBeenCalled();
    expect(result).toEqual({
      user: {
        id: mockUserDocument.id,
        name: mockUserDocument.name,
        email: mockUserDocument.email,
        role: mockUserDocument.role,
      },
      token: 'mock-token',
    });
  });

  // Test validation for missing email
  it('should throw an error if email is missing', async () => {
    // Arrange
    const loginUseCase = new LoginUseCase(mockUserRepository);
    const invalidCredentials = { ...mockCredentials, email: '' };

    // Act & Assert
    await expect(loginUseCase.execute(invalidCredentials)).rejects.toThrow(
      new AppError('Email is required', 400)
    );
    expect(mockUserRepository.findByEmail).not.toHaveBeenCalled();
  });

  // Test validation for missing password
  it('should throw an error if password is missing', async () => {
    // Arrange
    const loginUseCase = new LoginUseCase(mockUserRepository);
    const invalidCredentials = { ...mockCredentials, password: '' };

    // Act & Assert
    await expect(loginUseCase.execute(invalidCredentials)).rejects.toThrow(
      new AppError('Password is required', 400)
    );
    expect(mockUserRepository.findByEmail).not.toHaveBeenCalled();
  });

  // Test error handling for user not found
  it('should throw an error if user is not found', async () => {
    // Arrange
    mockUserRepository.findByEmail.mockResolvedValue(null);
    const loginUseCase = new LoginUseCase(mockUserRepository);

    // Act & Assert
    await expect(loginUseCase.execute(mockCredentials)).rejects.toThrow(
      new AppError('Invalid email or password', 401)
    );
    expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(mockCredentials.email);
  });

  // Test error handling for invalid password
  it('should throw an error if password is invalid', async () => {
    // Arrange
    mockUserRepository.findByEmail.mockResolvedValue(mockUserDocument as any);
    mockUserDocument.comparePassword.mockResolvedValue(false);
    const loginUseCase = new LoginUseCase(mockUserRepository);

    // Act & Assert
    await expect(loginUseCase.execute(mockCredentials)).rejects.toThrow(
      new AppError('Invalid email or password', 401)
    );
    expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(mockCredentials.email);
    expect(mockUserDocument.comparePassword).toHaveBeenCalledWith(mockCredentials.password);
  });

  // Test repository error handling
  it('should handle repository errors', async () => {
    // Arrange
    const errorMessage = 'Database error';
    mockUserRepository.findByEmail.mockRejectedValue(new Error(errorMessage));
    const loginUseCase = new LoginUseCase(mockUserRepository);

    // Act & Assert
    await expect(loginUseCase.execute(mockCredentials)).rejects.toThrow(
      new AppError(`Authentication failed: ${errorMessage}`, 500)
    );
    expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(mockCredentials.email);
  });
});
