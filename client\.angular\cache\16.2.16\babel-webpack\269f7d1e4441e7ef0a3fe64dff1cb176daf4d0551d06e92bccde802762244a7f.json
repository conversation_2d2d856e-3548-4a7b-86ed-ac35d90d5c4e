{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Profile module for user profile management\n * Contains components and services for viewing and editing user profile\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n// Define routes for the profile feature\nconst routes = [{\n  path: '',\n  loadChildren: () => import('./pages/profile-page/profile-page.module').then(m => m.ProfilePageModule)\n}];\nexport let ProfileModule = class ProfileModule {};\nProfileModule = __decorate([NgModule({\n  declarations: [],\n  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n})], ProfileModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "SharedModule", "routes", "path", "loadChildren", "then", "m", "ProfilePageModule", "ProfileModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\profile\\profile.module.ts"], "sourcesContent": ["/**\n * Profile module for user profile management\n * Contains components and services for viewing and editing user profile\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n\n// Define routes for the profile feature\nconst routes: Routes = [\n  {\n    path: '',\n    loadChildren: () => import('./pages/profile-page/profile-page.module').then(m => m.ProfilePageModule)\n  }\n];\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class ProfileModule { }\n"], "mappings": ";AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AAEzD;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB;CACrG,CACF;AAUM,WAAMC,aAAa,GAAnB,MAAMA,aAAa,GAAI;AAAjBA,aAAa,GAAAC,UAAA,EARzBX,QAAQ,CAAC;EACRY,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,CACPZ,YAAY,EACZE,YAAY,EACZD,YAAY,CAACY,QAAQ,CAACV,MAAM,CAAC;CAEhC,CAAC,C,EACWM,aAAa,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}