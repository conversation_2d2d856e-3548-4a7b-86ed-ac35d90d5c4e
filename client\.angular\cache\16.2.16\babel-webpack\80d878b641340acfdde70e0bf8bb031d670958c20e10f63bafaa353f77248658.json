{"ast": null, "code": "/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { DOCUMENT } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class LazyLoadDirective {\n  /**\n   * Constructor with dependency injection\n   * @param el - Reference to the host element\n   */\n  constructor(document) {\n    this.document = document;\n    /**\n     * Event emitted when element enters viewport\n     */\n    this.appLazyLoad = new EventEmitter();\n    /**\n     * Intersection observer instance\n     */\n    this.observer = null;\n  }\n  /**\n   * Initialize the intersection observer when directive is initialized\n   */\n  ngOnInit() {\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {\n      // Create observer with options\n      this.observer = new IntersectionObserver(entries => {\n        this.handleIntersection(entries);\n      }, {\n        root: null,\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      // Start observing the element\n      if (this.el && this.el.nativeElement) {\n        this.observer.observe(this.el.nativeElement);\n      }\n    }\n  }\n  /**\n   * Clean up observer when directive is destroyed to prevent memory leaks\n   */\n  ngOnDestroy() {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  handleIntersection(entries) {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LazyLoadDirective_Factory(t) {\n      return new (t || LazyLoadDirective)(i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: LazyLoadDirective,\n      selectors: [[\"\", \"appLazyLoad\", \"\"]],\n      outputs: {\n        appLazyLoad: \"appLazyLoad\"\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["DOCUMENT", "EventEmitter", "LazyLoadDirective", "constructor", "document", "appLazyLoad", "observer", "ngOnInit", "window", "IntersectionObserver", "entries", "handleIntersection", "root", "rootMargin", "threshold", "el", "nativeElement", "observe", "ngOnDestroy", "disconnect", "for<PERSON>ach", "entry", "isIntersecting", "emit", "unobserve", "target", "i0", "ɵɵdirectiveInject", "selectors", "outputs"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\directives\\lazy-load.directive.ts"], "sourcesContent": ["/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { DOCUMENT } from '@angular/common';\nimport { Directive, EventEmitter, Inject, Output } from '@angular/core';\n\n@Directive({\n  selector: '[appLazyLoad]'\n})\nexport class LazyLoadDirective {\n  /**\n   * Event emitted when element enters viewport\n   */\n  @Output() appLazyLoad = new EventEmitter<void>();\n  \n  /**\n   * Intersection observer instance\n   */\n  private observer: any = null;\n\n  /**\n   * Constructor with dependency injection\n   * @param el - Reference to the host element\n   */\n  constructor(@Inject(DOCUMENT) private document: Document) {}\n\n  /**\n   * Initialize the intersection observer when directive is initialized\n   */\n  ngOnInit(): void {\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {\n      // Create observer with options\n      this.observer = new IntersectionObserver((entries) => {\n        this.handleIntersection(entries);\n      }, {\n        root: null, // Use viewport as root\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      \n      // Start observing the element\n      if (this.el && this.el.nativeElement) {\n        this.observer.observe(this.el.nativeElement);\n      }\n    }\n  }\n\n  /**\n   * Clean up observer when directive is destroyed to prevent memory leaks\n   */\n  ngOnDestroy(): void {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  private handleIntersection(entries: IntersectionObserverEntry[]): void {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        \n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n}\n"], "mappings": "AAAA;;;;;AAKA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAAoBC,YAAY,QAAwB,eAAe;;AAKvE,OAAM,MAAOC,iBAAiB;EAW5B;;;;EAIAC,YAAsCC,QAAkB;IAAlB,KAAAA,QAAQ,GAARA,QAAQ;IAd9C;;;IAGU,KAAAC,WAAW,GAAG,IAAIJ,YAAY,EAAQ;IAEhD;;;IAGQ,KAAAK,QAAQ,GAAQ,IAAI;EAM+B;EAE3D;;;EAGAC,QAAQA,CAAA;IACN,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,sBAAsB,IAAIA,MAAM,EAAE;MACrE;MACA,IAAI,CAACF,QAAQ,GAAG,IAAIG,oBAAoB,CAAEC,OAAO,IAAI;QACnD,IAAI,CAACC,kBAAkB,CAACD,OAAO,CAAC;MAClC,CAAC,EAAE;QACDE,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,GAAG,CAAC;OAChB,CAAC;MAEF;MACA,IAAI,IAAI,CAACC,EAAE,IAAI,IAAI,CAACA,EAAE,CAACC,aAAa,EAAE;QACpC,IAAI,CAACV,QAAQ,CAACW,OAAO,CAAC,IAAI,CAACF,EAAE,CAACC,aAAa,CAAC;;;EAGlD;EAEA;;;EAGAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACZ,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACa,UAAU,EAAE;MAC1B,IAAI,CAACb,QAAQ,GAAG,IAAI;;EAExB;EAEA;;;;EAIQK,kBAAkBA,CAACD,OAAoC;IAC7DA,OAAO,CAACU,OAAO,CAACC,KAAK,IAAG;MACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxB;QACA,IAAI,CAACjB,WAAW,CAACkB,IAAI,EAAE;QAEvB;QACA,IAAI,IAAI,CAACjB,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,CAACkB,SAAS,CAACH,KAAK,CAACI,MAAM,CAAC;;;IAG3C,CAAC,CAAC;EACJ;;;uBAhEWvB,iBAAiB,EAAAwB,EAAA,CAAAC,iBAAA,CAeR3B,QAAQ;IAAA;EAAA;;;YAfjBE,iBAAiB;MAAA0B,SAAA;MAAAC,OAAA;QAAAxB,WAAA;MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}