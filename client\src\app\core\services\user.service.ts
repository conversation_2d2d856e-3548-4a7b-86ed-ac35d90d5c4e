/**
 * User Service
 * Handles API communication for user-related operations
 */
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { User } from '../models/user.model';
import { ApiResponse } from '../models/api-response.model';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  /**
   * API endpoint path
   */
  private endpoint = 'users';

  /**
   * Constructor with dependency injection
   * @param apiService - Base API service for HTTP requests
   */
  constructor(private apiService: ApiService) {}

  /**
   * Get all users
   * @returns Observable with array of users
   */
  getUsers(): Observable<User[]> {
    return this.apiService.get<ApiResponse<User[]>>(this.endpoint)
      .pipe(
        map((response: ApiResponse<User[]>) => response.data)
      );
  }

  /**
   * Get a user by ID
   * @param id - User ID
   * @returns Observable with user data
   */
  getUser(id: string): Observable<User> {
    return this.apiService.get<ApiResponse<User>>(`${this.endpoint}/${id}`)
      .pipe(
        map((response: ApiResponse<User>) => response.data)
      );
  }

  /**
   * Update user profile
   * @param id - User ID
   * @param userData - Updated user data
   * @returns Observable with updated user
   */
  updateUser(id: string, userData: Partial<User>): Observable<User> {
    return this.apiService.put<ApiResponse<User>>(`${this.endpoint}/${id}`, userData)
      .pipe(
        map((response: ApiResponse<User>) => response.data)
      );
  }

  /**
   * Update user password
   * @param id - User ID
   * @param currentPassword - Current password
   * @param newPassword - New password
   * @returns Observable with success status
   */
  updatePassword(id: string, currentPassword: string, newPassword: string): Observable<boolean> {
    return this.apiService.put<ApiResponse<Record<string, never>>>(`${this.endpoint}/${id}/password`, {
      currentPassword,
      newPassword
    }).pipe(
      map((response: ApiResponse<Record<string, never>>) => response.success)
    );
  }
}
