/**
 * Authentication Middleware
 * Verifies JWT tokens and protects routes
 */
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AppError } from '../../domain/common/AppError';
import { User, UserRole } from '../../domain/entities/User';

/**
 * Interface for JWT payload
 */
interface JwtPayload {
  id: string;
  role: string;
}

/**
 * Extend Express Request interface to include user property
 */
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
      };
    }
  }
}

/**
 * Middleware to protect routes that require authentication
 * Verifies JWT token from Authorization header
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const protect = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    let token: string | undefined;
    
    // Check if token exists in Authorization header
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      // Extract token from header
      token = req.headers.authorization.split(' ')[1];
    }
    
    // Check if token exists
    if (!token) {
      throw new AppError('Not authorized to access this route', 401);
    }
    
    try {
      // Verify token
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'your_jwt_secret_key'
      ) as JwtPayload;
      
      // Check if user still exists
      const user = await User.findById(decoded.id);
      
      if (!user) {
        throw new AppError('User no longer exists', 401);
      }
      
      // Add user to request object
      req.user = {
        id: decoded.id,
        role: decoded.role,
      };
      
      next();
    } catch (error) {
      throw new AppError('Not authorized to access this route', 401);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to restrict access to specific roles
 * Must be used after protect middleware
 * @param roles - Array of allowed roles
 * @returns Middleware function
 */
export const restrictTo = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // Check if user exists (should be set by protect middleware)
    if (!req.user) {
      return next(new AppError('Not authorized to access this route', 401));
    }
    
    // Check if user role is allowed
    if (!roles.includes(req.user.role as UserRole)) {
      return next(
        new AppError('You do not have permission to perform this action', 403)
      );
    }
    
    next();
  };
};
