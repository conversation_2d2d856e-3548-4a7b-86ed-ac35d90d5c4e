{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Dashboard Page Component\n * Main dashboard page displaying task summaries and charts\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nexport let DashboardPageComponent = class DashboardPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service\n   * @param userService - User service\n   * @param authService - Auth service\n   * @param cdr - Change detector reference\n   */\n  constructor(taskService, userService, authService, cdr) {\n    this.taskService = taskService;\n    this.userService = userService;\n    this.authService = authService;\n    this.cdr = cdr;\n    /**\n     * Current user\n     */\n    this.currentUser = null;\n    /**\n     * All tasks\n     */\n    this.tasks = [];\n    /**\n     * Recent tasks (last 5)\n     */\n    this.recentTasks = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = true;\n    /**\n     * Error message\n     */\n    this.error = null;\n    /**\n     * Task counts by status\n     */\n    this.taskStatusCounts = {\n      todo: 0,\n      inProgress: 0,\n      done: 0\n    };\n    /**\n     * Task counts by priority\n     */\n    this.taskPriorityCounts = {\n      high: 0,\n      medium: 0,\n      low: 0\n    };\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads current user and tasks\n   */\n  ngOnInit() {\n    this.loadCurrentUser();\n    this.loadTasks();\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Load current user data\n   */\n  loadCurrentUser() {\n    this.currentUser = this.authService.currentUserValue;\n    // If no current user in memory, try to get from API\n    if (!this.currentUser) {\n      this.authService.getProfile().pipe(takeUntil(this.destroy$)).subscribe({\n        next: user => {\n          this.currentUser = user;\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          console.error('Error loading user profile:', err);\n          this.error = 'Failed to load user profile';\n          this.cdr.markForCheck();\n        }\n      });\n    }\n  }\n  /**\n   * Load all tasks and calculate statistics\n   */\n  loadTasks() {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    this.taskService.getTasks().pipe(takeUntil(this.destroy$)).subscribe({\n      next: tasks => {\n        this.tasks = tasks;\n        this.recentTasks = [...tasks].sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()).slice(0, 5);\n        this.calculateTaskStatistics();\n        this.loading = false;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        console.error('Error loading tasks:', err);\n        this.error = 'Failed to load tasks';\n        this.loading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Calculate task statistics for charts\n   */\n  calculateTaskStatistics() {\n    // Reset counters\n    this.taskStatusCounts = {\n      todo: 0,\n      inProgress: 0,\n      done: 0\n    };\n    this.taskPriorityCounts = {\n      high: 0,\n      medium: 0,\n      low: 0\n    };\n    // Count tasks by status and priority\n    this.tasks.forEach(task => {\n      // Count by status\n      if (task.status === 'todo') {\n        this.taskStatusCounts.todo++;\n      } else if (task.status === 'in_progress') {\n        this.taskStatusCounts.inProgress++;\n      } else if (task.status === 'done') {\n        this.taskStatusCounts.done++;\n      }\n      // Count by priority\n      if (task.priority === 'high') {\n        this.taskPriorityCounts.high++;\n      } else if (task.priority === 'medium') {\n        this.taskPriorityCounts.medium++;\n      } else if (task.priority === 'low') {\n        this.taskPriorityCounts.low++;\n      }\n    });\n  }\n};\nDashboardPageComponent = __decorate([Component({\n  selector: 'app-dashboard-page',\n  templateUrl: './dashboard-page.component.html',\n  styleUrls: ['./dashboard-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], DashboardPageComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "Subject", "takeUntil", "DashboardPageComponent", "constructor", "taskService", "userService", "authService", "cdr", "currentUser", "tasks", "recentTasks", "loading", "error", "taskStatusCounts", "todo", "inProgress", "done", "taskPriorityCounts", "high", "medium", "low", "destroy$", "ngOnInit", "loadCurrentUser", "loadTasks", "ngOnDestroy", "next", "complete", "currentUserValue", "getProfile", "pipe", "subscribe", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "console", "getTasks", "sort", "a", "b", "Date", "updatedAt", "getTime", "slice", "calculateTaskStatistics", "for<PERSON>ach", "task", "status", "priority", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\pages\\dashboard-page\\dashboard-page.component.ts"], "sourcesContent": ["/**\n * Dashboard Page Component\n * Main dashboard page displaying task summaries and charts\n */\nimport { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { TaskService } from '../../../../core/services/task.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-dashboard-page',\n  templateUrl: './dashboard-page.component.html',\n  styleUrls: ['./dashboard-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class DashboardPageComponent implements OnInit, OnD<PERSON>roy {\n  /**\n   * Current user\n   */\n  currentUser: User | null = null;\n  \n  /**\n   * All tasks\n   */\n  tasks: Task[] = [];\n  \n  /**\n   * Recent tasks (last 5)\n   */\n  recentTasks: Task[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  loading = true;\n  \n  /**\n   * Error message\n   */\n  error: string | null = null;\n  \n  /**\n   * Task counts by status\n   */\n  taskStatusCounts = {\n    todo: 0,\n    inProgress: 0,\n    done: 0\n  };\n  \n  /**\n   * Task counts by priority\n   */\n  taskPriorityCounts = {\n    high: 0,\n    medium: 0,\n    low: 0\n  };\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service\n   * @param userService - User service\n   * @param authService - Auth service\n   * @param cdr - Change detector reference\n   */\n  constructor(\n    private taskService: TaskService,\n    private userService: UserService,\n    private authService: AuthService,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads current user and tasks\n   */\n  ngOnInit(): void {\n    this.loadCurrentUser();\n    this.loadTasks();\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Load current user data\n   */\n  private loadCurrentUser(): void {\n    this.currentUser = this.authService.currentUserValue;\n    \n    // If no current user in memory, try to get from API\n    if (!this.currentUser) {\n      this.authService.getProfile()\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: (user) => {\n            this.currentUser = user;\n            this.cdr.markForCheck();\n          },\n          error: (err) => {\n            console.error('Error loading user profile:', err);\n            this.error = 'Failed to load user profile';\n            this.cdr.markForCheck();\n          }\n        });\n    }\n  }\n\n  /**\n   * Load all tasks and calculate statistics\n   */\n  private loadTasks(): void {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    \n    this.taskService.getTasks()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (tasks) => {\n          this.tasks = tasks;\n          this.recentTasks = [...tasks].sort((a, b) => \n            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()\n          ).slice(0, 5);\n          \n          this.calculateTaskStatistics();\n          this.loading = false;\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          console.error('Error loading tasks:', err);\n          this.error = 'Failed to load tasks';\n          this.loading = false;\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Calculate task statistics for charts\n   */\n  private calculateTaskStatistics(): void {\n    // Reset counters\n    this.taskStatusCounts = {\n      todo: 0,\n      inProgress: 0,\n      done: 0\n    };\n    \n    this.taskPriorityCounts = {\n      high: 0,\n      medium: 0,\n      low: 0\n    };\n    \n    // Count tasks by status and priority\n    this.tasks.forEach(task => {\n      // Count by status\n      if (task.status === 'todo') {\n        this.taskStatusCounts.todo++;\n      } else if (task.status === 'in_progress') {\n        this.taskStatusCounts.inProgress++;\n      } else if (task.status === 'done') {\n        this.taskStatusCounts.done++;\n      }\n      \n      // Count by priority\n      if (task.priority === 'high') {\n        this.taskPriorityCounts.high++;\n      } else if (task.priority === 'medium') {\n        this.taskPriorityCounts.medium++;\n      } else if (task.priority === 'low') {\n        this.taskPriorityCounts.low++;\n      }\n    });\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AACxG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAanC,WAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAiDjC;;;;;;;EAOAC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,WAAwB,EACxBC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,GAAG,GAAHA,GAAG;IA3Db;;;IAGA,KAAAC,WAAW,GAAgB,IAAI;IAE/B;;;IAGA,KAAAC,KAAK,GAAW,EAAE;IAElB;;;IAGA,KAAAC,WAAW,GAAW,EAAE;IAExB;;;IAGA,KAAAC,OAAO,GAAG,IAAI;IAEd;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGA,KAAAC,gBAAgB,GAAG;MACjBC,IAAI,EAAE,CAAC;MACPC,UAAU,EAAE,CAAC;MACbC,IAAI,EAAE;KACP;IAED;;;IAGA,KAAAC,kBAAkB,GAAG;MACnBC,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,CAAC;MACTC,GAAG,EAAE;KACN;IAED;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIrB,OAAO,EAAQ;EAcnC;EAEH;;;;EAIAsB,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;EAGQJ,eAAeA,CAAA;IACrB,IAAI,CAACf,WAAW,GAAG,IAAI,CAACF,WAAW,CAACsB,gBAAgB;IAEpD;IACA,IAAI,CAAC,IAAI,CAACpB,WAAW,EAAE;MACrB,IAAI,CAACF,WAAW,CAACuB,UAAU,EAAE,CAC1BC,IAAI,CAAC7B,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAC9BU,SAAS,CAAC;QACTL,IAAI,EAAGM,IAAI,IAAI;UACb,IAAI,CAACxB,WAAW,GAAGwB,IAAI;UACvB,IAAI,CAACzB,GAAG,CAAC0B,YAAY,EAAE;QACzB,CAAC;QACDrB,KAAK,EAAGsB,GAAG,IAAI;UACbC,OAAO,CAACvB,KAAK,CAAC,6BAA6B,EAAEsB,GAAG,CAAC;UACjD,IAAI,CAACtB,KAAK,GAAG,6BAA6B;UAC1C,IAAI,CAACL,GAAG,CAAC0B,YAAY,EAAE;QACzB;OACD,CAAC;;EAER;EAEA;;;EAGQT,SAASA,CAAA;IACf,IAAI,CAACb,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACL,GAAG,CAAC0B,YAAY,EAAE;IAEvB,IAAI,CAAC7B,WAAW,CAACgC,QAAQ,EAAE,CACxBN,IAAI,CAAC7B,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAC9BU,SAAS,CAAC;MACTL,IAAI,EAAGjB,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACC,WAAW,GAAG,CAAC,GAAGD,KAAK,CAAC,CAAC4B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACtC,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,CAACC,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE,CAClE,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEb,IAAI,CAACC,uBAAuB,EAAE;QAC9B,IAAI,CAACjC,OAAO,GAAG,KAAK;QACpB,IAAI,CAACJ,GAAG,CAAC0B,YAAY,EAAE;MACzB,CAAC;MACDrB,KAAK,EAAGsB,GAAG,IAAI;QACbC,OAAO,CAACvB,KAAK,CAAC,sBAAsB,EAAEsB,GAAG,CAAC;QAC1C,IAAI,CAACtB,KAAK,GAAG,sBAAsB;QACnC,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACJ,GAAG,CAAC0B,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGQW,uBAAuBA,CAAA;IAC7B;IACA,IAAI,CAAC/B,gBAAgB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,UAAU,EAAE,CAAC;MACbC,IAAI,EAAE;KACP;IAED,IAAI,CAACC,kBAAkB,GAAG;MACxBC,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,CAAC;MACTC,GAAG,EAAE;KACN;IAED;IACA,IAAI,CAACX,KAAK,CAACoC,OAAO,CAACC,IAAI,IAAG;MACxB;MACA,IAAIA,IAAI,CAACC,MAAM,KAAK,MAAM,EAAE;QAC1B,IAAI,CAAClC,gBAAgB,CAACC,IAAI,EAAE;OAC7B,MAAM,IAAIgC,IAAI,CAACC,MAAM,KAAK,aAAa,EAAE;QACxC,IAAI,CAAClC,gBAAgB,CAACE,UAAU,EAAE;OACnC,MAAM,IAAI+B,IAAI,CAACC,MAAM,KAAK,MAAM,EAAE;QACjC,IAAI,CAAClC,gBAAgB,CAACG,IAAI,EAAE;;MAG9B;MACA,IAAI8B,IAAI,CAACE,QAAQ,KAAK,MAAM,EAAE;QAC5B,IAAI,CAAC/B,kBAAkB,CAACC,IAAI,EAAE;OAC/B,MAAM,IAAI4B,IAAI,CAACE,QAAQ,KAAK,QAAQ,EAAE;QACrC,IAAI,CAAC/B,kBAAkB,CAACE,MAAM,EAAE;OACjC,MAAM,IAAI2B,IAAI,CAACE,QAAQ,KAAK,KAAK,EAAE;QAClC,IAAI,CAAC/B,kBAAkB,CAACG,GAAG,EAAE;;IAEjC,CAAC,CAAC;EACJ;CACD;AA7KYlB,sBAAsB,GAAA+C,UAAA,EANlCnD,SAAS,CAAC;EACToD,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC,CAAC;EAC9CC,eAAe,EAAEtD,uBAAuB,CAACuD;CAC1C,CAAC,C,EACWpD,sBAAsB,CA6KlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}