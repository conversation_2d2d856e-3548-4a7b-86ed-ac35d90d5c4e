{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n// Define routes for the profile feature\nconst routes = [{\n  path: '',\n  loadChildren: () => import('./pages/profile-page/profile-page.module').then(m => m.ProfilePageModule)\n}];\nexport class ProfileModule {\n  static {\n    this.ɵfac = function ProfileModule_Factory(t) {\n      return new (t || ProfileModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileModule, {\n    imports: [CommonModule, SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "SharedModule", "routes", "path", "loadChildren", "then", "m", "ProfilePageModule", "ProfileModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\profile\\profile.module.ts"], "sourcesContent": ["/**\n * Profile module for user profile management\n * Contains components and services for viewing and editing user profile\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n\n// Define routes for the profile feature\nconst routes: Routes = [\n  {\n    path: '',\n    loadChildren: () => import('./pages/profile-page/profile-page.module').then(m => m.ProfilePageModule)\n  }\n];\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class ProfileModule { }\n"], "mappings": "AAKA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;;;AAEzD;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB;CACrG,CACF;AAUD,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBALtBT,YAAY,EACZE,YAAY,EACZD,YAAY,CAACS,QAAQ,CAACP,MAAM,CAAC;IAAA;EAAA;;;2EAGpBM,aAAa;IAAAE,OAAA,GALtBX,YAAY,EACZE,YAAY,EAAAU,EAAA,CAAAX,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}