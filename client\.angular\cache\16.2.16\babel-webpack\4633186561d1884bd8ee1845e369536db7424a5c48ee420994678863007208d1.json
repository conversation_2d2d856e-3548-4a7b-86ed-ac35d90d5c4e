{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Profile page component\n * Displays and allows editing of user profile information\n */\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nexport let ProfilePageComponent = class ProfilePageComponent {\n  /**\n   * Constructor\n   * @param fb - FormBuilder for creating reactive forms\n   * @param userService - Service for user-related operations\n   * @param authService - Service for authentication operations\n   * @param notificationService - Service for displaying notifications\n   */\n  constructor(fb, userService, authService, notificationService) {\n    this.fb = fb;\n    this.userService = userService;\n    this.authService = authService;\n    this.notificationService = notificationService;\n    // Current user profile data\n    this.currentUser = null;\n    // Loading state\n    this.isLoading = false;\n    // Edit mode toggle\n    this.isEditMode = false;\n    // Initialize the form\n    this.profileForm = this.fb.group({\n      name: ['', [Validators.required]],\n      email: [{\n        value: '',\n        disabled: true\n      }],\n      avatarUrl: ['']\n    });\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   */\n  ngOnInit() {\n    // Load the current user profile\n    this.loadUserProfile();\n  }\n  /**\n   * Loads the current user profile data\n   */\n  loadUserProfile() {\n    this.isLoading = true;\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (user) {\n        // Populate the form with user data\n        this.profileForm.patchValue({\n          name: user.name,\n          email: user.email,\n          avatarUrl: user.avatarUrl || ''\n        });\n      }\n      this.isLoading = false;\n    }, error => {\n      this.notificationService.error('Failed to load profile');\n      this.isLoading = false;\n    });\n  }\n  /**\n   * Toggles edit mode for the profile form\n   */\n  toggleEditMode() {\n    this.isEditMode = !this.isEditMode;\n    if (!this.isEditMode) {\n      // Reset form to original values when canceling edit\n      this.profileForm.patchValue({\n        name: this.currentUser?.name || '',\n        avatarUrl: this.currentUser?.avatarUrl || ''\n      });\n    }\n  }\n  /**\n   * Submits the profile update form\n   */\n  updateProfile() {\n    if (this.profileForm.invalid) {\n      return;\n    }\n    this.isLoading = true;\n    const updatedProfile = {\n      name: this.profileForm.get('name')?.value,\n      avatarUrl: this.profileForm.get('avatarUrl')?.value\n    };\n    if (this.currentUser) {\n      this.userService.updateUser(this.currentUser.id, updatedProfile).subscribe(response => {\n        this.notificationService.success('Profile updated successfully');\n        this.isLoading = false;\n        this.isEditMode = false;\n        // Update the current user in auth service\n        // Reload the current user data\n        this.loadUserProfile();\n      }, error => {\n        this.notificationService.error('Failed to update profile');\n        this.isLoading = false;\n      });\n    }\n  }\n};\nProfilePageComponent = __decorate([Component({\n  selector: 'app-profile-page',\n  templateUrl: './profile-page.component.html',\n  styleUrls: ['./profile-page.component.scss']\n})], ProfilePageComponent);", "map": {"version": 3, "names": ["Component", "Validators", "ProfilePageComponent", "constructor", "fb", "userService", "authService", "notificationService", "currentUser", "isLoading", "isEditMode", "profileForm", "group", "name", "required", "email", "value", "disabled", "avatarUrl", "ngOnInit", "loadUserProfile", "currentUser$", "subscribe", "user", "patchValue", "error", "toggleEditMode", "updateProfile", "invalid", "updatedProfile", "get", "updateUser", "id", "response", "success", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\profile\\pages\\profile-page\\profile-page.component.ts"], "sourcesContent": ["/**\n * Profile page component\n * Displays and allows editing of user profile information\n */\nimport { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { UserService } from '../../../../core/services/user.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-profile-page',\n  templateUrl: './profile-page.component.html',\n  styleUrls: ['./profile-page.component.scss']\n})\nexport class ProfilePageComponent implements OnInit {\n  // Current user profile data\n  currentUser: User | null = null;\n  \n  // Form for editing profile\n  profileForm: FormGroup;\n  \n  // Loading state\n  isLoading = false;\n  \n  // Edit mode toggle\n  isEditMode = false;\n\n  /**\n   * Constructor\n   * @param fb - FormBuilder for creating reactive forms\n   * @param userService - Service for user-related operations\n   * @param authService - Service for authentication operations\n   * @param notificationService - Service for displaying notifications\n   */\n  constructor(\n    private fb: FormBuilder,\n    private userService: UserService,\n    private authService: AuthService,\n    private notificationService: NotificationService\n  ) {\n    // Initialize the form\n    this.profileForm = this.fb.group({\n      name: ['', [Validators.required]],\n      email: [{value: '', disabled: true}],\n      avatarUrl: ['']\n    });\n  }\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   */\n  ngOnInit(): void {\n    // Load the current user profile\n    this.loadUserProfile();\n  }\n\n  /**\n   * Loads the current user profile data\n   */\n  loadUserProfile(): void {\n    this.isLoading = true;\n    \n    this.authService.currentUser$.subscribe(\n      (user: User | null) => {\n        this.currentUser = user;\n        \n        if (user) {\n          // Populate the form with user data\n          this.profileForm.patchValue({\n            name: user.name,\n            email: user.email,\n            avatarUrl: user.avatarUrl || ''\n          });\n        }\n        \n        this.isLoading = false;\n      },\n      (error: any) => {\n        this.notificationService.error('Failed to load profile');\n        this.isLoading = false;\n      }\n    );\n  }\n\n  /**\n   * Toggles edit mode for the profile form\n   */\n  toggleEditMode(): void {\n    this.isEditMode = !this.isEditMode;\n    \n    if (!this.isEditMode) {\n      // Reset form to original values when canceling edit\n      this.profileForm.patchValue({\n        name: this.currentUser?.name || '',\n        avatarUrl: this.currentUser?.avatarUrl || ''\n      });\n    }\n  }\n\n  /**\n   * Submits the profile update form\n   */\n  updateProfile(): void {\n    if (this.profileForm.invalid) {\n      return;\n    }\n    \n    this.isLoading = true;\n    \n    const updatedProfile = {\n      name: this.profileForm.get('name')?.value,\n      avatarUrl: this.profileForm.get('avatarUrl')?.value\n    };\n    \n    if (this.currentUser) {\n      this.userService.updateUser(this.currentUser.id, updatedProfile).subscribe(\n        (response: any) => {\n          this.notificationService.success('Profile updated successfully');\n          this.isLoading = false;\n          this.isEditMode = false;\n          \n          // Update the current user in auth service\n          // Reload the current user data\n          this.loadUserProfile();\n        },\n        (error: any) => {\n          this.notificationService.error('Failed to update profile');\n          this.isLoading = false;\n        }\n      );\n    }\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAAiCC,UAAU,QAAQ,gBAAgB;AAW5D,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAa/B;;;;;;;EAOAC,YACUC,EAAe,EACfC,WAAwB,EACxBC,WAAwB,EACxBC,mBAAwC;IAHxC,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAvB7B;IACA,KAAAC,WAAW,GAAgB,IAAI;IAK/B;IACA,KAAAC,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAC,UAAU,GAAG,KAAK;IAehB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAC/BC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACZ,UAAU,CAACa,QAAQ,CAAC,CAAC;MACjCC,KAAK,EAAE,CAAC;QAACC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAC,CAAC;MACpCC,SAAS,EAAE,CAAC,EAAE;KACf,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGAA,eAAeA,CAAA;IACb,IAAI,CAACX,SAAS,GAAG,IAAI;IAErB,IAAI,CAACH,WAAW,CAACe,YAAY,CAACC,SAAS,CACpCC,IAAiB,IAAI;MACpB,IAAI,CAACf,WAAW,GAAGe,IAAI;MAEvB,IAAIA,IAAI,EAAE;QACR;QACA,IAAI,CAACZ,WAAW,CAACa,UAAU,CAAC;UAC1BX,IAAI,EAAEU,IAAI,CAACV,IAAI;UACfE,KAAK,EAAEQ,IAAI,CAACR,KAAK;UACjBG,SAAS,EAAEK,IAAI,CAACL,SAAS,IAAI;SAC9B,CAAC;;MAGJ,IAAI,CAACT,SAAS,GAAG,KAAK;IACxB,CAAC,EACAgB,KAAU,IAAI;MACb,IAAI,CAAClB,mBAAmB,CAACkB,KAAK,CAAC,wBAAwB,CAAC;MACxD,IAAI,CAAChB,SAAS,GAAG,KAAK;IACxB,CAAC,CACF;EACH;EAEA;;;EAGAiB,cAAcA,CAAA;IACZ,IAAI,CAAChB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAElC,IAAI,CAAC,IAAI,CAACA,UAAU,EAAE;MACpB;MACA,IAAI,CAACC,WAAW,CAACa,UAAU,CAAC;QAC1BX,IAAI,EAAE,IAAI,CAACL,WAAW,EAAEK,IAAI,IAAI,EAAE;QAClCK,SAAS,EAAE,IAAI,CAACV,WAAW,EAAEU,SAAS,IAAI;OAC3C,CAAC;;EAEN;EAEA;;;EAGAS,aAAaA,CAAA;IACX,IAAI,IAAI,CAAChB,WAAW,CAACiB,OAAO,EAAE;MAC5B;;IAGF,IAAI,CAACnB,SAAS,GAAG,IAAI;IAErB,MAAMoB,cAAc,GAAG;MACrBhB,IAAI,EAAE,IAAI,CAACF,WAAW,CAACmB,GAAG,CAAC,MAAM,CAAC,EAAEd,KAAK;MACzCE,SAAS,EAAE,IAAI,CAACP,WAAW,CAACmB,GAAG,CAAC,WAAW,CAAC,EAAEd;KAC/C;IAED,IAAI,IAAI,CAACR,WAAW,EAAE;MACpB,IAAI,CAACH,WAAW,CAAC0B,UAAU,CAAC,IAAI,CAACvB,WAAW,CAACwB,EAAE,EAAEH,cAAc,CAAC,CAACP,SAAS,CACvEW,QAAa,IAAI;QAChB,IAAI,CAAC1B,mBAAmB,CAAC2B,OAAO,CAAC,8BAA8B,CAAC;QAChE,IAAI,CAACzB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACC,UAAU,GAAG,KAAK;QAEvB;QACA;QACA,IAAI,CAACU,eAAe,EAAE;MACxB,CAAC,EACAK,KAAU,IAAI;QACb,IAAI,CAAClB,mBAAmB,CAACkB,KAAK,CAAC,0BAA0B,CAAC;QAC1D,IAAI,CAAChB,SAAS,GAAG,KAAK;MACxB,CAAC,CACF;;EAEL;CACD;AAtHYP,oBAAoB,GAAAiC,UAAA,EALhCnC,SAAS,CAAC;EACToC,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B;CAC5C,CAAC,C,EACWpC,oBAAoB,CAsHhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}