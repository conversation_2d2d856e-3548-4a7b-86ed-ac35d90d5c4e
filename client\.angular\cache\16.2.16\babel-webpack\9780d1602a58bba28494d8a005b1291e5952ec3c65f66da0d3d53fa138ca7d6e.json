{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { Directive, Output } from '@angular/core';\nexport let LazyLoadDirective = class LazyLoadDirective {\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    /**\n     * Intersection observer instance\n     */\n    this.observer = null;\n    this.appLazyLoad = {};\n    this.appLazyLoad.emit = () => {};\n  }\n  /**\n   * Initialize the intersection observer\n   */\n  ngOnInit() {\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {\n      // Create observer with options\n      this.observer = new IntersectionObserver(entries => {\n        this.handleIntersection(entries);\n      }, {\n        root: null,\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      // Start observing the element\n      if (this.elementRef && this.elementRef.nativeElement) {\n        this.observer.observe(this.elementRef.nativeElement);\n      }\n    }\n  }\n  /**\n   * Clean up observer when directive is destroyed\n   */\n  ngOnDestroy() {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  handleIntersection(entries) {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n};\n__decorate([Output()], LazyLoadDirective.prototype, \"appLazyLoad\", void 0);\nLazyLoadDirective = __decorate([Directive({\n  selector: '[appLazyLoad]'\n})], LazyLoadDirective);", "map": {"version": 3, "names": ["Directive", "Output", "LazyLoadDirective", "constructor", "elementRef", "observer", "appLazyLoad", "emit", "ngOnInit", "window", "IntersectionObserver", "entries", "handleIntersection", "root", "rootMargin", "threshold", "nativeElement", "observe", "ngOnDestroy", "disconnect", "for<PERSON>ach", "entry", "isIntersecting", "unobserve", "target", "__decorate", "selector"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\directives\\lazy-load.directive.ts"], "sourcesContent": ["/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { Directive, Output } from '@angular/core';\n\n@Directive({\n  selector: '[appLazyLoad]'\n})\nexport class LazyLoadDirective {\n  /**\n   * Event emitted when element enters viewport\n   */\n  @Output() appLazyLoad: any;\n  \n  /**\n   * Intersection observer instance\n   */\n  private observer: any = null;\n\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(private elementRef: any) {\n    this.appLazyLoad = {};\n    this.appLazyLoad.emit = () => {};\n  }\n\n  /**\n   * Initialize the intersection observer\n   */\n  ngOnInit(): void {\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {\n      // Create observer with options\n      this.observer = new IntersectionObserver((entries: any[]) => {\n        this.handleIntersection(entries);\n      }, {\n        root: null, // Use viewport as root\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      \n      // Start observing the element\n      if (this.elementRef && this.elementRef.nativeElement) {\n        this.observer.observe(this.elementRef.nativeElement);\n      }\n    }\n  }\n\n  /**\n   * Clean up observer when directive is destroyed\n   */\n  ngOnDestroy(): void {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  private handleIntersection(entries: any[]): void {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        \n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;;AAKA,SAASA,SAAS,EAAEC,MAAM,QAAQ,eAAe;AAK1C,WAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAW5B;;;;EAIAC,YAAoBC,UAAe;IAAf,KAAAA,UAAU,GAAVA,UAAU;IAT9B;;;IAGQ,KAAAC,QAAQ,GAAQ,IAAI;IAO1B,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACA,WAAW,CAACC,IAAI,GAAG,MAAK,CAAE,CAAC;EAClC;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,sBAAsB,IAAIA,MAAM,EAAE;MACrE;MACA,IAAI,CAACJ,QAAQ,GAAG,IAAIK,oBAAoB,CAAEC,OAAc,IAAI;QAC1D,IAAI,CAACC,kBAAkB,CAACD,OAAO,CAAC;MAClC,CAAC,EAAE;QACDE,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,GAAG,CAAC;OAChB,CAAC;MAEF;MACA,IAAI,IAAI,CAACX,UAAU,IAAI,IAAI,CAACA,UAAU,CAACY,aAAa,EAAE;QACpD,IAAI,CAACX,QAAQ,CAACY,OAAO,CAAC,IAAI,CAACb,UAAU,CAACY,aAAa,CAAC;;;EAG1D;EAEA;;;EAGAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACb,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACc,UAAU,EAAE;MAC1B,IAAI,CAACd,QAAQ,GAAG,IAAI;;EAExB;EAEA;;;;EAIQO,kBAAkBA,CAACD,OAAc;IACvCA,OAAO,CAACS,OAAO,CAACC,KAAK,IAAG;MACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxB;QACA,IAAI,CAAChB,WAAW,CAACC,IAAI,EAAE;QAEvB;QACA,IAAI,IAAI,CAACF,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,CAACkB,SAAS,CAACF,KAAK,CAACG,MAAM,CAAC;;;IAG3C,CAAC,CAAC;EACJ;CACD;AAhEWC,UAAA,EAATxB,MAAM,EAAE,C,qDAAkB;AAJhBC,iBAAiB,GAAAuB,UAAA,EAH7BzB,SAAS,CAAC;EACT0B,QAAQ,EAAE;CACX,CAAC,C,EACWxB,iBAAiB,CAoE7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}