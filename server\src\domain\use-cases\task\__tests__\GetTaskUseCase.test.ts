/**
 * Tests for GetTaskUseCase
 * Verifies the business logic for retrieving tasks by ID
 */
import { GetTaskUseCase } from '../GetTaskUseCase';
import { ITaskRepository } from '../../../repositories/ITaskRepository';
import { TaskStatus, TaskPriority } from '../../../entities/Task';
import { AppError } from '../../../common/AppError';

// Mock task repository
const mockTaskRepository: jest.Mocked<ITaskRepository> = {
  create: jest.fn(),
  findById: jest.fn(),
  findAll: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  findByAssignee: jest.fn(),
};

// Mock task document (returned from repository)
const mockTaskDocument = {
  id: '123456789012',
  title: 'Test Task',
  description: 'Test Description',
  status: TaskStatus.TODO,
  priority: TaskPriority.MEDIUM,
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('GetTaskUseCase', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Test successful task retrieval
  it('should retrieve a task by ID successfully', async () => {
    // Arrange
    mockTaskRepository.findById.mockResolvedValue(mockTaskDocument as any);
    const getTaskUseCase = new GetTaskUseCase(mockTaskRepository);
    const taskId = '123456789012';

    // Act
    const result = await getTaskUseCase.execute(taskId);

    // Assert
    expect(mockTaskRepository.findById).toHaveBeenCalledWith(taskId);
    expect(result).toEqual(mockTaskDocument);
  });

  // Test validation for missing task ID
  it('should throw an error if task ID is missing', async () => {
    // Arrange
    const getTaskUseCase = new GetTaskUseCase(mockTaskRepository);
    const taskId = '';

    // Act & Assert
    await expect(getTaskUseCase.execute(taskId)).rejects.toThrow(
      new AppError('Task ID is required', 400)
    );
    expect(mockTaskRepository.findById).not.toHaveBeenCalled();
  });

  // Test error handling for task not found
  it('should throw an error if task is not found', async () => {
    // Arrange
    mockTaskRepository.findById.mockResolvedValue(null);
    const getTaskUseCase = new GetTaskUseCase(mockTaskRepository);
    const taskId = '123456789012';

    // Act & Assert
    await expect(getTaskUseCase.execute(taskId)).rejects.toThrow(
      new AppError(`Task with ID ${taskId} not found`, 404)
    );
    expect(mockTaskRepository.findById).toHaveBeenCalledWith(taskId);
  });

  // Test repository error handling
  it('should handle repository errors', async () => {
    // Arrange
    const errorMessage = 'Database error';
    mockTaskRepository.findById.mockRejectedValue(new Error(errorMessage));
    const getTaskUseCase = new GetTaskUseCase(mockTaskRepository);
    const taskId = '123456789012';

    // Act & Assert
    await expect(getTaskUseCase.execute(taskId)).rejects.toThrow(
      new AppError(`Error retrieving task: ${errorMessage}`, 500)
    );
    expect(mockTaskRepository.findById).toHaveBeenCalledWith(taskId);
  });
});
