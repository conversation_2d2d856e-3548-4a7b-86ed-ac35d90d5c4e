/**
 * Forgot password form component styles
 */

/* Container for the entire forgot password form */
.forgot-password-form-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 24px;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

/* Forgot password form */
.forgot-password-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Full width form fields */
.full-width {
  width: 100%;
}

/* Success message styling */
.success-message {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #e8f5e9;
  border-radius: 4px;
  margin-bottom: 24px;
  
  mat-icon {
    color: #4caf50;
    margin-right: 12px;
  }
  
  span {
    color: #2e7d32;
    font-weight: 500;
  }
}

/* Submit button */
.submit-button {
  height: 48px;
  font-size: 1rem;
  margin-top: 8px;
  
  mat-spinner {
    display: inline-block;
    margin: 0 auto;
  }
}

/* Back to login button (shown after success) */
.back-to-login-button {
  height: 48px;
  font-size: 1rem;
  width: 100%;
}

/* Login link container */
.login-link {
  text-align: center;
  margin-top: 16px;
  font-size: 0.9rem;
  
  a {
    color: var(--primary-color);
    cursor: pointer;
    margin-left: 4px;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .forgot-password-form-container {
    padding: 16px;
    box-shadow: none;
  }
}
