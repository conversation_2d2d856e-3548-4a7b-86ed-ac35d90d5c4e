{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Task Summary Component\n * Displays summary cards with task counts by status\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\nexport let TaskSummaryComponent = class TaskSummaryComponent {\n  constructor() {\n    /**\n     * Number of tasks with 'todo' status\n     */\n    this.todoCount = 0;\n    /**\n     * Number of tasks with 'in_progress' status\n     */\n    this.inProgressCount = 0;\n    /**\n     * Number of tasks with 'done' status\n     */\n    this.doneCount = 0;\n    /**\n     * Total number of tasks\n     */\n    this.totalCount = 0;\n  }\n};\n__decorate([Input()], TaskSummaryComponent.prototype, \"todoCount\", void 0);\n__decorate([Input()], TaskSummaryComponent.prototype, \"inProgressCount\", void 0);\n__decorate([Input()], TaskSummaryComponent.prototype, \"doneCount\", void 0);\n__decorate([Input()], TaskSummaryComponent.prototype, \"totalCount\", void 0);\nTaskSummaryComponent = __decorate([Component({\n  selector: 'app-task-summary',\n  templateUrl: './task-summary.component.html',\n  styleUrls: ['./task-summary.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskSummaryComponent);", "map": {"version": 3, "names": ["Component", "Input", "ChangeDetectionStrategy", "TaskSummaryComponent", "constructor", "todoCount", "inProgressCount", "doneCount", "totalCount", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\task-summary\\task-summary.component.ts"], "sourcesContent": ["/**\n * Task Summary Component\n * Displays summary cards with task counts by status\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-task-summary',\n  templateUrl: './task-summary.component.html',\n  styleUrls: ['./task-summary.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskSummaryComponent {\n  /**\n   * Number of tasks with 'todo' status\n   */\n  @Input() todoCount = 0;\n  \n  /**\n   * Number of tasks with 'in_progress' status\n   */\n  @Input() inProgressCount = 0;\n  \n  /**\n   * Number of tasks with 'done' status\n   */\n  @Input() doneCount = 0;\n  \n  /**\n   * Total number of tasks\n   */\n  @Input() totalCount = 0;\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,eAAe;AAQlE,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAA1BC,YAAA;IACL;;;IAGS,KAAAC,SAAS,GAAG,CAAC;IAEtB;;;IAGS,KAAAC,eAAe,GAAG,CAAC;IAE5B;;;IAGS,KAAAC,SAAS,GAAG,CAAC;IAEtB;;;IAGS,KAAAC,UAAU,GAAG,CAAC;EACzB;CAAC;AAhBUC,UAAA,EAARR,KAAK,EAAE,C,sDAAe;AAKdQ,UAAA,EAARR,KAAK,EAAE,C,4DAAqB;AAKpBQ,UAAA,EAARR,KAAK,EAAE,C,sDAAe;AAKdQ,UAAA,EAARR,KAAK,EAAE,C,uDAAgB;AAnBbE,oBAAoB,GAAAM,UAAA,EANhCT,SAAS,CAAC;EACTU,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B,CAAC;EAC5CC,eAAe,EAAEX,uBAAuB,CAACY;CAC1C,CAAC,C,EACWX,oBAAoB,CAoBhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}