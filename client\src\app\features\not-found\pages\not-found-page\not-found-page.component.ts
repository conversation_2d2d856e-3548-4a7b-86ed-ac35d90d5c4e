/**
 * Not Found Page component
 * Displays a 404 error page when a route is not found
 */
import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-not-found-page',
  templateUrl: './not-found-page.component.html',
  styleUrls: ['./not-found-page.component.scss']
})
export class NotFoundPageComponent {
  /**
   * Constructor
   * @param router - Router service for navigation
   */
  constructor(private router: Router) {}

  /**
   * Navigates back to the home page
   */
  goToHome(): void {
    this.router.navigate(['/']);
  }
}
