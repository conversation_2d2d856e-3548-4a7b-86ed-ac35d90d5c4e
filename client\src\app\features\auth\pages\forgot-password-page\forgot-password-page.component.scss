/**
 * Forgot password page component styles
 */

/* Container for the entire forgot password page */
.forgot-password-page-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

/* Authentication container */
.auth-container {
  width: 100%;
  max-width: 400px;
}

/* Header with logo and title */
.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

/* Logo styling */
.logo {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  
  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    color: var(--primary-color);
  }
}

/* App title */
.app-title {
  font-size: 2rem;
  font-weight: 500;
  margin: 0 0 8px;
  color: #333;
}

/* App subtitle */
.app-subtitle {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .forgot-password-page-container {
    padding: 16px;
    background-color: #fff;
  }
  
  .auth-header {
    margin-bottom: 24px;
  }
  
  .app-title {
    font-size: 1.8rem;
  }
}
