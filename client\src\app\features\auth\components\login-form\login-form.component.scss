/**
 * Login form component styles
 */

/* Container for the entire login form */
.login-form-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 24px;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

/* Login form */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Full width form fields */
.full-width {
  width: 100%;
}

/* Remember me and forgot password row */
.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Forgot password link */
.forgot-link {
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: none;
  font-size: 0.9rem;
  
  &:hover {
    text-decoration: underline;
  }
}

/* Submit button */
.submit-button {
  height: 48px;
  font-size: 1rem;
  margin-top: 8px;
  
  mat-spinner {
    display: inline-block;
    margin: 0 auto;
  }
}

/* Register link container */
.register-link {
  text-align: center;
  margin-top: 16px;
  font-size: 0.9rem;
  
  a {
    color: var(--primary-color);
    cursor: pointer;
    margin-left: 4px;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .login-form-container {
    padding: 16px;
    box-shadow: none;
  }
  
  .remember-forgot {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
