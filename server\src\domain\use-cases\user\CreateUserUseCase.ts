/**
 * Create User Use Case
 * Handles business logic for creating a new user
 */
import { IUser, IUserDocument } from '../../entities/User';
import { IUserRepository } from '../../repositories/IUserRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for creating a new user
 * Implements business rules and validation for user creation
 */
export class CreateUserUseCase {
  /**
   * Constructor for CreateUserUseCase
   * @param userRepository - Repository for user data access
   */
  constructor(private userRepository: IUserRepository) {}

  /**
   * Executes the use case to create a new user
   * @param userData - Data for the new user
   * @returns Promise resolving to the created user
   */
  async execute(userData: IUser): Promise<IUserDocument> {
    try {
      // Validate required fields
      if (!userData.name) {
        throw new AppError('Name is required', 400);
      }

      if (!userData.email) {
        throw new AppError('Email is required', 400);
      }

      if (!userData.password) {
        throw new AppError('Password is required', 400);
      }

      // Validate email format
      const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
      if (!emailRegex.test(userData.email)) {
        throw new AppError('Invalid email format', 400);
      }

      // Validate password length
      if (userData.password.length < 6) {
        throw new AppError('Password must be at least 6 characters long', 400);
      }

      // Create user using repository
      return await this.userRepository.create(userData);
    } catch (error) {
      // Rethrow AppErrors, wrap other errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Failed to create user: ${(error as Error).message}`, 500);
    }
  }
}
