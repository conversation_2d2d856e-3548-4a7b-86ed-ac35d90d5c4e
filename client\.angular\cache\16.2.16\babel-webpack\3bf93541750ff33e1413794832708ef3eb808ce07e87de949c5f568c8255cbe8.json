{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class TaskService {\n  /**\n   * Constructor with dependency injection\n   * @param apiService - Base API service for HTTP requests\n   */\n  constructor(apiService) {\n    this.apiService = apiService;\n    /**\n     * API endpoint path\n     */\n    this.endpoint = 'tasks';\n  }\n  /**\n   * Get all tasks with optional filtering\n   * @param filter - Optional filter parameters\n   * @returns Observable with array of tasks\n   */\n  getTasks(filter) {\n    // Build query parameters from filter\n    let params = new HttpParams();\n    if (filter) {\n      if (filter.status) {\n        params = params.set('status', filter.status);\n      }\n      if (filter.priority) {\n        params = params.set('priority', filter.priority);\n      }\n      if (filter.assigneeId) {\n        params = params.set('assigneeId', filter.assigneeId);\n      }\n    }\n    // Make API request\n    return this.apiService.get(this.endpoint, params).pipe(map(response => response.data));\n  }\n  /**\n   * Get a task by ID\n   * @param id - Task ID\n   * @returns Observable with task data\n   */\n  getTask(id) {\n    return this.apiService.get(`${this.endpoint}/${id}`).pipe(map(response => response.data));\n  }\n  /**\n   * Get task by ID (alias for getTask for compatibility)\n   * @param id - Task ID\n   * @returns Observable with task data\n   */\n  getTaskById(id) {\n    return this.getTask(id);\n  }\n  /**\n   * Create a new task\n   * @param task - Task data\n   * @returns Observable with created task\n   */\n  createTask(task) {\n    return this.apiService.post(this.endpoint, task).pipe(map(response => response.data));\n  }\n  /**\n   * Update an existing task\n   * @param id - Task ID\n   * @param task - Updated task data\n   * @returns Observable with updated task\n   */\n  updateTask(id, task) {\n    return this.apiService.put(`${this.endpoint}/${id}`, task).pipe(map(response => response.data));\n  }\n  /**\n   * Delete a task\n   * @param id - Task ID\n   * @returns Observable with success status\n   */\n  deleteTask(id) {\n    return this.apiService.delete(`${this.endpoint}/${id}`).pipe(map(response => response.success));\n  }\n  /**\n   * Get tasks assigned to a specific user\n   * @param userId - User ID\n   * @returns Observable with array of tasks\n   */\n  getTasksByAssignee(userId) {\n    const params = new HttpParams().set('assigneeId', userId);\n    return this.apiService.get(this.endpoint, params).pipe(map(response => response.data));\n  }\n  static {\n    this.ɵfac = function TaskService_Factory(t) {\n      return new (t || TaskService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TaskService,\n      factory: TaskService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "map", "TaskService", "constructor", "apiService", "endpoint", "getTasks", "filter", "params", "status", "set", "priority", "assigneeId", "get", "pipe", "response", "data", "getTask", "id", "getTaskById", "createTask", "task", "post", "updateTask", "put", "deleteTask", "delete", "success", "getTasksByAs<PERSON>ee", "userId", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\services\\task.service.ts"], "sourcesContent": ["/**\n * Task Service\n * Handles API communication for task-related operations\n */\nimport { Injectable } from '@angular/core';\nimport { HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { Task } from '../models/task.model';\nimport { TaskFilter } from '../models/task-filter.model';\nimport { ApiResponse } from '../models/api-response.model';\nimport { ApiService } from './api.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TaskService {\n  /**\n   * API endpoint path\n   */\n  private endpoint = 'tasks';\n\n  /**\n   * Constructor with dependency injection\n   * @param apiService - Base API service for HTTP requests\n   */\n  constructor(private apiService: ApiService) {}\n\n  /**\n   * Get all tasks with optional filtering\n   * @param filter - Optional filter parameters\n   * @returns Observable with array of tasks\n   */\n  getTasks(filter?: TaskFilter): Observable<Task[]> {\n    // Build query parameters from filter\n    let params = new HttpParams();\n    \n    if (filter) {\n      if (filter.status) {\n        params = params.set('status', filter.status);\n      }\n      \n      if (filter.priority) {\n        params = params.set('priority', filter.priority);\n      }\n      \n      if (filter.assigneeId) {\n        params = params.set('assigneeId', filter.assigneeId);\n      }\n    }\n    \n    // Make API request\n    return this.apiService.get<ApiResponse<Task[]>>(this.endpoint, params)\n      .pipe(\n        map(response => response.data)\n      );\n  }\n\n  /**\n   * Get a task by ID\n   * @param id - Task ID\n   * @returns Observable with task data\n   */\n  getTask(id: string): Observable<Task> {\n    return this.apiService.get<ApiResponse<Task>>(`${this.endpoint}/${id}`)\n      .pipe(\n        map(response => response.data)\n      );\n  }\n  \n  /**\n   * Get task by ID (alias for getTask for compatibility)\n   * @param id - Task ID\n   * @returns Observable with task data\n   */\n  getTaskById(id: string): Observable<Task> {\n    return this.getTask(id);\n  }\n\n  /**\n   * Create a new task\n   * @param task - Task data\n   * @returns Observable with created task\n   */\n  createTask(task: Partial<Task>): Observable<Task> {\n    return this.apiService.post<ApiResponse<Task>>(this.endpoint, task)\n      .pipe(\n        map(response => response.data)\n      );\n  }\n\n  /**\n   * Update an existing task\n   * @param id - Task ID\n   * @param task - Updated task data\n   * @returns Observable with updated task\n   */\n  updateTask(id: string, task: Partial<Task>): Observable<Task> {\n    return this.apiService.put<ApiResponse<Task>>(`${this.endpoint}/${id}`, task)\n      .pipe(\n        map(response => response.data)\n      );\n  }\n\n  /**\n   * Delete a task\n   * @param id - Task ID\n   * @returns Observable with success status\n   */\n  deleteTask(id: string): Observable<boolean> {\n    return this.apiService.delete<ApiResponse<{}>>(`${this.endpoint}/${id}`)\n      .pipe(\n        map(response => response.success)\n      );\n  }\n\n  /**\n   * Get tasks assigned to a specific user\n   * @param userId - User ID\n   * @returns Observable with array of tasks\n   */\n  getTasksByAssignee(userId: string): Observable<Task[]> {\n    const params = new HttpParams().set('assigneeId', userId);\n    \n    return this.apiService.get<ApiResponse<Task[]>>(this.endpoint, params)\n      .pipe(\n        map(response => response.data)\n      );\n  }\n}\n"], "mappings": "AAKA,SAASA,UAAU,QAAQ,sBAAsB;AAEjD,SAASC,GAAG,QAAQ,gBAAgB;;;AASpC,OAAM,MAAOC,WAAW;EAMtB;;;;EAIAC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAT9B;;;IAGQ,KAAAC,QAAQ,GAAG,OAAO;EAMmB;EAE7C;;;;;EAKAC,QAAQA,CAACC,MAAmB;IAC1B;IACA,IAAIC,MAAM,GAAG,IAAIR,UAAU,EAAE;IAE7B,IAAIO,MAAM,EAAE;MACV,IAAIA,MAAM,CAACE,MAAM,EAAE;QACjBD,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAC,QAAQ,EAAEH,MAAM,CAACE,MAAM,CAAC;;MAG9C,IAAIF,MAAM,CAACI,QAAQ,EAAE;QACnBH,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAC,UAAU,EAAEH,MAAM,CAACI,QAAQ,CAAC;;MAGlD,IAAIJ,MAAM,CAACK,UAAU,EAAE;QACrBJ,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAC,YAAY,EAAEH,MAAM,CAACK,UAAU,CAAC;;;IAIxD;IACA,OAAO,IAAI,CAACR,UAAU,CAACS,GAAG,CAAsB,IAAI,CAACR,QAAQ,EAAEG,MAAM,CAAC,CACnEM,IAAI,CACHb,GAAG,CAACc,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAC/B;EACL;EAEA;;;;;EAKAC,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACd,UAAU,CAACS,GAAG,CAAoB,GAAG,IAAI,CAACR,QAAQ,IAAIa,EAAE,EAAE,CAAC,CACpEJ,IAAI,CACHb,GAAG,CAACc,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAC/B;EACL;EAEA;;;;;EAKAG,WAAWA,CAACD,EAAU;IACpB,OAAO,IAAI,CAACD,OAAO,CAACC,EAAE,CAAC;EACzB;EAEA;;;;;EAKAE,UAAUA,CAACC,IAAmB;IAC5B,OAAO,IAAI,CAACjB,UAAU,CAACkB,IAAI,CAAoB,IAAI,CAACjB,QAAQ,EAAEgB,IAAI,CAAC,CAChEP,IAAI,CACHb,GAAG,CAACc,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAC/B;EACL;EAEA;;;;;;EAMAO,UAAUA,CAACL,EAAU,EAAEG,IAAmB;IACxC,OAAO,IAAI,CAACjB,UAAU,CAACoB,GAAG,CAAoB,GAAG,IAAI,CAACnB,QAAQ,IAAIa,EAAE,EAAE,EAAEG,IAAI,CAAC,CAC1EP,IAAI,CACHb,GAAG,CAACc,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAC/B;EACL;EAEA;;;;;EAKAS,UAAUA,CAACP,EAAU;IACnB,OAAO,IAAI,CAACd,UAAU,CAACsB,MAAM,CAAkB,GAAG,IAAI,CAACrB,QAAQ,IAAIa,EAAE,EAAE,CAAC,CACrEJ,IAAI,CACHb,GAAG,CAACc,QAAQ,IAAIA,QAAQ,CAACY,OAAO,CAAC,CAClC;EACL;EAEA;;;;;EAKAC,kBAAkBA,CAACC,MAAc;IAC/B,MAAMrB,MAAM,GAAG,IAAIR,UAAU,EAAE,CAACU,GAAG,CAAC,YAAY,EAAEmB,MAAM,CAAC;IAEzD,OAAO,IAAI,CAACzB,UAAU,CAACS,GAAG,CAAsB,IAAI,CAACR,QAAQ,EAAEG,MAAM,CAAC,CACnEM,IAAI,CACHb,GAAG,CAACc,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAC/B;EACL;;;uBAhHWd,WAAW,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAX/B,WAAW;MAAAgC,OAAA,EAAXhC,WAAW,CAAAiC,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}