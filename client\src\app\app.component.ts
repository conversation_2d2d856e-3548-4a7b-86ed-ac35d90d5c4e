/**
 * Root component of the application
 * Serves as the main container for all other components
 */
import { Component, ChangeDetectionStrategy, inject, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { AuthService } from './core/services/auth.service';

// Define interfaces for performance metrics
interface LargestContentfulPaintEntry extends PerformanceEntry {
  element: Element;
  renderTime: number;
  loadTime: number;
  size: number;
  id: string;
  url: string;
}

interface LayoutShiftEntry extends PerformanceEntry {
  value: number;
  hadRecentInput: boolean;
  lastInputTime: number;
  sources: Array<LayoutShiftSource>;
}

interface LayoutShiftSource {
  node?: Node;
  previousRect: DOMRectReadOnly;
  currentRect: DOMRectReadOnly;
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppComponent implements OnInit {
  /**
   * Title of the application
   */
  title = 'Task Management';

  /**
   * Flag to determine if the user is authenticated
   */
  isAuthenticated = false;

  /**
   * Performance metrics for analytics and monitoring
   */
  private performanceMetrics = {
    lcp: 0, // Largest Contentful Paint time in ms
    cls: 0  // Cumulative Layout Shift score
  };

  /**
   * Injected services using modern Angular inject function
   */
  private router = inject(Router);
  private authService = inject(AuthService);

  /**
   * Constructor - no longer needs dependency injection parameters
   * Initialize component with dependency injection via inject()
   */
  constructor() {
    // Initialize authentication status
    this.initializeAuthStatus();
  }

  /**
   * Lifecycle hook that is called after component initialization
   * Sets up route change tracking and authentication status
   */
  ngOnInit(): void {
    // Track route changes for analytics or other purposes
    this.router.events.pipe(
      filter((event: unknown) => event instanceof NavigationEnd)
    ).subscribe(() => {
      // Measure performance metrics
      this.measurePerformanceMetrics();
    });

    // Initialize authentication status
    this.initializeAuthStatus();
  }

  /**
   * Initialize authentication status subscription
   */
  private initializeAuthStatus(): void {
    this.authService.isAuthenticated$.subscribe(
      (isAuthenticated: boolean) => {
        this.isAuthenticated = isAuthenticated;
      }
    );
  }

  /**
   * Measures key performance metrics for the application
   * Focuses on LCP (Largest Contentful Paint) and CLS (Cumulative Layout Shift)
   */
  private measurePerformanceMetrics(): void {
    // Use Performance Observer to measure LCP
    if ('PerformanceObserver' in window) {
      // LCP measurement
      const lcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1] as LargestContentfulPaintEntry;
        // Performance metric: Largest Contentful Paint time
        // Store LCP value in class property for potential analytics
        this.performanceMetrics.lcp = lastEntry.startTime;
        // console.log('LCP:', this.performanceMetrics.lcp);
        // console.log('LCP Element:', lastEntry.element);
      });

      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });

      // CLS measurement
      const clsObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        let clsValue = 0;

        entries.forEach(entry => {
          // Cast to our custom interface
          const layoutShiftEntry = entry as LayoutShiftEntry;
          if (!layoutShiftEntry.hadRecentInput) {
            clsValue += layoutShiftEntry.value;
          }
        });

        // Performance metric: Cumulative Layout Shift value
        // Store CLS value in class property for potential analytics
        this.performanceMetrics.cls = clsValue;
        // console.log('CLS:', this.performanceMetrics.cls);
      });

      clsObserver.observe({ type: 'layout-shift', buffered: true });
    }
  }
}
