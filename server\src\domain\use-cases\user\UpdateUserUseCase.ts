/**
 * Update User Use Case
 * Handles business logic for updating an existing user
 */
import { IUser, IUserDocument } from '../../entities/User';
import { IUserRepository } from '../../repositories/IUserRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for updating an existing user
 * Implements business rules and validation for user updates
 */
export class UpdateUserUseCase {
  /**
   * Constructor for UpdateUserUseCase
   * @param userRepository - Repository for user data access
   */
  constructor(private userRepository: IUserRepository) {}

  /**
   * Executes the use case to update an existing user
   * @param userId - ID of the user to update
   * @param updateData - Data to update on the user
   * @returns Promise resolving to the updated user
   * @throws AppError if user not found or validation fails
   */
  async execute(userId: string, updateData: Partial<IUser>): Promise<IUserDocument> {
    try {
      // Validate user ID
      if (!userId) {
        throw new AppError('User ID is required', 400);
      }

      // Validate update data
      if (Object.keys(updateData).length === 0) {
        throw new AppError('No update data provided', 400);
      }

      // Validate email format if provided
      if (updateData.email) {
        const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
        if (!emailRegex.test(updateData.email)) {
          throw new AppError('Invalid email format', 400);
        }
      }

      // Validate password length if provided
      if (updateData.password && updateData.password.length < 6) {
        throw new AppError('Password must be at least 6 characters long', 400);
      }

      // Update user
      const updatedUser = await this.userRepository.update(userId, updateData);
      
      // Check if user exists
      if (!updatedUser) {
        throw new AppError(`User with ID ${userId} not found`, 404);
      }
      
      return updatedUser;
    } catch (error) {
      // Rethrow AppErrors, wrap other errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error updating user: ${(error as Error).message}`, 500);
    }
  }
}
