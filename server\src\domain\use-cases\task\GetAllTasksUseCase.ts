/**
 * Get All Tasks Use Case
 * Handles business logic for retrieving all tasks with optional filtering
 */
import { ITask, ITaskDocument } from '../../entities/Task';
import { ITaskRepository } from '../../repositories/ITaskRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for retrieving all tasks with optional filtering
 * Implements business rules and validation for task listing
 */
export class GetAllTasksUseCase {
  /**
   * Constructor for GetAllTasksUseCase
   * @param taskRepository - Repository for task data access
   */
  constructor(private taskRepository: ITaskRepository) {}

  /**
   * Executes the use case to retrieve all tasks with optional filtering
   * @param filter - Optional filter criteria
   * @returns Promise resolving to array of tasks
   */
  async execute(filter: Partial<ITask> = {}): Promise<ITaskDocument[]> {
    try {
      // Get all tasks with optional filter
      return await this.taskRepository.findAll(filter);
    } catch (error) {
      // Wrap all errors
      throw new AppError(`Error retrieving tasks: ${(error as Error).message}`, 500);
    }
  }
}
