<!-- Task list container -->
<div class="task-list-container">
  <!-- Header with actions -->
  <div class="task-list-header">
    <h2>Tasks</h2>
    <div class="task-list-actions">
      <button mat-icon-button (click)="onRefresh()" aria-label="Refresh tasks">
        <mat-icon>refresh</mat-icon>
      </button>
      <button mat-raised-button color="primary" [routerLink]="['/tasks/create']">
        <mat-icon>add</mat-icon>
        New Task
      </button>
    </div>
  </div>

  <!-- Task filter component -->
  <app-task-filter 
    [filter]="filter" 
    (filterChanged)="onFilterChange($event)">
  </app-task-filter>

  <!-- Loading state -->
  <div *ngIf="loading" class="task-list-loading">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Error state -->
  <app-error-message 
    *ngIf="error" 
    [message]="error" 
    [showRetry]="true"
    [onRetry]="onRefresh.bind(this)">
  </app-error-message>

  <!-- Empty state -->
  <div *ngIf="!loading && !error && displayedTasks.length === 0" class="task-list-empty">
    <mat-icon>assignment</mat-icon>
    <p>No tasks found</p>
    <button mat-stroked-button color="primary" [routerLink]="['/tasks/create']">
      Create your first task
    </button>
  </div>

  <!-- Task list -->
  <div *ngIf="!loading && !error && displayedTasks.length > 0" class="task-list">
    <!-- Table header -->
    <div class="task-list-header-row">
      <div class="task-column task-status-column" (click)="onSort('status')">
        Status
        <mat-icon *ngIf="sortField === 'status'">
          {{ sortDirection === 'asc' ? 'arrow_upward' : 'arrow_downward' }}
        </mat-icon>
      </div>
      <div class="task-column task-title-column" (click)="onSort('title')">
        Title
        <mat-icon *ngIf="sortField === 'title'">
          {{ sortDirection === 'asc' ? 'arrow_upward' : 'arrow_downward' }}
        </mat-icon>
      </div>
      <div class="task-column task-priority-column" (click)="onSort('priority')">
        Priority
        <mat-icon *ngIf="sortField === 'priority'">
          {{ sortDirection === 'asc' ? 'arrow_upward' : 'arrow_downward' }}
        </mat-icon>
      </div>
      <div class="task-column task-date-column" (click)="onSort('dueDate')">
        Due Date
        <mat-icon *ngIf="sortField === 'dueDate'">
          {{ sortDirection === 'asc' ? 'arrow_upward' : 'arrow_downward' }}
        </mat-icon>
      </div>
      <div class="task-column task-actions-column">
        Actions
      </div>
    </div>

    <!-- Task items -->
    <app-task-item
      *ngFor="let task of displayedTasks"
      [task]="task"
      (click)="onTaskSelect(task)"
      (delete)="onTaskDelete(task.id)"
      (statusChange)="onStatusChange(task.id, $event)">
    </app-task-item>
  </div>
</div>
