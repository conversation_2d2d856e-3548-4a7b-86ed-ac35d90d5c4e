{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Not Found module for 404 page\n * Contains components for displaying 404 error page\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n// Define routes for the not-found feature\nconst routes = [{\n  path: '',\n  loadChildren: () => import('./pages/not-found-page/not-found-page.module').then(m => m.NotFoundPageModule)\n}];\nexport let NotFoundModule = class NotFoundModule {};\nNotFoundModule = __decorate([NgModule({\n  declarations: [],\n  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n})], NotFoundModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "SharedModule", "routes", "path", "loadChildren", "then", "m", "NotFoundPageModule", "NotFoundModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\not-found\\not-found.module.ts"], "sourcesContent": ["/**\n * Not Found module for 404 page\n * Contains components for displaying 404 error page\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n\n// Define routes for the not-found feature\nconst routes: Routes = [\n  {\n    path: '',\n    loadChildren: () => import('./pages/not-found-page/not-found-page.module').then(m => m.NotFoundPageModule)\n  }\n];\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class NotFoundModule { }\n"], "mappings": ";AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AAEzD;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,kBAAkB;CAC1G,CACF;AAUM,WAAMC,cAAc,GAApB,MAAMA,cAAc,GAAI;AAAlBA,cAAc,GAAAC,UAAA,EAR1BX,QAAQ,CAAC;EACRY,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,CACPZ,YAAY,EACZE,YAAY,EACZD,YAAY,CAACY,QAAQ,CAACV,MAAM,CAAC;CAEhC,CAAC,C,EACWM,cAAc,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}