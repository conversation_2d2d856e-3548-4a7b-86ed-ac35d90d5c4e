{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/progress-spinner\";\nfunction LoadingSpinnerComponent_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.message);\n  }\n}\nexport class LoadingSpinnerComponent {\n  constructor() {\n    /**\n     * Diameter of the spinner in pixels\n     */\n    this.diameter = 40;\n    /**\n     * Optional message to display below the spinner\n     */\n    this.message = 'Loading...';\n    /**\n     * Whether to show the loading message\n     */\n    this.showMessage = true;\n    /**\n     * Color of the spinner\n     */\n    this.color = 'primary';\n  }\n  static {\n    this.ɵfac = function LoadingSpinnerComponent_Factory(t) {\n      return new (t || LoadingSpinnerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoadingSpinnerComponent,\n      selectors: [[\"app-loading-spinner\"]],\n      inputs: {\n        diameter: \"diameter\",\n        message: \"message\",\n        showMessage: \"showMessage\",\n        color: \"color\"\n      },\n      decls: 3,\n      vars: 3,\n      consts: [[1, \"spinner-container\"], [\"aria-label\", \"Loading content\", 3, \"diameter\", \"color\"], [\"class\", \"spinner-message\", 4, \"ngIf\"], [1, \"spinner-message\"]],\n      template: function LoadingSpinnerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"mat-spinner\", 1);\n          i0.ɵɵtemplate(2, LoadingSpinnerComponent_p_2_Template, 2, 1, \"p\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"diameter\", ctx.diameter)(\"color\", ctx.color);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessage);\n        }\n      },\n      dependencies: [i1.NgIf, i2.MatProgressSpinner],\n      styles: [\"\\n\\n\\n\\n\\n\\n.spinner-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n\\n\\n.spinner-message[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  color: #666;\\n  font-size: 0.9rem;\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvbG9hZGluZy1zcGlubmVyL2xvYWRpbmctc3Bpbm5lci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7RUFBQTtBQUlBLHNDQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtBQUFGOztBQUdBLG9CQUFBO0FBQ0E7RUFDRSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExvYWRpbmcgc3Bpbm5lciBjb21wb25lbnQgc3R5bGVzXG4gKi9cblxuLyogQ29udGFpbmVyIGZvciBzcGlubmVyIGFuZCBtZXNzYWdlICovXG4uc3Bpbm5lci1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgcGFkZGluZzogMjBweDtcbn1cblxuLyogTWVzc2FnZSBzdHlsaW5nICovXG4uc3Bpbm5lci1tZXNzYWdlIHtcbiAgbWFyZ2luLXRvcDogMTZweDtcbiAgY29sb3I6ICM2NjY7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "message", "LoadingSpinnerComponent", "constructor", "diameter", "showMessage", "color", "selectors", "inputs", "decls", "vars", "consts", "template", "LoadingSpinnerComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "LoadingSpinnerComponent_p_2_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\components\\loading-spinner\\loading-spinner.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\components\\loading-spinner\\loading-spinner.component.html"], "sourcesContent": ["/**\n * Loading Spinner Component\n * Displays a loading indicator for async operations\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\n\n@Component({\n  selector: 'app-loading-spinner',\n  templateUrl: './loading-spinner.component.html',\n  styleUrls: ['./loading-spinner.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class LoadingSpinnerComponent {\n  /**\n   * Diameter of the spinner in pixels\n   */\n  @Input() diameter = 40;\n  \n  /**\n   * Optional message to display below the spinner\n   */\n  @Input() message = 'Loading...';\n  \n  /**\n   * Whether to show the loading message\n   */\n  @Input() showMessage = true;\n  \n  /**\n   * Color of the spinner\n   */\n  @Input() color: 'primary' | 'accent' | 'warn' = 'primary';\n}\n", "<!-- Loading spinner container -->\n<div class=\"spinner-container\">\n  <!-- Material spinner with configurable size and color -->\n  <mat-spinner \n    [diameter]=\"diameter\" \n    [color]=\"color\"\n    aria-label=\"Loading content\">\n  </mat-spinner>\n  \n  <!-- Optional loading message -->\n  <p *ngIf=\"showMessage\" class=\"spinner-message\">{{ message }}</p>\n</div>\n"], "mappings": ";;;;;ICUEA,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAjBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAa;;;ADE9D,OAAM,MAAOC,uBAAuB;EANpCC,YAAA;IAOE;;;IAGS,KAAAC,QAAQ,GAAG,EAAE;IAEtB;;;IAGS,KAAAH,OAAO,GAAG,YAAY;IAE/B;;;IAGS,KAAAI,WAAW,GAAG,IAAI;IAE3B;;;IAGS,KAAAC,KAAK,GAAkC,SAAS;;;;uBAnB9CJ,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAK,SAAA;MAAAC,MAAA;QAAAJ,QAAA;QAAAH,OAAA;QAAAI,WAAA;QAAAC,KAAA;MAAA;MAAAG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXpCpB,EAAA,CAAAC,cAAA,aAA+B;UAE7BD,EAAA,CAAAsB,SAAA,qBAIc;UAGdtB,EAAA,CAAAuB,UAAA,IAAAC,oCAAA,eAAgE;UAClExB,EAAA,CAAAG,YAAA,EAAM;;;UAPFH,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAyB,UAAA,aAAAJ,GAAA,CAAAX,QAAA,CAAqB,UAAAW,GAAA,CAAAT,KAAA;UAMnBZ,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAyB,UAAA,SAAAJ,GAAA,CAAAV,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}