/**
 * User Routes
 * Defines API endpoints for user operations
 */
import { Router } from 'express';
import {
  createUser,
  getUser,
  getAllUsers,
  updateUser,
  deleteUser,
} from '../controllers/userController';
import { protect, restrictTo } from '../middleware/authMiddleware';
import { UserRole } from '../../domain/entities/User';

// Create router instance
const router = Router();

// Apply authentication middleware to all routes
router.use(protect);

// Restrict all user routes to admin only
router.use(restrictTo(UserRole.ADMIN));

// Define routes
router
  .route('/')
  .get(getAllUsers)
  .post(createUser);

router
  .route('/:id')
  .get(getUser)
  .put(updateUser)
  .delete(deleteUser);

export default router;
