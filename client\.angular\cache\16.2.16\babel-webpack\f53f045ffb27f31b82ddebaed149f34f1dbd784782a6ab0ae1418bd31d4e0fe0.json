{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Register Page Component\n * Page for user registration\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nexport let RegisterPageComponent = class RegisterPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, notificationService, router, cdr) {\n    this.authService = authService;\n    this.notificationService = notificationService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed registration attempt\n     */\n    this.error = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit() {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Handle register form submission\n   * @param userData - User registration data\n   */\n  onFormSubmit(userData) {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    this.authService.register(userData.name, userData.email, userData.password).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.loading = false;\n        this.notificationService.success('Registration successful! Please log in.');\n        this.router.navigate(['/auth/login']);\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.message || 'Registration failed. Please try again.';\n        console.error('Registration error:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Navigate to login page\n   */\n  onLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n};\nRegisterPageComponent = __decorate([Component({\n  selector: 'app-register-page',\n  templateUrl: './register-page.component.html',\n  styleUrls: ['./register-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], RegisterPageComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "Subject", "takeUntil", "RegisterPageComponent", "constructor", "authService", "notificationService", "router", "cdr", "loading", "error", "destroy$", "ngOnInit", "isAuthenticated", "navigate", "ngOnDestroy", "next", "complete", "onFormSubmit", "userData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register", "name", "email", "password", "pipe", "subscribe", "success", "err", "message", "console", "onLogin", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\register-page\\register-page.component.ts"], "sourcesContent": ["/**\n * Register Page Component\n * Page for user registration\n */\nimport { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-register-page',\n  templateUrl: './register-page.component.html',\n  styleUrls: ['./register-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class RegisterPageComponent implements OnInit, OnDestroy {\n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message from failed registration attempt\n   */\n  error: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private notificationService: NotificationService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit(): void {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Handle register form submission\n   * @param userData - User registration data\n   */\n  onFormSubmit(userData: { name: string; email: string; password: string }): void {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    \n    this.authService.register(userData.name, userData.email, userData.password)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.loading = false;\n          this.notificationService.success('Registration successful! Please log in.');\n          this.router.navigate(['/auth/login']);\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = err.message || 'Registration failed. Please try again.';\n          console.error('Registration error:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Navigate to login page\n   */\n  onLogin(): void {\n    this.router.navigate(['/auth/login']);\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AAExG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAUnC,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAgBhC;;;;;;;EAOAC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA1Bb;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIV,OAAO,EAAQ;EAcnC;EAEH;;;;EAIAW,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACP,WAAW,CAACQ,eAAe,EAAE,EAAE;MACtC,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;;EAIAC,YAAYA,CAACC,QAA2D;IACtE,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACF,GAAG,CAACY,YAAY,EAAE;IAEvB,IAAI,CAACf,WAAW,CAACgB,QAAQ,CAACF,QAAQ,CAACG,IAAI,EAAEH,QAAQ,CAACI,KAAK,EAAEJ,QAAQ,CAACK,QAAQ,CAAC,CACxEC,IAAI,CAACvB,SAAS,CAAC,IAAI,CAACS,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTV,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACP,OAAO,GAAG,KAAK;QACpB,IAAI,CAACH,mBAAmB,CAACqB,OAAO,CAAC,yCAAyC,CAAC;QAC3E,IAAI,CAACpB,MAAM,CAACO,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACvC,CAAC;MACDJ,KAAK,EAAGkB,GAAG,IAAI;QACb,IAAI,CAACnB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGkB,GAAG,CAACC,OAAO,IAAI,wCAAwC;QACpEC,OAAO,CAACpB,KAAK,CAAC,qBAAqB,EAAEkB,GAAG,CAAC;QACzC,IAAI,CAACpB,GAAG,CAACY,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAW,OAAOA,CAAA;IACL,IAAI,CAACxB,MAAM,CAACO,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;CACD;AAlFYX,qBAAqB,GAAA6B,UAAA,EANjCjC,SAAS,CAAC;EACTkC,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC,CAAC;EAC7CC,eAAe,EAAEpC,uBAAuB,CAACqC;CAC1C,CAAC,C,EACWlC,qBAAqB,CAkFjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}