{"ast": null, "code": "/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { ElementRef, EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class LazyLoadDirective {\n  /**\n   * Constructor with dependency injection\n   * @param el - Reference to the host element\n   */\n  constructor(el) {\n    this.el = el;\n    /**\n     * Event emitted when element enters viewport\n     */\n    this.appLazyLoad = new EventEmitter();\n    /**\n     * Intersection observer instance\n     */\n    this.observer = null;\n  }\n  /**\n   * Initialize the intersection observer\n   */\n  ngOnInit() {\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {\n      // Create observer with options\n      this.observer = new IntersectionObserver(entries => {\n        this.handleIntersection(entries);\n      }, {\n        root: null,\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      // Start observing the element\n      if (this.el && this.el.nativeElement) {\n        this.observer.observe(this.el.nativeElement);\n      }\n    }\n  }\n  /**\n   * Clean up observer when directive is destroyed\n   */\n  ngOnDestroy() {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  handleIntersection(entries) {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LazyLoadDirective_Factory(t) {\n      return new (t || LazyLoadDirective)(i0.ɵɵdirectiveInject(ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: LazyLoadDirective,\n      selectors: [[\"\", \"appLazyLoad\", \"\"]],\n      outputs: {\n        appLazyLoad: \"appLazyLoad\"\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["ElementRef", "EventEmitter", "LazyLoadDirective", "constructor", "el", "appLazyLoad", "observer", "ngOnInit", "window", "IntersectionObserver", "entries", "handleIntersection", "root", "rootMargin", "threshold", "nativeElement", "observe", "ngOnDestroy", "disconnect", "for<PERSON>ach", "entry", "isIntersecting", "emit", "unobserve", "target", "i0", "ɵɵdirectiveInject", "selectors", "outputs"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\directives\\lazy-load.directive.ts"], "sourcesContent": ["/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { Directive, ElementRef, EventEmitter, Inject, OnDestroy, OnInit, Output } from '@angular/core';\n\n@Directive({\n  selector: '[appLazyLoad]'\n})\nexport class LazyLoadDirective implements OnInit, OnDestroy {\n  /**\n   * Event emitted when element enters viewport\n   */\n  @Output() appLazyLoad = new EventEmitter<void>();\n  \n  /**\n   * Intersection observer instance\n   */\n  private observer: IntersectionObserver | null = null;\n\n  /**\n   * Constructor with dependency injection\n   * @param el - Reference to the host element\n   */\n  constructor(@Inject(ElementRef) private el: any) {}\n\n  /**\n   * Initialize the intersection observer\n   */\n  ngOnInit(): void {\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {\n      // Create observer with options\n      this.observer = new IntersectionObserver((entries) => {\n        this.handleIntersection(entries);\n      }, {\n        root: null, // Use viewport as root\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      \n      // Start observing the element\n      if (this.el && this.el.nativeElement) {\n        this.observer.observe(this.el.nativeElement);\n      }\n    }\n  }\n\n  /**\n   * Clean up observer when directive is destroyed\n   */\n  ngOnDestroy(): void {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  private handleIntersection(entries: IntersectionObserverEntry[]): void {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        \n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n}\n"], "mappings": "AAAA;;;;;AAKA,SAAoBA,UAAU,EAAEC,YAAY,QAA2C,eAAe;;AAKtG,OAAM,MAAOC,iBAAiB;EAW5B;;;;EAIAC,YAAwCC,EAAO;IAAP,KAAAA,EAAE,GAAFA,EAAE;IAd1C;;;IAGU,KAAAC,WAAW,GAAG,IAAIJ,YAAY,EAAQ;IAEhD;;;IAGQ,KAAAK,QAAQ,GAAgC,IAAI;EAMF;EAElD;;;EAGAC,QAAQA,CAAA;IACN,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,sBAAsB,IAAIA,MAAM,EAAE;MACrE;MACA,IAAI,CAACF,QAAQ,GAAG,IAAIG,oBAAoB,CAAEC,OAAO,IAAI;QACnD,IAAI,CAACC,kBAAkB,CAACD,OAAO,CAAC;MAClC,CAAC,EAAE;QACDE,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,GAAG,CAAC;OAChB,CAAC;MAEF;MACA,IAAI,IAAI,CAACV,EAAE,IAAI,IAAI,CAACA,EAAE,CAACW,aAAa,EAAE;QACpC,IAAI,CAACT,QAAQ,CAACU,OAAO,CAAC,IAAI,CAACZ,EAAE,CAACW,aAAa,CAAC;;;EAGlD;EAEA;;;EAGAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACX,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACY,UAAU,EAAE;MAC1B,IAAI,CAACZ,QAAQ,GAAG,IAAI;;EAExB;EAEA;;;;EAIQK,kBAAkBA,CAACD,OAAoC;IAC7DA,OAAO,CAACS,OAAO,CAACC,KAAK,IAAG;MACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxB;QACA,IAAI,CAAChB,WAAW,CAACiB,IAAI,EAAE;QAEvB;QACA,IAAI,IAAI,CAAChB,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,CAACiB,SAAS,CAACH,KAAK,CAACI,MAAM,CAAC;;;IAG3C,CAAC,CAAC;EACJ;;;uBAhEWtB,iBAAiB,EAAAuB,EAAA,CAAAC,iBAAA,CAeR1B,UAAU;IAAA;EAAA;;;YAfnBE,iBAAiB;MAAAyB,SAAA;MAAAC,OAAA;QAAAvB,WAAA;MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}