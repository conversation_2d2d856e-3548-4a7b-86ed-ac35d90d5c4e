/**
 * Main routing module for the application
 * Defines routes and lazy loading strategy
 */
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';
import { GuestGuard } from './core/guards/guest.guard';
import { CustomPreloadingStrategy } from './core/strategies/custom-preloading.strategy';

/**
 * Application routes configuration
 * Implements lazy loading for better performance
 */
const routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'auth',
    loadChildren: (): Promise<typeof import('./features/auth/auth.module')> => import('./features/auth/auth.module').then(m => m.AuthModule),
    canActivate: [GuestGuard]
  },
  {
    path: 'dashboard',
    loadChildren: (): Promise<typeof import('./features/dashboard/dashboard.module')> => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'tasks',
    loadChildren: (): Promise<typeof import('./features/tasks/tasks.module')> => import('./features/tasks/tasks.module').then(m => m.TasksModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'profile',
    loadChildren: (): Promise<typeof import('./features/profile/profile.module')> => import('./features/profile/profile.module').then(m => m.ProfileModule),
    canActivate: [AuthGuard]
  },
  {
    path: '**',
    loadChildren: (): Promise<typeof import('./features/not-found/not-found.module')> => import('./features/not-found/not-found.module').then(m => m.NotFoundModule)
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    initialNavigation: 'enabledBlocking', // For SSR
    scrollPositionRestoration: 'enabled', // Scroll to top on navigation
    relativeLinkResolution: 'legacy',
    // Preload all modules for better UX after initial load
    preloadingStrategy: CustomPreloadingStrategy
  })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
