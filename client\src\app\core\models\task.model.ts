/**
 * Task model interface
 * Represents a task in the system
 */
export interface Task {
  /**
   * Unique identifier for the task
   */
  id: string;
  
  /**
   * Title of the task
   */
  title: string;
  
  /**
   * Detailed description of the task
   */
  description: string;
  
  /**
   * Current status of the task
   */
  status: 'todo' | 'in_progress' | 'review' | 'done';
  
  /**
   * Priority level of the task
   */
  priority: 'low' | 'medium' | 'high';
  
  /**
   * Due date for the task
   */
  dueDate?: Date;
  
  /**
   * ID of the user who created the task
   */
  createdBy: string;
  
  /**
   * ID of the user assigned to the task
   */
  assigneeId?: string;
  
  /**
   * Tags associated with the task
   */
  tags?: string[];
  
  /**
   * Date when the task was created
   */
  createdAt: Date;
  
  /**
   * Date when the task was last updated
   */
  updatedAt: Date;
}
