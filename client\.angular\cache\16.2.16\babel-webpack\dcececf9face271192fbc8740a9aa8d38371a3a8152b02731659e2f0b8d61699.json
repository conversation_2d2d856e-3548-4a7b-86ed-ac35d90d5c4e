{"ast": null, "code": "import * as i8 from '@angular/cdk/overlay';\nimport { Overlay, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i7 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Optional, Inject, Self, Attribute, Input, ViewChild, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, mixinDisabled, mixinErrorState, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, take, filter, map, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\nfunction MatSelect_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.placeholder);\n  }\n}\nfunction MatSelect_span_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.triggerValue);\n  }\n}\nfunction MatSelect_span_5_ng_content_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 0, [\"*ngSwitchCase\", \"true\"]);\n  }\n}\nfunction MatSelect_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtemplate(1, MatSelect_span_5_span_1_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtemplate(2, MatSelect_span_5_ng_content_2_Template, 1, 0, \"ng-content\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngSwitch\", !!ctx_r3.customTrigger);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitchCase\", true);\n  }\n}\nfunction MatSelect_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 15, 16);\n    i0.ɵɵlistener(\"@transformPanel.done\", function MatSelect_ng_template_10_Template_div_animation_transformPanel_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8._panelDoneAnimatingStream.next($event.toState));\n    })(\"keydown\", function MatSelect_ng_template_10_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10._handleKeydown($event));\n    });\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open \", ctx_r4._getPanelTheme(), \"\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.panelClass)(\"@transformPanel\", \"showing\");\n    i0.ɵɵattribute(\"id\", ctx_r4.id + \"-panel\")(\"aria-multiselectable\", ctx_r4.multiple)(\"aria-label\", ctx_r4.ariaLabel || null)(\"aria-labelledby\", ctx_r4._getPanelAriaLabelledby());\n  }\n}\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nconst matSelectAnimations = {\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: trigger('transformPanelWrap', [transition('* => void', query('@transformPanel', [animateChild()], {\n    optional: true\n  }))]),\n  /** This animation transforms the select's overlay panel on and off the page. */\n  transformPanel: trigger('transformPanel', [state('void', style({\n    opacity: 0,\n    transform: 'scale(1, 0.8)'\n  })), transition('void => showing', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 1,\n    transform: 'scale(1, 1)'\n  }))), transition('* => void', animate('100ms linear', style({\n    opacity: 0\n  })))])\n};\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\nlet nextUniqueId = 0;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy');\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n  constructor( /** Reference to the select that emitted the change event. */\n  source, /** Current value of the select that emitted the event. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n// Boilerplate for applying mixins to MatSelect.\n/** @docs-private */\nconst _MatSelectMixinBase = mixinDisableRipple(mixinTabIndex(mixinDisabled(mixinErrorState(class {\n  constructor(_elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup,\n  /**\n   * Form control bound to the component.\n   * Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  ngControl) {\n    this._elementRef = _elementRef;\n    this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n    this._parentForm = _parentForm;\n    this._parentFormGroup = _parentFormGroup;\n    this.ngControl = ngControl;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    this.stateChanges = new Subject();\n  }\n}))));\n/** Base class with all of the `MatSelect` functionality. */\nclass _MatSelectBase extends _MatSelectMixinBase {\n  /** Whether the select is focused. */\n  get focused() {\n    return this._focused || this._panelOpen;\n  }\n  /** Placeholder to be shown if no value has been selected. */\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  /** Whether the component is required. */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n    this.stateChanges.next();\n  }\n  /** Whether the user should be allowed to select multiple options. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectDynamicMultipleError();\n    }\n    this._multiple = coerceBooleanProperty(value);\n  }\n  /** Whether to center the active option over the trigger. */\n  get disableOptionCentering() {\n    return this._disableOptionCentering;\n  }\n  set disableOptionCentering(value) {\n    this._disableOptionCentering = coerceBooleanProperty(value);\n  }\n  /**\n   * Function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectNonFunctionValueError();\n    }\n    this._compareWith = fn;\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /** Value of the select control. */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    const hasAssigned = this._assignValue(newValue);\n    if (hasAssigned) {\n      this._onChange(newValue);\n    }\n  }\n  /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n  get typeaheadDebounceInterval() {\n    return this._typeaheadDebounceInterval;\n  }\n  set typeaheadDebounceInterval(value) {\n    this._typeaheadDebounceInterval = coerceNumberProperty(value);\n  }\n  /** Unique id of the element. */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n    this.stateChanges.next();\n  }\n  constructor(_viewportRuler, _changeDetectorRef, _ngZone, _defaultErrorStateMatcher, elementRef, _dir, _parentForm, _parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n    super(elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n    this._viewportRuler = _viewportRuler;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._ngZone = _ngZone;\n    this._dir = _dir;\n    this._parentFormField = _parentFormField;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._defaultOptions = _defaultOptions;\n    /** Whether or not the overlay panel is open. */\n    this._panelOpen = false;\n    /** Comparison function to specify which option is displayed. Defaults to object equality. */\n    this._compareWith = (o1, o2) => o1 === o2;\n    /** Unique id for this input. */\n    this._uid = `mat-select-${nextUniqueId++}`;\n    /** Current `aria-labelledby` value for the select trigger. */\n    this._triggerAriaLabelledBy = null;\n    /** Emits whenever the component is destroyed. */\n    this._destroy = new Subject();\n    /** `View -> model callback called when value changes` */\n    this._onChange = () => {};\n    /** `View -> model callback called when select has been touched` */\n    this._onTouched = () => {};\n    /** ID for the DOM node containing the select's value. */\n    this._valueId = `mat-select-value-${nextUniqueId++}`;\n    /** Emits when the panel element is finished transforming in. */\n    this._panelDoneAnimatingStream = new Subject();\n    this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n    this._focused = false;\n    /** A name for this control that can be used by `mat-form-field`. */\n    this.controlType = 'mat-select';\n    this._multiple = false;\n    this._disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n    /** Aria label of the select. */\n    this.ariaLabel = '';\n    /** Combined stream of all of the child options' change events. */\n    this.optionSelectionChanges = defer(() => {\n      const options = this.options;\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n      return this._ngZone.onStable.pipe(take(1), switchMap(() => this.optionSelectionChanges));\n    });\n    /** Event emitted when the select panel has been toggled. */\n    this.openedChange = new EventEmitter();\n    /** Event emitted when the select has been opened. */\n    this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the select has been closed. */\n    this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the selected value has been changed by the user. */\n    this.selectionChange = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the select changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    this.valueChange = new EventEmitter();\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    this._trackedModal = null;\n    if (this.ngControl) {\n      // Note: we provide the value accessor through here, instead of\n      // the `providers` to avoid running into a circular import.\n      this.ngControl.valueAccessor = this;\n    }\n    // Note that we only want to set this when the defaults pass it in, otherwise it should\n    // stay as `undefined` so that it falls back to the default in the key manager.\n    if (_defaultOptions?.typeaheadDebounceInterval != null) {\n      this._typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n    }\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this._scrollStrategy = this._scrollStrategyFactory();\n    this.tabIndex = parseInt(tabIndex) || 0;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple);\n    this.stateChanges.next();\n    // We need `distinctUntilChanged` here, because some browsers will\n    // fire the animation end event twice for the same animation. See:\n    // https://github.com/angular/angular/issues/24084\n    this._panelDoneAnimatingStream.pipe(distinctUntilChanged(), takeUntil(this._destroy)).subscribe(() => this._panelDoneAnimating(this.panelOpen));\n  }\n  ngAfterContentInit() {\n    this._initKeyManager();\n    this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n      event.added.forEach(option => option.select());\n      event.removed.forEach(option => option.deselect());\n    });\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n      this._resetOptions();\n      this._initializeSelection();\n    });\n  }\n  ngDoCheck() {\n    const newAriaLabelledby = this._getTriggerAriaLabelledby();\n    const ngControl = this.ngControl;\n    // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n    // is computed as a result of a content query which can cause this binding to trigger a\n    // \"changed after checked\" error.\n    if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n      const element = this._elementRef.nativeElement;\n      this._triggerAriaLabelledBy = newAriaLabelledby;\n      if (newAriaLabelledby) {\n        element.setAttribute('aria-labelledby', newAriaLabelledby);\n      } else {\n        element.removeAttribute('aria-labelledby');\n      }\n    }\n    if (ngControl) {\n      // The disabled state might go out of sync if the form group is swapped out. See #17860.\n      if (this._previousControl !== ngControl.control) {\n        if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n          this.disabled = ngControl.disabled;\n        }\n        this._previousControl = ngControl.control;\n      }\n      this.updateErrorState();\n    }\n  }\n  ngOnChanges(changes) {\n    // Updating the disabled state is handled by `mixinDisabled`, but we need to additionally let\n    // the parent form field know to run change detection when the disabled state changes.\n    if (changes['disabled'] || changes['userAriaDescribedBy']) {\n      this.stateChanges.next();\n    }\n    if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n      this._keyManager.withTypeAhead(this._typeaheadDebounceInterval);\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._destroy.next();\n    this._destroy.complete();\n    this.stateChanges.complete();\n    this._clearFromModal();\n  }\n  /** Toggles the overlay panel open or closed. */\n  toggle() {\n    this.panelOpen ? this.close() : this.open();\n  }\n  /** Opens the overlay panel. */\n  open() {\n    if (this._canOpen()) {\n      this._applyModalPanelOwnership();\n      this._panelOpen = true;\n      this._keyManager.withHorizontalOrientation(null);\n      this._highlightCorrectOption();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n  /** Clears the reference to the listbox overlay element from the modal it was added to. */\n  _clearFromModal() {\n    if (!this._trackedModal) {\n      // Most commonly, the autocomplete trigger is not used inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    this._trackedModal = null;\n  }\n  /** Closes the overlay panel and focuses the host element. */\n  close() {\n    if (this._panelOpen) {\n      this._panelOpen = false;\n      this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n      this._changeDetectorRef.markForCheck();\n      this._onTouched();\n    }\n  }\n  /**\n   * Sets the select's value. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param value New value to be written to the model.\n   */\n  writeValue(value) {\n    this._assignValue(value);\n  }\n  /**\n   * Saves a callback function to be invoked when the select's value\n   * changes from user input. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the value changes.\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Saves a callback function to be invoked when the select is blurred\n   * by the user. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the component has been touched.\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Disables the select. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param isDisabled Sets whether the component is disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n  /** Whether or not the overlay panel is open. */\n  get panelOpen() {\n    return this._panelOpen;\n  }\n  /** The currently selected option. */\n  get selected() {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n  /** The value displayed in the trigger. */\n  get triggerValue() {\n    if (this.empty) {\n      return '';\n    }\n    if (this._multiple) {\n      const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n      if (this._isRtl()) {\n        selectedOptions.reverse();\n      }\n      // TODO(crisbeto): delimiter should be configurable for proper localization.\n      return selectedOptions.join(', ');\n    }\n    return this._selectionModel.selected[0].viewValue;\n  }\n  /** Whether the element is in RTL mode. */\n  _isRtl() {\n    return this._dir ? this._dir.value === 'rtl' : false;\n  }\n  /** Handles all keydown events on the select. */\n  _handleKeydown(event) {\n    if (!this.disabled) {\n      this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n    }\n  }\n  /** Handles keyboard events while the select is closed. */\n  _handleClosedKeydown(event) {\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n    const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n    const manager = this._keyManager;\n    // Open the select on ALT + arrow key to match the native <select>\n    if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n      event.preventDefault(); // prevents the page from scrolling down when pressing space\n      this.open();\n    } else if (!this.multiple) {\n      const previouslySelectedOption = this.selected;\n      manager.onKeydown(event);\n      const selectedOption = this.selected;\n      // Since the value has changed, we need to announce it ourselves.\n      if (selectedOption && previouslySelectedOption !== selectedOption) {\n        // We set a duration on the live announcement, because we want the live element to be\n        // cleared after a while so that users can't navigate to it using the arrow keys.\n        this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n      }\n    }\n  }\n  /** Handles keyboard events when the selected is open. */\n  _handleOpenKeydown(event) {\n    const manager = this._keyManager;\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n    const isTyping = manager.isTyping();\n    if (isArrowKey && event.altKey) {\n      // Close the select on ALT + arrow key to match the native <select>\n      event.preventDefault();\n      this.close();\n      // Don't do anything in this case if the user is typing,\n      // because the typing sequence can include the space key.\n    } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n      event.preventDefault();\n      manager.activeItem._selectViaInteraction();\n    } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n      event.preventDefault();\n      const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n      this.options.forEach(option => {\n        if (!option.disabled) {\n          hasDeselectedOptions ? option.select() : option.deselect();\n        }\n      });\n    } else {\n      const previouslyFocusedIndex = manager.activeItemIndex;\n      manager.onKeydown(event);\n      if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n        manager.activeItem._selectViaInteraction();\n      }\n    }\n  }\n  _onFocus() {\n    if (!this.disabled) {\n      this._focused = true;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n   * \"blur\" to the panel when it opens, causing a false positive.\n   */\n  _onBlur() {\n    this._focused = false;\n    this._keyManager?.cancelTypeahead();\n    if (!this.disabled && !this.panelOpen) {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Callback that is invoked when the overlay panel has been attached.\n   */\n  _onAttached() {\n    this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n      this._changeDetectorRef.detectChanges();\n      this._positioningSettled();\n    });\n  }\n  /** Returns the theme to be used on the panel. */\n  _getPanelTheme() {\n    return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n  }\n  /** Whether the select has a value. */\n  get empty() {\n    return !this._selectionModel || this._selectionModel.isEmpty();\n  }\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl) {\n        this._value = this.ngControl.value;\n      }\n      this._setSelectionByValue(this._value);\n      this.stateChanges.next();\n    });\n  }\n  /**\n   * Sets the selected option based on a value. If no option can be\n   * found with the designated value, the select trigger is cleared.\n   */\n  _setSelectionByValue(value) {\n    this.options.forEach(option => option.setInactiveStyles());\n    this._selectionModel.clear();\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonArrayValueError();\n      }\n      value.forEach(currentValue => this._selectOptionByValue(currentValue));\n      this._sortValues();\n    } else {\n      const correspondingOption = this._selectOptionByValue(value);\n      // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what option the user interacted with last.\n      if (correspondingOption) {\n        this._keyManager.updateActiveItem(correspondingOption);\n      } else if (!this.panelOpen) {\n        // Otherwise reset the highlighted option. Note that we only want to do this while\n        // closed, because doing it while open can shift the user's focus unnecessarily.\n        this._keyManager.updateActiveItem(-1);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Finds and selects and option based on its value.\n   * @returns Option that has the corresponding value.\n   */\n  _selectOptionByValue(value) {\n    const correspondingOption = this.options.find(option => {\n      // Skip options that are already in the model. This allows us to handle cases\n      // where the same primitive value is selected multiple times.\n      if (this._selectionModel.isSelected(option)) {\n        return false;\n      }\n      try {\n        // Treat null as a special reset value.\n        return option.value != null && this._compareWith(option.value, value);\n      } catch (error) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          // Notify developers of errors in their comparator.\n          console.warn(error);\n        }\n        return false;\n      }\n    });\n    if (correspondingOption) {\n      this._selectionModel.select(correspondingOption);\n    }\n    return correspondingOption;\n  }\n  /** Assigns a specific value to the select. Returns whether the value has changed. */\n  _assignValue(newValue) {\n    // Always re-assign an array, because it might have been mutated.\n    if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n      if (this.options) {\n        this._setSelectionByValue(newValue);\n      }\n      this._value = newValue;\n      return true;\n    }\n    return false;\n  }\n  _skipPredicate(item) {\n    return item.disabled;\n  }\n  /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n  _initKeyManager() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this._typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(['shiftKey']).skipPredicate(this._skipPredicate);\n    this._keyManager.tabOut.subscribe(() => {\n      if (this.panelOpen) {\n        // Select the active item when tabbing away. This is consistent with how the native\n        // select behaves. Note that we only want to do this in single selection mode.\n        if (!this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n        // Restore focus to the trigger before closing. Ensures that the focus\n        // position won't be lost if the user got focus into the overlay.\n        this.focus();\n        this.close();\n      }\n    });\n    this._keyManager.change.subscribe(() => {\n      if (this._panelOpen && this.panel) {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n      } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n        this._keyManager.activeItem._selectViaInteraction();\n      }\n    });\n  }\n  /** Drops current option subscriptions and IDs and resets from scratch. */\n  _resetOptions() {\n    const changedOrDestroyed = merge(this.options.changes, this._destroy);\n    this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n      this._onSelect(event.source, event.isUserInput);\n      if (event.isUserInput && !this.multiple && this._panelOpen) {\n        this.close();\n        this.focus();\n      }\n    });\n    // Listen to changes in the internal state of the options and react accordingly.\n    // Handles cases like the labels of the selected options changing.\n    merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n      // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n      // be the result of an expression changing. We have to use `detectChanges` in order\n      // to avoid \"changed after checked\" errors (see #14793).\n      this._changeDetectorRef.detectChanges();\n      this.stateChanges.next();\n    });\n  }\n  /** Invoked when an option is clicked. */\n  _onSelect(option, isUserInput) {\n    const wasSelected = this._selectionModel.isSelected(option);\n    if (option.value == null && !this._multiple) {\n      option.deselect();\n      this._selectionModel.clear();\n      if (this.value != null) {\n        this._propagateChanges(option.value);\n      }\n    } else {\n      if (wasSelected !== option.selected) {\n        option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n      }\n      if (isUserInput) {\n        this._keyManager.setActiveItem(option);\n      }\n      if (this.multiple) {\n        this._sortValues();\n        if (isUserInput) {\n          // In case the user selected the option with their mouse, we\n          // want to restore focus back to the trigger, in order to\n          // prevent the select keyboard controls from clashing with\n          // the ones from `mat-option`.\n          this.focus();\n        }\n      }\n    }\n    if (wasSelected !== this._selectionModel.isSelected(option)) {\n      this._propagateChanges();\n    }\n    this.stateChanges.next();\n  }\n  /** Sorts the selected values in the selected based on their order in the panel. */\n  _sortValues() {\n    if (this.multiple) {\n      const options = this.options.toArray();\n      this._selectionModel.sort((a, b) => {\n        return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n      });\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges(fallbackValue) {\n    let valueToEmit = null;\n    if (this.multiple) {\n      valueToEmit = this.selected.map(option => option.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n    this._value = valueToEmit;\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Highlights the selected item. If no option is selected, it will highlight\n   * the first *enabled* option.\n   */\n  _highlightCorrectOption() {\n    if (this._keyManager) {\n      if (this.empty) {\n        // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n        // because it activates the first option that passes the skip predicate, rather than the\n        // first *enabled* option.\n        let firstEnabledOptionIndex = -1;\n        for (let index = 0; index < this.options.length; index++) {\n          const option = this.options.get(index);\n          if (!option.disabled) {\n            firstEnabledOptionIndex = index;\n            break;\n          }\n        }\n        this._keyManager.setActiveItem(firstEnabledOptionIndex);\n      } else {\n        this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n      }\n    }\n  }\n  /** Whether the panel is allowed to open. */\n  _canOpen() {\n    return !this._panelOpen && !this.disabled && this.options?.length > 0;\n  }\n  /** Focuses the select element. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Gets the aria-labelledby for the select panel. */\n  _getPanelAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId();\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Determines the `aria-activedescendant` to be set on the host. */\n  _getAriaActiveDescendant() {\n    if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n      return this._keyManager.activeItem.id;\n    }\n    return null;\n  }\n  /** Gets the aria-labelledby of the select component trigger. */\n  _getTriggerAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId();\n    let value = (labelId ? labelId + ' ' : '') + this._valueId;\n    if (this.ariaLabelledby) {\n      value += ' ' + this.ariaLabelledby;\n    }\n    return value;\n  }\n  /** Called when the overlay panel is done animating. */\n  _panelDoneAnimating(isOpen) {\n    this.openedChange.emit(isOpen);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    this.focus();\n    this.open();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    return this._panelOpen || !this.empty || this._focused && !!this._placeholder;\n  }\n  static {\n    this.ɵfac = function _MatSelectBase_Factory(t) {\n      return new (t || _MatSelectBase)(i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.ErrorStateMatcher), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i4.NgForm, 8), i0.ɵɵdirectiveInject(i4.FormGroupDirective, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8), i0.ɵɵdirectiveInject(i4.NgControl, 10), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_SELECT_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.LiveAnnouncer), i0.ɵɵdirectiveInject(MAT_SELECT_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatSelectBase,\n      viewQuery: function _MatSelectBase_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n        }\n      },\n      inputs: {\n        userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"],\n        panelClass: \"panelClass\",\n        placeholder: \"placeholder\",\n        required: \"required\",\n        multiple: \"multiple\",\n        disableOptionCentering: \"disableOptionCentering\",\n        compareWith: \"compareWith\",\n        value: \"value\",\n        ariaLabel: [\"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n        errorStateMatcher: \"errorStateMatcher\",\n        typeaheadDebounceInterval: \"typeaheadDebounceInterval\",\n        sortComparator: \"sortComparator\",\n        id: \"id\"\n      },\n      outputs: {\n        openedChange: \"openedChange\",\n        _openedStream: \"opened\",\n        _closedStream: \"closed\",\n        selectionChange: \"selectionChange\",\n        valueChange: \"valueChange\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSelectBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i1.ViewportRuler\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.ErrorStateMatcher\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i4.NgForm,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i4.FormGroupDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i6.MatFormField,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_FORM_FIELD]\n      }]\n    }, {\n      type: i4.NgControl,\n      decorators: [{\n        type: Self\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SELECT_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i5.LiveAnnouncer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_SELECT_CONFIG]\n      }]\n    }];\n  }, {\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    trigger: [{\n      type: ViewChild,\n      args: ['trigger']\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    _overlayDir: [{\n      type: ViewChild,\n      args: [CdkConnectedOverlay]\n    }],\n    panelClass: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }],\n    disableOptionCentering: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    typeaheadDebounceInterval: [{\n      type: Input\n    }],\n    sortComparator: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n  static {\n    this.ɵfac = function MatSelectTrigger_Factory(t) {\n      return new (t || MatSelectTrigger)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSelectTrigger,\n      selectors: [[\"mat-select-trigger\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectTrigger, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-select-trigger',\n      providers: [{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }]\n    }]\n  }], null, null);\n})();\nclass MatSelect extends _MatSelectBase {\n  constructor() {\n    super(...arguments);\n    /**\n     * Width of the panel. If set to `auto`, the panel will match the trigger width.\n     * If set to null or an empty string, the panel will grow to match the longest option's text.\n     */\n    this.panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined' ? this._defaultOptions.panelWidth : 'auto';\n    this._positions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }, {\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }];\n    this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    this._skipPredicate = option => {\n      if (this.panelOpen) {\n        // Support keyboard focusing disabled options in an ARIA listbox.\n        return false;\n      }\n      // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n      // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n      // closed.\n      return option.disabled;\n    };\n  }\n  get shouldLabelFloat() {\n    // Since the panel doesn't overlap the trigger, we\n    // want the label to only float when there's a value.\n    return this.panelOpen || !this.empty || this.focused && !!this.placeholder;\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this.panelOpen) {\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._changeDetectorRef.detectChanges();\n      }\n    });\n  }\n  open() {\n    // It's important that we read this as late as possible, because doing so earlier will\n    // return a different element since it's based on queries in the form field which may\n    // not have run yet. Also this needs to be assigned before we measure the overlay width.\n    if (this._parentFormField) {\n      this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n    }\n    this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n    super.open();\n    // Required for the MDC form field to pick up when the overlay has been opened.\n    this.stateChanges.next();\n  }\n  close() {\n    super.close();\n    // Required for the MDC form field to pick up when the overlay has been closed.\n    this.stateChanges.next();\n  }\n  /** Scrolls the active option into view. */\n  _scrollOptionIntoView(index) {\n    const option = this.options.toArray()[index];\n    if (option) {\n      const panel = this.panel.nativeElement;\n      const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n      const element = option._getHostElement();\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        panel.scrollTop = 0;\n      } else {\n        panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n      }\n    }\n  }\n  _positioningSettled() {\n    this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n  }\n  _getChangeEvent(value) {\n    return new MatSelectChange(this, value);\n  }\n  /** Gets how wide the overlay panel should be. */\n  _getOverlayWidth(preferredOrigin) {\n    if (this.panelWidth === 'auto') {\n      const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin ? preferredOrigin.elementRef : preferredOrigin || this._elementRef;\n      return refToMeasure.nativeElement.getBoundingClientRect().width;\n    }\n    return this.panelWidth === null ? '' : this.panelWidth;\n  }\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n    this._syncParentProperties();\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatSelect_BaseFactory;\n      return function MatSelect_Factory(t) {\n        return (ɵMatSelect_BaseFactory || (ɵMatSelect_BaseFactory = i0.ɵɵgetInheritedFactory(MatSelect)))(t || MatSelect);\n      };\n    }();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSelect,\n      selectors: [[\"mat-select\"]],\n      contentQueries: function MatSelect_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"combobox\", \"aria-autocomplete\", \"none\", \"aria-haspopup\", \"listbox\", \"ngSkipHydration\", \"\", 1, \"mat-mdc-select\"],\n      hostVars: 19,\n      hostBindings: function MatSelect_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatSelect_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"focus\", function MatSelect_focus_HostBindingHandler() {\n            return ctx._onFocus();\n          })(\"blur\", function MatSelect_blur_HostBindingHandler() {\n            return ctx._onBlur();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n          i0.ɵɵclassProp(\"mat-mdc-select-disabled\", ctx.disabled)(\"mat-mdc-select-invalid\", ctx.errorState)(\"mat-mdc-select-required\", ctx.required)(\"mat-mdc-select-empty\", ctx.empty)(\"mat-mdc-select-multiple\", ctx.multiple);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        disableRipple: \"disableRipple\",\n        tabIndex: \"tabIndex\",\n        panelWidth: \"panelWidth\",\n        hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\"\n      },\n      exportAs: [\"matSelect\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c3,\n      decls: 11,\n      vars: 10,\n      consts: [[\"cdk-overlay-origin\", \"\", 1, \"mat-mdc-select-trigger\", 3, \"click\"], [\"fallbackOverlayOrigin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [1, \"mat-mdc-select-value\", 3, \"ngSwitch\"], [\"class\", \"mat-mdc-select-placeholder mat-mdc-select-min-line\", 4, \"ngSwitchCase\"], [\"class\", \"mat-mdc-select-value-text\", 3, \"ngSwitch\", 4, \"ngSwitchCase\"], [1, \"mat-mdc-select-arrow-wrapper\"], [1, \"mat-mdc-select-arrow\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M7 10l5 5 5-5z\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayWidth\", \"backdropClick\", \"attach\", \"detach\"], [1, \"mat-mdc-select-placeholder\", \"mat-mdc-select-min-line\"], [1, \"mat-mdc-select-value-text\", 3, \"ngSwitch\"], [\"class\", \"mat-mdc-select-min-line\", 4, \"ngSwitchDefault\"], [4, \"ngSwitchCase\"], [1, \"mat-mdc-select-min-line\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"ngClass\", \"keydown\"], [\"panel\", \"\"]],\n      template: function MatSelect_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵelementStart(0, \"div\", 0, 1);\n          i0.ɵɵlistener(\"click\", function MatSelect_Template_div_click_0_listener() {\n            return ctx.toggle();\n          });\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵtemplate(4, MatSelect_span_4_Template, 2, 1, \"span\", 3);\n          i0.ɵɵtemplate(5, MatSelect_span_5_Template, 3, 2, \"span\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 7);\n          i0.ɵɵelement(9, \"path\", 8);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, MatSelect_ng_template_10_Template, 3, 9, \"ng-template\", 9);\n          i0.ɵɵlistener(\"backdropClick\", function MatSelect_Template_ng_template_backdropClick_10_listener() {\n            return ctx.close();\n          })(\"attach\", function MatSelect_Template_ng_template_attach_10_listener() {\n            return ctx._onAttached();\n          })(\"detach\", function MatSelect_Template_ng_template_detach_10_listener() {\n            return ctx.close();\n          });\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngSwitch\", ctx.empty);\n          i0.ɵɵattribute(\"id\", ctx._valueId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", ctx._preferredOverlayOrigin || _r0)(\"cdkConnectedOverlayOpen\", ctx.panelOpen)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayWidth\", ctx._overlayWidth);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgSwitch, i7.NgSwitchCase, i7.NgSwitchDefault, i8.CdkConnectedOverlay, i8.CdkOverlayOrigin],\n      styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:translateY(-8px)}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matSelectAnimations.transformPanel]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelect, [{\n    type: Component,\n    args: [{\n      selector: 'mat-select',\n      exportAs: 'matSelect',\n      inputs: ['disabled', 'disableRipple', 'tabIndex'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'role': 'combobox',\n        'aria-autocomplete': 'none',\n        'aria-haspopup': 'listbox',\n        'class': 'mat-mdc-select',\n        '[attr.id]': 'id',\n        '[attr.tabindex]': 'tabIndex',\n        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n        '[attr.aria-expanded]': 'panelOpen',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.aria-required]': 'required.toString()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n        'ngSkipHydration': '',\n        '[class.mat-mdc-select-disabled]': 'disabled',\n        '[class.mat-mdc-select-invalid]': 'errorState',\n        '[class.mat-mdc-select-required]': 'required',\n        '[class.mat-mdc-select-empty]': 'empty',\n        '[class.mat-mdc-select-multiple]': 'multiple',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': '_onFocus()',\n        '(blur)': '_onBlur()'\n      },\n      animations: [matSelectAnimations.transformPanel],\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }],\n      template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"toggle()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n  <div class=\\\"mat-mdc-select-value\\\" [ngSwitch]=\\\"empty\\\" [attr.id]=\\\"_valueId\\\">\\n    <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\" *ngSwitchCase=\\\"true\\\">{{placeholder}}</span>\\n    <span class=\\\"mat-mdc-select-value-text\\\" *ngSwitchCase=\\\"false\\\" [ngSwitch]=\\\"!!customTrigger\\\">\\n      <span class=\\\"mat-mdc-select-min-line\\\" *ngSwitchDefault>{{triggerValue}}</span>\\n      <ng-content select=\\\"mat-select-trigger\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n    </span>\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:translateY(-8px)}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\"]\n    }]\n  }], null, {\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    customTrigger: [{\n      type: ContentChild,\n      args: [MAT_SELECT_TRIGGER]\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input\n    }]\n  });\n})();\nclass MatSelectModule {\n  static {\n    this.ɵfac = function MatSelectModule_Factory(t) {\n      return new (t || MatSelectModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSelectModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n      imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule],\n      exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule],\n      declarations: [MatSelect, MatSelectTrigger],\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, _MatSelectBase, matSelectAnimations };", "map": {"version": 3, "names": ["i8", "Overlay", "CdkConnectedOverlay", "CdkOverlayOrigin", "OverlayModule", "i7", "CommonModule", "i0", "InjectionToken", "EventEmitter", "Directive", "Optional", "Inject", "Self", "Attribute", "Input", "ViewChild", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "ContentChild", "NgModule", "i2", "mixinDisableRipple", "mixinTabIndex", "mixinDisabled", "mixinErrorState", "_countGroupLabelsBeforeOption", "_getOptionScrollPosition", "MAT_OPTION_PARENT_COMPONENT", "MatOption", "MAT_OPTGROUP", "MatOptionModule", "MatCommonModule", "i6", "MAT_FORM_FIELD", "MatFormFieldControl", "MatFormFieldModule", "i1", "CdkScrollableModule", "i5", "removeAriaReferencedId", "addAriaReferencedId", "ActiveDescendantKeyManager", "i3", "coerceBooleanProperty", "coerceNumberProperty", "SelectionModel", "DOWN_ARROW", "UP_ARROW", "LEFT_ARROW", "RIGHT_ARROW", "ENTER", "SPACE", "hasModifierKey", "A", "i4", "Validators", "Subject", "defer", "merge", "startWith", "switchMap", "take", "filter", "map", "distinctUntilChanged", "takeUntil", "trigger", "transition", "query", "animate<PERSON><PERSON><PERSON>", "state", "style", "animate", "_c0", "_c1", "MatSelect_span_4_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r2", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "placeholder", "MatSelect_span_5_span_1_Template", "ctx_r5", "triggerValue", "MatSelect_span_5_ng_content_2_Template", "ɵɵprojection", "MatSelect_span_5_Template", "ɵɵtemplate", "ctx_r3", "ɵɵproperty", "customTrigger", "MatSelect_ng_template_10_Template", "_r9", "ɵɵgetCurrentView", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵlistener", "MatSelect_ng_template_10_Template_div_animation_transformPanel_done_0_listener", "$event", "ɵɵrestoreView", "ctx_r8", "ɵɵresetView", "_panelDoneAnimatingStream", "next", "toState", "MatSelect_ng_template_10_Template_div_keydown_0_listener", "ctx_r10", "_handleKeydown", "ctx_r4", "ɵɵclassMapInterpolate1", "_getPanelTheme", "panelClass", "ɵɵattribute", "id", "multiple", "aria<PERSON><PERSON><PERSON>", "_getPanelAriaLabe<PERSON>by", "_c2", "_c3", "matSelectAnimations", "transformPanelWrap", "optional", "transformPanel", "opacity", "transform", "getMatSelectDynamicMultipleError", "Error", "getMatSelectNonArrayValueError", "getMatSelectNonFunctionValueError", "nextUniqueId", "MAT_SELECT_SCROLL_STRATEGY", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY", "overlay", "scrollStrategies", "reposition", "MAT_SELECT_CONFIG", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "MAT_SELECT_TRIGGER", "MatSelectChange", "constructor", "source", "value", "_MatSelectMixinBase", "_elementRef", "_defaultErrorStateMatcher", "_parentForm", "_parentFormGroup", "ngControl", "stateChanges", "_MatSelectBase", "focused", "_focused", "_panelOpen", "_placeholder", "required", "_required", "control", "hasValidator", "_multiple", "_selectionModel", "ngDevMode", "disableOptionCentering", "_disableOptionCentering", "compareWith", "_compareWith", "fn", "_initializeSelection", "_value", "newValue", "hasAssigned", "_assignValue", "_onChange", "typeaheadDebounceInterval", "_typeaheadDebounceInterval", "_id", "_uid", "_viewportRuler", "_changeDetectorRef", "_ngZone", "elementRef", "_dir", "_parentFormField", "tabIndex", "scrollStrategyFactory", "_liveAnnouncer", "_defaultOptions", "o1", "o2", "_triggerAriaLabelledBy", "_destroy", "_onTouched", "_valueId", "_overlayPanelClass", "overlayPanelClass", "controlType", "optionSelectionChanges", "options", "changes", "pipe", "option", "onSelectionChange", "onStable", "openedChange", "_openedStream", "o", "_closedStream", "selectionChange", "valueChange", "_trackedModal", "valueAccessor", "_scrollStrategyFactory", "_scrollStrategy", "parseInt", "ngOnInit", "subscribe", "_panelDoneAnimating", "panelOpen", "ngAfterContentInit", "_initKeyManager", "changed", "event", "added", "for<PERSON>ach", "select", "removed", "deselect", "_resetOptions", "ngDoCheck", "newAria<PERSON><PERSON><PERSON><PERSON>", "_getTriggerAriaLabe<PERSON>by", "element", "nativeElement", "setAttribute", "removeAttribute", "_previousControl", "undefined", "disabled", "updateErrorState", "ngOnChanges", "_keyManager", "withTypeAhead", "ngOnDestroy", "destroy", "complete", "_clearFromModal", "toggle", "close", "open", "_canOpen", "_applyModalPanelOwnership", "withHorizontalOrientation", "_highlightCorrectOption", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modal", "closest", "panelId", "_isRtl", "writeValue", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "selected", "empty", "selectedOptions", "viewValue", "reverse", "join", "_handleOpenKeydown", "_handleClosedKeydown", "keyCode", "isArrowKey", "isOpenKey", "manager", "isTyping", "altKey", "preventDefault", "previouslySelectedOption", "onKeydown", "selectedOption", "announce", "activeItem", "_selectViaInteraction", "ctrl<PERSON>ey", "hasDeselectedOptions", "some", "opt", "previouslyFocusedIndex", "activeItemIndex", "shift<PERSON>ey", "_onFocus", "_onBlur", "cancelTypeahead", "_onAttached", "_overlayDir", "positionChange", "detectChanges", "_positioningSettled", "color", "isEmpty", "Promise", "resolve", "then", "_setSelectionByValue", "setInactiveStyles", "clear", "Array", "isArray", "currentValue", "_selectOptionByValue", "_sortValues", "correspondingOption", "updateActiveItem", "find", "isSelected", "error", "console", "warn", "_skipPredicate", "item", "withVerticalOrientation", "withHomeAndEnd", "withPageUpDown", "withAllowedModifierKeys", "skipPredicate", "tabOut", "focus", "change", "panel", "_scrollOptionIntoView", "changedOrDestroyed", "_onSelect", "isUserInput", "_stateChanges", "wasSelected", "_propagateChanges", "setActiveItem", "toArray", "sort", "a", "b", "sortComparator", "indexOf", "fallback<PERSON><PERSON><PERSON>", "valueToEmit", "emit", "_getChangeEvent", "firstEnabledOptionIndex", "index", "length", "get", "labelId", "getLabelId", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getAriaActiveDescendant", "isOpen", "setDescribedByIds", "ids", "onContainerClick", "shouldLabelFloat", "ɵfac", "_MatSelectBase_Factory", "t", "ɵɵdirectiveInject", "ViewportRuler", "ChangeDetectorRef", "NgZone", "ErrorStateMatcher", "ElementRef", "Directionality", "NgForm", "FormGroupDirective", "NgControl", "ɵɵinjectAttribute", "LiveAnnouncer", "ɵdir", "ɵɵdefineDirective", "type", "viewQuery", "_MatSelectBase_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "userAriaDescribedBy", "errorStateMatcher", "outputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "decorators", "MatFormField", "args", "MatSelectTrigger", "MatSelectTrigger_Factory", "selectors", "ɵɵProvidersFeature", "useExisting", "selector", "providers", "MatSelect", "arguments", "panelWidth", "_positions", "originX", "originY", "overlayX", "overlayY", "_hideSingleSelectionIndicator", "hideSingleSelectionIndicator", "_overlayWidth", "_getOverlayWidth", "_preferredOverlayOrigin", "getConnectedOverlayOrigin", "labelCount", "optionGroups", "_getHostElement", "scrollTop", "offsetTop", "offsetHeight", "preferred<PERSON><PERSON>in", "refToMeasure", "getBoundingClientRect", "width", "_syncParentProperties", "ɵMatSelect_BaseFactory", "MatSelect_Factory", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatSelect_ContentQueries", "dirIndex", "ɵɵcontentQuery", "hostAttrs", "hostVars", "hostBindings", "MatSelect_HostBindings", "MatSelect_keydown_HostBindingHandler", "MatSelect_focus_HostBindingHandler", "MatSelect_blur_HostBindingHandler", "toString", "errorState", "ɵɵclassProp", "disable<PERSON><PERSON><PERSON>", "exportAs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatSelect_Template", "ɵɵprojectionDef", "MatSelect_Template_div_click_0_listener", "ɵɵelement", "MatSelect_Template_ng_template_backdropClick_10_listener", "MatSelect_Template_ng_template_attach_10_listener", "MatSelect_Template_ng_template_detach_10_listener", "_r0", "ɵɵreference", "dependencies", "Ng<PERSON><PERSON>", "NgSwitch", "NgSwitchCase", "NgSwitchDefault", "styles", "encapsulation", "data", "animation", "changeDetection", "None", "OnPush", "host", "animations", "descendants", "MatSelectModule", "MatSelectModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/@angular+material@16.2.14_4056c724f738b156ccd72c3e8383c8cb/node_modules/@angular/material/fesm2022/select.mjs"], "sourcesContent": ["import * as i8 from '@angular/cdk/overlay';\nimport { Overlay, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i7 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Optional, Inject, Self, Attribute, Input, ViewChild, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, mixinDisabled, mixinErrorState, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, take, filter, map, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst matSelectAnimations = {\n    /**\n     * This animation ensures the select's overlay panel animation (transformPanel) is called when\n     * closing the select.\n     * This is needed due to https://github.com/angular/angular/issues/23302\n     */\n    transformPanelWrap: trigger('transformPanelWrap', [\n        transition('* => void', query('@transformPanel', [animateChild()], { optional: true })),\n    ]),\n    /** This animation transforms the select's overlay panel on and off the page. */\n    transformPanel: trigger('transformPanel', [\n        state('void', style({\n            opacity: 0,\n            transform: 'scale(1, 0.8)',\n        })),\n        transition('void => showing', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n            opacity: 1,\n            transform: 'scale(1, 1)',\n        }))),\n        transition('* => void', animate('100ms linear', style({ opacity: 0 }))),\n    ]),\n};\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n    return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n    return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n    return Error('`compareWith` must be a function.');\n}\n\nlet nextUniqueId = 0;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy');\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_SELECT_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n    constructor(\n    /** Reference to the select that emitted the change event. */\n    source, \n    /** Current value of the select that emitted the event. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n// Boilerplate for applying mixins to MatSelect.\n/** @docs-private */\nconst _MatSelectMixinBase = mixinDisableRipple(mixinTabIndex(mixinDisabled(mixinErrorState(class {\n    constructor(_elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, \n    /**\n     * Form control bound to the component.\n     * Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    ngControl) {\n        this._elementRef = _elementRef;\n        this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n        this._parentForm = _parentForm;\n        this._parentFormGroup = _parentFormGroup;\n        this.ngControl = ngControl;\n        /**\n         * Emits whenever the component state changes and should cause the parent\n         * form-field to update. Implemented as part of `MatFormFieldControl`.\n         * @docs-private\n         */\n        this.stateChanges = new Subject();\n    }\n}))));\n/** Base class with all of the `MatSelect` functionality. */\nclass _MatSelectBase extends _MatSelectMixinBase {\n    /** Whether the select is focused. */\n    get focused() {\n        return this._focused || this._panelOpen;\n    }\n    /** Placeholder to be shown if no value has been selected. */\n    get placeholder() {\n        return this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    /** Whether the component is required. */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n        this.stateChanges.next();\n    }\n    /** Whether the user should be allowed to select multiple options. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectDynamicMultipleError();\n        }\n        this._multiple = coerceBooleanProperty(value);\n    }\n    /** Whether to center the active option over the trigger. */\n    get disableOptionCentering() {\n        return this._disableOptionCentering;\n    }\n    set disableOptionCentering(value) {\n        this._disableOptionCentering = coerceBooleanProperty(value);\n    }\n    /**\n     * Function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n        return this._compareWith;\n    }\n    set compareWith(fn) {\n        if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectNonFunctionValueError();\n        }\n        this._compareWith = fn;\n        if (this._selectionModel) {\n            // A different comparator means the selection could change.\n            this._initializeSelection();\n        }\n    }\n    /** Value of the select control. */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        const hasAssigned = this._assignValue(newValue);\n        if (hasAssigned) {\n            this._onChange(newValue);\n        }\n    }\n    /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n    get typeaheadDebounceInterval() {\n        return this._typeaheadDebounceInterval;\n    }\n    set typeaheadDebounceInterval(value) {\n        this._typeaheadDebounceInterval = coerceNumberProperty(value);\n    }\n    /** Unique id of the element. */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n        this.stateChanges.next();\n    }\n    constructor(_viewportRuler, _changeDetectorRef, _ngZone, _defaultErrorStateMatcher, elementRef, _dir, _parentForm, _parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n        super(elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n        this._viewportRuler = _viewportRuler;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._ngZone = _ngZone;\n        this._dir = _dir;\n        this._parentFormField = _parentFormField;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._defaultOptions = _defaultOptions;\n        /** Whether or not the overlay panel is open. */\n        this._panelOpen = false;\n        /** Comparison function to specify which option is displayed. Defaults to object equality. */\n        this._compareWith = (o1, o2) => o1 === o2;\n        /** Unique id for this input. */\n        this._uid = `mat-select-${nextUniqueId++}`;\n        /** Current `aria-labelledby` value for the select trigger. */\n        this._triggerAriaLabelledBy = null;\n        /** Emits whenever the component is destroyed. */\n        this._destroy = new Subject();\n        /** `View -> model callback called when value changes` */\n        this._onChange = () => { };\n        /** `View -> model callback called when select has been touched` */\n        this._onTouched = () => { };\n        /** ID for the DOM node containing the select's value. */\n        this._valueId = `mat-select-value-${nextUniqueId++}`;\n        /** Emits when the panel element is finished transforming in. */\n        this._panelDoneAnimatingStream = new Subject();\n        this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n        this._focused = false;\n        /** A name for this control that can be used by `mat-form-field`. */\n        this.controlType = 'mat-select';\n        this._multiple = false;\n        this._disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n        /** Aria label of the select. */\n        this.ariaLabel = '';\n        /** Combined stream of all of the child options' change events. */\n        this.optionSelectionChanges = defer(() => {\n            const options = this.options;\n            if (options) {\n                return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n            }\n            return this._ngZone.onStable.pipe(take(1), switchMap(() => this.optionSelectionChanges));\n        });\n        /** Event emitted when the select panel has been toggled. */\n        this.openedChange = new EventEmitter();\n        /** Event emitted when the select has been opened. */\n        this._openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n        /** Event emitted when the select has been closed. */\n        this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n        /** Event emitted when the selected value has been changed by the user. */\n        this.selectionChange = new EventEmitter();\n        /**\n         * Event that emits whenever the raw value of the select changes. This is here primarily\n         * to facilitate the two-way binding for the `value` input.\n         * @docs-private\n         */\n        this.valueChange = new EventEmitter();\n        /**\n         * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n         * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n         * panel. Track the modal we have changed so we can undo the changes on destroy.\n         */\n        this._trackedModal = null;\n        if (this.ngControl) {\n            // Note: we provide the value accessor through here, instead of\n            // the `providers` to avoid running into a circular import.\n            this.ngControl.valueAccessor = this;\n        }\n        // Note that we only want to set this when the defaults pass it in, otherwise it should\n        // stay as `undefined` so that it falls back to the default in the key manager.\n        if (_defaultOptions?.typeaheadDebounceInterval != null) {\n            this._typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n        }\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this._scrollStrategy = this._scrollStrategyFactory();\n        this.tabIndex = parseInt(tabIndex) || 0;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple);\n        this.stateChanges.next();\n        // We need `distinctUntilChanged` here, because some browsers will\n        // fire the animation end event twice for the same animation. See:\n        // https://github.com/angular/angular/issues/24084\n        this._panelDoneAnimatingStream\n            .pipe(distinctUntilChanged(), takeUntil(this._destroy))\n            .subscribe(() => this._panelDoneAnimating(this.panelOpen));\n    }\n    ngAfterContentInit() {\n        this._initKeyManager();\n        this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n            event.added.forEach(option => option.select());\n            event.removed.forEach(option => option.deselect());\n        });\n        this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n            this._resetOptions();\n            this._initializeSelection();\n        });\n    }\n    ngDoCheck() {\n        const newAriaLabelledby = this._getTriggerAriaLabelledby();\n        const ngControl = this.ngControl;\n        // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n        // is computed as a result of a content query which can cause this binding to trigger a\n        // \"changed after checked\" error.\n        if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n            const element = this._elementRef.nativeElement;\n            this._triggerAriaLabelledBy = newAriaLabelledby;\n            if (newAriaLabelledby) {\n                element.setAttribute('aria-labelledby', newAriaLabelledby);\n            }\n            else {\n                element.removeAttribute('aria-labelledby');\n            }\n        }\n        if (ngControl) {\n            // The disabled state might go out of sync if the form group is swapped out. See #17860.\n            if (this._previousControl !== ngControl.control) {\n                if (this._previousControl !== undefined &&\n                    ngControl.disabled !== null &&\n                    ngControl.disabled !== this.disabled) {\n                    this.disabled = ngControl.disabled;\n                }\n                this._previousControl = ngControl.control;\n            }\n            this.updateErrorState();\n        }\n    }\n    ngOnChanges(changes) {\n        // Updating the disabled state is handled by `mixinDisabled`, but we need to additionally let\n        // the parent form field know to run change detection when the disabled state changes.\n        if (changes['disabled'] || changes['userAriaDescribedBy']) {\n            this.stateChanges.next();\n        }\n        if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n            this._keyManager.withTypeAhead(this._typeaheadDebounceInterval);\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._destroy.next();\n        this._destroy.complete();\n        this.stateChanges.complete();\n        this._clearFromModal();\n    }\n    /** Toggles the overlay panel open or closed. */\n    toggle() {\n        this.panelOpen ? this.close() : this.open();\n    }\n    /** Opens the overlay panel. */\n    open() {\n        if (this._canOpen()) {\n            this._applyModalPanelOwnership();\n            this._panelOpen = true;\n            this._keyManager.withHorizontalOrientation(null);\n            this._highlightCorrectOption();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        if (!modal) {\n            // Most commonly, the autocomplete trigger is not inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n        addAriaReferencedId(modal, 'aria-owns', panelId);\n        this._trackedModal = modal;\n    }\n    /** Clears the reference to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n        if (!this._trackedModal) {\n            // Most commonly, the autocomplete trigger is not used inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        this._trackedModal = null;\n    }\n    /** Closes the overlay panel and focuses the host element. */\n    close() {\n        if (this._panelOpen) {\n            this._panelOpen = false;\n            this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n            this._changeDetectorRef.markForCheck();\n            this._onTouched();\n        }\n    }\n    /**\n     * Sets the select's value. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param value New value to be written to the model.\n     */\n    writeValue(value) {\n        this._assignValue(value);\n    }\n    /**\n     * Saves a callback function to be invoked when the select's value\n     * changes from user input. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the value changes.\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Saves a callback function to be invoked when the select is blurred\n     * by the user. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the component has been touched.\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Disables the select. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param isDisabled Sets whether the component is disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    /** Whether or not the overlay panel is open. */\n    get panelOpen() {\n        return this._panelOpen;\n    }\n    /** The currently selected option. */\n    get selected() {\n        return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The value displayed in the trigger. */\n    get triggerValue() {\n        if (this.empty) {\n            return '';\n        }\n        if (this._multiple) {\n            const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n            if (this._isRtl()) {\n                selectedOptions.reverse();\n            }\n            // TODO(crisbeto): delimiter should be configurable for proper localization.\n            return selectedOptions.join(', ');\n        }\n        return this._selectionModel.selected[0].viewValue;\n    }\n    /** Whether the element is in RTL mode. */\n    _isRtl() {\n        return this._dir ? this._dir.value === 'rtl' : false;\n    }\n    /** Handles all keydown events on the select. */\n    _handleKeydown(event) {\n        if (!this.disabled) {\n            this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n        }\n    }\n    /** Handles keyboard events while the select is closed. */\n    _handleClosedKeydown(event) {\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW ||\n            keyCode === UP_ARROW ||\n            keyCode === LEFT_ARROW ||\n            keyCode === RIGHT_ARROW;\n        const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n        const manager = this._keyManager;\n        // Open the select on ALT + arrow key to match the native <select>\n        if ((!manager.isTyping() && isOpenKey && !hasModifierKey(event)) ||\n            ((this.multiple || event.altKey) && isArrowKey)) {\n            event.preventDefault(); // prevents the page from scrolling down when pressing space\n            this.open();\n        }\n        else if (!this.multiple) {\n            const previouslySelectedOption = this.selected;\n            manager.onKeydown(event);\n            const selectedOption = this.selected;\n            // Since the value has changed, we need to announce it ourselves.\n            if (selectedOption && previouslySelectedOption !== selectedOption) {\n                // We set a duration on the live announcement, because we want the live element to be\n                // cleared after a while so that users can't navigate to it using the arrow keys.\n                this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n            }\n        }\n    }\n    /** Handles keyboard events when the selected is open. */\n    _handleOpenKeydown(event) {\n        const manager = this._keyManager;\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n        const isTyping = manager.isTyping();\n        if (isArrowKey && event.altKey) {\n            // Close the select on ALT + arrow key to match the native <select>\n            event.preventDefault();\n            this.close();\n            // Don't do anything in this case if the user is typing,\n            // because the typing sequence can include the space key.\n        }\n        else if (!isTyping &&\n            (keyCode === ENTER || keyCode === SPACE) &&\n            manager.activeItem &&\n            !hasModifierKey(event)) {\n            event.preventDefault();\n            manager.activeItem._selectViaInteraction();\n        }\n        else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n            event.preventDefault();\n            const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n            this.options.forEach(option => {\n                if (!option.disabled) {\n                    hasDeselectedOptions ? option.select() : option.deselect();\n                }\n            });\n        }\n        else {\n            const previouslyFocusedIndex = manager.activeItemIndex;\n            manager.onKeydown(event);\n            if (this._multiple &&\n                isArrowKey &&\n                event.shiftKey &&\n                manager.activeItem &&\n                manager.activeItemIndex !== previouslyFocusedIndex) {\n                manager.activeItem._selectViaInteraction();\n            }\n        }\n    }\n    _onFocus() {\n        if (!this.disabled) {\n            this._focused = true;\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n     * \"blur\" to the panel when it opens, causing a false positive.\n     */\n    _onBlur() {\n        this._focused = false;\n        this._keyManager?.cancelTypeahead();\n        if (!this.disabled && !this.panelOpen) {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Callback that is invoked when the overlay panel has been attached.\n     */\n    _onAttached() {\n        this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n            this._changeDetectorRef.detectChanges();\n            this._positioningSettled();\n        });\n    }\n    /** Returns the theme to be used on the panel. */\n    _getPanelTheme() {\n        return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n    }\n    /** Whether the select has a value. */\n    get empty() {\n        return !this._selectionModel || this._selectionModel.isEmpty();\n    }\n    _initializeSelection() {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n            if (this.ngControl) {\n                this._value = this.ngControl.value;\n            }\n            this._setSelectionByValue(this._value);\n            this.stateChanges.next();\n        });\n    }\n    /**\n     * Sets the selected option based on a value. If no option can be\n     * found with the designated value, the select trigger is cleared.\n     */\n    _setSelectionByValue(value) {\n        this.options.forEach(option => option.setInactiveStyles());\n        this._selectionModel.clear();\n        if (this.multiple && value) {\n            if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getMatSelectNonArrayValueError();\n            }\n            value.forEach((currentValue) => this._selectOptionByValue(currentValue));\n            this._sortValues();\n        }\n        else {\n            const correspondingOption = this._selectOptionByValue(value);\n            // Shift focus to the active item. Note that we shouldn't do this in multiple\n            // mode, because we don't know what option the user interacted with last.\n            if (correspondingOption) {\n                this._keyManager.updateActiveItem(correspondingOption);\n            }\n            else if (!this.panelOpen) {\n                // Otherwise reset the highlighted option. Note that we only want to do this while\n                // closed, because doing it while open can shift the user's focus unnecessarily.\n                this._keyManager.updateActiveItem(-1);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Finds and selects and option based on its value.\n     * @returns Option that has the corresponding value.\n     */\n    _selectOptionByValue(value) {\n        const correspondingOption = this.options.find((option) => {\n            // Skip options that are already in the model. This allows us to handle cases\n            // where the same primitive value is selected multiple times.\n            if (this._selectionModel.isSelected(option)) {\n                return false;\n            }\n            try {\n                // Treat null as a special reset value.\n                return option.value != null && this._compareWith(option.value, value);\n            }\n            catch (error) {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    // Notify developers of errors in their comparator.\n                    console.warn(error);\n                }\n                return false;\n            }\n        });\n        if (correspondingOption) {\n            this._selectionModel.select(correspondingOption);\n        }\n        return correspondingOption;\n    }\n    /** Assigns a specific value to the select. Returns whether the value has changed. */\n    _assignValue(newValue) {\n        // Always re-assign an array, because it might have been mutated.\n        if (newValue !== this._value || (this._multiple && Array.isArray(newValue))) {\n            if (this.options) {\n                this._setSelectionByValue(newValue);\n            }\n            this._value = newValue;\n            return true;\n        }\n        return false;\n    }\n    _skipPredicate(item) {\n        return item.disabled;\n    }\n    /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n    _initKeyManager() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withTypeAhead(this._typeaheadDebounceInterval)\n            .withVerticalOrientation()\n            .withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr')\n            .withHomeAndEnd()\n            .withPageUpDown()\n            .withAllowedModifierKeys(['shiftKey'])\n            .skipPredicate(this._skipPredicate);\n        this._keyManager.tabOut.subscribe(() => {\n            if (this.panelOpen) {\n                // Select the active item when tabbing away. This is consistent with how the native\n                // select behaves. Note that we only want to do this in single selection mode.\n                if (!this.multiple && this._keyManager.activeItem) {\n                    this._keyManager.activeItem._selectViaInteraction();\n                }\n                // Restore focus to the trigger before closing. Ensures that the focus\n                // position won't be lost if the user got focus into the overlay.\n                this.focus();\n                this.close();\n            }\n        });\n        this._keyManager.change.subscribe(() => {\n            if (this._panelOpen && this.panel) {\n                this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n            }\n            else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n                this._keyManager.activeItem._selectViaInteraction();\n            }\n        });\n    }\n    /** Drops current option subscriptions and IDs and resets from scratch. */\n    _resetOptions() {\n        const changedOrDestroyed = merge(this.options.changes, this._destroy);\n        this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n            this._onSelect(event.source, event.isUserInput);\n            if (event.isUserInput && !this.multiple && this._panelOpen) {\n                this.close();\n                this.focus();\n            }\n        });\n        // Listen to changes in the internal state of the options and react accordingly.\n        // Handles cases like the labels of the selected options changing.\n        merge(...this.options.map(option => option._stateChanges))\n            .pipe(takeUntil(changedOrDestroyed))\n            .subscribe(() => {\n            // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n            // be the result of an expression changing. We have to use `detectChanges` in order\n            // to avoid \"changed after checked\" errors (see #14793).\n            this._changeDetectorRef.detectChanges();\n            this.stateChanges.next();\n        });\n    }\n    /** Invoked when an option is clicked. */\n    _onSelect(option, isUserInput) {\n        const wasSelected = this._selectionModel.isSelected(option);\n        if (option.value == null && !this._multiple) {\n            option.deselect();\n            this._selectionModel.clear();\n            if (this.value != null) {\n                this._propagateChanges(option.value);\n            }\n        }\n        else {\n            if (wasSelected !== option.selected) {\n                option.selected\n                    ? this._selectionModel.select(option)\n                    : this._selectionModel.deselect(option);\n            }\n            if (isUserInput) {\n                this._keyManager.setActiveItem(option);\n            }\n            if (this.multiple) {\n                this._sortValues();\n                if (isUserInput) {\n                    // In case the user selected the option with their mouse, we\n                    // want to restore focus back to the trigger, in order to\n                    // prevent the select keyboard controls from clashing with\n                    // the ones from `mat-option`.\n                    this.focus();\n                }\n            }\n        }\n        if (wasSelected !== this._selectionModel.isSelected(option)) {\n            this._propagateChanges();\n        }\n        this.stateChanges.next();\n    }\n    /** Sorts the selected values in the selected based on their order in the panel. */\n    _sortValues() {\n        if (this.multiple) {\n            const options = this.options.toArray();\n            this._selectionModel.sort((a, b) => {\n                return this.sortComparator\n                    ? this.sortComparator(a, b, options)\n                    : options.indexOf(a) - options.indexOf(b);\n            });\n            this.stateChanges.next();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n        let valueToEmit = null;\n        if (this.multiple) {\n            valueToEmit = this.selected.map(option => option.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : fallbackValue;\n        }\n        this._value = valueToEmit;\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Highlights the selected item. If no option is selected, it will highlight\n     * the first *enabled* option.\n     */\n    _highlightCorrectOption() {\n        if (this._keyManager) {\n            if (this.empty) {\n                // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n                // because it activates the first option that passes the skip predicate, rather than the\n                // first *enabled* option.\n                let firstEnabledOptionIndex = -1;\n                for (let index = 0; index < this.options.length; index++) {\n                    const option = this.options.get(index);\n                    if (!option.disabled) {\n                        firstEnabledOptionIndex = index;\n                        break;\n                    }\n                }\n                this._keyManager.setActiveItem(firstEnabledOptionIndex);\n            }\n            else {\n                this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n            }\n        }\n    }\n    /** Whether the panel is allowed to open. */\n    _canOpen() {\n        return !this._panelOpen && !this.disabled && this.options?.length > 0;\n    }\n    /** Focuses the select element. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Gets the aria-labelledby for the select panel. */\n    _getPanelAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Determines the `aria-activedescendant` to be set on the host. */\n    _getAriaActiveDescendant() {\n        if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n            return this._keyManager.activeItem.id;\n        }\n        return null;\n    }\n    /** Gets the aria-labelledby of the select component trigger. */\n    _getTriggerAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        let value = (labelId ? labelId + ' ' : '') + this._valueId;\n        if (this.ariaLabelledby) {\n            value += ' ' + this.ariaLabelledby;\n        }\n        return value;\n    }\n    /** Called when the overlay panel is done animating. */\n    _panelDoneAnimating(isOpen) {\n        this.openedChange.emit(isOpen);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        if (ids.length) {\n            this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            this._elementRef.nativeElement.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        this.focus();\n        this.open();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        return this._panelOpen || !this.empty || (this._focused && !!this._placeholder);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatSelectBase, deps: [{ token: i1.ViewportRuler }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i2.ErrorStateMatcher }, { token: i0.ElementRef }, { token: i3.Directionality, optional: true }, { token: i4.NgForm, optional: true }, { token: i4.FormGroupDirective, optional: true }, { token: MAT_FORM_FIELD, optional: true }, { token: i4.NgControl, optional: true, self: true }, { token: 'tabindex', attribute: true }, { token: MAT_SELECT_SCROLL_STRATEGY }, { token: i5.LiveAnnouncer }, { token: MAT_SELECT_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatSelectBase, inputs: { userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], panelClass: \"panelClass\", placeholder: \"placeholder\", required: \"required\", multiple: \"multiple\", disableOptionCentering: \"disableOptionCentering\", compareWith: \"compareWith\", value: \"value\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], errorStateMatcher: \"errorStateMatcher\", typeaheadDebounceInterval: \"typeaheadDebounceInterval\", sortComparator: \"sortComparator\", id: \"id\" }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", _closedStream: \"closed\", selectionChange: \"selectionChange\", valueChange: \"valueChange\" }, viewQueries: [{ propertyName: \"trigger\", first: true, predicate: [\"trigger\"], descendants: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }, { propertyName: \"_overlayDir\", first: true, predicate: CdkConnectedOverlay, descendants: true }], usesInheritance: true, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatSelectBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i1.ViewportRuler }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i2.ErrorStateMatcher }, { type: i0.ElementRef }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.NgForm, decorators: [{\n                    type: Optional\n                }] }, { type: i4.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i6.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }] }, { type: i4.NgControl, decorators: [{\n                    type: Self\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SELECT_SCROLL_STRATEGY]\n                }] }, { type: i5.LiveAnnouncer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SELECT_CONFIG]\n                }] }]; }, propDecorators: { userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], trigger: [{\n                type: ViewChild,\n                args: ['trigger']\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], _overlayDir: [{\n                type: ViewChild,\n                args: [CdkConnectedOverlay]\n            }], panelClass: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], multiple: [{\n                type: Input\n            }], disableOptionCentering: [{\n                type: Input\n            }], compareWith: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], errorStateMatcher: [{\n                type: Input\n            }], typeaheadDebounceInterval: [{\n                type: Input\n            }], sortComparator: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], selectionChange: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }] } });\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSelectTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSelectTrigger, selector: \"mat-select-trigger\", providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSelectTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-select-trigger',\n                    providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }],\n                }]\n        }] });\nclass MatSelect extends _MatSelectBase {\n    constructor() {\n        super(...arguments);\n        /**\n         * Width of the panel. If set to `auto`, the panel will match the trigger width.\n         * If set to null or an empty string, the panel will grow to match the longest option's text.\n         */\n        this.panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined'\n            ? this._defaultOptions.panelWidth\n            : 'auto';\n        this._positions = [\n            {\n                originX: 'start',\n                originY: 'bottom',\n                overlayX: 'start',\n                overlayY: 'top',\n            },\n            {\n                originX: 'end',\n                originY: 'bottom',\n                overlayX: 'end',\n                overlayY: 'top',\n            },\n            {\n                originX: 'start',\n                originY: 'top',\n                overlayX: 'start',\n                overlayY: 'bottom',\n                panelClass: 'mat-mdc-select-panel-above',\n            },\n            {\n                originX: 'end',\n                originY: 'top',\n                overlayX: 'end',\n                overlayY: 'bottom',\n                panelClass: 'mat-mdc-select-panel-above',\n            },\n        ];\n        this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n        // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n        // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n        // recommendation.\n        //\n        // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n        // makes a few exceptions for compound widgets.\n        //\n        // From [Developing a Keyboard Interface](\n        // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n        //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n        //   Listbox...\"\n        //\n        // The user can focus disabled options using the keyboard, but the user cannot click disabled\n        // options.\n        this._skipPredicate = (option) => {\n            if (this.panelOpen) {\n                // Support keyboard focusing disabled options in an ARIA listbox.\n                return false;\n            }\n            // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n            // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n            // closed.\n            return option.disabled;\n        };\n    }\n    get shouldLabelFloat() {\n        // Since the panel doesn't overlap the trigger, we\n        // want the label to only float when there's a value.\n        return this.panelOpen || !this.empty || (this.focused && !!this.placeholder);\n    }\n    ngOnInit() {\n        super.ngOnInit();\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroy))\n            .subscribe(() => {\n            if (this.panelOpen) {\n                this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n                this._changeDetectorRef.detectChanges();\n            }\n        });\n    }\n    open() {\n        // It's important that we read this as late as possible, because doing so earlier will\n        // return a different element since it's based on queries in the form field which may\n        // not have run yet. Also this needs to be assigned before we measure the overlay width.\n        if (this._parentFormField) {\n            this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n        }\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        super.open();\n        // Required for the MDC form field to pick up when the overlay has been opened.\n        this.stateChanges.next();\n    }\n    close() {\n        super.close();\n        // Required for the MDC form field to pick up when the overlay has been closed.\n        this.stateChanges.next();\n    }\n    /** Scrolls the active option into view. */\n    _scrollOptionIntoView(index) {\n        const option = this.options.toArray()[index];\n        if (option) {\n            const panel = this.panel.nativeElement;\n            const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n            const element = option._getHostElement();\n            if (index === 0 && labelCount === 1) {\n                // If we've got one group label before the option and we're at the top option,\n                // scroll the list to the top. This is better UX than scrolling the list to the\n                // top of the option, because it allows the user to read the top group's label.\n                panel.scrollTop = 0;\n            }\n            else {\n                panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n            }\n        }\n    }\n    _positioningSettled() {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n    }\n    _getChangeEvent(value) {\n        return new MatSelectChange(this, value);\n    }\n    /** Gets how wide the overlay panel should be. */\n    _getOverlayWidth(preferredOrigin) {\n        if (this.panelWidth === 'auto') {\n            const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin\n                ? preferredOrigin.elementRef\n                : preferredOrigin || this._elementRef;\n            return refToMeasure.nativeElement.getBoundingClientRect().width;\n        }\n        return this.panelWidth === null ? '' : this.panelWidth;\n    }\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n        this._syncParentProperties();\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n        if (this.options) {\n            for (const option of this.options) {\n                option._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSelect, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSelect, selector: \"mat-select\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", tabIndex: \"tabIndex\", panelWidth: \"panelWidth\", hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\" }, host: { attributes: { \"role\": \"combobox\", \"aria-autocomplete\": \"none\", \"aria-haspopup\": \"listbox\", \"ngSkipHydration\": \"\" }, listeners: { \"keydown\": \"_handleKeydown($event)\", \"focus\": \"_onFocus()\", \"blur\": \"_onBlur()\" }, properties: { \"attr.id\": \"id\", \"attr.tabindex\": \"tabIndex\", \"attr.aria-controls\": \"panelOpen ? id + \\\"-panel\\\" : null\", \"attr.aria-expanded\": \"panelOpen\", \"attr.aria-label\": \"ariaLabel || null\", \"attr.aria-required\": \"required.toString()\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"attr.aria-activedescendant\": \"_getAriaActiveDescendant()\", \"class.mat-mdc-select-disabled\": \"disabled\", \"class.mat-mdc-select-invalid\": \"errorState\", \"class.mat-mdc-select-required\": \"required\", \"class.mat-mdc-select-empty\": \"empty\", \"class.mat-mdc-select-multiple\": \"multiple\" }, classAttribute: \"mat-mdc-select\" }, providers: [\n            { provide: MatFormFieldControl, useExisting: MatSelect },\n            { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n        ], queries: [{ propertyName: \"customTrigger\", first: true, predicate: MAT_SELECT_TRIGGER, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }, { propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }], exportAs: [\"matSelect\"], usesInheritance: true, ngImport: i0, template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"toggle()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n  <div class=\\\"mat-mdc-select-value\\\" [ngSwitch]=\\\"empty\\\" [attr.id]=\\\"_valueId\\\">\\n    <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\" *ngSwitchCase=\\\"true\\\">{{placeholder}}</span>\\n    <span class=\\\"mat-mdc-select-value-text\\\" *ngSwitchCase=\\\"false\\\" [ngSwitch]=\\\"!!customTrigger\\\">\\n      <span class=\\\"mat-mdc-select-min-line\\\" *ngSwitchDefault>{{triggerValue}}</span>\\n      <ng-content select=\\\"mat-select-trigger\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n    </span>\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:translateY(-8px)}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\"], dependencies: [{ kind: \"directive\", type: i7.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i7.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { kind: \"directive\", type: i7.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { kind: \"directive\", type: i7.NgSwitchDefault, selector: \"[ngSwitchDefault]\" }, { kind: \"directive\", type: i8.CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: [\"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPositionStrategy\", \"cdkConnectedOverlayOffsetX\", \"cdkConnectedOverlayOffsetY\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayHeight\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayMinHeight\", \"cdkConnectedOverlayBackdropClass\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayViewportMargin\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayTransformOriginOn\", \"cdkConnectedOverlayHasBackdrop\", \"cdkConnectedOverlayLockPosition\", \"cdkConnectedOverlayFlexibleDimensions\", \"cdkConnectedOverlayGrowAfterOpen\", \"cdkConnectedOverlayPush\"], outputs: [\"backdropClick\", \"positionChange\", \"attach\", \"detach\", \"overlayKeydown\", \"overlayOutsideClick\"], exportAs: [\"cdkConnectedOverlay\"] }, { kind: \"directive\", type: i8.CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"] }], animations: [matSelectAnimations.transformPanel], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSelect, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-select', exportAs: 'matSelect', inputs: ['disabled', 'disableRipple', 'tabIndex'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'role': 'combobox',\n                        'aria-autocomplete': 'none',\n                        'aria-haspopup': 'listbox',\n                        'class': 'mat-mdc-select',\n                        '[attr.id]': 'id',\n                        '[attr.tabindex]': 'tabIndex',\n                        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n                        '[attr.aria-expanded]': 'panelOpen',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.aria-required]': 'required.toString()',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n                        'ngSkipHydration': '',\n                        '[class.mat-mdc-select-disabled]': 'disabled',\n                        '[class.mat-mdc-select-invalid]': 'errorState',\n                        '[class.mat-mdc-select-required]': 'required',\n                        '[class.mat-mdc-select-empty]': 'empty',\n                        '[class.mat-mdc-select-multiple]': 'multiple',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(focus)': '_onFocus()',\n                        '(blur)': '_onBlur()',\n                    }, animations: [matSelectAnimations.transformPanel], providers: [\n                        { provide: MatFormFieldControl, useExisting: MatSelect },\n                        { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n                    ], template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"toggle()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n  <div class=\\\"mat-mdc-select-value\\\" [ngSwitch]=\\\"empty\\\" [attr.id]=\\\"_valueId\\\">\\n    <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\" *ngSwitchCase=\\\"true\\\">{{placeholder}}</span>\\n    <span class=\\\"mat-mdc-select-value-text\\\" *ngSwitchCase=\\\"false\\\" [ngSwitch]=\\\"!!customTrigger\\\">\\n      <span class=\\\"mat-mdc-select-min-line\\\" *ngSwitchDefault>{{triggerValue}}</span>\\n      <ng-content select=\\\"mat-select-trigger\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n    </span>\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:translateY(-8px)}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\"] }]\n        }], propDecorators: { options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], customTrigger: [{\n                type: ContentChild,\n                args: [MAT_SELECT_TRIGGER]\n            }], panelWidth: [{\n                type: Input\n            }], hideSingleSelectionIndicator: [{\n                type: Input\n            }] } });\n\nclass MatSelectModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSelectModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSelectModule, declarations: [MatSelect, MatSelectTrigger], imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule], exports: [CdkScrollableModule,\n            MatFormFieldModule,\n            MatSelect,\n            MatSelectTrigger,\n            MatOptionModule,\n            MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSelectModule, providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER], imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule,\n            MatFormFieldModule,\n            MatOptionModule,\n            MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSelectModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatFormFieldModule,\n                        MatSelect,\n                        MatSelectTrigger,\n                        MatOptionModule,\n                        MatCommonModule,\n                    ],\n                    declarations: [MatSelect, MatSelectTrigger],\n                    providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, _MatSelectBase, matSelectAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,sBAAsB;AAC1C,SAASC,OAAO,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,sBAAsB;AACpG,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACpO,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,eAAe,EAAEC,6BAA6B,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC3P,OAAO,KAAKC,EAAE,MAAM,8BAA8B;AAClD,SAASC,cAAc,EAAEC,mBAAmB,EAAEC,kBAAkB,QAAQ,8BAA8B;AACtG,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,sBAAsB,EAAEC,mBAAmB,EAAEC,0BAA0B,QAAQ,mBAAmB;AAC3G,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,EAAEC,cAAc,EAAEC,CAAC,QAAQ,uBAAuB;AACtH,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AACzG,SAASC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;AAErG;AACA;AACA;AACA;AACA;AACA;AACA;AANA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,0BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAg3BoGzE,EAAE,CAAA2E,cAAA,cA0P6jB,CAAC;IA1PhkB3E,EAAE,CAAA4E,MAAA,EA0P4kB,CAAC;IA1P/kB5E,EAAE,CAAA6E,YAAA,CA0PmlB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GA1PtlB9E,EAAE,CAAA+E,aAAA;IAAF/E,EAAE,CAAAgF,SAAA,EA0P4kB,CAAC;IA1P/kBhF,EAAE,CAAAiF,iBAAA,CAAAH,MAAA,CAAAI,WA0P4kB,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1P/kBzE,EAAE,CAAA2E,cAAA,cA0P2vB,CAAC;IA1P9vB3E,EAAE,CAAA4E,MAAA,EA0P2wB,CAAC;IA1P9wB5E,EAAE,CAAA6E,YAAA,CA0PkxB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GA1PrxBpF,EAAE,CAAA+E,aAAA;IAAF/E,EAAE,CAAAgF,SAAA,EA0P2wB,CAAC;IA1P9wBhF,EAAE,CAAAiF,iBAAA,CAAAG,MAAA,CAAAC,YA0P2wB,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1P9wBzE,EAAE,CAAAuF,YAAA,gCA0Pw2B,CAAC;EAAA;AAAA;AAAA,SAAAC,0BAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1P32BzE,EAAE,CAAA2E,cAAA,cA0P0rB,CAAC;IA1P7rB3E,EAAE,CAAAyF,UAAA,IAAAN,gCAAA,kBA0PkxB,CAAC;IA1PrxBnF,EAAE,CAAAyF,UAAA,IAAAH,sCAAA,wBA0Pw2B,CAAC;IA1P32BtF,EAAE,CAAA6E,YAAA,CA0Pq3B,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAiB,MAAA,GA1Px3B1F,EAAE,CAAA+E,aAAA;IAAF/E,EAAE,CAAA2F,UAAA,eAAAD,MAAA,CAAAE,aA0PyrB,CAAC;IA1P5rB5F,EAAE,CAAAgF,SAAA,EA0Pw1B,CAAC;IA1P31BhF,EAAE,CAAA2F,UAAA,qBA0Pw1B,CAAC;EAAA;AAAA;AAAA,SAAAE,kCAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqB,GAAA,GA1P31B9F,EAAE,CAAA+F,gBAAA;IAAF/F,EAAE,CAAAgG,cAAA;IAAFhG,EAAE,CAAAiG,eAAA,CA0Pq3E,CAAC;IA1Px3EjG,EAAE,CAAA2E,cAAA,iBA0Pq3E,CAAC;IA1Px3E3E,EAAE,CAAAkG,UAAA,kCAAAC,+EAAAC,MAAA;MAAFpG,EAAE,CAAAqG,aAAA,CAAAP,GAAA;MAAA,MAAAQ,MAAA,GAAFtG,EAAE,CAAA+E,aAAA;MAAA,OAAF/E,EAAE,CAAAuG,WAAA,CA0P2xED,MAAA,CAAAE,yBAAA,CAAAC,IAAA,CAAAL,MAAA,CAAAM,OAA6C,EAAC;IAAA,CAAC,CAAC,qBAAAC,yDAAAP,MAAA;MA1P70EpG,EAAE,CAAAqG,aAAA,CAAAP,GAAA;MAAA,MAAAc,OAAA,GAAF5G,EAAE,CAAA+E,aAAA;MAAA,OAAF/E,EAAE,CAAAuG,WAAA,CA0P61EK,OAAA,CAAAC,cAAA,CAAAT,MAAqB,EAAC;IAAA,CAAzC,CAAC;IA1P70EpG,EAAE,CAAAuF,YAAA,KA0Po5E,CAAC;IA1Pv5EvF,EAAE,CAAA6E,YAAA,CA0P85E,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAqC,MAAA,GA1Pj6E9G,EAAE,CAAA+E,aAAA;IAAF/E,EAAE,CAAA+G,sBAAA,kEAAAD,MAAA,CAAAE,cAAA,MA0PkgE,CAAC;IA1PrgEhH,EAAE,CAAA2F,UAAA,YAAAmB,MAAA,CAAAG,UA0PstE,CAAC,6BAAD,CAAC;IA1PztEjH,EAAE,CAAAkH,WAAA,OAAAJ,MAAA,CAAAK,EAAA,WA0PmiE,CAAC,yBAAAL,MAAA,CAAAM,QAAD,CAAC,eAAAN,MAAA,CAAAO,SAAA,QAAD,CAAC,oBAAAP,MAAA,CAAAQ,uBAAA,EAAD,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAnmC1oE,MAAMC,mBAAmB,GAAG;EACxB;AACJ;AACA;AACA;AACA;EACIC,kBAAkB,EAAE3D,OAAO,CAAC,oBAAoB,EAAE,CAC9CC,UAAU,CAAC,WAAW,EAAEC,KAAK,CAAC,iBAAiB,EAAE,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE;IAAEyD,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC,CAC1F,CAAC;EACF;EACAC,cAAc,EAAE7D,OAAO,CAAC,gBAAgB,EAAE,CACtCI,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;IAChByD,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,EACH9D,UAAU,CAAC,iBAAiB,EAAEK,OAAO,CAAC,kCAAkC,EAAED,KAAK,CAAC;IAC5EyD,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,EACJ9D,UAAU,CAAC,WAAW,EAAEK,OAAO,CAAC,cAAc,EAAED,KAAK,CAAC;IAAEyD,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC,CAC1E;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gCAAgCA,CAAA,EAAG;EACxC,OAAOC,KAAK,CAAC,+DAA+D,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,8BAA8BA,CAAA,EAAG;EACtC,OAAOD,KAAK,CAAC,oDAAoD,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iCAAiCA,CAAA,EAAG;EACzC,OAAOF,KAAK,CAAC,mCAAmC,CAAC;AACrD;AAEA,IAAIG,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,0BAA0B,GAAG,IAAInI,cAAc,CAAC,4BAA4B,CAAC;AACnF;AACA,SAASoI,2CAA2CA,CAACC,OAAO,EAAE;EAC1D,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA,MAAMC,iBAAiB,GAAG,IAAIxI,cAAc,CAAC,mBAAmB,CAAC;AACjE;AACA,MAAMyI,mCAAmC,GAAG;EACxCC,OAAO,EAAEP,0BAA0B;EACnCQ,IAAI,EAAE,CAAClJ,OAAO,CAAC;EACfmJ,UAAU,EAAER;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMS,kBAAkB,GAAG,IAAI7I,cAAc,CAAC,kBAAkB,CAAC;AACjE;AACA,MAAM8I,eAAe,CAAC;EAClBC,WAAWA,CAAA,CACX;EACAC,MAAM,EACN;EACAC,KAAK,EAAE;IACH,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGjI,kBAAkB,CAACC,aAAa,CAACC,aAAa,CAACC,eAAe,CAAC,MAAM;EAC7F2H,WAAWA,CAACI,WAAW,EAAEC,yBAAyB,EAAEC,WAAW,EAAEC,gBAAgB;EACjF;AACJ;AACA;AACA;AACA;EACIC,SAAS,EAAE;IACP,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAIpG,OAAO,CAAC,CAAC;EACrC;AACJ,CAAC,CAAC,CAAC,CAAC,CAAC;AACL;AACA,MAAMqG,cAAc,SAASP,mBAAmB,CAAC;EAC7C;EACA,IAAIQ,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,UAAU;EAC3C;EACA;EACA,IAAI3E,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC4E,YAAY;EAC5B;EACA,IAAI5E,WAAWA,CAACgE,KAAK,EAAE;IACnB,IAAI,CAACY,YAAY,GAAGZ,KAAK;IACzB,IAAI,CAACO,YAAY,CAAChD,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIsD,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACR,SAAS,EAAES,OAAO,EAAEC,YAAY,CAAC9G,UAAU,CAAC2G,QAAQ,CAAC,IAAI,KAAK;EAChG;EACA,IAAIA,QAAQA,CAACb,KAAK,EAAE;IAChB,IAAI,CAACc,SAAS,GAAGxH,qBAAqB,CAAC0G,KAAK,CAAC;IAC7C,IAAI,CAACO,YAAY,CAAChD,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIW,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC+C,SAAS;EACzB;EACA,IAAI/C,QAAQA,CAAC8B,KAAK,EAAE;IAChB,IAAI,IAAI,CAACkB,eAAe,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAMtC,gCAAgC,CAAC,CAAC;IAC5C;IACA,IAAI,CAACoC,SAAS,GAAG3H,qBAAqB,CAAC0G,KAAK,CAAC;EACjD;EACA;EACA,IAAIoB,sBAAsBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACC,uBAAuB;EACvC;EACA,IAAID,sBAAsBA,CAACpB,KAAK,EAAE;IAC9B,IAAI,CAACqB,uBAAuB,GAAG/H,qBAAqB,CAAC0G,KAAK,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIsB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACE,EAAE,EAAE;IAChB,IAAI,OAAOA,EAAE,KAAK,UAAU,KAAK,OAAOL,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAMnC,iCAAiC,CAAC,CAAC;IAC7C;IACA,IAAI,CAACuC,YAAY,GAAGC,EAAE;IACtB,IAAI,IAAI,CAACN,eAAe,EAAE;MACtB;MACA,IAAI,CAACO,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;EACA,IAAIzB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC0B,MAAM;EACtB;EACA,IAAI1B,KAAKA,CAAC2B,QAAQ,EAAE;IAChB,MAAMC,WAAW,GAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC;IAC/C,IAAIC,WAAW,EAAE;MACb,IAAI,CAACE,SAAS,CAACH,QAAQ,CAAC;IAC5B;EACJ;EACA;EACA,IAAII,yBAAyBA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAACC,0BAA0B;EAC1C;EACA,IAAID,yBAAyBA,CAAC/B,KAAK,EAAE;IACjC,IAAI,CAACgC,0BAA0B,GAAGzI,oBAAoB,CAACyG,KAAK,CAAC;EACjE;EACA;EACA,IAAI/B,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACgE,GAAG;EACnB;EACA,IAAIhE,EAAEA,CAAC+B,KAAK,EAAE;IACV,IAAI,CAACiC,GAAG,GAAGjC,KAAK,IAAI,IAAI,CAACkC,IAAI;IAC7B,IAAI,CAAC3B,YAAY,CAAChD,IAAI,CAAC,CAAC;EAC5B;EACAuC,WAAWA,CAACqC,cAAc,EAAEC,kBAAkB,EAAEC,OAAO,EAAElC,yBAAyB,EAAEmC,UAAU,EAAEC,IAAI,EAAEnC,WAAW,EAAEC,gBAAgB,EAAEmC,gBAAgB,EAAElC,SAAS,EAAEmC,QAAQ,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,eAAe,EAAE;IAChO,KAAK,CAACN,UAAU,EAAEnC,yBAAyB,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,CAAC;IACtF,IAAI,CAAC6B,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC;IACA,IAAI,CAACjC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACY,YAAY,GAAG,CAACsB,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE;IACzC;IACA,IAAI,CAACZ,IAAI,GAAI,cAAajD,YAAY,EAAG,EAAC;IAC1C;IACA,IAAI,CAAC8D,sBAAsB,GAAG,IAAI;IAClC;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI7I,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC2H,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B;IACA,IAAI,CAACmB,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B;IACA,IAAI,CAACC,QAAQ,GAAI,oBAAmBjE,YAAY,EAAG,EAAC;IACpD;IACA,IAAI,CAAC3B,yBAAyB,GAAG,IAAInD,OAAO,CAAC,CAAC;IAC9C,IAAI,CAACgJ,kBAAkB,GAAG,IAAI,CAACP,eAAe,EAAEQ,iBAAiB,IAAI,EAAE;IACvE,IAAI,CAAC1C,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAAC2C,WAAW,GAAG,YAAY;IAC/B,IAAI,CAACpC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,uBAAuB,GAAG,IAAI,CAACuB,eAAe,EAAExB,sBAAsB,IAAI,KAAK;IACpF;IACA,IAAI,CAACjD,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAACmF,sBAAsB,GAAGlJ,KAAK,CAAC,MAAM;MACtC,MAAMmJ,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACT,OAAOA,OAAO,CAACC,OAAO,CAACC,IAAI,CAACnJ,SAAS,CAACiJ,OAAO,CAAC,EAAEhJ,SAAS,CAAC,MAAMF,KAAK,CAAC,GAAGkJ,OAAO,CAAC7I,GAAG,CAACgJ,MAAM,IAAIA,MAAM,CAACC,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/H;MACA,OAAO,IAAI,CAACtB,OAAO,CAACuB,QAAQ,CAACH,IAAI,CAACjJ,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,MAAM,IAAI,CAAC+I,sBAAsB,CAAC,CAAC;IAC5F,CAAC,CAAC;IACF;IACA,IAAI,CAACO,YAAY,GAAG,IAAI7M,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAAC8M,aAAa,GAAG,IAAI,CAACD,YAAY,CAACJ,IAAI,CAAChJ,MAAM,CAACsJ,CAAC,IAAIA,CAAC,CAAC,EAAErJ,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC3E;IACA,IAAI,CAACsJ,aAAa,GAAG,IAAI,CAACH,YAAY,CAACJ,IAAI,CAAChJ,MAAM,CAACsJ,CAAC,IAAI,CAACA,CAAC,CAAC,EAAErJ,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC5E;IACA,IAAI,CAACuJ,eAAe,GAAG,IAAIjN,YAAY,CAAC,CAAC;IACzC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACkN,WAAW,GAAG,IAAIlN,YAAY,CAAC,CAAC;IACrC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACmN,aAAa,GAAG,IAAI;IACzB,IAAI,IAAI,CAAC7D,SAAS,EAAE;MAChB;MACA;MACA,IAAI,CAACA,SAAS,CAAC8D,aAAa,GAAG,IAAI;IACvC;IACA;IACA;IACA,IAAIxB,eAAe,EAAEb,yBAAyB,IAAI,IAAI,EAAE;MACpD,IAAI,CAACC,0BAA0B,GAAGY,eAAe,CAACb,yBAAyB;IAC/E;IACA,IAAI,CAACsC,sBAAsB,GAAG3B,qBAAqB;IACnD,IAAI,CAAC4B,eAAe,GAAG,IAAI,CAACD,sBAAsB,CAAC,CAAC;IACpD,IAAI,CAAC5B,QAAQ,GAAG8B,QAAQ,CAAC9B,QAAQ,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAACxE,EAAE,GAAG,IAAI,CAACA,EAAE;EACrB;EACAuG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACtD,eAAe,GAAG,IAAI1H,cAAc,CAAC,IAAI,CAAC0E,QAAQ,CAAC;IACxD,IAAI,CAACqC,YAAY,CAAChD,IAAI,CAAC,CAAC;IACxB;IACA;IACA;IACA,IAAI,CAACD,yBAAyB,CACzBmG,IAAI,CAAC9I,oBAAoB,CAAC,CAAC,EAAEC,SAAS,CAAC,IAAI,CAACoI,QAAQ,CAAC,CAAC,CACtDyB,SAAS,CAAC,MAAM,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC;EAClE;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC3D,eAAe,CAAC4D,OAAO,CAACrB,IAAI,CAAC7I,SAAS,CAAC,IAAI,CAACoI,QAAQ,CAAC,CAAC,CAACyB,SAAS,CAACM,KAAK,IAAI;MAC3EA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACvB,MAAM,IAAIA,MAAM,CAACwB,MAAM,CAAC,CAAC,CAAC;MAC9CH,KAAK,CAACI,OAAO,CAACF,OAAO,CAACvB,MAAM,IAAIA,MAAM,CAAC0B,QAAQ,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAAC7B,OAAO,CAACC,OAAO,CAACC,IAAI,CAACnJ,SAAS,CAAC,IAAI,CAAC,EAAEM,SAAS,CAAC,IAAI,CAACoI,QAAQ,CAAC,CAAC,CAACyB,SAAS,CAAC,MAAM;MACjF,IAAI,CAACY,aAAa,CAAC,CAAC;MACpB,IAAI,CAAC5D,oBAAoB,CAAC,CAAC;IAC/B,CAAC,CAAC;EACN;EACA6D,SAASA,CAAA,EAAG;IACR,MAAMC,iBAAiB,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAC1D,MAAMlF,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC;IACA;IACA;IACA,IAAIiF,iBAAiB,KAAK,IAAI,CAACxC,sBAAsB,EAAE;MACnD,MAAM0C,OAAO,GAAG,IAAI,CAACvF,WAAW,CAACwF,aAAa;MAC9C,IAAI,CAAC3C,sBAAsB,GAAGwC,iBAAiB;MAC/C,IAAIA,iBAAiB,EAAE;QACnBE,OAAO,CAACE,YAAY,CAAC,iBAAiB,EAAEJ,iBAAiB,CAAC;MAC9D,CAAC,MACI;QACDE,OAAO,CAACG,eAAe,CAAC,iBAAiB,CAAC;MAC9C;IACJ;IACA,IAAItF,SAAS,EAAE;MACX;MACA,IAAI,IAAI,CAACuF,gBAAgB,KAAKvF,SAAS,CAACS,OAAO,EAAE;QAC7C,IAAI,IAAI,CAAC8E,gBAAgB,KAAKC,SAAS,IACnCxF,SAAS,CAACyF,QAAQ,KAAK,IAAI,IAC3BzF,SAAS,CAACyF,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;UACtC,IAAI,CAACA,QAAQ,GAAGzF,SAAS,CAACyF,QAAQ;QACtC;QACA,IAAI,CAACF,gBAAgB,GAAGvF,SAAS,CAACS,OAAO;MAC7C;MACA,IAAI,CAACiF,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAC,WAAWA,CAACzC,OAAO,EAAE;IACjB;IACA;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACvD,IAAI,CAACjD,YAAY,CAAChD,IAAI,CAAC,CAAC;IAC5B;IACA,IAAIiG,OAAO,CAAC,2BAA2B,CAAC,IAAI,IAAI,CAAC0C,WAAW,EAAE;MAC1D,IAAI,CAACA,WAAW,CAACC,aAAa,CAAC,IAAI,CAACnE,0BAA0B,CAAC;IACnE;EACJ;EACAoE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,WAAW,EAAEG,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACrD,QAAQ,CAACzF,IAAI,CAAC,CAAC;IACpB,IAAI,CAACyF,QAAQ,CAACsD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAAC/F,YAAY,CAAC+F,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC7B,SAAS,GAAG,IAAI,CAAC8B,KAAK,CAAC,CAAC,GAAG,IAAI,CAACC,IAAI,CAAC,CAAC;EAC/C;EACA;EACAA,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MACjB,IAAI,CAACC,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAACjG,UAAU,GAAG,IAAI;MACtB,IAAI,CAACuF,WAAW,CAACW,yBAAyB,CAAC,IAAI,CAAC;MAChD,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAAC1E,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMI,KAAK,GAAG,IAAI,CAAC9G,WAAW,CAACwF,aAAa,CAACuB,OAAO,CAAC,mDAAmD,CAAC;IACzG,IAAI,CAACD,KAAK,EAAE;MACR;MACA;IACJ;IACA,MAAME,OAAO,GAAI,GAAE,IAAI,CAACjJ,EAAG,QAAO;IAClC,IAAI,IAAI,CAACkG,aAAa,EAAE;MACpBjL,sBAAsB,CAAC,IAAI,CAACiL,aAAa,EAAE,WAAW,EAAE+C,OAAO,CAAC;IACpE;IACA/N,mBAAmB,CAAC6N,KAAK,EAAE,WAAW,EAAEE,OAAO,CAAC;IAChD,IAAI,CAAC/C,aAAa,GAAG6C,KAAK;EAC9B;EACA;EACAT,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACpC,aAAa,EAAE;MACrB;MACA;IACJ;IACA,MAAM+C,OAAO,GAAI,GAAE,IAAI,CAACjJ,EAAG,QAAO;IAClC/E,sBAAsB,CAAC,IAAI,CAACiL,aAAa,EAAE,WAAW,EAAE+C,OAAO,CAAC;IAChE,IAAI,CAAC/C,aAAa,GAAG,IAAI;EAC7B;EACA;EACAsC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAC9F,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAACuF,WAAW,CAACW,yBAAyB,CAAC,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;MACzE,IAAI,CAAC/E,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;MACtC,IAAI,CAAC9D,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACImE,UAAUA,CAACpH,KAAK,EAAE;IACd,IAAI,CAAC6B,YAAY,CAAC7B,KAAK,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqH,gBAAgBA,CAAC7F,EAAE,EAAE;IACjB,IAAI,CAACM,SAAS,GAAGN,EAAE;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI8F,iBAAiBA,CAAC9F,EAAE,EAAE;IAClB,IAAI,CAACyB,UAAU,GAAGzB,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+F,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACzB,QAAQ,GAAGyB,UAAU;IAC1B,IAAI,CAACpF,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;IACtC,IAAI,CAACxG,YAAY,CAAChD,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIoH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChE,UAAU;EAC1B;EACA;EACA,IAAI8G,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACvJ,QAAQ,GAAG,IAAI,CAACgD,eAAe,EAAEuG,QAAQ,IAAI,EAAE,GAAG,IAAI,CAACvG,eAAe,EAAEuG,QAAQ,CAAC,CAAC,CAAC;EACnG;EACA;EACA,IAAItL,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAACuL,KAAK,EAAE;MACZ,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACzG,SAAS,EAAE;MAChB,MAAM0G,eAAe,GAAG,IAAI,CAACzG,eAAe,CAACuG,QAAQ,CAAC/M,GAAG,CAACgJ,MAAM,IAAIA,MAAM,CAACkE,SAAS,CAAC;MACrF,IAAI,IAAI,CAACT,MAAM,CAAC,CAAC,EAAE;QACfQ,eAAe,CAACE,OAAO,CAAC,CAAC;MAC7B;MACA;MACA,OAAOF,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC;IACrC;IACA,OAAO,IAAI,CAAC5G,eAAe,CAACuG,QAAQ,CAAC,CAAC,CAAC,CAACG,SAAS;EACrD;EACA;EACAT,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC5E,IAAI,GAAG,IAAI,CAACA,IAAI,CAACvC,KAAK,KAAK,KAAK,GAAG,KAAK;EACxD;EACA;EACArC,cAAcA,CAACoH,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACgB,QAAQ,EAAE;MAChB,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACoD,kBAAkB,CAAChD,KAAK,CAAC,GAAG,IAAI,CAACiD,oBAAoB,CAACjD,KAAK,CAAC;IACtF;EACJ;EACA;EACAiD,oBAAoBA,CAACjD,KAAK,EAAE;IACxB,MAAMkD,OAAO,GAAGlD,KAAK,CAACkD,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKxO,UAAU,IACrCwO,OAAO,KAAKvO,QAAQ,IACpBuO,OAAO,KAAKtO,UAAU,IACtBsO,OAAO,KAAKrO,WAAW;IAC3B,MAAMuO,SAAS,GAAGF,OAAO,KAAKpO,KAAK,IAAIoO,OAAO,KAAKnO,KAAK;IACxD,MAAMsO,OAAO,GAAG,IAAI,CAAClC,WAAW;IAChC;IACA,IAAK,CAACkC,OAAO,CAACC,QAAQ,CAAC,CAAC,IAAIF,SAAS,IAAI,CAACpO,cAAc,CAACgL,KAAK,CAAC,IAC1D,CAAC,IAAI,CAAC7G,QAAQ,IAAI6G,KAAK,CAACuD,MAAM,KAAKJ,UAAW,EAAE;MACjDnD,KAAK,CAACwD,cAAc,CAAC,CAAC,CAAC,CAAC;MACxB,IAAI,CAAC7B,IAAI,CAAC,CAAC;IACf,CAAC,MACI,IAAI,CAAC,IAAI,CAACxI,QAAQ,EAAE;MACrB,MAAMsK,wBAAwB,GAAG,IAAI,CAACf,QAAQ;MAC9CW,OAAO,CAACK,SAAS,CAAC1D,KAAK,CAAC;MACxB,MAAM2D,cAAc,GAAG,IAAI,CAACjB,QAAQ;MACpC;MACA,IAAIiB,cAAc,IAAIF,wBAAwB,KAAKE,cAAc,EAAE;QAC/D;QACA;QACA,IAAI,CAAC/F,cAAc,CAACgG,QAAQ,CAACD,cAAc,CAACd,SAAS,EAAE,KAAK,CAAC;MACjE;IACJ;EACJ;EACA;EACAG,kBAAkBA,CAAChD,KAAK,EAAE;IACtB,MAAMqD,OAAO,GAAG,IAAI,CAAClC,WAAW;IAChC,MAAM+B,OAAO,GAAGlD,KAAK,CAACkD,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKxO,UAAU,IAAIwO,OAAO,KAAKvO,QAAQ;IACjE,MAAM2O,QAAQ,GAAGD,OAAO,CAACC,QAAQ,CAAC,CAAC;IACnC,IAAIH,UAAU,IAAInD,KAAK,CAACuD,MAAM,EAAE;MAC5B;MACAvD,KAAK,CAACwD,cAAc,CAAC,CAAC;MACtB,IAAI,CAAC9B,KAAK,CAAC,CAAC;MACZ;MACA;IACJ,CAAC,MACI,IAAI,CAAC4B,QAAQ,KACbJ,OAAO,KAAKpO,KAAK,IAAIoO,OAAO,KAAKnO,KAAK,CAAC,IACxCsO,OAAO,CAACQ,UAAU,IAClB,CAAC7O,cAAc,CAACgL,KAAK,CAAC,EAAE;MACxBA,KAAK,CAACwD,cAAc,CAAC,CAAC;MACtBH,OAAO,CAACQ,UAAU,CAACC,qBAAqB,CAAC,CAAC;IAC9C,CAAC,MACI,IAAI,CAACR,QAAQ,IAAI,IAAI,CAACpH,SAAS,IAAIgH,OAAO,KAAKjO,CAAC,IAAI+K,KAAK,CAAC+D,OAAO,EAAE;MACpE/D,KAAK,CAACwD,cAAc,CAAC,CAAC;MACtB,MAAMQ,oBAAoB,GAAG,IAAI,CAACxF,OAAO,CAACyF,IAAI,CAACC,GAAG,IAAI,CAACA,GAAG,CAAClD,QAAQ,IAAI,CAACkD,GAAG,CAACxB,QAAQ,CAAC;MACrF,IAAI,CAAClE,OAAO,CAAC0B,OAAO,CAACvB,MAAM,IAAI;QAC3B,IAAI,CAACA,MAAM,CAACqC,QAAQ,EAAE;UAClBgD,oBAAoB,GAAGrF,MAAM,CAACwB,MAAM,CAAC,CAAC,GAAGxB,MAAM,CAAC0B,QAAQ,CAAC,CAAC;QAC9D;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAM8D,sBAAsB,GAAGd,OAAO,CAACe,eAAe;MACtDf,OAAO,CAACK,SAAS,CAAC1D,KAAK,CAAC;MACxB,IAAI,IAAI,CAAC9D,SAAS,IACdiH,UAAU,IACVnD,KAAK,CAACqE,QAAQ,IACdhB,OAAO,CAACQ,UAAU,IAClBR,OAAO,CAACe,eAAe,KAAKD,sBAAsB,EAAE;QACpDd,OAAO,CAACQ,UAAU,CAACC,qBAAqB,CAAC,CAAC;MAC9C;IACJ;EACJ;EACAQ,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACtD,QAAQ,EAAE;MAChB,IAAI,CAACrF,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACH,YAAY,CAAChD,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACI+L,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC5I,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACwF,WAAW,EAAEqD,eAAe,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAACxD,QAAQ,IAAI,CAAC,IAAI,CAACpB,SAAS,EAAE;MACnC,IAAI,CAAC1B,UAAU,CAAC,CAAC;MACjB,IAAI,CAACb,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;MACtC,IAAI,CAACxG,YAAY,CAAChD,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;EACIiM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,CAACC,cAAc,CAACjG,IAAI,CAACjJ,IAAI,CAAC,CAAC,CAAC,CAAC,CAACiK,SAAS,CAAC,MAAM;MAC1D,IAAI,CAACrC,kBAAkB,CAACuH,aAAa,CAAC,CAAC;MACvC,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC9B,CAAC,CAAC;EACN;EACA;EACA9L,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC0E,gBAAgB,GAAI,OAAM,IAAI,CAACA,gBAAgB,CAACqH,KAAM,EAAC,GAAG,EAAE;EAC5E;EACA;EACA,IAAInC,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACxG,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC4I,OAAO,CAAC,CAAC;EAClE;EACArI,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACAsI,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,IAAI,CAAC3J,SAAS,EAAE;QAChB,IAAI,CAACoB,MAAM,GAAG,IAAI,CAACpB,SAAS,CAACN,KAAK;MACtC;MACA,IAAI,CAACkK,oBAAoB,CAAC,IAAI,CAACxI,MAAM,CAAC;MACtC,IAAI,CAACnB,YAAY,CAAChD,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI2M,oBAAoBA,CAAClK,KAAK,EAAE;IACxB,IAAI,CAACuD,OAAO,CAAC0B,OAAO,CAACvB,MAAM,IAAIA,MAAM,CAACyG,iBAAiB,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACjJ,eAAe,CAACkJ,KAAK,CAAC,CAAC;IAC5B,IAAI,IAAI,CAAClM,QAAQ,IAAI8B,KAAK,EAAE;MACxB,IAAI,CAACqK,KAAK,CAACC,OAAO,CAACtK,KAAK,CAAC,KAAK,OAAOmB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC1E,MAAMpC,8BAA8B,CAAC,CAAC;MAC1C;MACAiB,KAAK,CAACiF,OAAO,CAAEsF,YAAY,IAAK,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAAC,CAAC;MACxE,IAAI,CAACE,WAAW,CAAC,CAAC;IACtB,CAAC,MACI;MACD,MAAMC,mBAAmB,GAAG,IAAI,CAACF,oBAAoB,CAACxK,KAAK,CAAC;MAC5D;MACA;MACA,IAAI0K,mBAAmB,EAAE;QACrB,IAAI,CAACxE,WAAW,CAACyE,gBAAgB,CAACD,mBAAmB,CAAC;MAC1D,CAAC,MACI,IAAI,CAAC,IAAI,CAAC/F,SAAS,EAAE;QACtB;QACA;QACA,IAAI,CAACuB,WAAW,CAACyE,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAACvI,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIyD,oBAAoBA,CAACxK,KAAK,EAAE;IACxB,MAAM0K,mBAAmB,GAAG,IAAI,CAACnH,OAAO,CAACqH,IAAI,CAAElH,MAAM,IAAK;MACtD;MACA;MACA,IAAI,IAAI,CAACxC,eAAe,CAAC2J,UAAU,CAACnH,MAAM,CAAC,EAAE;QACzC,OAAO,KAAK;MAChB;MACA,IAAI;QACA;QACA,OAAOA,MAAM,CAAC1D,KAAK,IAAI,IAAI,IAAI,IAAI,CAACuB,YAAY,CAACmC,MAAM,CAAC1D,KAAK,EAAEA,KAAK,CAAC;MACzE,CAAC,CACD,OAAO8K,KAAK,EAAE;QACV,IAAI,OAAO3J,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C;UACA4J,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;QACvB;QACA,OAAO,KAAK;MAChB;IACJ,CAAC,CAAC;IACF,IAAIJ,mBAAmB,EAAE;MACrB,IAAI,CAACxJ,eAAe,CAACgE,MAAM,CAACwF,mBAAmB,CAAC;IACpD;IACA,OAAOA,mBAAmB;EAC9B;EACA;EACA7I,YAAYA,CAACF,QAAQ,EAAE;IACnB;IACA,IAAIA,QAAQ,KAAK,IAAI,CAACD,MAAM,IAAK,IAAI,CAACT,SAAS,IAAIoJ,KAAK,CAACC,OAAO,CAAC3I,QAAQ,CAAE,EAAE;MACzE,IAAI,IAAI,CAAC4B,OAAO,EAAE;QACd,IAAI,CAAC2G,oBAAoB,CAACvI,QAAQ,CAAC;MACvC;MACA,IAAI,CAACD,MAAM,GAAGC,QAAQ;MACtB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAsJ,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAOA,IAAI,CAACnF,QAAQ;EACxB;EACA;EACAlB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACqB,WAAW,GAAG,IAAI9M,0BAA0B,CAAC,IAAI,CAACmK,OAAO,CAAC,CAC1D4C,aAAa,CAAC,IAAI,CAACnE,0BAA0B,CAAC,CAC9CmJ,uBAAuB,CAAC,CAAC,CACzBtE,yBAAyB,CAAC,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CACxDiE,cAAc,CAAC,CAAC,CAChBC,cAAc,CAAC,CAAC,CAChBC,uBAAuB,CAAC,CAAC,UAAU,CAAC,CAAC,CACrCC,aAAa,CAAC,IAAI,CAACN,cAAc,CAAC;IACvC,IAAI,CAAC/E,WAAW,CAACsF,MAAM,CAAC/G,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAACE,SAAS,EAAE;QAChB;QACA;QACA,IAAI,CAAC,IAAI,CAACzG,QAAQ,IAAI,IAAI,CAACgI,WAAW,CAAC0C,UAAU,EAAE;UAC/C,IAAI,CAAC1C,WAAW,CAAC0C,UAAU,CAACC,qBAAqB,CAAC,CAAC;QACvD;QACA;QACA;QACA,IAAI,CAAC4C,KAAK,CAAC,CAAC;QACZ,IAAI,CAAChF,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF,IAAI,CAACP,WAAW,CAACwF,MAAM,CAACjH,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAAC9D,UAAU,IAAI,IAAI,CAACgL,KAAK,EAAE;QAC/B,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAAC1F,WAAW,CAACiD,eAAe,IAAI,CAAC,CAAC;MACrE,CAAC,MACI,IAAI,CAAC,IAAI,CAACxI,UAAU,IAAI,CAAC,IAAI,CAACzC,QAAQ,IAAI,IAAI,CAACgI,WAAW,CAAC0C,UAAU,EAAE;QACxE,IAAI,CAAC1C,WAAW,CAAC0C,UAAU,CAACC,qBAAqB,CAAC,CAAC;MACvD;IACJ,CAAC,CAAC;EACN;EACA;EACAxD,aAAaA,CAAA,EAAG;IACZ,MAAMwG,kBAAkB,GAAGxR,KAAK,CAAC,IAAI,CAACkJ,OAAO,CAACC,OAAO,EAAE,IAAI,CAACR,QAAQ,CAAC;IACrE,IAAI,CAACM,sBAAsB,CAACG,IAAI,CAAC7I,SAAS,CAACiR,kBAAkB,CAAC,CAAC,CAACpH,SAAS,CAACM,KAAK,IAAI;MAC/E,IAAI,CAAC+G,SAAS,CAAC/G,KAAK,CAAChF,MAAM,EAAEgF,KAAK,CAACgH,WAAW,CAAC;MAC/C,IAAIhH,KAAK,CAACgH,WAAW,IAAI,CAAC,IAAI,CAAC7N,QAAQ,IAAI,IAAI,CAACyC,UAAU,EAAE;QACxD,IAAI,CAAC8F,KAAK,CAAC,CAAC;QACZ,IAAI,CAACgF,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF;IACA;IACApR,KAAK,CAAC,GAAG,IAAI,CAACkJ,OAAO,CAAC7I,GAAG,CAACgJ,MAAM,IAAIA,MAAM,CAACsI,aAAa,CAAC,CAAC,CACrDvI,IAAI,CAAC7I,SAAS,CAACiR,kBAAkB,CAAC,CAAC,CACnCpH,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACrC,kBAAkB,CAACuH,aAAa,CAAC,CAAC;MACvC,IAAI,CAACpJ,YAAY,CAAChD,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;EACAuO,SAASA,CAACpI,MAAM,EAAEqI,WAAW,EAAE;IAC3B,MAAME,WAAW,GAAG,IAAI,CAAC/K,eAAe,CAAC2J,UAAU,CAACnH,MAAM,CAAC;IAC3D,IAAIA,MAAM,CAAC1D,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAACiB,SAAS,EAAE;MACzCyC,MAAM,CAAC0B,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAClE,eAAe,CAACkJ,KAAK,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACpK,KAAK,IAAI,IAAI,EAAE;QACpB,IAAI,CAACkM,iBAAiB,CAACxI,MAAM,CAAC1D,KAAK,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAIiM,WAAW,KAAKvI,MAAM,CAAC+D,QAAQ,EAAE;QACjC/D,MAAM,CAAC+D,QAAQ,GACT,IAAI,CAACvG,eAAe,CAACgE,MAAM,CAACxB,MAAM,CAAC,GACnC,IAAI,CAACxC,eAAe,CAACkE,QAAQ,CAAC1B,MAAM,CAAC;MAC/C;MACA,IAAIqI,WAAW,EAAE;QACb,IAAI,CAAC7F,WAAW,CAACiG,aAAa,CAACzI,MAAM,CAAC;MAC1C;MACA,IAAI,IAAI,CAACxF,QAAQ,EAAE;QACf,IAAI,CAACuM,WAAW,CAAC,CAAC;QAClB,IAAIsB,WAAW,EAAE;UACb;UACA;UACA;UACA;UACA,IAAI,CAACN,KAAK,CAAC,CAAC;QAChB;MACJ;IACJ;IACA,IAAIQ,WAAW,KAAK,IAAI,CAAC/K,eAAe,CAAC2J,UAAU,CAACnH,MAAM,CAAC,EAAE;MACzD,IAAI,CAACwI,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC3L,YAAY,CAAChD,IAAI,CAAC,CAAC;EAC5B;EACA;EACAkN,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACvM,QAAQ,EAAE;MACf,MAAMqF,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC6I,OAAO,CAAC,CAAC;MACtC,IAAI,CAAClL,eAAe,CAACmL,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAChC,OAAO,IAAI,CAACC,cAAc,GACpB,IAAI,CAACA,cAAc,CAACF,CAAC,EAAEC,CAAC,EAAEhJ,OAAO,CAAC,GAClCA,OAAO,CAACkJ,OAAO,CAACH,CAAC,CAAC,GAAG/I,OAAO,CAACkJ,OAAO,CAACF,CAAC,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,CAAChM,YAAY,CAAChD,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACA2O,iBAAiBA,CAACQ,aAAa,EAAE;IAC7B,IAAIC,WAAW,GAAG,IAAI;IACtB,IAAI,IAAI,CAACzO,QAAQ,EAAE;MACfyO,WAAW,GAAG,IAAI,CAAClF,QAAQ,CAAC/M,GAAG,CAACgJ,MAAM,IAAIA,MAAM,CAAC1D,KAAK,CAAC;IAC3D,CAAC,MACI;MACD2M,WAAW,GAAG,IAAI,CAAClF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACzH,KAAK,GAAG0M,aAAa;IACrE;IACA,IAAI,CAAChL,MAAM,GAAGiL,WAAW;IACzB,IAAI,CAACzI,WAAW,CAAC0I,IAAI,CAACD,WAAW,CAAC;IAClC,IAAI,CAAC7K,SAAS,CAAC6K,WAAW,CAAC;IAC3B,IAAI,CAAC1I,eAAe,CAAC2I,IAAI,CAAC,IAAI,CAACC,eAAe,CAACF,WAAW,CAAC,CAAC;IAC5D,IAAI,CAACvK,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACID,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACZ,WAAW,EAAE;MAClB,IAAI,IAAI,CAACwB,KAAK,EAAE;QACZ;QACA;QACA;QACA,IAAIoF,uBAAuB,GAAG,CAAC,CAAC;QAChC,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACxJ,OAAO,CAACyJ,MAAM,EAAED,KAAK,EAAE,EAAE;UACtD,MAAMrJ,MAAM,GAAG,IAAI,CAACH,OAAO,CAAC0J,GAAG,CAACF,KAAK,CAAC;UACtC,IAAI,CAACrJ,MAAM,CAACqC,QAAQ,EAAE;YAClB+G,uBAAuB,GAAGC,KAAK;YAC/B;UACJ;QACJ;QACA,IAAI,CAAC7G,WAAW,CAACiG,aAAa,CAACW,uBAAuB,CAAC;MAC3D,CAAC,MACI;QACD,IAAI,CAAC5G,WAAW,CAACiG,aAAa,CAAC,IAAI,CAACjL,eAAe,CAACuG,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpE;IACJ;EACJ;EACA;EACAd,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAAChG,UAAU,IAAI,CAAC,IAAI,CAACoF,QAAQ,IAAI,IAAI,CAACxC,OAAO,EAAEyJ,MAAM,GAAG,CAAC;EACzE;EACA;EACAvB,KAAKA,CAAClI,OAAO,EAAE;IACX,IAAI,CAACrD,WAAW,CAACwF,aAAa,CAAC+F,KAAK,CAAClI,OAAO,CAAC;EACjD;EACA;EACAnF,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACD,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAM+O,OAAO,GAAG,IAAI,CAAC1K,gBAAgB,EAAE2K,UAAU,CAAC,CAAC;IACnD,MAAMC,eAAe,GAAGF,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE;IACpD,OAAO,IAAI,CAACG,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACC,cAAc,GAAGH,OAAO;EAChF;EACA;EACAI,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC3I,SAAS,IAAI,IAAI,CAACuB,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC0C,UAAU,EAAE;MACnE,OAAO,IAAI,CAAC1C,WAAW,CAAC0C,UAAU,CAAC3K,EAAE;IACzC;IACA,OAAO,IAAI;EACf;EACA;EACAuH,yBAAyBA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACrH,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAM+O,OAAO,GAAG,IAAI,CAAC1K,gBAAgB,EAAE2K,UAAU,CAAC,CAAC;IACnD,IAAInN,KAAK,GAAG,CAACkN,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE,IAAI,IAAI,CAAChK,QAAQ;IAC1D,IAAI,IAAI,CAACmK,cAAc,EAAE;MACrBrN,KAAK,IAAI,GAAG,GAAG,IAAI,CAACqN,cAAc;IACtC;IACA,OAAOrN,KAAK;EAChB;EACA;EACA0E,mBAAmBA,CAAC6I,MAAM,EAAE;IACxB,IAAI,CAAC1J,YAAY,CAAC+I,IAAI,CAACW,MAAM,CAAC;EAClC;EACA;AACJ;AACA;AACA;EACIC,iBAAiBA,CAACC,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACT,MAAM,EAAE;MACZ,IAAI,CAAC9M,WAAW,CAACwF,aAAa,CAACC,YAAY,CAAC,kBAAkB,EAAE8H,GAAG,CAAC3F,IAAI,CAAC,GAAG,CAAC,CAAC;IAClF,CAAC,MACI;MACD,IAAI,CAAC5H,WAAW,CAACwF,aAAa,CAACE,eAAe,CAAC,kBAAkB,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;EACI8H,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACjC,KAAK,CAAC,CAAC;IACZ,IAAI,CAAC/E,IAAI,CAAC,CAAC;EACf;EACA;AACJ;AACA;AACA;EACI,IAAIiH,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAChN,UAAU,IAAI,CAAC,IAAI,CAAC+G,KAAK,IAAK,IAAI,CAAChH,QAAQ,IAAI,CAAC,CAAC,IAAI,CAACE,YAAa;EACnF;EACA;IAAS,IAAI,CAACgN,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFtN,cAAc,EAAxB1J,EAAE,CAAAiX,iBAAA,CAAwChV,EAAE,CAACiV,aAAa,GAA1DlX,EAAE,CAAAiX,iBAAA,CAAqEjX,EAAE,CAACmX,iBAAiB,GAA3FnX,EAAE,CAAAiX,iBAAA,CAAsGjX,EAAE,CAACoX,MAAM,GAAjHpX,EAAE,CAAAiX,iBAAA,CAA4HhW,EAAE,CAACoW,iBAAiB,GAAlJrX,EAAE,CAAAiX,iBAAA,CAA6JjX,EAAE,CAACsX,UAAU,GAA5KtX,EAAE,CAAAiX,iBAAA,CAAuL1U,EAAE,CAACgV,cAAc,MAA1MvX,EAAE,CAAAiX,iBAAA,CAAqO9T,EAAE,CAACqU,MAAM,MAAhPxX,EAAE,CAAAiX,iBAAA,CAA2Q9T,EAAE,CAACsU,kBAAkB,MAAlSzX,EAAE,CAAAiX,iBAAA,CAA6TnV,cAAc,MAA7U9B,EAAE,CAAAiX,iBAAA,CAAwW9T,EAAE,CAACuU,SAAS,OAAtX1X,EAAE,CAAA2X,iBAAA,CAA6Z,UAAU,GAAza3X,EAAE,CAAAiX,iBAAA,CAAqc7O,0BAA0B,GAAjepI,EAAE,CAAAiX,iBAAA,CAA4e9U,EAAE,CAACyV,aAAa,GAA9f5X,EAAE,CAAAiX,iBAAA,CAAygBxO,iBAAiB;IAAA,CAA4D;EAAE;EAC1rB;IAAS,IAAI,CAACoP,IAAI,kBAD8E7X,EAAE,CAAA8X,iBAAA;MAAAC,IAAA,EACJrO,cAAc;MAAAsO,SAAA,WAAAC,qBAAAxT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADZzE,EAAE,CAAAkY,WAAA,CAAA5T,GAAA;UAAFtE,EAAE,CAAAkY,WAAA,CAAA3T,GAAA;UAAFvE,EAAE,CAAAkY,WAAA,CAC64BvY,mBAAmB;QAAA;QAAA,IAAA8E,EAAA;UAAA,IAAA0T,EAAA;UADl6BnY,EAAE,CAAAoY,cAAA,CAAAD,EAAA,GAAFnY,EAAE,CAAAqY,WAAA,QAAA3T,GAAA,CAAAX,OAAA,GAAAoU,EAAA,CAAAG,KAAA;UAAFtY,EAAE,CAAAoY,cAAA,CAAAD,EAAA,GAAFnY,EAAE,CAAAqY,WAAA,QAAA3T,GAAA,CAAAmQ,KAAA,GAAAsD,EAAA,CAAAG,KAAA;UAAFtY,EAAE,CAAAoY,cAAA,CAAAD,EAAA,GAAFnY,EAAE,CAAAqY,WAAA,QAAA3T,GAAA,CAAAiO,WAAA,GAAAwF,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,MAAA;QAAAC,mBAAA;QAAAvR,UAAA;QAAA/B,WAAA;QAAA6E,QAAA;QAAA3C,QAAA;QAAAkD,sBAAA;QAAAE,WAAA;QAAAtB,KAAA;QAAA7B,SAAA;QAAAkP,cAAA;QAAAkC,iBAAA;QAAAxN,yBAAA;QAAAyK,cAAA;QAAAvO,EAAA;MAAA;MAAAuR,OAAA;QAAA3L,YAAA;QAAAC,aAAA;QAAAE,aAAA;QAAAC,eAAA;QAAAC,WAAA;MAAA;MAAAuL,QAAA,GAAF3Y,EAAE,CAAA4Y,0BAAA,EAAF5Y,EAAE,CAAA6Y,oBAAA;IAAA,EACm/B;EAAE;AAC3lC;AACA;EAAA,QAAAxO,SAAA,oBAAAA,SAAA,KAHoGrK,EAAE,CAAA8Y,iBAAA,CAGXpP,cAAc,EAAc,CAAC;IAC5GqO,IAAI,EAAE5X;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE4X,IAAI,EAAE9V,EAAE,CAACiV;IAAc,CAAC,EAAE;MAAEa,IAAI,EAAE/X,EAAE,CAACmX;IAAkB,CAAC,EAAE;MAAEY,IAAI,EAAE/X,EAAE,CAACoX;IAAO,CAAC,EAAE;MAAEW,IAAI,EAAE9W,EAAE,CAACoW;IAAkB,CAAC,EAAE;MAAEU,IAAI,EAAE/X,EAAE,CAACsX;IAAW,CAAC,EAAE;MAAES,IAAI,EAAExV,EAAE,CAACgV,cAAc;MAAEwB,UAAU,EAAE,CAAC;QAChNhB,IAAI,EAAE3X;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2X,IAAI,EAAE5U,EAAE,CAACqU,MAAM;MAAEuB,UAAU,EAAE,CAAC;QAClChB,IAAI,EAAE3X;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2X,IAAI,EAAE5U,EAAE,CAACsU,kBAAkB;MAAEsB,UAAU,EAAE,CAAC;QAC9ChB,IAAI,EAAE3X;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2X,IAAI,EAAElW,EAAE,CAACmX,YAAY;MAAED,UAAU,EAAE,CAAC;QACxChB,IAAI,EAAE3X;MACV,CAAC,EAAE;QACC2X,IAAI,EAAE1X,MAAM;QACZ4Y,IAAI,EAAE,CAACnX,cAAc;MACzB,CAAC;IAAE,CAAC,EAAE;MAAEiW,IAAI,EAAE5U,EAAE,CAACuU,SAAS;MAAEqB,UAAU,EAAE,CAAC;QACrChB,IAAI,EAAEzX;MACV,CAAC,EAAE;QACCyX,IAAI,EAAE3X;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2X,IAAI,EAAE/I,SAAS;MAAE+J,UAAU,EAAE,CAAC;QAClChB,IAAI,EAAExX,SAAS;QACf0Y,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAElB,IAAI,EAAE/I,SAAS;MAAE+J,UAAU,EAAE,CAAC;QAClChB,IAAI,EAAE1X,MAAM;QACZ4Y,IAAI,EAAE,CAAC7Q,0BAA0B;MACrC,CAAC;IAAE,CAAC,EAAE;MAAE2P,IAAI,EAAE5V,EAAE,CAACyV;IAAc,CAAC,EAAE;MAAEG,IAAI,EAAE/I,SAAS;MAAE+J,UAAU,EAAE,CAAC;QAC9DhB,IAAI,EAAE3X;MACV,CAAC,EAAE;QACC2X,IAAI,EAAE1X,MAAM;QACZ4Y,IAAI,EAAE,CAACxQ,iBAAiB;MAC5B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE+P,mBAAmB,EAAE,CAAC;MAClDT,IAAI,EAAEvX,KAAK;MACXyY,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAElV,OAAO,EAAE,CAAC;MACVgU,IAAI,EAAEtX,SAAS;MACfwY,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEpE,KAAK,EAAE,CAAC;MACRkD,IAAI,EAAEtX,SAAS;MACfwY,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEtG,WAAW,EAAE,CAAC;MACdoF,IAAI,EAAEtX,SAAS;MACfwY,IAAI,EAAE,CAACtZ,mBAAmB;IAC9B,CAAC,CAAC;IAAEsH,UAAU,EAAE,CAAC;MACb8Q,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAE0E,WAAW,EAAE,CAAC;MACd6S,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAEuJ,QAAQ,EAAE,CAAC;MACXgO,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAE4G,QAAQ,EAAE,CAAC;MACX2Q,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAE8J,sBAAsB,EAAE,CAAC;MACzByN,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAEgK,WAAW,EAAE,CAAC;MACduN,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAE0I,KAAK,EAAE,CAAC;MACR6O,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAE6G,SAAS,EAAE,CAAC;MACZ0Q,IAAI,EAAEvX,KAAK;MACXyY,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE1C,cAAc,EAAE,CAAC;MACjBwB,IAAI,EAAEvX,KAAK;MACXyY,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAER,iBAAiB,EAAE,CAAC;MACpBV,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAEyK,yBAAyB,EAAE,CAAC;MAC5B8M,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAEkV,cAAc,EAAE,CAAC;MACjBqC,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAE2G,EAAE,EAAE,CAAC;MACL4Q,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAEuM,YAAY,EAAE,CAAC;MACfgL,IAAI,EAAErX;IACV,CAAC,CAAC;IAAEsM,aAAa,EAAE,CAAC;MAChB+K,IAAI,EAAErX,MAAM;MACZuY,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE/L,aAAa,EAAE,CAAC;MAChB6K,IAAI,EAAErX,MAAM;MACZuY,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE9L,eAAe,EAAE,CAAC;MAClB4K,IAAI,EAAErX;IACV,CAAC,CAAC;IAAE0M,WAAW,EAAE,CAAC;MACd2K,IAAI,EAAErX;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMwY,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACpC,IAAI,YAAAqC,yBAAAnC,CAAA;MAAA,YAAAA,CAAA,IAAwFkC,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACrB,IAAI,kBAzF8E7X,EAAE,CAAA8X,iBAAA;MAAAC,IAAA,EAyFJmB,gBAAgB;MAAAE,SAAA;MAAAT,QAAA,GAzFd3Y,EAAE,CAAAqZ,kBAAA,CAyFyD,CAAC;QAAE1Q,OAAO,EAAEG,kBAAkB;QAAEwQ,WAAW,EAAEJ;MAAiB,CAAC,CAAC;IAAA,EAAiB;EAAE;AAClP;AACA;EAAA,QAAA7O,SAAA,oBAAAA,SAAA,KA3FoGrK,EAAE,CAAA8Y,iBAAA,CA2FXI,gBAAgB,EAAc,CAAC;IAC9GnB,IAAI,EAAE5X,SAAS;IACf8Y,IAAI,EAAE,CAAC;MACCM,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAE7Q,OAAO,EAAEG,kBAAkB;QAAEwQ,WAAW,EAAEJ;MAAiB,CAAC;IAC9E,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMO,SAAS,SAAS/P,cAAc,CAAC;EACnCV,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG0Q,SAAS,CAAC;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC7N,eAAe,IAAI,OAAO,IAAI,CAACA,eAAe,CAAC6N,UAAU,KAAK,WAAW,GAC1F,IAAI,CAAC7N,eAAe,CAAC6N,UAAU,GAC/B,MAAM;IACZ,IAAI,CAACC,UAAU,GAAG,CACd;MACIC,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACd,CAAC,EACD;MACIH,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;IACd,CAAC,EACD;MACIH,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,QAAQ;MAClB/S,UAAU,EAAE;IAChB,CAAC,EACD;MACI4S,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,QAAQ;MAClB/S,UAAU,EAAE;IAChB,CAAC,CACJ;IACD,IAAI,CAACgT,6BAA6B,GAAG,IAAI,CAACnO,eAAe,EAAEoO,4BAA4B,IAAI,KAAK;IAChG;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC/F,cAAc,GAAIvH,MAAM,IAAK;MAC9B,IAAI,IAAI,CAACiB,SAAS,EAAE;QAChB;QACA,OAAO,KAAK;MAChB;MACA;MACA;MACA;MACA,OAAOjB,MAAM,CAACqC,QAAQ;IAC1B,CAAC;EACL;EACA,IAAI4H,gBAAgBA,CAAA,EAAG;IACnB;IACA;IACA,OAAO,IAAI,CAAChJ,SAAS,IAAI,CAAC,IAAI,CAAC+C,KAAK,IAAK,IAAI,CAACjH,OAAO,IAAI,CAAC,CAAC,IAAI,CAACzE,WAAY;EAChF;EACAwI,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAACrC,cAAc,CACduJ,MAAM,CAAC,CAAC,CACRjI,IAAI,CAAC7I,SAAS,CAAC,IAAI,CAACoI,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACE,SAAS,EAAE;QAChB,IAAI,CAACsM,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,uBAAuB,CAAC;QACxE,IAAI,CAAC/O,kBAAkB,CAACuH,aAAa,CAAC,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN;EACAjD,IAAIA,CAAA,EAAG;IACH;IACA;IACA;IACA,IAAI,IAAI,CAAClE,gBAAgB,EAAE;MACvB,IAAI,CAAC2O,uBAAuB,GAAG,IAAI,CAAC3O,gBAAgB,CAAC4O,yBAAyB,CAAC,CAAC;IACpF;IACA,IAAI,CAACH,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,uBAAuB,CAAC;IACxE,KAAK,CAACzK,IAAI,CAAC,CAAC;IACZ;IACA,IAAI,CAACnG,YAAY,CAAChD,IAAI,CAAC,CAAC;EAC5B;EACAkJ,KAAKA,CAAA,EAAG;IACJ,KAAK,CAACA,KAAK,CAAC,CAAC;IACb;IACA,IAAI,CAAClG,YAAY,CAAChD,IAAI,CAAC,CAAC;EAC5B;EACA;EACAqO,qBAAqBA,CAACmB,KAAK,EAAE;IACzB,MAAMrJ,MAAM,GAAG,IAAI,CAACH,OAAO,CAAC6I,OAAO,CAAC,CAAC,CAACW,KAAK,CAAC;IAC5C,IAAIrJ,MAAM,EAAE;MACR,MAAMiI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACjG,aAAa;MACtC,MAAM2L,UAAU,GAAGjZ,6BAA6B,CAAC2U,KAAK,EAAE,IAAI,CAACxJ,OAAO,EAAE,IAAI,CAAC+N,YAAY,CAAC;MACxF,MAAM7L,OAAO,GAAG/B,MAAM,CAAC6N,eAAe,CAAC,CAAC;MACxC,IAAIxE,KAAK,KAAK,CAAC,IAAIsE,UAAU,KAAK,CAAC,EAAE;QACjC;QACA;QACA;QACA1F,KAAK,CAAC6F,SAAS,GAAG,CAAC;MACvB,CAAC,MACI;QACD7F,KAAK,CAAC6F,SAAS,GAAGnZ,wBAAwB,CAACoN,OAAO,CAACgM,SAAS,EAAEhM,OAAO,CAACiM,YAAY,EAAE/F,KAAK,CAAC6F,SAAS,EAAE7F,KAAK,CAAC+F,YAAY,CAAC;MAC5H;IACJ;EACJ;EACA9H,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACgC,qBAAqB,CAAC,IAAI,CAAC1F,WAAW,CAACiD,eAAe,IAAI,CAAC,CAAC;EACrE;EACA0D,eAAeA,CAAC7M,KAAK,EAAE;IACnB,OAAO,IAAIH,eAAe,CAAC,IAAI,EAAEG,KAAK,CAAC;EAC3C;EACA;EACAkR,gBAAgBA,CAACS,eAAe,EAAE;IAC9B,IAAI,IAAI,CAAClB,UAAU,KAAK,MAAM,EAAE;MAC5B,MAAMmB,YAAY,GAAGD,eAAe,YAAYjb,gBAAgB,GAC1Dib,eAAe,CAACrP,UAAU,GAC1BqP,eAAe,IAAI,IAAI,CAACzR,WAAW;MACzC,OAAO0R,YAAY,CAAClM,aAAa,CAACmM,qBAAqB,CAAC,CAAC,CAACC,KAAK;IACnE;IACA,OAAO,IAAI,CAACrB,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAACA,UAAU;EAC1D;EACA;EACA,IAAIO,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACD,6BAA6B;EAC7C;EACA,IAAIC,4BAA4BA,CAAChR,KAAK,EAAE;IACpC,IAAI,CAAC+Q,6BAA6B,GAAGzX,qBAAqB,CAAC0G,KAAK,CAAC;IACjE,IAAI,CAAC+R,qBAAqB,CAAC,CAAC;EAChC;EACA;EACAA,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACxO,OAAO,EAAE;MACd,KAAK,MAAMG,MAAM,IAAI,IAAI,CAACH,OAAO,EAAE;QAC/BG,MAAM,CAACtB,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;MAC5C;IACJ;EACJ;EACA;IAAS,IAAI,CAAC6G,IAAI;MAAA,IAAAoE,sBAAA;MAAA,gBAAAC,kBAAAnE,CAAA;QAAA,QAAAkE,sBAAA,KAAAA,sBAAA,GAtP8Elb,EAAE,CAAAob,qBAAA,CAsPQ3B,SAAS,IAAAzC,CAAA,IAATyC,SAAS;MAAA;IAAA,GAAqD;EAAE;EAC1K;IAAS,IAAI,CAAC4B,IAAI,kBAvP8Erb,EAAE,CAAAsb,iBAAA;MAAAvD,IAAA,EAuPJ0B,SAAS;MAAAL,SAAA;MAAAmC,cAAA,WAAAC,yBAAA/W,EAAA,EAAAC,GAAA,EAAA+W,QAAA;QAAA,IAAAhX,EAAA;UAvPPzE,EAAE,CAAA0b,cAAA,CAAAD,QAAA,EA0PxB3S,kBAAkB;UA1PI9I,EAAE,CAAA0b,cAAA,CAAAD,QAAA,EA0PuDha,SAAS;UA1PlEzB,EAAE,CAAA0b,cAAA,CAAAD,QAAA,EA0PkI/Z,YAAY;QAAA;QAAA,IAAA+C,EAAA;UAAA,IAAA0T,EAAA;UA1PhJnY,EAAE,CAAAoY,cAAA,CAAAD,EAAA,GAAFnY,EAAE,CAAAqY,WAAA,QAAA3T,GAAA,CAAAkB,aAAA,GAAAuS,EAAA,CAAAG,KAAA;UAAFtY,EAAE,CAAAoY,cAAA,CAAAD,EAAA,GAAFnY,EAAE,CAAAqY,WAAA,QAAA3T,GAAA,CAAA+H,OAAA,GAAA0L,EAAA;UAAFnY,EAAE,CAAAoY,cAAA,CAAAD,EAAA,GAAFnY,EAAE,CAAAqY,WAAA,QAAA3T,GAAA,CAAA8V,YAAA,GAAArC,EAAA;QAAA;MAAA;MAAAwD,SAAA,WAuP6O,UAAU,uBAAuB,MAAM,mBAAmB,SAAS,qBAAqB,EAAE;MAAAC,QAAA;MAAAC,YAAA,WAAAC,uBAAArX,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvPzUzE,EAAE,CAAAkG,UAAA,qBAAA6V,qCAAA3V,MAAA;YAAA,OAuPJ1B,GAAA,CAAAmC,cAAA,CAAAT,MAAqB,CAAC;UAAA,qBAAA4V,mCAAA;YAAA,OAAtBtX,GAAA,CAAA6N,QAAA,CAAS,CAAC;UAAA,oBAAA0J,kCAAA;YAAA,OAAVvX,GAAA,CAAA8N,OAAA,CAAQ,CAAC;UAAA;QAAA;QAAA,IAAA/N,EAAA;UAvPPzE,EAAE,CAAAkH,WAAA,OAAAxC,GAAA,CAAAyC,EAAA,cAAAzC,GAAA,CAAAiH,QAAA,mBAAAjH,GAAA,CAAAmJ,SAAA,GAAAnJ,GAAA,CAAAyC,EAAA,qCAAAzC,GAAA,CAAAmJ,SAAA,gBAAAnJ,GAAA,CAAA2C,SAAA,2BAAA3C,GAAA,CAAAqF,QAAA,CAAAmS,QAAA,qBAAAxX,GAAA,CAAAuK,QAAA,CAAAiN,QAAA,oBAAAxX,GAAA,CAAAyX,UAAA,2BAAAzX,GAAA,CAAA8R,wBAAA;UAAFxW,EAAE,CAAAoc,WAAA,4BAAA1X,GAAA,CAAAuK,QAAA,4BAAAvK,GAAA,CAAAyX,UAAA,6BAAAzX,GAAA,CAAAqF,QAAA,0BAAArF,GAAA,CAAAkM,KAAA,6BAAAlM,GAAA,CAAA0C,QAAA;QAAA;MAAA;MAAAmR,MAAA;QAAAtJ,QAAA;QAAAoN,aAAA;QAAA1Q,QAAA;QAAAgO,UAAA;QAAAO,4BAAA;MAAA;MAAAoC,QAAA;MAAA3D,QAAA,GAAF3Y,EAAE,CAAAqZ,kBAAA,CAuPojC,CAC9oC;QAAE1Q,OAAO,EAAE5G,mBAAmB;QAAEuX,WAAW,EAAEG;MAAU,CAAC,EACxD;QAAE9Q,OAAO,EAAEnH,2BAA2B;QAAE8X,WAAW,EAAEG;MAAU,CAAC,CACnE,GA1P2FzZ,EAAE,CAAA4Y,0BAAA;MAAA2D,kBAAA,EAAA/U,GAAA;MAAAgV,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mBAAAnY,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzE,EAAE,CAAA6c,eAAA,CAAAtV,GAAA;UAAFvH,EAAE,CAAA2E,cAAA,eA0PyY,CAAC;UA1P5Y3E,EAAE,CAAAkG,UAAA,mBAAA4W,wCAAA;YAAA,OA0P8TpY,GAAA,CAAAgL,MAAA,CAAO,CAAC;UAAA,CAAC,CAAC;UA1P1U1P,EAAE,CAAA2E,cAAA,YA0P6d,CAAC;UA1Phe3E,EAAE,CAAAyF,UAAA,IAAAjB,yBAAA,iBA0PmlB,CAAC;UA1PtlBxE,EAAE,CAAAyF,UAAA,IAAAD,yBAAA,iBA0Pq3B,CAAC;UA1Px3BxF,EAAE,CAAA6E,YAAA,CA0P+3B,CAAC;UA1Pl4B7E,EAAE,CAAA2E,cAAA,YA0Pi7B,CAAC,YAAD,CAAC;UA1Pp7B3E,EAAE,CAAAgG,cAAA,CA0P4qC,CAAC;UA1P/qChG,EAAE,CAAA2E,cAAA,YA0P4qC,CAAC;UA1P/qC3E,EAAE,CAAA+c,SAAA,aA0PktC,CAAC;UA1PrtC/c,EAAE,CAAA6E,YAAA,CA0PguC,CAAC,CAAD,CAAC,CAAD,CAAC,CAAD,CAAC;UA1PnuC7E,EAAE,CAAAyF,UAAA,KAAAI,iCAAA,wBA0P86E,CAAC;UA1Pj7E7F,EAAE,CAAAkG,UAAA,2BAAA8W,yDAAA;YAAA,OA0PiyDtY,GAAA,CAAAiL,KAAA,CAAM,CAAC;UAAA,CAAC,CAAC,oBAAAsN,kDAAA;YAAA,OAAevY,GAAA,CAAAgO,WAAA,CAAY,CAAC;UAAA,CAA7B,CAAC,oBAAAwK,kDAAA;YAAA,OAA6CxY,GAAA,CAAAiL,KAAA,CAAM,CAAC;UAAA,CAArD,CAAC;QAAA;QAAA,IAAAlL,EAAA;UAAA,MAAA0Y,GAAA,GA1P5yDnd,EAAE,CAAAod,WAAA;UAAFpd,EAAE,CAAAgF,SAAA,EA0Pqc,CAAC;UA1PxchF,EAAE,CAAA2F,UAAA,aAAAjB,GAAA,CAAAkM,KA0Pqc,CAAC;UA1Pxc5Q,EAAE,CAAAkH,WAAA,OAAAxC,GAAA,CAAA0H,QA0P4d,CAAC;UA1P/dpM,EAAE,CAAAgF,SAAA,EA0P0jB,CAAC;UA1P7jBhF,EAAE,CAAA2F,UAAA,qBA0P0jB,CAAC;UA1P7jB3F,EAAE,CAAAgF,SAAA,EA0PwpB,CAAC;UA1P3pBhF,EAAE,CAAA2F,UAAA,sBA0PwpB,CAAC;UA1P3pB3F,EAAE,CAAAgF,SAAA,EA0P++C,CAAC;UA1Pl/ChF,EAAE,CAAA2F,UAAA,kCAAAjB,GAAA,CAAA2H,kBA0P++C,CAAC,sCAAA3H,GAAA,CAAA8I,eAAD,CAAC,8BAAA9I,GAAA,CAAA2V,uBAAA,IAAA8C,GAAD,CAAC,4BAAAzY,GAAA,CAAAmJ,SAAD,CAAC,iCAAAnJ,GAAA,CAAAkV,UAAD,CAAC,6BAAAlV,GAAA,CAAAyV,aAAD,CAAC;QAAA;MAAA;MAAAkD,YAAA,GAA4qJvd,EAAE,CAACwd,OAAO,EAAoFxd,EAAE,CAACyd,QAAQ,EAA6Ezd,EAAE,CAAC0d,YAAY,EAAqF1d,EAAE,CAAC2d,eAAe,EAA8Dhe,EAAE,CAACE,mBAAmB,EAAk8BF,EAAE,CAACG,gBAAgB;MAAA8d,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAAyH,CAACpW,mBAAmB,CAACG,cAAc;MAAC;MAAAkW,eAAA;IAAA,EAAiG;EAAE;AACv1P;AACA;EAAA,QAAAzT,SAAA,oBAAAA,SAAA,KA5PoGrK,EAAE,CAAA8Y,iBAAA,CA4PXW,SAAS,EAAc,CAAC;IACvG1B,IAAI,EAAEpX,SAAS;IACfsY,IAAI,EAAE,CAAC;MAAEM,QAAQ,EAAE,YAAY;MAAE+C,QAAQ,EAAE,WAAW;MAAE/D,MAAM,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC;MAAEoF,aAAa,EAAE/c,iBAAiB,CAACmd,IAAI;MAAED,eAAe,EAAEjd,uBAAuB,CAACmd,MAAM;MAAEC,IAAI,EAAE;QAC7L,MAAM,EAAE,UAAU;QAClB,mBAAmB,EAAE,MAAM;QAC3B,eAAe,EAAE,SAAS;QAC1B,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,UAAU;QAC7B,sBAAsB,EAAE,kCAAkC;QAC1D,sBAAsB,EAAE,WAAW;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,sBAAsB,EAAE,qBAAqB;QAC7C,sBAAsB,EAAE,qBAAqB;QAC7C,qBAAqB,EAAE,YAAY;QACnC,8BAA8B,EAAE,4BAA4B;QAC5D,iBAAiB,EAAE,EAAE;QACrB,iCAAiC,EAAE,UAAU;QAC7C,gCAAgC,EAAE,YAAY;QAC9C,iCAAiC,EAAE,UAAU;QAC7C,8BAA8B,EAAE,OAAO;QACvC,iCAAiC,EAAE,UAAU;QAC7C,WAAW,EAAE,wBAAwB;QACrC,SAAS,EAAE,YAAY;QACvB,QAAQ,EAAE;MACd,CAAC;MAAEC,UAAU,EAAE,CAACzW,mBAAmB,CAACG,cAAc,CAAC;MAAE4R,SAAS,EAAE,CAC5D;QAAE7Q,OAAO,EAAE5G,mBAAmB;QAAEuX,WAAW,EAAEG;MAAU,CAAC,EACxD;QAAE9Q,OAAO,EAAEnH,2BAA2B;QAAE8X,WAAW,EAAEG;MAAU,CAAC,CACnE;MAAEkD,QAAQ,EAAE,osEAAosE;MAAEe,MAAM,EAAE,CAAC,krHAAkrH;IAAE,CAAC;EAC75L,CAAC,CAAC,QAAkB;IAAEjR,OAAO,EAAE,CAAC;MACxBsL,IAAI,EAAEjX,eAAe;MACrBmY,IAAI,EAAE,CAACxX,SAAS,EAAE;QAAE0c,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAE3D,YAAY,EAAE,CAAC;MACfzC,IAAI,EAAEjX,eAAe;MACrBmY,IAAI,EAAE,CAACvX,YAAY,EAAE;QAAEyc,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEvY,aAAa,EAAE,CAAC;MAChBmS,IAAI,EAAEhX,YAAY;MAClBkY,IAAI,EAAE,CAACnQ,kBAAkB;IAC7B,CAAC,CAAC;IAAE6Q,UAAU,EAAE,CAAC;MACb5B,IAAI,EAAEvX;IACV,CAAC,CAAC;IAAE0Z,4BAA4B,EAAE,CAAC;MAC/BnC,IAAI,EAAEvX;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4d,eAAe,CAAC;EAClB;IAAS,IAAI,CAACtH,IAAI,YAAAuH,wBAAArH,CAAA;MAAA,YAAAA,CAAA,IAAwFoH,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBA1S8Ete,EAAE,CAAAue,gBAAA;MAAAxG,IAAA,EA0SSqG;IAAe,EAK/F;EAAE;EAC7B;IAAS,IAAI,CAACI,IAAI,kBAhT8Exe,EAAE,CAAAye,gBAAA;MAAAjF,SAAA,EAgTqC,CAAC9Q,mCAAmC,CAAC;MAAAgW,OAAA,GAAY3e,YAAY,EAAEF,aAAa,EAAE8B,eAAe,EAAEC,eAAe,EAAEM,mBAAmB,EAClQF,kBAAkB,EAClBL,eAAe,EACfC,eAAe;IAAA,EAAI;EAAE;AACjC;AACA;EAAA,QAAAyI,SAAA,oBAAAA,SAAA,KArToGrK,EAAE,CAAA8Y,iBAAA,CAqTXsF,eAAe,EAAc,CAAC;IAC7GrG,IAAI,EAAE/W,QAAQ;IACdiY,IAAI,EAAE,CAAC;MACCyF,OAAO,EAAE,CAAC3e,YAAY,EAAEF,aAAa,EAAE8B,eAAe,EAAEC,eAAe,CAAC;MACxE+c,OAAO,EAAE,CACLzc,mBAAmB,EACnBF,kBAAkB,EAClByX,SAAS,EACTP,gBAAgB,EAChBvX,eAAe,EACfC,eAAe,CAClB;MACDgd,YAAY,EAAE,CAACnF,SAAS,EAAEP,gBAAgB,CAAC;MAC3CM,SAAS,EAAE,CAAC9Q,mCAAmC;IACnD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASD,iBAAiB,EAAEL,0BAA0B,EAAEM,mCAAmC,EAAEL,2CAA2C,EAAES,kBAAkB,EAAE2Q,SAAS,EAAE1Q,eAAe,EAAEqV,eAAe,EAAElF,gBAAgB,EAAExP,cAAc,EAAEjC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}