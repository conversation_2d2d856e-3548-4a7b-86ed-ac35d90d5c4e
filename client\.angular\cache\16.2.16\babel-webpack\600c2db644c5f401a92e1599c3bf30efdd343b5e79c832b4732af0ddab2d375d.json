{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Main routing module for the application\n * Defines routes and lazy loading strategy\n */\nimport { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\nimport { GuestGuard } from './core/guards/guest.guard';\nimport { CustomPreloadingStrategy } from './core/strategies/custom-preloading.strategy';\n/**\n * Application routes configuration\n * Implements lazy loading for better performance\n */\nconst routes = [{\n  path: '',\n  redirectTo: 'dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule),\n  canActivate: [GuestGuard]\n}, {\n  path: 'dashboard',\n  loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'tasks',\n  loadChildren: () => import('./features/tasks/tasks.module').then(m => m.TasksModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'profile',\n  loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule),\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  loadChildren: () => import('./features/not-found/not-found.module').then(m => m.NotFoundModule)\n}];\nexport let AppRoutingModule = class AppRoutingModule {};\nAppRoutingModule = __decorate([NgModule({\n  imports: [RouterModule.forRoot(routes, {\n    initialNavigation: 'enabledBlocking',\n    scrollPositionRestoration: 'enabled',\n    relativeLinkResolution: 'legacy',\n    // Preload all modules for better UX after initial load\n    preloadingStrategy: CustomPreloadingStrategy\n  })],\n  exports: [RouterModule]\n})], AppRoutingModule);", "map": {"version": 3, "names": ["NgModule", "RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CustomPreloadingStrategy", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "AuthModule", "canActivate", "DashboardModule", "TasksModule", "ProfileModule", "NotFoundModule", "AppRoutingModule", "__decorate", "imports", "forRoot", "initialNavigation", "scrollPositionRestoration", "relativeLinkResolution", "preloadingStrategy", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\app-routing.module.ts"], "sourcesContent": ["/**\n * Main routing module for the application\n * Defines routes and lazy loading strategy\n */\nimport { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\nimport { GuestGuard } from './core/guards/guest.guard';\nimport { CustomPreloadingStrategy } from './core/strategies/custom-preloading.strategy';\nimport { Type } from '@angular/core';\n\n/**\n * Application routes configuration\n * Implements lazy loading for better performance\n */\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: 'dashboard',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: (): Promise<Type<any>> => import('./features/auth/auth.module').then(m => m.AuthModule),\n    canActivate: [GuestGuard]\n  },\n  {\n    path: 'dashboard',\n    loadChildren: (): Promise<Type<any>> => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'tasks',\n    loadChildren: (): Promise<Type<any>> => import('./features/tasks/tasks.module').then(m => m.TasksModule),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'profile',\n    loadChildren: (): Promise<Type<any>> => import('./features/profile/profile.module').then(m => m.ProfileModule),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: '**',\n    loadChildren: (): Promise<Type<any>> => import('./features/not-found/not-found.module').then(m => m.NotFoundModule)\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes, {\n    initialNavigation: 'enabledBlocking', // For SSR\n    scrollPositionRestoration: 'enabled', // Scroll to top on navigation\n    relativeLinkResolution: 'legacy',\n    // Preload all modules for better UX after initial load\n    preloadingStrategy: CustomPreloadingStrategy\n  })],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,wBAAwB,QAAQ,8CAA8C;AAGvF;;;;AAIA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,WAAW;EACvBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAA0B,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC;EACrGC,WAAW,EAAE,CAACV,UAAU;CACzB,EACD;EACEG,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAA0B,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,eAAe,CAAC;EACpHD,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAA0B,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,WAAW,CAAC;EACxGF,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAA0B,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,aAAa,CAAC;EAC9GH,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,IAAI;EACVG,YAAY,EAAEA,CAAA,KAA0B,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,cAAc;CACnH,CACF;AAYM,WAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB,GAAI;AAApBA,gBAAgB,GAAAC,UAAA,EAV5BnB,QAAQ,CAAC;EACRoB,OAAO,EAAE,CAACnB,YAAY,CAACoB,OAAO,CAAChB,MAAM,EAAE;IACrCiB,iBAAiB,EAAE,iBAAiB;IACpCC,yBAAyB,EAAE,SAAS;IACpCC,sBAAsB,EAAE,QAAQ;IAChC;IACAC,kBAAkB,EAAErB;GACrB,CAAC,CAAC;EACHsB,OAAO,EAAE,CAACzB,YAAY;CACvB,CAAC,C,EACWiB,gBAAgB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}