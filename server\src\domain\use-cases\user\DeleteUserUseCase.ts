/**
 * Delete User Use Case
 * Handles business logic for deleting a user
 */
import { IUserRepository } from '../../repositories/IUserRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for deleting a user
 * Implements business rules and validation for user deletion
 */
export class DeleteUserUseCase {
  /**
   * Constructor for DeleteUserUseCase
   * @param userRepository - Repository for user data access
   */
  constructor(private userRepository: IUserRepository) {}

  /**
   * Executes the use case to delete a user
   * @param userId - ID of the user to delete
   * @returns Promise resolving to boolean indicating success
   * @throws AppError if user not found or deletion fails
   */
  async execute(userId: string): Promise<boolean> {
    try {
      // Validate user ID
      if (!userId) {
        throw new AppError('User ID is required', 400);
      }

      // Delete user
      const deleted = await this.userRepository.delete(userId);
      
      // Check if user existed
      if (!deleted) {
        throw new AppError(`User with ID ${userId} not found`, 404);
      }
      
      return true;
    } catch (error) {
      // Rethrow AppErrors, wrap other errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error deleting user: ${(error as Error).message}`, 500);
    }
  }
}
