/**
 * Reset Password Form Component
 * Handles password reset with token validation
 */
import { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';

@Component({
  selector: 'app-reset-password-form',
  templateUrl: './reset-password-form.component.html',
  styleUrls: ['./reset-password-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ResetPasswordFormComponent implements OnInit {
  /**
   * Loading state indicator
   */
  @Input() loading = false;
  
  /**
   * Error message from failed request
   */
  @Input() error: string | null = null;
  
  /**
   * Success message after successful request
   */
  @Input() success: string | null = null;
  
  /**
   * Reset token from URL
   */
  @Input() token: string | null = null;
  
  /**
   * Event emitted when form is submitted
   */
  @Output() formSubmit = new EventEmitter<{ password: string; token: string }>();
  
  /**
   * Event emitted when login link is clicked
   */
  @Output() login = new EventEmitter<void>();
  
  /**
   * Reset password form group
   */
  resetPasswordForm!: FormGroup;
  
  /**
   * Flag to toggle password visibility
   */
  hidePassword = true;
  
  /**
   * Flag to toggle confirm password visibility
   */
  hideConfirmPassword = true;

  /**
   * Constructor with dependency injection
   * @param fb - FormBuilder for reactive forms
   */
  constructor(private fb: FormBuilder) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Initializes the reset password form
   */
  ngOnInit(): void {
    this.initForm();
  }

  /**
   * Initialize reset password form with validation
   */
  private initForm(): void {
    this.resetPasswordForm = this.fb.group({
      password: ['', [
        Validators.required, 
        Validators.minLength(8),
        this.passwordStrengthValidator
      ]],
      confirmPassword: ['', Validators.required]
    }, { validators: this.passwordMatchValidator });
  }

  /**
   * Custom validator for password strength
   * @param control - Form control
   * @returns Validation errors or null
   */
  private passwordStrengthValidator(control: AbstractControl): ValidationErrors | null {
    const value: string = control.value || '';
    
    if (!value) {
      return null;
    }
    
    const hasUpperCase = /[A-Z]+/.test(value);
    const hasLowerCase = /[a-z]+/.test(value);
    const hasNumeric = /[0-9]+/.test(value);
    const hasSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]+/.test(value);
    
    const passwordValid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;
    
    return !passwordValid ? { passwordStrength: true } : null;
  }

  /**
   * Custom validator for password matching
   * @param group - Form group
   * @returns Validation errors or null
   */
  private passwordMatchValidator(group: AbstractControl): ValidationErrors | null {
    const password = group.get('password')?.value;
    const confirmPassword = group.get('confirmPassword')?.value;
    
    return password === confirmPassword ? null : { passwordMismatch: true };
  }

  /**
   * Handle form submission
   */
  onSubmit(): void {
    if (this.resetPasswordForm.invalid || this.loading || !this.token) {
      // Mark all fields as touched to trigger validation messages
      this.resetPasswordForm.markAllAsTouched();
      return;
    }
    
    const { password } = this.resetPasswordForm.value;
    this.formSubmit.emit({ password, token: this.token });
  }

  /**
   * Handle login link click
   */
  onLogin(): void {
    this.login.emit();
  }

  /**
   * Check if form control has error
   * @param controlName - Name of form control
   * @param errorName - Name of error
   * @returns True if control has error
   */
  hasError(controlName: string, errorName: string): boolean {
    const control = this.resetPasswordForm.get(controlName);
    return !!(control && control.touched && control.hasError(errorName));
  }

  /**
   * Check if form has error
   * @param errorName - Name of error
   * @returns True if form has error
   */
  hasFormError(errorName: string): boolean {
    return this.resetPasswordForm.touched && this.resetPasswordForm.hasError(errorName);
  }

  /**
   * Toggle password visibility
   */
  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  /**
   * Toggle confirm password visibility
   */
  toggleConfirmPasswordVisibility(): void {
    this.hideConfirmPassword = !this.hideConfirmPassword;
  }
}
