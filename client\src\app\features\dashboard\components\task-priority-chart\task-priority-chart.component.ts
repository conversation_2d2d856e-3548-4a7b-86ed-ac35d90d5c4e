/**
 * Task Priority Chart Component
 * Displays a chart showing task distribution by priority
 */
import { Component, Input, ChangeDetectionStrategy } from '@angular/core';

@Component({
  selector: 'app-task-priority-chart',
  templateUrl: './task-priority-chart.component.html',
  styleUrls: ['./task-priority-chart.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskPriorityChartComponent {
  /**
   * Number of tasks with 'high' priority
   */
  @Input() highCount = 0;
  
  /**
   * Number of tasks with 'medium' priority
   */
  @Input() mediumCount = 0;
  
  /**
   * Number of tasks with 'low' priority
   */
  @Input() lowCount = 0;
  
  /**
   * Chart data for rendering
   */
  chartData: any[] = [];
  
  /**
   * Chart view dimensions
   */
  view: [number, number] = [300, 200];
  
  /**
   * Chart color scheme
   */
  colorScheme = {
    domain: ['#f44336', '#ff9800', '#4caf50']
  };
  
  /**
   * Flag to show/hide chart labels
   */
  showLabels = true;
  
  /**
   * Flag to enable/disable chart animations
   */
  animations = true;
  
  /**
   * Flag to show/hide legend
   */
  showLegend = true;
  
  /**
   * Flag to enable/disable gradient fills
   */
  gradient = false;
  
  /**
   * Lifecycle hook that is called when input properties change
   * Updates chart data when counts change
   * @param changes - Input changes
   */
  ngOnChanges(changes: any): void {
    this.updateChartData();
  }
  
  /**
   * Update chart data based on current counts
   */
  private updateChartData(): void {
    this.chartData = [
      {
        name: 'High',
        value: this.highCount
      },
      {
        name: 'Medium',
        value: this.mediumCount
      },
      {
        name: 'Low',
        value: this.lowCount
      }
    ];
  }
  
  /**
   * Calculate the height percentage for a bar based on its count
   * @param count - The count value for the bar
   * @returns Percentage height (0-100)
   */
  getBarHeight(count: number): number {
    const total = this.highCount + this.mediumCount + this.lowCount;
    if (total === 0) return 0;
    
    // Calculate percentage with a minimum height of 5% for visibility
    const percentage = (count / total) * 100;
    return Math.max(percentage, 5);
  }
}
