{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../../../shared/shared.module';\nimport { ProfilePageComponent } from './profile-page.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n// Define routes for the profile page\nconst routes = [{\n  path: '',\n  component: ProfilePageComponent\n}];\nexport class ProfilePageModule {\n  static {\n    this.ɵfac = function ProfilePageModule_Factory(t) {\n      return new (t || ProfilePageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfilePageModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfilePageModule, {\n    declarations: [ProfilePageComponent],\n    imports: [CommonModule, SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "SharedModule", "ProfilePageComponent", "routes", "path", "component", "ProfilePageModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\profile\\pages\\profile-page\\profile-page.module.ts"], "sourcesContent": ["/**\n * Profile page module\n * Contains the main component for displaying and editing user profile\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../../../shared/shared.module';\nimport { ProfilePageComponent } from './profile-page.component';\n\n// Define routes for the profile page\nconst routes: Routes = [\n  {\n    path: '',\n    component: ProfilePageComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    ProfilePageComponent\n  ],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class ProfilePageModule { }\n"], "mappings": "AAKA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,oBAAoB,QAAQ,0BAA0B;;;AAE/D;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAYD,OAAM,MAAOI,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAL1BP,YAAY,EACZE,YAAY,EACZD,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC;IAAA;EAAA;;;2EAGpBG,iBAAiB;IAAAE,YAAA,GAR1BN,oBAAoB;IAAAO,OAAA,GAGpBV,YAAY,EACZE,YAAY,EAAAS,EAAA,CAAAV,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}