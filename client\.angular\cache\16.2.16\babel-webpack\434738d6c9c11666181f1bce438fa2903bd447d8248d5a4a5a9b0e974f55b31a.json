{"ast": null, "code": "/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { Output } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class LazyLoadDirective {\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    /**\n     * Event emitted when element enters viewport\n     */\n    this.appLazyLoad = new Output();\n    /**\n     * Intersection observer instance\n     */\n    this.observer = null;\n  }\n  /**\n   * Initialize the intersection observer\n   */\n  ngOnInit() {\n    if (typeof IntersectionObserver !== 'undefined') {\n      // Create observer with options\n      this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {\n        root: null,\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      // Start observing the element\n      this.observer.observe(this.elementRef.nativeElement);\n    }\n  }\n  /**\n   * Clean up observer when directive is destroyed\n   */\n  ngOnDestroy() {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  handleIntersection(entries) {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LazyLoadDirective_Factory(t) {\n      return new (t || LazyLoadDirective)(i0.ɵɵdirectiveInject('ElementRef'));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: LazyLoadDirective,\n      selectors: [[\"\", \"appLazyLoad\", \"\"]],\n      outputs: {\n        appLazyLoad: \"appLazyLoad\"\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["Output", "LazyLoadDirective", "constructor", "elementRef", "appLazyLoad", "observer", "ngOnInit", "IntersectionObserver", "handleIntersection", "bind", "root", "rootMargin", "threshold", "observe", "nativeElement", "ngOnDestroy", "disconnect", "entries", "for<PERSON>ach", "entry", "isIntersecting", "emit", "unobserve", "target", "i0", "ɵɵdirectiveInject", "selectors", "outputs"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\directives\\lazy-load.directive.ts"], "sourcesContent": ["/**\n * Lazy Load Directive\n * Defers loading of elements until they are in viewport\n * Improves performance by reducing initial load time\n */\nimport { Directive, Output, Inject } from '@angular/core';\n\n@Directive({\n  selector: '[appLazyLoad]'\n})\nexport class LazyLoadDirective {\n  /**\n   * Event emitted when element enters viewport\n   */\n  @Output() appLazyLoad = new Output();\n  \n  /**\n   * Intersection observer instance\n   */\n  private observer: IntersectionObserver | null = null;\n\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(@Inject('ElementRef') private elementRef: any) {}\n\n  /**\n   * Initialize the intersection observer\n   */\n  ngOnInit(): void {\n    if (typeof IntersectionObserver !== 'undefined') {\n      // Create observer with options\n      this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {\n        root: null, // Use viewport as root\n        rootMargin: '0px',\n        threshold: 0.1 // Trigger when 10% of element is visible\n      });\n      \n      // Start observing the element\n      this.observer.observe(this.elementRef.nativeElement);\n    }\n  }\n\n  /**\n   * Clean up observer when directive is destroyed\n   */\n  ngOnDestroy(): void {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n\n  /**\n   * Handle intersection events\n   * @param entries - Intersection observer entries\n   */\n  private handleIntersection(entries: any[]): void {\n    entries.forEach(entry => {\n      if (entry.isIntersecting) {\n        // Element is now visible in viewport\n        this.appLazyLoad.emit();\n        \n        // Stop observing once triggered\n        if (this.observer) {\n          this.observer.unobserve(entry.target);\n        }\n      }\n    });\n  }\n}\n"], "mappings": "AAAA;;;;;AAKA,SAAoBA,MAAM,QAAgB,eAAe;;AAKzD,OAAM,MAAOC,iBAAiB;EAW5B;;;;EAIAC,YAA0CC,UAAe;IAAf,KAAAA,UAAU,GAAVA,UAAU;IAdpD;;;IAGU,KAAAC,WAAW,GAAG,IAAIJ,MAAM,EAAE;IAEpC;;;IAGQ,KAAAK,QAAQ,GAAgC,IAAI;EAMQ;EAE5D;;;EAGAC,QAAQA,CAAA;IACN,IAAI,OAAOC,oBAAoB,KAAK,WAAW,EAAE;MAC/C;MACA,IAAI,CAACF,QAAQ,GAAG,IAAIE,oBAAoB,CAAC,IAAI,CAACC,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC3EC,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,GAAG,CAAC;OAChB,CAAC;MAEF;MACA,IAAI,CAACP,QAAQ,CAACQ,OAAO,CAAC,IAAI,CAACV,UAAU,CAACW,aAAa,CAAC;;EAExD;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACV,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACW,UAAU,EAAE;MAC1B,IAAI,CAACX,QAAQ,GAAG,IAAI;;EAExB;EAEA;;;;EAIQG,kBAAkBA,CAACS,OAAc;IACvCA,OAAO,CAACC,OAAO,CAACC,KAAK,IAAG;MACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxB;QACA,IAAI,CAAChB,WAAW,CAACiB,IAAI,EAAE;QAEvB;QACA,IAAI,IAAI,CAAChB,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,CAACiB,SAAS,CAACH,KAAK,CAACI,MAAM,CAAC;;;IAG3C,CAAC,CAAC;EACJ;;;uBA5DWtB,iBAAiB,EAAAuB,EAAA,CAAAC,iBAAA,CAeR,YAAY;IAAA;EAAA;;;YAfrBxB,iBAAiB;MAAAyB,SAAA;MAAAC,OAAA;QAAAvB,WAAA;MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}