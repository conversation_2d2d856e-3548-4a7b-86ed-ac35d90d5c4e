/**
 * Authentication Controller
 * Handles HTTP requests for authentication operations
 */
import { Request, Response, NextFunction } from 'express';
import { MongoUserRepository } from '../repositories/MongoUserRepository';
import { CreateUserUseCase } from '../../domain/use-cases/user/CreateUserUseCase';
import { LoginUseCase } from '../../domain/use-cases/auth/LoginUseCase';
import { AppError } from '../../domain/common/AppError';

// Create repository instance
const userRepository = new MongoUserRepository();

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 format: password
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
export const register = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Create use case instance
    const createUserUseCase = new CreateUserUseCase(userRepository);
    
    // Execute use case
    const user = await createUserUseCase.execute(req.body);
    
    // Generate token
    const token = user.generateAuthToken();
    
    // Send response
    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
        },
        token,
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login with email and password
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 format: password
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 *       500:
 *         description: Server error
 */
export const login = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Validate request body
    const { email, password } = req.body;
    
    if (!email || !password) {
      throw new AppError('Please provide email and password', 400);
    }
    
    // Create use case instance
    const loginUseCase = new LoginUseCase(userRepository);
    
    // Execute use case
    const result = await loginUseCase.execute({ email, password });
    
    // Send response
    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       401:
 *         description: Not authorized
 *       500:
 *         description: Server error
 */
export const getMe = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Check if user exists in request (set by auth middleware)
    if (!req.user) {
      throw new AppError('Not authorized', 401);
    }
    
    // Get user from database
    const user = await userRepository.findById(req.user.id);
    
    if (!user) {
      throw new AppError('User not found', 404);
    }
    
    // Send response
    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    });
  } catch (error) {
    next(error);
  }
};
