/**
 * Tasks page component styles
 */

/* Container for the entire tasks page */
.tasks-page-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Page header */
.page-header {
  margin-bottom: 24px;
  
  h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 500;
    color: #333;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tasks-page-container {
    padding: 16px;
  }
  
  .page-header h1 {
    font-size: 1.5rem;
  }
}
