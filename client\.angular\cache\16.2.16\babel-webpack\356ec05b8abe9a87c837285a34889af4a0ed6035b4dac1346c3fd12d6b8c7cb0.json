{"ast": null, "code": "import { map, take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   */\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  /**\n   * Determines if a route can be activated\n   * @param route - The route being activated\n   * @param state - Router state\n   * @returns Boolean or UrlTree indicating if route can be activated\n   */\n  canActivate(route, state) {\n    return this.authService.isAuthenticated$.pipe(take(1), map(isAuthenticated => {\n      // If authenticated, allow access\n      if (isAuthenticated) {\n        return true;\n      }\n      // Store attempted URL for redirecting after login\n      const returnUrl = state.url;\n      // Navigate to login page with return URL\n      return this.router.createUrlTree(['/auth/login'], {\n        queryParams: {\n          returnUrl\n        }\n      });\n    }));\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "take", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "isAuthenticated$", "pipe", "isAuthenticated", "returnUrl", "url", "createUrlTree", "queryParams", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["/**\n * Authentication Guard\n * Protects routes that require authentication\n */\nimport { Injectable } from '@angular/core';\nimport { \n  ActivatedRouteSnapshot, \n  RouterStateSnapshot, \n  Router,\n  UrlTree\n} from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   */\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  /**\n   * Determines if a route can be activated\n   * @param route - The route being activated\n   * @param state - Router state\n   * @returns Boolean or UrlTree indicating if route can be activated\n   */\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {\n    return this.authService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        // If authenticated, allow access\n        if (isAuthenticated) {\n          return true;\n        }\n        \n        // Store attempted URL for redirecting after login\n        const returnUrl = state.url;\n        \n        // Navigate to login page with return URL\n        return this.router.createUrlTree(['/auth/login'], { \n          queryParams: { returnUrl } \n        });\n      })\n    );\n  }\n}\n"], "mappings": "AAYA,SAASA,GAAG,EAAEC,IAAI,QAAQ,gBAAgB;;;;AAM1C,OAAM,MAAOC,SAAS;EACpB;;;;;EAKAC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEH;;;;;;EAMAC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACJ,WAAW,CAACK,gBAAgB,CAACC,IAAI,CAC3CT,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACW,eAAe,IAAG;MACpB;MACA,IAAIA,eAAe,EAAE;QACnB,OAAO,IAAI;;MAGb;MACA,MAAMC,SAAS,GAAGJ,KAAK,CAACK,GAAG;MAE3B;MACA,OAAO,IAAI,CAACR,MAAM,CAACS,aAAa,CAAC,CAAC,aAAa,CAAC,EAAE;QAChDC,WAAW,EAAE;UAAEH;QAAS;OACzB,CAAC;IACJ,CAAC,CAAC,CACH;EACH;;;uBAtCWV,SAAS,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATnB,SAAS;MAAAoB,OAAA,EAATpB,SAAS,CAAAqB,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}