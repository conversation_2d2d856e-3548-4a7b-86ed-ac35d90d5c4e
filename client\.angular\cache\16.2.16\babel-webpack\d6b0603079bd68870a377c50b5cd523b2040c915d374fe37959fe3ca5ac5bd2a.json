{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Reset Password Page Component\n * Page for resetting password with token\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nexport let ResetPasswordPageComponent = class ResetPasswordPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param route - Activated route for getting route parameters\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, notificationService, route, router, cdr) {\n    this.authService = authService;\n    this.notificationService = notificationService;\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed request\n     */\n    this.error = null;\n    /**\n     * Success message after successful request\n     */\n    this.success = null;\n    /**\n     * Reset token from URL\n     */\n    this.token = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Gets token from URL and checks if user is already authenticated\n   */\n  ngOnInit() {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n      return;\n    }\n    // Get token from query params\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.token = params['token'] || null;\n      this.cdr.markForCheck();\n    });\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Handle reset password form submission\n   * @param data - Form data with password and token\n   */\n  onFormSubmit(data) {\n    this.loading = true;\n    this.error = null;\n    this.success = null;\n    this.cdr.markForCheck();\n    this.authService.resetPassword(data.token, data.password).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.loading = false;\n        this.success = 'Your password has been successfully reset.';\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.message || 'Failed to reset password. The link may have expired.';\n        console.error('Reset password error:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Navigate to login page\n   */\n  onLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n};\nResetPasswordPageComponent = __decorate([Component({\n  selector: 'app-reset-password-page',\n  templateUrl: './reset-password-page.component.html',\n  styleUrls: ['./reset-password-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], ResetPasswordPageComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "Subject", "takeUntil", "ResetPasswordPageComponent", "constructor", "authService", "notificationService", "route", "router", "cdr", "loading", "error", "success", "token", "destroy$", "ngOnInit", "isAuthenticated", "navigate", "queryParams", "pipe", "subscribe", "params", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "next", "complete", "onFormSubmit", "data", "resetPassword", "password", "err", "message", "console", "onLogin", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\reset-password-page\\reset-password-page.component.ts"], "sourcesContent": ["/**\n * Reset Password Page Component\n * Page for resetting password with token\n */\nimport { Component, OnInit, OnD<PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-reset-password-page',\n  templateUrl: './reset-password-page.component.html',\n  styleUrls: ['./reset-password-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ResetPasswordPageComponent implements OnInit, OnDestroy {\n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message from failed request\n   */\n  error: string | null = null;\n  \n  /**\n   * Success message after successful request\n   */\n  success: string | null = null;\n  \n  /**\n   * Reset token from URL\n   */\n  token: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param route - Activated route for getting route parameters\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private notificationService: NotificationService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Gets token from URL and checks if user is already authenticated\n   */\n  ngOnInit(): void {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n      return;\n    }\n    \n    // Get token from query params\n    this.route.queryParams\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(params => {\n        this.token = params['token'] || null;\n        this.cdr.markForCheck();\n      });\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Handle reset password form submission\n   * @param data - Form data with password and token\n   */\n  onFormSubmit(data: { password: string; token: string }): void {\n    this.loading = true;\n    this.error = null;\n    this.success = null;\n    this.cdr.markForCheck();\n    \n    this.authService.resetPassword(data.token, data.password)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.loading = false;\n          this.success = 'Your password has been successfully reset.';\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = err.message || 'Failed to reset password. The link may have expired.';\n          console.error('Reset password error:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Navigate to login page\n   */\n  onLogin(): void {\n    this.router.navigate(['/auth/login']);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AAExG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAUnC,WAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EA0BrC;;;;;;;;EAQAC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,KAAqB,EACrBC,MAAc,EACdC,GAAsB;IAJtB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IAtCb;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGA,KAAAC,OAAO,GAAkB,IAAI;IAE7B;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIb,OAAO,EAAQ;EAgBnC;EAEH;;;;EAIAc,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACV,WAAW,CAACW,eAAe,EAAE,EAAE;MACtC,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MACpC;;IAGF;IACA,IAAI,CAACV,KAAK,CAACW,WAAW,CACnBC,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACY,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAACC,MAAM,IAAG;MAClB,IAAI,CAACR,KAAK,GAAGQ,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI;MACpC,IAAI,CAACZ,GAAG,CAACa,YAAY,EAAE;IACzB,CAAC,CAAC;EACN;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACT,QAAQ,CAACU,IAAI,EAAE;IACpB,IAAI,CAACV,QAAQ,CAACW,QAAQ,EAAE;EAC1B;EAEA;;;;EAIAC,YAAYA,CAACC,IAAyC;IACpD,IAAI,CAACjB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,GAAG,CAACa,YAAY,EAAE;IAEvB,IAAI,CAACjB,WAAW,CAACuB,aAAa,CAACD,IAAI,CAACd,KAAK,EAAEc,IAAI,CAACE,QAAQ,CAAC,CACtDV,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACY,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAAC;MACTI,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACd,OAAO,GAAG,KAAK;QACpB,IAAI,CAACE,OAAO,GAAG,4CAA4C;QAC3D,IAAI,CAACH,GAAG,CAACa,YAAY,EAAE;MACzB,CAAC;MACDX,KAAK,EAAGmB,GAAG,IAAI;QACb,IAAI,CAACpB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGmB,GAAG,CAACC,OAAO,IAAI,sDAAsD;QAClFC,OAAO,CAACrB,KAAK,CAAC,uBAAuB,EAAEmB,GAAG,CAAC;QAC3C,IAAI,CAACrB,GAAG,CAACa,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAW,OAAOA,CAAA;IACL,IAAI,CAACzB,MAAM,CAACS,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;CACD;AAxGYd,0BAA0B,GAAA+B,UAAA,EANtCnC,SAAS,CAAC;EACToC,QAAQ,EAAE,yBAAyB;EACnCC,WAAW,EAAE,sCAAsC;EACnDC,SAAS,EAAE,CAAC,sCAAsC,CAAC;EACnDC,eAAe,EAAEtC,uBAAuB,CAACuC;CAC1C,CAAC,C,EACWpC,0BAA0B,CAwGtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}