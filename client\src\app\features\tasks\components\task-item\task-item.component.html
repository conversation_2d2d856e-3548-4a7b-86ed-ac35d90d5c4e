<!-- Task item container -->
<div class="task-item" (click)="onSelect($event)" (keydown.enter)="onSelect($event)" (keydown.space)="onSelect($event)" tabindex="0" role="button" [ngClass]="getStatusClass()">
  <!-- Status column -->
  <div class="task-column task-status-column">
    <div class="status-indicator" [ngClass]="getStatusClass()" (click)="toggleStatusMenu($event)" (keydown.enter)="toggleStatusMenu($event)" (keydown.space)="toggleStatusMenu($event)" tabindex="0" role="button">
      {{ getStatusLabel() }}
      <mat-icon>arrow_drop_down</mat-icon>
      
      <!-- Status dropdown menu -->
      <div class="status-menu" *ngIf="statusMenuOpen">
        <div 
          *ngFor="let status of statuses" 
          class="status-option" 
          [ngClass]="'status-' + status.value"
          (click)="onStatusChange($event, status.value)" (keydown.enter)="onStatusChange($event, status.value)" (keydown.space)="onStatusChange($event, status.value)" tabindex="0" role="button">
          {{ status.label }}
        </div>
      </div>
    </div>
  </div>
  
  <!-- Title column -->
  <div class="task-column task-title-column">
    <div class="task-title">{{ task.title }}</div>
    <div class="task-description">{{ task.description | truncate:100:true }}</div>
    
    <!-- Tags -->
    <div class="task-tags" *ngIf="task.tags && task.tags.length > 0">
      <span class="task-tag" *ngFor="let tag of task.tags">{{ tag }}</span>
    </div>
  </div>
  
  <!-- Priority column -->
  <div class="task-column task-priority-column">
    <div class="priority-badge" [ngClass]="getPriorityClass()">
      {{ task.priority }}
    </div>
  </div>
  
  <!-- Due date column -->
  <div class="task-column task-date-column">
    <div class="due-date" [ngClass]="{'overdue': isOverdue()}">
      <mat-icon *ngIf="isOverdue()">warning</mat-icon>
      {{ task.dueDate | date:'MMM d' }}
    </div>
  </div>
  
  <!-- Actions column -->
  <div class="task-column task-actions-column">
    <div class="task-actions">
      <button mat-icon-button [routerLink]="['/tasks', task.id, 'edit']" (click)="$event.stopPropagation()">
        <mat-icon>edit</mat-icon>
      </button>
      <button mat-icon-button color="warn" (click)="onDelete($event)">
        <mat-icon>delete</mat-icon>
      </button>
    </div>
  </div>
</div>
