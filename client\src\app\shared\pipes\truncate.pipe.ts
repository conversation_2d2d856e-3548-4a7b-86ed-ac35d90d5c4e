/**
 * Truncate Pipe
 * Truncates text to a specified length and adds ellipsis
 */
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'truncate'
})
export class TruncatePipe implements PipeTransform {
  /**
   * Transform method to truncate text
   * @param value - Input text to truncate
   * @param limit - Maximum length (default: 50)
   * @param completeWords - Whether to preserve complete words (default: false)
   * @param ellipsis - String to append at the end (default: '...')
   * @returns Truncated string
   */
  transform(
    value: string, 
    limit = 50, 
    completeWords = false, 
    ellipsis = '...'
  ): string {
    if (!value) {
      return '';
    }
    
    if (value.length <= limit) {
      return value;
    }

    // Truncate to limit
    let truncatedText = value.substring(0, limit);
    
    // If complete words is true, adjust to not cut words in half
    if (completeWords && truncatedText.lastIndexOf(' ') > 0) {
      truncatedText = truncatedText.substring(0, truncatedText.lastIndexOf(' '));
    }
    
    // Add ellipsis
    return truncatedText + ellipsis;
  }
}
