<div class="profile-container">
  <div class="profile-header">
    <h1>User Profile</h1>
    <button mat-raised-button color="primary" (click)="toggleEditMode()" *ngIf="!isEditMode">
      <mat-icon>edit</mat-icon>
      Edit Profile
    </button>
  </div>

  <!-- Loading spinner -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Profile content -->
  <div class="profile-content" *ngIf="!isLoading && currentUser">
    <!-- View mode -->
    <div class="profile-view" *ngIf="!isEditMode">
      <div class="profile-avatar">
        <div class="avatar-image" [style.backgroundImage]="currentUser.avatarUrl ? 'url(' + currentUser.avatarUrl + ')' : ''">
          <span *ngIf="!currentUser.avatarUrl">{{ currentUser.name.charAt(0) }}</span>
        </div>
      </div>
      
      <div class="profile-details">
        <div class="profile-field">
          <span class="field-label">Name:</span>
          <span class="field-value">{{ currentUser.name }}</span>
        </div>
        
        <div class="profile-field">
          <span class="field-label">Email:</span>
          <span class="field-value">{{ currentUser.email }}</span>
        </div>
        
        <div class="profile-field">
          <span class="field-label">Role:</span>
          <span class="field-value">{{ currentUser.role | titlecase }}</span>
        </div>
        
        <div class="profile-field">
          <span class="field-label">Member Since:</span>
          <span class="field-value">{{ currentUser.createdAt | date:'mediumDate' }}</span>
        </div>
      </div>
    </div>
    
    <!-- Edit mode -->
    <form [formGroup]="profileForm" (ngSubmit)="updateProfile()" class="profile-form" *ngIf="isEditMode">
      <div class="form-field">
        <mat-form-field appearance="outline">
          <mat-label>Name</mat-label>
          <input matInput formControlName="name" placeholder="Your name">
          <mat-error *ngIf="profileForm.get('name')?.hasError('required')">
            Name is required
          </mat-error>
        </mat-form-field>
      </div>
      
      <div class="form-field">
        <mat-form-field appearance="outline">
          <mat-label>Email</mat-label>
          <input matInput formControlName="email" placeholder="Your email">
        </mat-form-field>
      </div>
      
      <div class="form-field">
        <mat-form-field appearance="outline">
          <mat-label>Avatar URL</mat-label>
          <input matInput formControlName="avatarUrl" placeholder="URL to your avatar image">
        </mat-form-field>
      </div>
      
      <div class="form-actions">
        <button mat-button type="button" (click)="toggleEditMode()">
          Cancel
        </button>
        <button mat-raised-button color="primary" type="submit" [disabled]="profileForm.invalid">
          Save Changes
        </button>
      </div>
    </form>
  </div>
</div>
