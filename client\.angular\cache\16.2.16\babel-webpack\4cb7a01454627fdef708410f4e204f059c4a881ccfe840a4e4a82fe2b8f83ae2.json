{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Task Detail Component\n * Displays detailed information about a task\n */\nimport { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';\nexport let TaskDetailComponent = class TaskDetailComponent {\n  constructor() {\n    /**\n     * Input list of users for assignee selection\n     */\n    this.users = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Event emitted when edit button is clicked\n     */\n    this.edit = new EventEmitter();\n    /**\n     * Event emitted when delete button is clicked\n     */\n    this.delete = new EventEmitter();\n    /**\n     * Event emitted when status is changed\n     */\n    this.statusChange = new EventEmitter();\n    /**\n     * Event emitted when assignee is changed\n     */\n    this.assigneeChange = new EventEmitter();\n    /**\n     * Available task statuses\n     */\n    this.statuses = [{\n      value: 'todo',\n      label: 'To Do'\n    }, {\n      value: 'in_progress',\n      label: 'In Progress'\n    }, {\n      value: 'review',\n      label: 'Review'\n    }, {\n      value: 'done',\n      label: 'Done'\n    }];\n  }\n  /**\n   * Get CSS class for priority\n   * @returns CSS class name\n   */\n  getPriorityClass() {\n    return `priority-${this.task.priority}`;\n  }\n  /**\n   * Get CSS class for status\n   * @returns CSS class name\n   */\n  getStatusClass() {\n    return `status-${this.task.status}`;\n  }\n  /**\n   * Get formatted status label\n   * @returns Status label\n   */\n  getStatusLabel() {\n    const status = this.statuses.find(s => s.value === this.task.status);\n    return status ? status.label : this.task.status;\n  }\n  /**\n   * Handle status change\n   * @param status - New status\n   */\n  onStatusChange(status) {\n    this.statusChange.emit(status);\n  }\n  /**\n   * Handle assignee change\n   * @param assigneeId - New assignee ID\n   */\n  onAssigneeChange(assigneeId) {\n    this.assigneeChange.emit(assigneeId);\n  }\n  /**\n   * Handle edit button click\n   */\n  onEdit() {\n    this.edit.emit();\n  }\n  /**\n   * Handle delete button click\n   */\n  onDelete() {\n    this.delete.emit();\n  }\n  /**\n   * Check if task is overdue\n   * @returns True if task is overdue\n   */\n  isOverdue() {\n    if (!this.task.dueDate || this.task.status === 'done') {\n      return false;\n    }\n    const dueDate = new Date(this.task.dueDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    return dueDate < today;\n  }\n  /**\n   * Get assignee name\n   * @returns Assignee name or 'Unassigned'\n   */\n  getAssigneeName() {\n    if (!this.task.assignee) {\n      return 'Unassigned';\n    }\n    const user = this.users.find(u => u.id === this.task.assignee);\n    return user ? user.name : 'Unknown User';\n  }\n  /**\n   * Get creator name\n   * @returns Creator name or 'Unknown'\n   */\n  getCreatorName() {\n    if (!this.task.creator) {\n      return 'Unknown';\n    }\n    const user = this.users.find(u => u.id === this.task.creator);\n    return user ? user.name : 'Unknown User';\n  }\n};\n__decorate([Input()], TaskDetailComponent.prototype, \"task\", void 0);\n__decorate([Input()], TaskDetailComponent.prototype, \"users\", void 0);\n__decorate([Input()], TaskDetailComponent.prototype, \"loading\", void 0);\n__decorate([Output()], TaskDetailComponent.prototype, \"edit\", void 0);\n__decorate([Output()], TaskDetailComponent.prototype, \"delete\", void 0);\n__decorate([Output()], TaskDetailComponent.prototype, \"statusChange\", void 0);\n__decorate([Output()], TaskDetailComponent.prototype, \"assigneeChange\", void 0);\nTaskDetailComponent = __decorate([Component({\n  selector: 'app-task-detail',\n  templateUrl: './task-detail.component.html',\n  styleUrls: ['./task-detail.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskDetailComponent);", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "ChangeDetectionStrategy", "TaskDetailComponent", "constructor", "users", "loading", "edit", "delete", "statusChange", "assignee<PERSON><PERSON><PERSON>", "statuses", "value", "label", "getPriorityClass", "task", "priority", "getStatusClass", "status", "getStatusLabel", "find", "s", "onStatusChange", "emit", "onAssigneeChange", "assigneeId", "onEdit", "onDelete", "isOverdue", "dueDate", "Date", "today", "setHours", "getAs<PERSON>eeName", "assignee", "user", "u", "id", "name", "getCreatorName", "creator", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\components\\task-detail\\task-detail.component.ts"], "sourcesContent": ["/**\n * Task Detail Component\n * Displays detailed information about a task\n */\nimport { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-task-detail',\n  templateUrl: './task-detail.component.html',\n  styleUrls: ['./task-detail.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskDetailComponent {\n  /**\n   * Input task to display\n   */\n  @Input() task!: Task;\n  \n  /**\n   * Input list of users for assignee selection\n   */\n  @Input() users: User[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Event emitted when edit button is clicked\n   */\n  @Output() edit = new EventEmitter<void>();\n  \n  /**\n   * Event emitted when delete button is clicked\n   */\n  @Output() delete = new EventEmitter<void>();\n  \n  /**\n   * Event emitted when status is changed\n   */\n  @Output() statusChange = new EventEmitter<string>();\n  \n  /**\n   * Event emitted when assignee is changed\n   */\n  @Output() assigneeChange = new EventEmitter<string>();\n  \n  /**\n   * Available task statuses\n   */\n  statuses = [\n    { value: 'todo', label: 'To Do' },\n    { value: 'in_progress', label: 'In Progress' },\n    { value: 'review', label: 'Review' },\n    { value: 'done', label: 'Done' }\n  ];\n\n  /**\n   * Get CSS class for priority\n   * @returns CSS class name\n   */\n  getPriorityClass(): string {\n    return `priority-${this.task.priority}`;\n  }\n\n  /**\n   * Get CSS class for status\n   * @returns CSS class name\n   */\n  getStatusClass(): string {\n    return `status-${this.task.status}`;\n  }\n\n  /**\n   * Get formatted status label\n   * @returns Status label\n   */\n  getStatusLabel(): string {\n    const status = this.statuses.find(s => s.value === this.task.status);\n    return status ? status.label : this.task.status;\n  }\n\n  /**\n   * Handle status change\n   * @param status - New status\n   */\n  onStatusChange(status: string): void {\n    this.statusChange.emit(status);\n  }\n\n  /**\n   * Handle assignee change\n   * @param assigneeId - New assignee ID\n   */\n  onAssigneeChange(assigneeId: string): void {\n    this.assigneeChange.emit(assigneeId);\n  }\n\n  /**\n   * Handle edit button click\n   */\n  onEdit(): void {\n    this.edit.emit();\n  }\n\n  /**\n   * Handle delete button click\n   */\n  onDelete(): void {\n    this.delete.emit();\n  }\n\n  /**\n   * Check if task is overdue\n   * @returns True if task is overdue\n   */\n  isOverdue(): boolean {\n    if (!this.task.dueDate || this.task.status === 'done') {\n      return false;\n    }\n    \n    const dueDate = new Date(this.task.dueDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    \n    return dueDate < today;\n  }\n\n  /**\n   * Get assignee name\n   * @returns Assignee name or 'Unassigned'\n   */\n  getAssigneeName(): string {\n    if (!this.task.assignee) {\n      return 'Unassigned';\n    }\n    \n    const user = this.users.find(u => u.id === this.task.assignee);\n    return user ? user.name : 'Unknown User';\n  }\n\n  /**\n   * Get creator name\n   * @returns Creator name or 'Unknown'\n   */\n  getCreatorName(): string {\n    if (!this.task.creator) {\n      return 'Unknown';\n    }\n    \n    const user = this.users.find(u => u.id === this.task.creator);\n    return user ? user.name : 'Unknown User';\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,uBAAuB,QAAQ,eAAe;AAUxF,WAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAAzBC,YAAA;IAML;;;IAGS,KAAAC,KAAK,GAAW,EAAE;IAE3B;;;IAGS,KAAAC,OAAO,GAAG,KAAK;IAExB;;;IAGU,KAAAC,IAAI,GAAG,IAAIN,YAAY,EAAQ;IAEzC;;;IAGU,KAAAO,MAAM,GAAG,IAAIP,YAAY,EAAQ;IAE3C;;;IAGU,KAAAQ,YAAY,GAAG,IAAIR,YAAY,EAAU;IAEnD;;;IAGU,KAAAS,cAAc,GAAG,IAAIT,YAAY,EAAU;IAErD;;;IAGA,KAAAU,QAAQ,GAAG,CACT;MAAEC,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAE,EACjC;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAa,CAAE,EAC9C;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAM,CAAE,CACjC;EAkGH;EAhGE;;;;EAIAC,gBAAgBA,CAAA;IACd,OAAO,YAAY,IAAI,CAACC,IAAI,CAACC,QAAQ,EAAE;EACzC;EAEA;;;;EAIAC,cAAcA,CAAA;IACZ,OAAO,UAAU,IAAI,CAACF,IAAI,CAACG,MAAM,EAAE;EACrC;EAEA;;;;EAIAC,cAAcA,CAAA;IACZ,MAAMD,MAAM,GAAG,IAAI,CAACP,QAAQ,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACT,KAAK,KAAK,IAAI,CAACG,IAAI,CAACG,MAAM,CAAC;IACpE,OAAOA,MAAM,GAAGA,MAAM,CAACL,KAAK,GAAG,IAAI,CAACE,IAAI,CAACG,MAAM;EACjD;EAEA;;;;EAIAI,cAAcA,CAACJ,MAAc;IAC3B,IAAI,CAACT,YAAY,CAACc,IAAI,CAACL,MAAM,CAAC;EAChC;EAEA;;;;EAIAM,gBAAgBA,CAACC,UAAkB;IACjC,IAAI,CAACf,cAAc,CAACa,IAAI,CAACE,UAAU,CAAC;EACtC;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAI,CAACnB,IAAI,CAACgB,IAAI,EAAE;EAClB;EAEA;;;EAGAI,QAAQA,CAAA;IACN,IAAI,CAACnB,MAAM,CAACe,IAAI,EAAE;EACpB;EAEA;;;;EAIAK,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACb,IAAI,CAACc,OAAO,IAAI,IAAI,CAACd,IAAI,CAACG,MAAM,KAAK,MAAM,EAAE;MACrD,OAAO,KAAK;;IAGd,MAAMW,OAAO,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACf,IAAI,CAACc,OAAO,CAAC;IAC3C,MAAME,KAAK,GAAG,IAAID,IAAI,EAAE;IACxBC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,OAAOH,OAAO,GAAGE,KAAK;EACxB;EAEA;;;;EAIAE,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAClB,IAAI,CAACmB,QAAQ,EAAE;MACvB,OAAO,YAAY;;IAGrB,MAAMC,IAAI,GAAG,IAAI,CAAC9B,KAAK,CAACe,IAAI,CAACgB,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAK,IAAI,CAACtB,IAAI,CAACmB,QAAQ,CAAC;IAC9D,OAAOC,IAAI,GAAGA,IAAI,CAACG,IAAI,GAAG,cAAc;EAC1C;EAEA;;;;EAIAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACxB,IAAI,CAACyB,OAAO,EAAE;MACtB,OAAO,SAAS;;IAGlB,MAAML,IAAI,GAAG,IAAI,CAAC9B,KAAK,CAACe,IAAI,CAACgB,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAK,IAAI,CAACtB,IAAI,CAACyB,OAAO,CAAC;IAC7D,OAAOL,IAAI,GAAGA,IAAI,CAACG,IAAI,GAAG,cAAc;EAC1C;CACD;AA1IUG,UAAA,EAAR1C,KAAK,EAAE,C,gDAAa;AAKZ0C,UAAA,EAAR1C,KAAK,EAAE,C,iDAAoB;AAKnB0C,UAAA,EAAR1C,KAAK,EAAE,C,mDAAiB;AAKf0C,UAAA,EAATzC,MAAM,EAAE,C,gDAAiC;AAKhCyC,UAAA,EAATzC,MAAM,EAAE,C,kDAAmC;AAKlCyC,UAAA,EAATzC,MAAM,EAAE,C,wDAA2C;AAK1CyC,UAAA,EAATzC,MAAM,EAAE,C,0DAA6C;AAlC3CG,mBAAmB,GAAAsC,UAAA,EAN/B3C,SAAS,CAAC;EACT4C,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,8BAA8B,CAAC;EAC3CC,eAAe,EAAE3C,uBAAuB,CAAC4C;CAC1C,CAAC,C,EACW3C,mBAAmB,CA8I/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}