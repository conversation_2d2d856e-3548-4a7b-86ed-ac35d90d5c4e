/**
 * Recent tasks component styles
 */

/* Container for recent tasks */
.recent-tasks-container {
  width: 100%;
  overflow-x: auto;
}

/* Tasks table */
.tasks-table {
  width: 100%;
  
  th.mat-header-cell {
    font-weight: 500;
    color: #333;
  }
  
  .mat-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

/* Priority badge styles */
.priority-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

/* Priority-specific styles */
.priority-high {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.priority-medium {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.priority-low {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

/* Status badge styles */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  min-width: 80px;
}

/* Status-specific styles */
.status-todo {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.status-in-progress {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.status-done {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

/* Empty state styles */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: #9e9e9e;
  
  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 1rem;
    margin: 0;
  }
}

/* View all tasks link container */
.view-all-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  
  a {
    display: flex;
    align-items: center;
    
    mat-icon {
      margin-left: 4px;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .tasks-table {
    .mat-header-row, .mat-row {
      padding: 0 8px;
    }
    
    .mat-cell, .mat-header-cell {
      padding: 8px 4px;
    }
  }
}
