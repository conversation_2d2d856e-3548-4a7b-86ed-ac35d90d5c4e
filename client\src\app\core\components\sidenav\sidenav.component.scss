/**
 * Sidenav component styles
 */

/* Main sidenav container */
.sidenav {
  width: 64px;
  height: 100%;
  background-color: #fff;
  box-shadow: 1px 0 5px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  /* Expanded state */
  &.expanded {
    width: 240px;
  }
}

/* Toggle button container */
.sidenav-toggle {
  display: flex;
  justify-content: flex-end;
  padding: 8px;
  
  button {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

/* Navigation section */
.sidenav-nav {
  flex: 1;
  overflow-y: auto;
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
  
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Navigation list */
.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Navigation item */
.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  &.active {
    background-color: rgba(63, 81, 181, 0.1);
    color: #3f51b5;
    border-left: 3px solid #3f51b5;
  }
}

/* Navigation icon */
.nav-icon {
  margin-right: 16px;
}

/* Navigation label */
.nav-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Footer section */
.sidenav-footer {
  padding: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Profile link in footer */
.profile-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
  
  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #3f51b5;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    background-size: cover;
    background-position: center;
    margin-right: 12px;
  }
  
  .user-info {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .user-name {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .user-role {
    font-size: 0.8rem;
    color: #666;
    text-transform: capitalize;
  }
}
