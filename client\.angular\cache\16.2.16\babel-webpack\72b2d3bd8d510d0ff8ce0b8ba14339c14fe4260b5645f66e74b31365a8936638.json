{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Auth Module\n * Feature module for authentication functionality\n */\nimport { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n// Components\nimport { LoginFormComponent } from './components/login-form/login-form.component';\nimport { RegisterFormComponent } from './components/register-form/register-form.component';\nimport { ForgotPasswordFormComponent } from './components/forgot-password-form/forgot-password-form.component';\nimport { ResetPasswordFormComponent } from './components/reset-password-form/reset-password-form.component';\n// Pages\nimport { LoginPageComponent } from './pages/login-page/login-page.component';\nimport { RegisterPageComponent } from './pages/register-page/register-page.component';\nimport { ForgotPasswordPageComponent } from './pages/forgot-password-page/forgot-password-page.component';\nimport { ResetPasswordPageComponent } from './pages/reset-password-page/reset-password-page.component';\n/**\n * Routes for the auth feature module\n */\nconst routes = [{\n  path: 'login',\n  component: LoginPageComponent\n}, {\n  path: 'register',\n  component: RegisterPageComponent\n}, {\n  path: 'forgot-password',\n  component: ForgotPasswordPageComponent\n}, {\n  path: 'reset-password',\n  component: ResetPasswordPageComponent\n}, {\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}];\nexport let AuthModule = class AuthModule {};\nAuthModule = __decorate([NgModule({\n  declarations: [LoginFormComponent, RegisterFormComponent, ForgotPasswordFormComponent, ResetPasswordFormComponent, LoginPageComponent, RegisterPageComponent, ForgotPasswordPageComponent, ResetPasswordPageComponent],\n  imports: [SharedModule, RouterModule.forChild(routes)]\n})], AuthModule);", "map": {"version": 3, "names": ["NgModule", "RouterModule", "SharedModule", "LoginFormComponent", "RegisterFormComponent", "ForgotPasswordFormComponent", "ResetPasswordFormComponent", "LoginPageComponent", "RegisterPageComponent", "ForgotPasswordPageComponent", "ResetPasswordPageComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AuthModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\auth.module.ts"], "sourcesContent": ["/**\n * Auth Module\n * Feature module for authentication functionality\n */\nimport { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\n\n// Components\nimport { LoginFormComponent } from './components/login-form/login-form.component';\nimport { RegisterFormComponent } from './components/register-form/register-form.component';\nimport { ForgotPasswordFormComponent } from './components/forgot-password-form/forgot-password-form.component';\nimport { ResetPasswordFormComponent } from './components/reset-password-form/reset-password-form.component';\n\n// Pages\nimport { LoginPageComponent } from './pages/login-page/login-page.component';\nimport { RegisterPageComponent } from './pages/register-page/register-page.component';\nimport { ForgotPasswordPageComponent } from './pages/forgot-password-page/forgot-password-page.component';\nimport { ResetPasswordPageComponent } from './pages/reset-password-page/reset-password-page.component';\n\n/**\n * Routes for the auth feature module\n */\nconst routes: Routes = [\n  {\n    path: 'login',\n    component: LoginPageComponent\n  },\n  {\n    path: 'register',\n    component: RegisterPageComponent\n  },\n  {\n    path: 'forgot-password',\n    component: ForgotPasswordPageComponent\n  },\n  {\n    path: 'reset-password',\n    component: ResetPasswordPageComponent\n  },\n  {\n    path: '',\n    redirectTo: 'login',\n    pathMatch: 'full'\n  }\n];\n\n@NgModule({\n  declarations: [\n    LoginFormComponent,\n    RegisterFormComponent,\n    ForgotPasswordFormComponent,\n    ResetPasswordFormComponent,\n    LoginPageComponent,\n    RegisterPageComponent,\n    ForgotPasswordPageComponent,\n    ResetPasswordPageComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class AuthModule { }\n"], "mappings": ";AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AAEzD;AACA,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,2BAA2B,QAAQ,kEAAkE;AAC9G,SAASC,0BAA0B,QAAQ,gEAAgE;AAE3G;AACA,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,2BAA2B,QAAQ,6DAA6D;AACzG,SAASC,0BAA0B,QAAQ,2DAA2D;AAEtG;;;AAGA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,EAAE;EACRE,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,CACF;AAkBM,WAAMC,UAAU,GAAhB,MAAMA,UAAU,GAAI;AAAdA,UAAU,GAAAC,UAAA,EAhBtBjB,QAAQ,CAAC;EACRkB,YAAY,EAAE,CACZf,kBAAkB,EAClBC,qBAAqB,EACrBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,kBAAkB,EAClBC,qBAAqB,EACrBC,2BAA2B,EAC3BC,0BAA0B,CAC3B;EACDS,OAAO,EAAE,CACPjB,YAAY,EACZD,YAAY,CAACmB,QAAQ,CAACT,MAAM,CAAC;CAEhC,CAAC,C,EACWK,UAAU,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}