/**
 * Register form component styles
 */

/* Container for the entire register form */
.register-form-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 24px;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

/* Register form */
.register-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Full width form fields */
.full-width {
  width: 100%;
}

/* Terms and conditions container */
.terms-container {
  margin-top: 8px;
  
  a {
    color: var(--primary-color);
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

/* Terms error message */
.terms-error {
  font-size: 0.75rem;
  margin-top: 4px;
  color: var(--warn-color);
}

/* Submit button */
.submit-button {
  height: 48px;
  font-size: 1rem;
  margin-top: 8px;
  
  mat-spinner {
    display: inline-block;
    margin: 0 auto;
  }
}

/* Login link container */
.login-link {
  text-align: center;
  margin-top: 16px;
  font-size: 0.9rem;
  
  a {
    color: var(--primary-color);
    cursor: pointer;
    margin-left: 4px;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .register-form-container {
    padding: 16px;
    box-shadow: none;
  }
}
