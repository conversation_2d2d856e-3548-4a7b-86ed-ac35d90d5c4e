/**
 * Login Use Case
 * Handles business logic for user authentication
 */
import { IUserRepository } from '../../repositories/IUserRepository';
import { AppError } from '../../common/AppError';
import { IUserDocument } from '../../entities/User';

/**
 * Interface for login credentials
 */
export interface LoginCredentials {
  email: string;
  password: string;
}

/**
 * Interface for login response
 */
export interface LoginResponse {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  token: string;
}

/**
 * Use case for user login
 * Implements business rules and validation for authentication
 */
export class LoginUseCase {
  /**
   * Constructor for LoginUseCase
   * @param userRepository - Repository for user data access
   */
  constructor(private userRepository: IUserRepository) {}

  /**
   * Executes the use case to authenticate a user
   * @param credentials - User login credentials
   * @returns Promise resolving to login response with user data and token
   * @throws AppError if authentication fails
   */
  async execute(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Validate required fields
      if (!credentials.email) {
        throw new AppError('Email is required', 400);
      }

      if (!credentials.password) {
        throw new AppError('Password is required', 400);
      }

      // Find user by email
      const user = await this.userRepository.findByEmail(credentials.email);
      
      // Check if user exists
      if (!user) {
        throw new AppError('Invalid email or password', 401);
      }
      
      // Check if password matches
      const isPasswordValid = await user.comparePassword(credentials.password);
      
      if (!isPasswordValid) {
        throw new AppError('Invalid email or password', 401);
      }
      
      // Generate authentication token
      const token = user.generateAuthToken();
      
      // Return user data and token
      return {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
        },
        token,
      };
    } catch (error) {
      // Rethrow AppErrors, wrap other errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Authentication failed: ${(error as Error).message}`, 500);
    }
  }
}
