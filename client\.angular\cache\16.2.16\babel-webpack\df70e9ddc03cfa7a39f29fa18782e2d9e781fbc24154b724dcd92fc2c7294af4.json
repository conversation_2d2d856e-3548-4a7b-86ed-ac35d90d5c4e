{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TruncatePipe {\n  /**\n   * Transform method to truncate text\n   * @param value - Input text to truncate\n   * @param limit - Maximum length (default: 50)\n   * @param completeWords - Whether to preserve complete words (default: false)\n   * @param ellipsis - String to append at the end (default: '...')\n   * @returns Truncated string\n   */\n  transform(value, limit = 50, completeWords = false, ellipsis = '...') {\n    if (!value) {\n      return '';\n    }\n    if (value.length <= limit) {\n      return value;\n    }\n    // Truncate to limit\n    let truncatedText = value.substring(0, limit);\n    // If complete words is true, adjust to not cut words in half\n    if (completeWords && truncatedText.lastIndexOf(' ') > 0) {\n      truncatedText = truncatedText.substring(0, truncatedText.lastIndexOf(' '));\n    }\n    // Add ellipsis\n    return truncatedText + ellipsis;\n  }\n  static {\n    this.ɵfac = function TruncatePipe_Factory(t) {\n      return new (t || TruncatePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"truncate\",\n      type: TruncatePipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["TruncatePipe", "transform", "value", "limit", "completeWords", "ellipsis", "length", "truncatedText", "substring", "lastIndexOf", "pure"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\pipes\\truncate.pipe.ts"], "sourcesContent": ["/**\n * Truncate Pipe\n * Truncates text to a specified length and adds ellipsis\n */\nimport { Pi<PERSON> } from '@angular/core';\n\n@Pipe({\n  name: 'truncate'\n})\nexport class TruncatePipe {\n  /**\n   * Transform method to truncate text\n   * @param value - Input text to truncate\n   * @param limit - Maximum length (default: 50)\n   * @param completeWords - Whether to preserve complete words (default: false)\n   * @param ellipsis - String to append at the end (default: '...')\n   * @returns Truncated string\n   */\n  transform(\n    value: string, \n    limit = 50, \n    completeWords = false, \n    ellipsis = '...'\n  ): string {\n    if (!value) {\n      return '';\n    }\n    \n    if (value.length <= limit) {\n      return value;\n    }\n\n    // Truncate to limit\n    let truncatedText = value.substring(0, limit);\n    \n    // If complete words is true, adjust to not cut words in half\n    if (completeWords && truncatedText.lastIndexOf(' ') > 0) {\n      truncatedText = truncatedText.substring(0, truncatedText.lastIndexOf(' '));\n    }\n    \n    // Add ellipsis\n    return truncatedText + ellipsis;\n  }\n}\n"], "mappings": ";AASA,OAAM,MAAOA,YAAY;EACvB;;;;;;;;EAQAC,SAASA,CACPC,KAAa,EACbC,KAAK,GAAG,EAAE,EACVC,aAAa,GAAG,KAAK,EACrBC,QAAQ,GAAG,KAAK;IAEhB,IAAI,CAACH,KAAK,EAAE;MACV,OAAO,EAAE;;IAGX,IAAIA,KAAK,CAACI,MAAM,IAAIH,KAAK,EAAE;MACzB,OAAOD,KAAK;;IAGd;IACA,IAAIK,aAAa,GAAGL,KAAK,CAACM,SAAS,CAAC,CAAC,EAAEL,KAAK,CAAC;IAE7C;IACA,IAAIC,aAAa,IAAIG,aAAa,CAACE,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACvDF,aAAa,GAAGA,aAAa,CAACC,SAAS,CAAC,CAAC,EAAED,aAAa,CAACE,WAAW,CAAC,GAAG,CAAC,CAAC;;IAG5E;IACA,OAAOF,aAAa,GAAGF,QAAQ;EACjC;;;uBAjCWL,YAAY;IAAA;EAAA;;;;YAAZA,YAAY;MAAAU,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}