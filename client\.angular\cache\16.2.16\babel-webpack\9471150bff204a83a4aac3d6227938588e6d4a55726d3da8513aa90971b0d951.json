{"ast": null, "code": "import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function mergeScan(accumulator, seed, concurrent = Infinity) {\n  return operate((source, subscriber) => {\n    let state = seed;\n    return mergeInternals(source, subscriber, (value, index) => accumulator(state, value, index), concurrent, value => {\n      state = value;\n    }, false, undefined, () => state = null);\n  });\n}", "map": {"version": 3, "names": ["operate", "mergeInternals", "mergeScan", "accumulator", "seed", "concurrent", "Infinity", "source", "subscriber", "state", "value", "index", "undefined"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/esm/internal/operators/mergeScan.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function mergeScan(accumulator, seed, concurrent = Infinity) {\n    return operate((source, subscriber) => {\n        let state = seed;\n        return mergeInternals(source, subscriber, (value, index) => accumulator(state, value, index), concurrent, (value) => {\n            state = value;\n        }, false, undefined, () => (state = null));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,SAASC,SAASA,CAACC,WAAW,EAAEC,IAAI,EAAEC,UAAU,GAAGC,QAAQ,EAAE;EAChE,OAAON,OAAO,CAAC,CAACO,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,KAAK,GAAGL,IAAI;IAChB,OAAOH,cAAc,CAACM,MAAM,EAAEC,UAAU,EAAE,CAACE,KAAK,EAAEC,KAAK,KAAKR,WAAW,CAACM,KAAK,EAAEC,KAAK,EAAEC,KAAK,CAAC,EAAEN,UAAU,EAAGK,KAAK,IAAK;MACjHD,KAAK,GAAGC,KAAK;IACjB,CAAC,EAAE,KAAK,EAAEE,SAAS,EAAE,MAAOH,KAAK,GAAG,IAAK,CAAC;EAC9C,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}