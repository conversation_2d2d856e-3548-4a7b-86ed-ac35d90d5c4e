{"ast": null, "code": "import { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class CustomPreloadingStrategy {\n  /**\n   * Determines which routes to preload based on data.preload property\n   * @param route - Route configuration\n   * @param load - Function to load the module\n   * @returns Observable that completes when preloading is done\n   */\n  preload(route, load) {\n    // Check if the route has data and preload flag is set to true\n    if (route.data && route.data['preload']) {\n      // Log which route is being preloaded\n      console.log('Preloaded:', route.path);\n      // Return the loading Observable\n      return load();\n    } else {\n      // Skip preloading\n      return of(null);\n    }\n  }\n  static {\n    this.ɵfac = function CustomPreloadingStrategy_Factory(t) {\n      return new (t || CustomPreloadingStrategy)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CustomPreloadingStrategy,\n      factory: CustomPreloadingStrategy.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "CustomPreloadingStrategy", "preload", "route", "load", "data", "console", "log", "path", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\strategies\\custom-preloading.strategy.ts"], "sourcesContent": ["/**\n * Custom Preloading Strategy\n * Allows selective preloading of Angular modules based on data in route configuration\n */\nimport { Injectable } from '@angular/core';\nimport { PreloadingStrategy, Route } from '@angular/router';\nimport { Observable, of } from 'rxjs';\nimport { mergeMap } from 'rxjs/operators';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CustomPreloadingStrategy implements PreloadingStrategy {\n  /**\n   * Determines which routes to preload based on data.preload property\n   * @param route - Route configuration\n   * @param load - Function to load the module\n   * @returns Observable that completes when preloading is done\n   */\n  preload(route: Route, load: () => Observable<any>): Observable<any> {\n    // Check if the route has data and preload flag is set to true\n    if (route.data && route.data['preload']) {\n      // Log which route is being preloaded\n      console.log('Preloaded:', route.path);\n      \n      // Return the loading Observable\n      return load();\n    } else {\n      // Skip preloading\n      return of(null);\n    }\n  }\n}\n"], "mappings": "AAMA,SAAqBA,EAAE,QAAQ,MAAM;;AAMrC,OAAM,MAAOC,wBAAwB;EACnC;;;;;;EAMAC,OAAOA,CAACC,KAAY,EAAEC,IAA2B;IAC/C;IACA,IAAID,KAAK,CAACE,IAAI,IAAIF,KAAK,CAACE,IAAI,CAAC,SAAS,CAAC,EAAE;MACvC;MACAC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEJ,KAAK,CAACK,IAAI,CAAC;MAErC;MACA,OAAOJ,IAAI,EAAE;KACd,MAAM;MACL;MACA,OAAOJ,EAAE,CAAC,IAAI,CAAC;;EAEnB;;;uBAnBWC,wBAAwB;IAAA;EAAA;;;aAAxBA,wBAAwB;MAAAQ,OAAA,EAAxBR,wBAAwB,CAAAS,IAAA;MAAAC,UAAA,EAFvB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}