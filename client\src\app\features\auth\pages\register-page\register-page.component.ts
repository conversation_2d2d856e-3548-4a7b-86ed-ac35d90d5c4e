/**
 * Register Page Component
 * Page for user registration
 */
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AuthService } from '../../../../core/services/auth.service';
import { NotificationService } from '../../../../core/services/notification.service';

@Component({
  selector: 'app-register-page',
  templateUrl: './register-page.component.html',
  styleUrls: ['./register-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RegisterPageComponent implements OnInit, OnDestroy {
  /**
   * Loading state indicator
   */
  loading = false;
  
  /**
   * Error message from failed registration attempt
   */
  error: string | null = null;
  
  /**
   * Subject for unsubscribing from observables
   */
  private destroy$ = new Subject<void>();

  /**
   * Constructor with dependency injection
   * @param authService - Authentication service
   * @param notificationService - Notification service for displaying messages
   * @param router - Router for navigation
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(
    private authService: AuthService,
    private notificationService: NotificationService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Checks if user is already authenticated
   */
  ngOnInit(): void {
    // Redirect to dashboard if already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
    }
  }

  /**
   * Lifecycle hook that is called before component destruction
   * Unsubscribes from observables
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Handle register form submission
   * @param userData - User registration data
   */
  onFormSubmit(userData: { name: string; email: string; password: string }): void {
    this.loading = true;
    this.error = null;
    this.cdr.markForCheck();
    
    this.authService.register(userData.name, userData.email, userData.password)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.loading = false;
          this.notificationService.success('Registration successful! Please log in.');
          this.router.navigate(['/auth/login']);
        },
        error: (err) => {
          this.loading = false;
          this.error = err.message || 'Registration failed. Please try again.';
          console.error('Registration error:', err);
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Navigate to login page
   */
  onLogin(): void {
    this.router.navigate(['/auth/login']);
  }
}
