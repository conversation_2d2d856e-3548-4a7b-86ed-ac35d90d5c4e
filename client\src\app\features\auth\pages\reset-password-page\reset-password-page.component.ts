/**
 * Reset Password Page Component
 * Page for resetting password with token
 */
import { Component, OnInit, OnD<PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AuthService } from '../../../../core/services/auth.service';
import { NotificationService } from '../../../../core/services/notification.service';

@Component({
  selector: 'app-reset-password-page',
  templateUrl: './reset-password-page.component.html',
  styleUrls: ['./reset-password-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ResetPasswordPageComponent implements OnInit, OnDestroy {
  /**
   * Loading state indicator
   */
  loading = false;
  
  /**
   * Error message from failed request
   */
  error: string | null = null;
  
  /**
   * Success message after successful request
   */
  success: string | null = null;
  
  /**
   * Reset token from URL
   */
  token: string | null = null;
  
  /**
   * Subject for unsubscribing from observables
   */
  private destroy$ = new Subject<void>();

  /**
   * Constructor with dependency injection
   * @param authService - Authentication service
   * @param notificationService - Notification service for displaying messages
   * @param route - Activated route for getting route parameters
   * @param router - Router for navigation
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(
    private authService: AuthService,
    private notificationService: NotificationService,
    private route: ActivatedRoute,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Gets token from URL and checks if user is already authenticated
   */
  ngOnInit(): void {
    // Redirect to dashboard if already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
      return;
    }
    
    // Get token from query params
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        this.token = params['token'] || null;
        this.cdr.markForCheck();
      });
  }

  /**
   * Lifecycle hook that is called before component destruction
   * Unsubscribes from observables
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Handle reset password form submission
   * @param data - Form data with password and token
   */
  onFormSubmit(data: { password: string; token: string }): void {
    this.loading = true;
    this.error = null;
    this.success = null;
    this.cdr.markForCheck();
    
    this.authService.resetPassword(data.token, data.password)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.loading = false;
          this.success = 'Your password has been successfully reset.';
          this.cdr.markForCheck();
        },
        error: (err) => {
          this.loading = false;
          this.error = err.message || 'Failed to reset password. The link may have expired.';
          console.error('Reset password error:', err);
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Navigate to login page
   */
  onLogin(): void {
    this.router.navigate(['/auth/login']);
  }
}
