/**
 * API Response model interface
 * Represents the standard response format from the API
 */
export interface ApiResponse<T> {
  /**
   * Indicates if the request was successful
   */
  success: boolean;
  
  /**
   * Response data of generic type T
   */
  data: T;
  
  /**
   * Optional message from the server
   */
  message?: string;
  
  /**
   * Optional error details
   */
  error?: {
    /**
     * Error code
     */
    code: string;
    
    /**
     * Detailed error message
     */
    details?: string;
  };
}
