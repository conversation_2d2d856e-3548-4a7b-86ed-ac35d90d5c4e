{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { DashboardPageComponent } from './pages/dashboard-page/dashboard-page.component';\nimport { TaskSummaryComponent } from './components/task-summary/task-summary.component';\nimport { RecentTasksComponent } from './components/recent-tasks/recent-tasks.component';\nimport { TaskStatusChartComponent } from './components/task-status-chart/task-status-chart.component';\nimport { TaskPriorityChartComponent } from './components/task-priority-chart/task-priority-chart.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n// Dashboard routes\nconst routes = [{\n  path: '',\n  component: DashboardPageComponent\n}];\nexport class DashboardModule {\n  static {\n    this.ɵfac = function DashboardModule_Factory(t) {\n      return new (t || DashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardModule, {\n    declarations: [DashboardPageComponent, TaskSummaryComponent, RecentTasksComponent, TaskStatusChartComponent, TaskPriorityChartComponent],\n    imports: [CommonModule, SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "SharedModule", "DashboardPageComponent", "TaskSummaryComponent", "RecentTasksComponent", "TaskStatusChartComponent", "TaskPriorityChartComponent", "routes", "path", "component", "DashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\dashboard.module.ts"], "sourcesContent": ["/**\n * Dashboard Module\n * Contains components for the dashboard feature\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { DashboardPageComponent } from './pages/dashboard-page/dashboard-page.component';\nimport { TaskSummaryComponent } from './components/task-summary/task-summary.component';\nimport { RecentTasksComponent } from './components/recent-tasks/recent-tasks.component';\nimport { TaskStatusChartComponent } from './components/task-status-chart/task-status-chart.component';\nimport { TaskPriorityChartComponent } from './components/task-priority-chart/task-priority-chart.component';\n\n// Dashboard routes\nconst routes: Routes = [\n  {\n    path: '',\n    component: DashboardPageComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    DashboardPageComponent,\n    TaskSummaryComponent,\n    RecentTasksComponent,\n    TaskStatusChartComponent,\n    TaskPriorityChartComponent\n  ],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class DashboardModule { }\n"], "mappings": "AAKA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,sBAAsB,QAAQ,iDAAiD;AACxF,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,wBAAwB,QAAQ,4DAA4D;AACrG,SAASC,0BAA0B,QAAQ,gEAAgE;;;AAE3G;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEP;CACZ,CACF;AAgBD,OAAM,MAAOQ,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBALxBX,YAAY,EACZE,YAAY,EACZD,YAAY,CAACW,QAAQ,CAACJ,MAAM,CAAC;IAAA;EAAA;;;2EAGpBG,eAAe;IAAAE,YAAA,GAZxBV,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,wBAAwB,EACxBC,0BAA0B;IAAAO,OAAA,GAG1Bd,YAAY,EACZE,YAAY,EAAAa,EAAA,CAAAd,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}