/* You can add global styles to this file, and also import other style files */

/* Import Angular Material theme */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Global styles */
html, body {
  height: 100%;
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

body {
  background-color: #f5f5f5;
}

/* Common utility classes */
.spacer {
  flex: 1 1 auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.page-container {
  padding: 1.5rem;
}

.page-title {
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  font-weight: 500;
}

/* Card styles */
.mat-card {
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Form styles */
.form-container {
  max-width: 600px;
}

.form-field {
  width: 100%;
  margin-bottom: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

/* Button styles */
.action-button {
  margin-right: 0.5rem;
}

/* Status colors */
.status-todo {
  color: #ff9800;
}

.status-in-progress {
  color: #2196f3;
}

.status-review {
  color: #9c27b0;
}

.status-done {
  color: #4caf50;
}

/* Priority colors */
.priority-low {
  color: #8bc34a;
}

.priority-medium {
  color: #ff9800;
}

.priority-high {
  color: #f44336;
}

/* Animation for loading transitions */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .page-container {
    padding: 1rem;
  }
  
  .form-actions {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
}
