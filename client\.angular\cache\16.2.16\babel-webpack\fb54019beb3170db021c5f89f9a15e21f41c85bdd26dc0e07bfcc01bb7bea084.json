{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Not Found Page component\n * Displays a 404 error page when a route is not found\n */\nimport { Component } from '@angular/core';\nexport let NotFoundPageComponent = class NotFoundPageComponent {\n  /**\n   * Constructor\n   * @param router - Router service for navigation\n   */\n  constructor(router) {\n    this.router = router;\n  }\n  /**\n   * Navigates back to the home page\n   */\n  goToHome() {\n    this.router.navigate(['/']);\n  }\n};\nNotFoundPageComponent = __decorate([Component({\n  selector: 'app-not-found-page',\n  templateUrl: './not-found-page.component.html',\n  styleUrls: ['./not-found-page.component.scss']\n})], NotFoundPageComponent);", "map": {"version": 3, "names": ["Component", "NotFoundPageComponent", "constructor", "router", "goToHome", "navigate", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\not-found\\pages\\not-found-page\\not-found-page.component.ts"], "sourcesContent": ["/**\n * Not Found Page component\n * Displays a 404 error page when a route is not found\n */\nimport { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-not-found-page',\n  templateUrl: './not-found-page.component.html',\n  styleUrls: ['./not-found-page.component.scss']\n})\nexport class NotFoundPageComponent {\n  /**\n   * Constructor\n   * @param router - Router service for navigation\n   */\n  constructor(private router: Router) {}\n\n  /**\n   * Navigates back to the home page\n   */\n  goToHome(): void {\n    this.router.navigate(['/']);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,QAAQ,eAAe;AAQlC,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAChC;;;;EAIAC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErC;;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;CACD;AAbYJ,qBAAqB,GAAAK,UAAA,EALjCN,SAAS,CAAC;EACTO,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC;CAC9C,CAAC,C,EACWR,qBAAqB,CAajC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}