{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Task Item Component\n * Displays a single task item in the task list\n */\nimport { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';\nexport let TaskItemComponent = class TaskItemComponent {\n  constructor() {\n    /**\n     * Event emitted when task is selected\n     */\n    this.select = new EventEmitter();\n    /**\n     * Event emitted when task is deleted\n     */\n    this.delete = new EventEmitter();\n    /**\n     * Event emitted when task status is changed\n     */\n    this.statusChange = new EventEmitter();\n    /**\n     * Available task statuses\n     */\n    this.statuses = [{\n      value: 'todo',\n      label: 'To Do'\n    }, {\n      value: 'in_progress',\n      label: 'In Progress'\n    }, {\n      value: 'review',\n      label: 'Review'\n    }, {\n      value: 'done',\n      label: 'Done'\n    }];\n    /**\n     * Status menu is open\n     */\n    this.statusMenuOpen = false;\n  }\n  /**\n   * Handle task selection\n   * @param event - Mouse event\n   */\n  onSelect(event) {\n    // Prevent event bubbling if clicking on actions\n    if (event.target.closest('.task-actions')) {\n      return;\n    }\n    this.select.emit();\n  }\n  /**\n   * Handle task deletion\n   * @param event - Mouse event\n   */\n  onDelete(event) {\n    event.stopPropagation();\n    this.delete.emit(this.task.id);\n  }\n  /**\n   * Handle status change\n   * @param event - Mouse event\n   * @param status - New status\n   */\n  onStatusChange(event, status) {\n    event.stopPropagation();\n    this.statusChange.emit(status);\n    this.statusMenuOpen = false;\n  }\n  /**\n   * Toggle status menu\n   * @param event - Mouse event\n   */\n  toggleStatusMenu(event) {\n    event.stopPropagation();\n    this.statusMenuOpen = !this.statusMenuOpen;\n  }\n  /**\n   * Get CSS class for priority\n   * @returns CSS class name\n   */\n  getPriorityClass() {\n    return `priority-${this.task.priority}`;\n  }\n  /**\n   * Get CSS class for status\n   * @returns CSS class name\n   */\n  getStatusClass() {\n    return `status-${this.task.status}`;\n  }\n  /**\n   * Get formatted status label\n   * @returns Status label\n   */\n  getStatusLabel() {\n    const status = this.statuses.find(s => s.value === this.task.status);\n    return status ? status.label : this.task.status;\n  }\n  /**\n   * Check if task is overdue\n   * @returns True if task is overdue\n   */\n  isOverdue() {\n    if (!this.task.dueDate || this.task.status === 'done') {\n      return false;\n    }\n    const dueDate = new Date(this.task.dueDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    return dueDate < today;\n  }\n};\n__decorate([Input()], TaskItemComponent.prototype, \"task\", void 0);\n__decorate([Output()], TaskItemComponent.prototype, \"select\", void 0);\n__decorate([Output()], TaskItemComponent.prototype, \"delete\", void 0);\n__decorate([Output()], TaskItemComponent.prototype, \"statusChange\", void 0);\nTaskItemComponent = __decorate([Component({\n  selector: 'app-task-item',\n  templateUrl: './task-item.component.html',\n  styleUrls: ['./task-item.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], TaskItemComponent);", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "ChangeDetectionStrategy", "TaskItemComponent", "constructor", "select", "delete", "statusChange", "statuses", "value", "label", "statusMenuOpen", "onSelect", "event", "target", "closest", "emit", "onDelete", "stopPropagation", "task", "id", "onStatusChange", "status", "toggleStatusMenu", "getPriorityClass", "priority", "getStatusClass", "getStatusLabel", "find", "s", "isOverdue", "dueDate", "Date", "today", "setHours", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\components\\task-item\\task-item.component.ts"], "sourcesContent": ["/**\n * Task Item Component\n * Displays a single task item in the task list\n */\nimport { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';\nimport { Task } from '../../../../core/models/task.model';\n\n@Component({\n  selector: 'app-task-item',\n  templateUrl: './task-item.component.html',\n  styleUrls: ['./task-item.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskItemComponent {\n  /**\n   * Input task to display\n   */\n  @Input() task!: Task;\n  \n  /**\n   * Event emitted when task is selected\n   */\n  @Output() select = new EventEmitter<void>();\n  \n  /**\n   * Event emitted when task is deleted\n   */\n  @Output() delete = new EventEmitter<string>();\n  \n  /**\n   * Event emitted when task status is changed\n   */\n  @Output() statusChange = new EventEmitter<string>();\n  \n  /**\n   * Available task statuses\n   */\n  statuses = [\n    { value: 'todo', label: 'To Do' },\n    { value: 'in_progress', label: 'In Progress' },\n    { value: 'review', label: 'Review' },\n    { value: 'done', label: 'Done' }\n  ];\n  \n  /**\n   * Status menu is open\n   */\n  statusMenuOpen = false;\n\n  /**\n   * Handle task selection\n   * @param event - Mouse event\n   */\n  onSelect(event: MouseEvent): void {\n    // Prevent event bubbling if clicking on actions\n    if ((event.target as HTMLElement).closest('.task-actions')) {\n      return;\n    }\n    this.select.emit();\n  }\n\n  /**\n   * Handle task deletion\n   * @param event - Mouse event\n   */\n  onDelete(event: MouseEvent): void {\n    event.stopPropagation();\n    this.delete.emit(this.task.id);\n  }\n\n  /**\n   * Handle status change\n   * @param event - Mouse event\n   * @param status - New status\n   */\n  onStatusChange(event: MouseEvent, status: string): void {\n    event.stopPropagation();\n    this.statusChange.emit(status);\n    this.statusMenuOpen = false;\n  }\n\n  /**\n   * Toggle status menu\n   * @param event - Mouse event\n   */\n  toggleStatusMenu(event: MouseEvent): void {\n    event.stopPropagation();\n    this.statusMenuOpen = !this.statusMenuOpen;\n  }\n\n  /**\n   * Get CSS class for priority\n   * @returns CSS class name\n   */\n  getPriorityClass(): string {\n    return `priority-${this.task.priority}`;\n  }\n\n  /**\n   * Get CSS class for status\n   * @returns CSS class name\n   */\n  getStatusClass(): string {\n    return `status-${this.task.status}`;\n  }\n\n  /**\n   * Get formatted status label\n   * @returns Status label\n   */\n  getStatusLabel(): string {\n    const status = this.statuses.find(s => s.value === this.task.status);\n    return status ? status.label : this.task.status;\n  }\n\n  /**\n   * Check if task is overdue\n   * @returns True if task is overdue\n   */\n  isOverdue(): boolean {\n    if (!this.task.dueDate || this.task.status === 'done') {\n      return false;\n    }\n    \n    const dueDate = new Date(this.task.dueDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    \n    return dueDate < today;\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,uBAAuB,QAAQ,eAAe;AASxF,WAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAAvBC,YAAA;IAML;;;IAGU,KAAAC,MAAM,GAAG,IAAIJ,YAAY,EAAQ;IAE3C;;;IAGU,KAAAK,MAAM,GAAG,IAAIL,YAAY,EAAU;IAE7C;;;IAGU,KAAAM,YAAY,GAAG,IAAIN,YAAY,EAAU;IAEnD;;;IAGA,KAAAO,QAAQ,GAAG,CACT;MAAEC,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAE,EACjC;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAa,CAAE,EAC9C;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAM,CAAE,CACjC;IAED;;;IAGA,KAAAC,cAAc,GAAG,KAAK;EAmFxB;EAjFE;;;;EAIAC,QAAQA,CAACC,KAAiB;IACxB;IACA,IAAKA,KAAK,CAACC,MAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,EAAE;MAC1D;;IAEF,IAAI,CAACV,MAAM,CAACW,IAAI,EAAE;EACpB;EAEA;;;;EAIAC,QAAQA,CAACJ,KAAiB;IACxBA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACZ,MAAM,CAACU,IAAI,CAAC,IAAI,CAACG,IAAI,CAACC,EAAE,CAAC;EAChC;EAEA;;;;;EAKAC,cAAcA,CAACR,KAAiB,EAAES,MAAc;IAC9CT,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACX,YAAY,CAACS,IAAI,CAACM,MAAM,CAAC;IAC9B,IAAI,CAACX,cAAc,GAAG,KAAK;EAC7B;EAEA;;;;EAIAY,gBAAgBA,CAACV,KAAiB;IAChCA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACP,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA;;;;EAIAa,gBAAgBA,CAAA;IACd,OAAO,YAAY,IAAI,CAACL,IAAI,CAACM,QAAQ,EAAE;EACzC;EAEA;;;;EAIAC,cAAcA,CAAA;IACZ,OAAO,UAAU,IAAI,CAACP,IAAI,CAACG,MAAM,EAAE;EACrC;EAEA;;;;EAIAK,cAAcA,CAAA;IACZ,MAAML,MAAM,GAAG,IAAI,CAACd,QAAQ,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpB,KAAK,KAAK,IAAI,CAACU,IAAI,CAACG,MAAM,CAAC;IACpE,OAAOA,MAAM,GAAGA,MAAM,CAACZ,KAAK,GAAG,IAAI,CAACS,IAAI,CAACG,MAAM;EACjD;EAEA;;;;EAIAQ,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACX,IAAI,CAACY,OAAO,IAAI,IAAI,CAACZ,IAAI,CAACG,MAAM,KAAK,MAAM,EAAE;MACrD,OAAO,KAAK;;IAGd,MAAMS,OAAO,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACb,IAAI,CAACY,OAAO,CAAC;IAC3C,MAAME,KAAK,GAAG,IAAID,IAAI,EAAE;IACxBC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,OAAOH,OAAO,GAAGE,KAAK;EACxB;CACD;AAjHUE,UAAA,EAARpC,KAAK,EAAE,C,8CAAa;AAKXoC,UAAA,EAATnC,MAAM,EAAE,C,gDAAmC;AAKlCmC,UAAA,EAATnC,MAAM,EAAE,C,gDAAqC;AAKpCmC,UAAA,EAATnC,MAAM,EAAE,C,sDAA2C;AAnBzCG,iBAAiB,GAAAgC,UAAA,EAN7BrC,SAAS,CAAC;EACTsC,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,CAAC,4BAA4B,CAAC;EACzCC,eAAe,EAAErC,uBAAuB,CAACsC;CAC1C,CAAC,C,EACWrC,iBAAiB,CAqH7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}