/**
 * Task View Page Component
 * Page for viewing a single task's details
 */
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TaskService } from '../../../../core/services/task.service';
import { UserService } from '../../../../core/services/user.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { Task } from '../../../../core/models/task.model';
import { User } from '../../../../core/models/user.model';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-task-view-page',
  templateUrl: './task-view-page.component.html',
  styleUrls: ['./task-view-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskViewPageComponent implements OnInit, OnDestroy {
  /**
   * Current task being viewed
   */
  task: Task | null = null;
  
  /**
   * List of users
   */
  users: User[] = [];
  
  /**
   * Loading state indicator
   */
  loading = false;
  
  /**
   * Error message if loading failed
   */
  error: string | null = null;
  
  /**
   * Subject for unsubscribing from observables
   */
  private destroy$ = new Subject<void>();

  /**
   * Constructor with dependency injection
   * @param taskService - Task service for CRUD operations
   * @param userService - User service for user data
   * @param notificationService - Notification service for displaying messages
   * @param dialog - Dialog service for confirmation dialogs
   * @param route - Activated route for getting route parameters
   * @param router - Router for navigation
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(
    private taskService: TaskService,
    private userService: UserService,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Loads task and users
   */
  ngOnInit(): void {
    this.loadTask();
    this.loadUsers();
  }

  /**
   * Lifecycle hook that is called before component destruction
   * Unsubscribes from observables
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load task details
   */
  loadTask(): void {
    const taskId = this.route.snapshot.paramMap.get('id');
    if (!taskId) {
      this.error = 'Task ID not provided';
      return;
    }
    
    this.loading = true;
    this.error = null;
    this.cdr.markForCheck();
    
    this.taskService.getTaskById(taskId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (task) => {
          this.task = task;
          this.loading = false;
          this.cdr.markForCheck();
        },
        error: (err) => {
          this.error = 'Failed to load task. It may have been deleted or you do not have permission to view it.';
          this.loading = false;
          console.error('Error loading task:', err);
          this.cdr.markForCheck();
        }
      });
  }

  /**
   * Load users for assignee selection
   */
  loadUsers(): void {
    this.userService.getUsers()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (users) => {
          this.users = users;
          this.cdr.markForCheck();
        },
        error: (err) => {
          console.error('Error loading users:', err);
        }
      });
  }

  /**
   * Handle edit button click
   */
  onEdit(): void {
    if (this.task) {
      this.router.navigate(['/tasks', this.task.id, 'edit']);
    }
  }

  /**
   * Handle delete button click
   */
  onDelete(): void {
    if (!this.task) return;
    
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Delete Task',
        message: 'Are you sure you want to delete this task? This action cannot be undone.',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        confirmColor: 'warn'
      }
    });
    
    dialogRef.afterClosed().subscribe(result => {
      if (result && this.task) {
        this.taskService.deleteTask(this.task.id)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.notificationService.success('Task deleted successfully');
              this.router.navigate(['/tasks']);
            },
            error: (err) => {
              this.notificationService.error('Failed to delete task');
              console.error('Error deleting task:', err);
            }
          });
      }
    });
  }

  /**
   * Handle status change
   * @param status - New status
   */
  onStatusChange(status: string): void {
    if (!this.task) return;
    
    this.taskService.updateTask(this.task.id, { status })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedTask) => {
          this.task = updatedTask;
          this.notificationService.success('Task status updated');
          this.cdr.markForCheck();
        },
        error: (err) => {
          this.notificationService.error('Failed to update task status');
          console.error('Error updating task status:', err);
        }
      });
  }

  /**
   * Handle assignee change
   * @param assigneeId - New assignee ID
   */
  onAssigneeChange(assigneeId: string): void {
    if (!this.task) return;
    
    this.taskService.updateTask(this.task.id, { assignee: assigneeId })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedTask) => {
          this.task = updatedTask;
          this.notificationService.success('Task assignee updated');
          this.cdr.markForCheck();
        },
        error: (err) => {
          this.notificationService.error('Failed to update task assignee');
          console.error('Error updating task assignee:', err);
        }
      });
  }

  /**
   * Navigate back to tasks list
   */
  goBack(): void {
    this.router.navigate(['/tasks']);
  }
}
