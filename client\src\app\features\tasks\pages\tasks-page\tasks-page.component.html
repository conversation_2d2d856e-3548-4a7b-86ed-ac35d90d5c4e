<!-- Tasks page container -->
<div class="tasks-page-container">
  <div class="page-header">
    <h1>Task Management</h1>
  </div>
  
  <!-- Task list component -->
  <app-task-list
    [tasks]="tasks"
    [loading]="loading"
    [error]="error"
    [filter]="filter"
    (taskSelected)="onTaskSelected($event)"
    (taskDeleted)="onTaskDeleted($event)"
    (statusChanged)="onStatusChanged($event)"
    (filterChanged)="onFilterChanged($event)"
    (refresh)="onRefresh()">
  </app-task-list>
</div>
