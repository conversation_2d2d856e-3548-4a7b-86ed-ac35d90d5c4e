{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Root component of the application\n * Serves as the main container for all other components\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nexport let AppComponent = class AppComponent {\n  /**\n   * Constructor with dependency injection\n   * @param router - Angular Router service\n   * @param authService - Authentication service\n   */\n  constructor(router, authService) {\n    this.router = router;\n    this.authService = authService;\n    /**\n     * Title of the application\n     */\n    this.title = 'Task Management';\n    /**\n     * Flag to determine if the user is authenticated\n     */\n    this.isAuthenticated = false;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up route change tracking and authentication status\n   */\n  ngOnInit() {\n    // Track route changes for analytics or other purposes\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n      // Measure performance metrics\n      this.measurePerformanceMetrics();\n    });\n    // Subscribe to authentication status changes\n    this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n      this.isAuthenticated = isAuthenticated;\n    });\n  }\n  /**\n   * Measures key performance metrics for the application\n   * Focuses on LCP (Largest Contentful Paint) and CLS (Cumulative Layout Shift)\n   */\n  measurePerformanceMetrics() {\n    // Use Performance Observer to measure LCP\n    if ('PerformanceObserver' in window) {\n      // LCP measurement\n      const lcpObserver = new PerformanceObserver(entryList => {\n        const entries = entryList.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        console.log('LCP:', lastEntry.startTime);\n        console.log('LCP Element:', lastEntry.element);\n      });\n      lcpObserver.observe({\n        type: 'largest-contentful-paint',\n        buffered: true\n      });\n      // CLS measurement\n      const clsObserver = new PerformanceObserver(entryList => {\n        const entries = entryList.getEntries();\n        let clsValue = 0;\n        entries.forEach(entry => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        console.log('CLS:', clsValue);\n      });\n      clsObserver.observe({\n        type: 'layout-shift',\n        buffered: true\n      });\n    }\n  }\n};\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], AppComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "NavigationEnd", "filter", "AppComponent", "constructor", "router", "authService", "title", "isAuthenticated", "ngOnInit", "events", "pipe", "event", "subscribe", "measurePerformanceMetrics", "isAuthenticated$", "window", "lcpObserver", "PerformanceObserver", "entryList", "entries", "getEntries", "lastEntry", "length", "console", "log", "startTime", "element", "observe", "type", "buffered", "clsObserver", "clsValue", "for<PERSON>ach", "entry", "hadRecentInput", "value", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\app.component.ts"], "sourcesContent": ["/**\n * Root component of the application\n * Serves as the main container for all other components\n */\nimport { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { AuthService } from './core/services/auth.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class AppComponent implements OnInit {\n  /**\n   * Title of the application\n   */\n  title = 'Task Management';\n  \n  /**\n   * Flag to determine if the user is authenticated\n   */\n  isAuthenticated = false;\n\n  /**\n   * Constructor with dependency injection\n   * @param router - Angular Router service\n   * @param authService - Authentication service\n   */\n  constructor(\n    private router: Router,\n    private authService: AuthService\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up route change tracking and authentication status\n   */\n  ngOnInit(): void {\n    // Track route changes for analytics or other purposes\n    this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd)\n    ).subscribe(() => {\n      // Measure performance metrics\n      this.measurePerformanceMetrics();\n    });\n\n    // Subscribe to authentication status changes\n    this.authService.isAuthenticated$.subscribe(\n      isAuthenticated => {\n        this.isAuthenticated = isAuthenticated;\n      }\n    );\n  }\n\n  /**\n   * Measures key performance metrics for the application\n   * Focuses on LCP (Largest Contentful Paint) and CLS (Cumulative Layout Shift)\n   */\n  private measurePerformanceMetrics(): void {\n    // Use Performance Observer to measure LCP\n    if ('PerformanceObserver' in window) {\n      // LCP measurement\n      const lcpObserver = new PerformanceObserver((entryList) => {\n        const entries = entryList.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        console.log('LCP:', lastEntry.startTime);\n        console.log('LCP Element:', lastEntry.element);\n      });\n      \n      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });\n      \n      // CLS measurement\n      const clsObserver = new PerformanceObserver((entryList) => {\n        const entries = entryList.getEntries();\n        let clsValue = 0;\n        \n        entries.forEach(entry => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        \n        console.log('CLS:', clsValue);\n      });\n      \n      clsObserver.observe({ type: 'layout-shift', buffered: true });\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAUC,uBAAuB,QAAQ,eAAe;AAC1E,SAAiBC,aAAa,QAAQ,iBAAiB;AACvD,SAASC,MAAM,QAAQ,gBAAgB;AAShC,WAAMC,YAAY,GAAlB,MAAMA,YAAY;EAWvB;;;;;EAKAC,YACUC,MAAc,EACdC,WAAwB;IADxB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAjBrB;;;IAGA,KAAAC,KAAK,GAAG,iBAAiB;IAEzB;;;IAGA,KAAAC,eAAe,GAAG,KAAK;EAUpB;EAEH;;;;EAIAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,MAAM,CAACK,MAAM,CAACC,IAAI,CACrBT,MAAM,CAACU,KAAK,IAAIA,KAAK,YAAYX,aAAa,CAAC,CAChD,CAACY,SAAS,CAAC,MAAK;MACf;MACA,IAAI,CAACC,yBAAyB,EAAE;IAClC,CAAC,CAAC;IAEF;IACA,IAAI,CAACR,WAAW,CAACS,gBAAgB,CAACF,SAAS,CACzCL,eAAe,IAAG;MAChB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACxC,CAAC,CACF;EACH;EAEA;;;;EAIQM,yBAAyBA,CAAA;IAC/B;IACA,IAAI,qBAAqB,IAAIE,MAAM,EAAE;MACnC;MACA,MAAMC,WAAW,GAAG,IAAIC,mBAAmB,CAAEC,SAAS,IAAI;QACxD,MAAMC,OAAO,GAAGD,SAAS,CAACE,UAAU,EAAE;QACtC,MAAMC,SAAS,GAAGF,OAAO,CAACA,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC;QAC7CC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEH,SAAS,CAACI,SAAS,CAAC;QACxCF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEH,SAAS,CAACK,OAAO,CAAC;MAChD,CAAC,CAAC;MAEFV,WAAW,CAACW,OAAO,CAAC;QAAEC,IAAI,EAAE,0BAA0B;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAEzE;MACA,MAAMC,WAAW,GAAG,IAAIb,mBAAmB,CAAEC,SAAS,IAAI;QACxD,MAAMC,OAAO,GAAGD,SAAS,CAACE,UAAU,EAAE;QACtC,IAAIW,QAAQ,GAAG,CAAC;QAEhBZ,OAAO,CAACa,OAAO,CAACC,KAAK,IAAG;UACtB,IAAI,CAACA,KAAK,CAACC,cAAc,EAAE;YACzBH,QAAQ,IAAIE,KAAK,CAACE,KAAK;;QAE3B,CAAC,CAAC;QAEFZ,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEO,QAAQ,CAAC;MAC/B,CAAC,CAAC;MAEFD,WAAW,CAACH,OAAO,CAAC;QAAEC,IAAI,EAAE,cAAc;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;;EAEjE;CACD;AA5EY3B,YAAY,GAAAkC,UAAA,EANxBtC,SAAS,CAAC;EACTuC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,sBAAsB;EACnCC,SAAS,EAAE,CAAC,sBAAsB,CAAC;EACnCC,eAAe,EAAEzC,uBAAuB,CAAC0C;CAC1C,CAAC,C,EACWvC,YAAY,CA4ExB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}