{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Sidenav Component\n * Side navigation menu for the application\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nexport let SidenavComponent = class SidenavComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, router, cdr) {\n    this.authService = authService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Current authenticated user\n     */\n    this.currentUser = null;\n    /**\n     * Flag to control sidenav expanded state\n     */\n    this.isExpanded = true;\n    /**\n     * Current active route\n     */\n    this.activeRoute = '';\n    /**\n     * Navigation menu items\n     */\n    this.navItems = [{\n      label: 'Dashboard',\n      icon: 'dashboard',\n      route: '/dashboard',\n      requiresAdmin: false\n    }, {\n      label: 'My Tasks',\n      icon: 'assignment',\n      route: '/tasks',\n      requiresAdmin: false\n    }, {\n      label: 'Create Task',\n      icon: 'add_task',\n      route: '/tasks/create',\n      requiresAdmin: false\n    }, {\n      label: 'Team',\n      icon: 'people',\n      route: '/team',\n      requiresAdmin: false\n    }, {\n      label: 'Reports',\n      icon: 'bar_chart',\n      route: '/reports',\n      requiresAdmin: true\n    }, {\n      label: 'Settings',\n      icon: 'settings',\n      route: '/settings',\n      requiresAdmin: true\n    }];\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up subscriptions to current user and route changes\n   */\n  ngOnInit() {\n    // Subscribe to current user changes\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.cdr.markForCheck();\n    });\n    // Track active route\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      this.activeRoute = event.urlAfterRedirects;\n      this.cdr.markForCheck();\n    });\n  }\n  /**\n   * Toggle the expanded state of the sidenav\n   */\n  toggleSidenav() {\n    this.isExpanded = !this.isExpanded;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Check if a route is currently active\n   * @param route - Route to check\n   * @returns Boolean indicating if route is active\n   */\n  isActive(route) {\n    return this.activeRoute.startsWith(route);\n  }\n  /**\n   * Check if the current user has admin privileges\n   * @returns Boolean indicating if user is admin\n   */\n  isAdmin() {\n    return this.currentUser?.role === 'admin';\n  }\n};\nSidenavComponent = __decorate([Component({\n  selector: 'app-sidenav',\n  templateUrl: './sidenav.component.html',\n  styleUrls: ['./sidenav.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], SidenavComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "NavigationEnd", "filter", "SidenavComponent", "constructor", "authService", "router", "cdr", "currentUser", "isExpanded", "activeRoute", "navItems", "label", "icon", "route", "requiresAdmin", "ngOnInit", "currentUser$", "subscribe", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "events", "pipe", "event", "urlAfterRedirects", "toggle<PERSON><PERSON><PERSON>", "isActive", "startsWith", "isAdmin", "role", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\components\\sidenav\\sidenav.component.ts"], "sourcesContent": ["/**\n * Sidenav Component\n * Side navigation menu for the application\n */\nimport { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { AuthService } from '../../services/auth.service';\nimport { User } from '../../models/user.model';\n\n@Component({\n  selector: 'app-sidenav',\n  templateUrl: './sidenav.component.html',\n  styleUrls: ['./sidenav.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class SidenavComponent implements OnInit {\n  /**\n   * Current authenticated user\n   */\n  currentUser: User | null = null;\n  \n  /**\n   * Flag to control sidenav expanded state\n   */\n  isExpanded = true;\n  \n  /**\n   * Current active route\n   */\n  activeRoute = '';\n\n  /**\n   * Navigation menu items\n   */\n  navItems = [\n    { \n      label: 'Dashboard', \n      icon: 'dashboard', \n      route: '/dashboard',\n      requiresAdmin: false\n    },\n    { \n      label: 'My Tasks', \n      icon: 'assignment', \n      route: '/tasks',\n      requiresAdmin: false\n    },\n    { \n      label: 'Create Task', \n      icon: 'add_task', \n      route: '/tasks/create',\n      requiresAdmin: false\n    },\n    { \n      label: 'Team', \n      icon: 'people', \n      route: '/team',\n      requiresAdmin: false\n    },\n    { \n      label: 'Reports', \n      icon: 'bar_chart', \n      route: '/reports',\n      requiresAdmin: true\n    },\n    { \n      label: 'Settings', \n      icon: 'settings', \n      route: '/settings',\n      requiresAdmin: true\n    }\n  ];\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up subscriptions to current user and route changes\n   */\n  ngOnInit(): void {\n    // Subscribe to current user changes\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.cdr.markForCheck();\n    });\n    \n    // Track active route\n    this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd)\n    ).subscribe((event: NavigationEnd) => {\n      this.activeRoute = event.urlAfterRedirects;\n      this.cdr.markForCheck();\n    });\n  }\n\n  /**\n   * Toggle the expanded state of the sidenav\n   */\n  toggleSidenav(): void {\n    this.isExpanded = !this.isExpanded;\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Check if a route is currently active\n   * @param route - Route to check\n   * @returns Boolean indicating if route is active\n   */\n  isActive(route: string): boolean {\n    return this.activeRoute.startsWith(route);\n  }\n\n  /**\n   * Check if the current user has admin privileges\n   * @returns Boolean indicating if user is admin\n   */\n  isAdmin(): boolean {\n    return this.currentUser?.role === 'admin';\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAUC,uBAAuB,QAA2B,eAAe;AAC7F,SAAiBC,aAAa,QAAQ,iBAAiB;AACvD,SAASC,MAAM,QAAQ,gBAAgB;AAUhC,WAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EA0D3B;;;;;;EAMAC,YACUC,WAAwB,EACxBC,MAAc,EACdC,GAAsB;IAFtB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IAlEb;;;IAGA,KAAAC,WAAW,GAAgB,IAAI;IAE/B;;;IAGA,KAAAC,UAAU,GAAG,IAAI;IAEjB;;;IAGA,KAAAC,WAAW,GAAG,EAAE;IAEhB;;;IAGA,KAAAC,QAAQ,GAAG,CACT;MACEC,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,YAAY;MACnBC,aAAa,EAAE;KAChB,EACD;MACEH,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,QAAQ;MACfC,aAAa,EAAE;KAChB,EACD;MACEH,KAAK,EAAE,aAAa;MACpBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,eAAe;MACtBC,aAAa,EAAE;KAChB,EACD;MACEH,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdC,aAAa,EAAE;KAChB,EACD;MACEH,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,UAAU;MACjBC,aAAa,EAAE;KAChB,EACD;MACEH,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,WAAW;MAClBC,aAAa,EAAE;KAChB,CACF;EAYE;EAEH;;;;EAIAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACX,WAAW,CAACY,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACX,WAAW,GAAGW,IAAI;MACvB,IAAI,CAACZ,GAAG,CAACa,YAAY,EAAE;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAACd,MAAM,CAACe,MAAM,CAACC,IAAI,CACrBpB,MAAM,CAACqB,KAAK,IAAIA,KAAK,YAAYtB,aAAa,CAAC,CAChD,CAACiB,SAAS,CAAEK,KAAoB,IAAI;MACnC,IAAI,CAACb,WAAW,GAAGa,KAAK,CAACC,iBAAiB;MAC1C,IAAI,CAACjB,GAAG,CAACa,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEA;;;EAGAK,aAAaA,CAAA;IACX,IAAI,CAAChB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACF,GAAG,CAACa,YAAY,EAAE;EACzB;EAEA;;;;;EAKAM,QAAQA,CAACZ,KAAa;IACpB,OAAO,IAAI,CAACJ,WAAW,CAACiB,UAAU,CAACb,KAAK,CAAC;EAC3C;EAEA;;;;EAIAc,OAAOA,CAAA;IACL,OAAO,IAAI,CAACpB,WAAW,EAAEqB,IAAI,KAAK,OAAO;EAC3C;CACD;AAlHY1B,gBAAgB,GAAA2B,UAAA,EAN5B/B,SAAS,CAAC;EACTgC,QAAQ,EAAE,aAAa;EACvBC,WAAW,EAAE,0BAA0B;EACvCC,SAAS,EAAE,CAAC,0BAA0B,CAAC;EACvCC,eAAe,EAAElC,uBAAuB,CAACmC;CAC1C,CAAC,C,EACWhC,gBAAgB,CAkH5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}