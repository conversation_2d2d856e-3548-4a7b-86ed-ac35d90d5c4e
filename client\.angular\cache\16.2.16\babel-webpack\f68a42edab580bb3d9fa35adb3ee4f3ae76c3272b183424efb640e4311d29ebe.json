{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Header Component\n * Main navigation header for the application\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nexport let HeaderComponent = class HeaderComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, router, cdr) {\n    this.authService = authService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Current authenticated user\n     */\n    this.currentUser = null;\n    /**\n     * Flag to show/hide the user menu\n     */\n    this.isUserMenuOpen = false;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up subscription to current user\n   */\n  ngOnInit() {\n    // Subscribe to current user changes\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.cdr.markForCheck();\n    });\n  }\n  /**\n   * Toggle the user menu dropdown\n   */\n  toggleUserMenu() {\n    this.isUserMenuOpen = !this.isUserMenuOpen;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Close the user menu dropdown\n   */\n  closeUserMenu() {\n    this.isUserMenuOpen = false;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Navigate to user profile page\n   */\n  goToProfile() {\n    this.closeUserMenu();\n    this.router.navigate(['/profile']);\n  }\n  /**\n   * Log out the current user\n   */\n  logout() {\n    this.closeUserMenu();\n    this.authService.logout();\n  }\n};\nHeaderComponent = __decorate([Component({\n  selector: 'app-header',\n  templateUrl: './header.component.html',\n  styleUrls: ['./header.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], HeaderComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "HeaderComponent", "constructor", "authService", "router", "cdr", "currentUser", "isUserMenuOpen", "ngOnInit", "currentUser$", "subscribe", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toggleUserMenu", "closeUserMenu", "goToProfile", "navigate", "logout", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\components\\header\\header.component.ts"], "sourcesContent": ["/**\n * Header Component\n * Main navigation header for the application\n */\nimport { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { User } from '../../models/user.model';\n\n@Component({\n  selector: 'app-header',\n  templateUrl: './header.component.html',\n  styleUrls: ['./header.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class HeaderComponent implements OnInit {\n  /**\n   * Current authenticated user\n   */\n  currentUser: User | null = null;\n  \n  /**\n   * Flag to show/hide the user menu\n   */\n  isUserMenuOpen = false;\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up subscription to current user\n   */\n  ngOnInit(): void {\n    // Subscribe to current user changes\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.cdr.markForCheck();\n    });\n  }\n\n  /**\n   * Toggle the user menu dropdown\n   */\n  toggleUserMenu(): void {\n    this.isUserMenuOpen = !this.isUserMenuOpen;\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Close the user menu dropdown\n   */\n  closeUserMenu(): void {\n    this.isUserMenuOpen = false;\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Navigate to user profile page\n   */\n  goToProfile(): void {\n    this.closeUserMenu();\n    this.router.navigate(['/profile']);\n  }\n\n  /**\n   * Log out the current user\n   */\n  logout(): void {\n    this.closeUserMenu();\n    this.authService.logout();\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAUC,uBAAuB,QAA2B,eAAe;AAWtF,WAAMC,eAAe,GAArB,MAAMA,eAAe;EAW1B;;;;;;EAMAC,YACUC,WAAwB,EACxBC,MAAc,EACdC,GAAsB;IAFtB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IAnBb;;;IAGA,KAAAC,WAAW,GAAgB,IAAI;IAE/B;;;IAGA,KAAAC,cAAc,GAAG,KAAK;EAYnB;EAEH;;;;EAIAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACL,WAAW,CAACM,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACL,WAAW,GAAGK,IAAI;MACvB,IAAI,CAACN,GAAG,CAACO,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAACN,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,CAACF,GAAG,CAACO,YAAY,EAAE;EACzB;EAEA;;;EAGAE,aAAaA,CAAA;IACX,IAAI,CAACP,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACF,GAAG,CAACO,YAAY,EAAE;EACzB;EAEA;;;EAGAG,WAAWA,CAAA;IACT,IAAI,CAACD,aAAa,EAAE;IACpB,IAAI,CAACV,MAAM,CAACY,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAI,CAACH,aAAa,EAAE;IACpB,IAAI,CAACX,WAAW,CAACc,MAAM,EAAE;EAC3B;CACD;AAlEYhB,eAAe,GAAAiB,UAAA,EAN3BnB,SAAS,CAAC;EACToB,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,yBAAyB;EACtCC,SAAS,EAAE,CAAC,yBAAyB,CAAC;EACtCC,eAAe,EAAEtB,uBAAuB,CAACuB;CAC1C,CAAC,C,EACWtB,eAAe,CAkE3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}