/**
 * Click Outside Directive
 * Detects clicks outside of an element and emits an event
 */
import { Directive, ElementRef, EventEmitter, HostListener, Output } from '@angular/core';

@Directive({
  selector: '[appClickOutside]'
})
export class ClickOutsideDirective {
  /**
   * Event emitted when a click occurs outside the element
   */
  @Output() appClickOutside = new EventEmitter();

  /**
   * Constructor with dependency injection
   * @param elementRef - Reference to the host element
   */
  constructor(private elementRef: any) {}

  /**
   * Listen for document click events
   * @param event - Mouse event
   */
  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent): void {
    // Check if the click was outside the element
    const clickedInside = this.elementRef.nativeElement.contains(event.target);
    
    if (!clickedInside) {
      this.appClickOutside.emit();
    }
  }
}
