{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/task.service\";\nimport * as i2 from \"../../../../core/services/user.service\";\nimport * as i3 from \"../../../../core/services/notification.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"../../components/task-form/task-form.component\";\nexport class TaskCreatePageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(taskService, userService, notificationService, router, cdr) {\n    this.taskService = taskService;\n    this.userService = userService;\n    this.notificationService = notificationService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * List of users\n     */\n    this.users = [];\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads users for assignee selection\n   */\n  ngOnInit() {\n    this.loadUsers();\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers() {\n    this.userService.getUsers().pipe(takeUntil(this.destroy$)).subscribe({\n      next: users => {\n        this.users = users;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        console.error('Error loading users:', err);\n        this.notificationService.error('Failed to load users');\n      }\n    });\n  }\n  /**\n   * Handle form submission\n   * @param taskData - Task data from form\n   */\n  onFormSubmit(taskData) {\n    this.loading = true;\n    this.cdr.markForCheck();\n    this.taskService.createTask(taskData).pipe(takeUntil(this.destroy$)).subscribe({\n      next: createdTask => {\n        this.loading = false;\n        this.notificationService.success('Task created successfully');\n        this.router.navigate(['/tasks', createdTask.id]);\n      },\n      error: err => {\n        this.loading = false;\n        this.notificationService.error('Failed to create task');\n        console.error('Error creating task:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Handle form cancellation\n   */\n  onFormCancel() {\n    this.router.navigate(['/tasks']);\n  }\n  static {\n    this.ɵfac = function TaskCreatePageComponent_Factory(t) {\n      return new (t || TaskCreatePageComponent)(i0.ɵɵdirectiveInject(i1.TaskService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.NotificationService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskCreatePageComponent,\n      selectors: [[\"app-task-create-page\"]],\n      decls: 9,\n      vars: 2,\n      consts: [[1, \"task-create-page-container\"], [1, \"page-header\"], [\"mat-button\", \"\", 1, \"back-button\", 3, \"click\"], [3, \"users\", \"loading\", \"formSubmit\", \"formCancel\"]],\n      template: function TaskCreatePageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Create New Task\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function TaskCreatePageComponent_Template_button_click_4_listener() {\n            return ctx.onFormCancel();\n          });\n          i0.ɵɵelementStart(5, \"mat-icon\");\n          i0.ɵɵtext(6, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(7, \" Back to Tasks \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"app-task-form\", 3);\n          i0.ɵɵlistener(\"formSubmit\", function TaskCreatePageComponent_Template_app_task_form_formSubmit_8_listener($event) {\n            return ctx.onFormSubmit($event);\n          })(\"formCancel\", function TaskCreatePageComponent_Template_app_task_form_formCancel_8_listener() {\n            return ctx.onFormCancel();\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"users\", ctx.users)(\"loading\", ctx.loading);\n        }\n      },\n      dependencies: [i5.MatButton, i6.MatIcon, i7.TaskFormComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.task-create-page-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.8rem;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.back-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .task-create-page-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 12px;\\n  }\\n  .page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvdGFza3MvcGFnZXMvdGFzay1jcmVhdGUtcGFnZS90YXNrLWNyZWF0ZS1wYWdlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOztFQUFBO0FBSUEsOENBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7QUFBRjs7QUFHQSwyQ0FBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQUFGO0FBRUU7RUFDRSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7QUFBSjs7QUFJQSx3QkFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0FBREY7QUFHRTtFQUNFLGlCQUFBO0FBREo7O0FBS0EsMkJBQUE7QUFDQTtFQUNFO0lBQ0UsYUFBQTtFQUZGO0VBS0E7SUFDRSxzQkFBQTtJQUNBLHVCQUFBO0lBQ0EsU0FBQTtFQUhGO0VBS0U7SUFDRSxpQkFBQTtFQUhKO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFRhc2sgY3JlYXRlIHBhZ2UgY29tcG9uZW50IHN0eWxlc1xuICovXG5cbi8qIENvbnRhaW5lciBmb3IgdGhlIGVudGlyZSB0YXNrIGNyZWF0ZSBwYWdlICovXG4udGFzay1jcmVhdGUtcGFnZS1jb250YWluZXIge1xuICBwYWRkaW5nOiAyNHB4O1xuICBtYXgtd2lkdGg6IDEwMDBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG59XG5cbi8qIFBhZ2UgaGVhZGVyIHdpdGggdGl0bGUgYW5kIGJhY2sgYnV0dG9uICovXG4ucGFnZS1oZWFkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gIFxuICBoMSB7XG4gICAgbWFyZ2luOiAwO1xuICAgIGZvbnQtc2l6ZTogMS44cmVtO1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgY29sb3I6ICMzMzM7XG4gIH1cbn1cblxuLyogQmFjayBidXR0b24gc3R5bGluZyAqL1xuLmJhY2stYnV0dG9uIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgXG4gIG1hdC1pY29uIHtcbiAgICBtYXJnaW4tcmlnaHQ6IDhweDtcbiAgfVxufVxuXG4vKiBSZXNwb25zaXZlIGFkanVzdG1lbnRzICovXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLnRhc2stY3JlYXRlLXBhZ2UtY29udGFpbmVyIHtcbiAgICBwYWRkaW5nOiAxNnB4O1xuICB9XG4gIFxuICAucGFnZS1oZWFkZXIge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gICAgZ2FwOiAxMnB4O1xuICAgIFxuICAgIGgxIHtcbiAgICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICAgIH1cbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "TaskCreatePageComponent", "constructor", "taskService", "userService", "notificationService", "router", "cdr", "users", "loading", "destroy$", "ngOnInit", "loadUsers", "ngOnDestroy", "next", "complete", "getUsers", "pipe", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "err", "console", "onFormSubmit", "taskData", "createTask", "createdTask", "success", "navigate", "id", "onFormCancel", "i0", "ɵɵdirectiveInject", "i1", "TaskService", "i2", "UserService", "i3", "NotificationService", "i4", "Router", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "TaskCreatePageComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TaskCreatePageComponent_Template_button_click_4_listener", "TaskCreatePageComponent_Template_app_task_form_formSubmit_8_listener", "$event", "TaskCreatePageComponent_Template_app_task_form_formCancel_8_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\pages\\task-create-page\\task-create-page.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\pages\\task-create-page\\task-create-page.component.html"], "sourcesContent": ["/**\n * Task Create Page Component\n * Page for creating a new task\n */\nimport { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { TaskService } from '../../../../core/services/task.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\nimport { Task } from '../../../../core/models/task.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-task-create-page',\n  templateUrl: './task-create-page.component.html',\n  styleUrls: ['./task-create-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskCreatePageComponent implements OnInit, On<PERSON><PERSON>roy {\n  /**\n   * List of users\n   */\n  users: User[] = [];\n  \n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param taskService - Task service for CRUD operations\n   * @param userService - User service for user data\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private taskService: TaskService,\n    private userService: UserService,\n    private notificationService: NotificationService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Loads users for assignee selection\n   */\n  ngOnInit(): void {\n    this.loadUsers();\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Load users for assignee selection\n   */\n  loadUsers(): void {\n    this.userService.getUsers()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (users) => {\n          this.users = users;\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          console.error('Error loading users:', err);\n          this.notificationService.error('Failed to load users');\n        }\n      });\n  }\n\n  /**\n   * Handle form submission\n   * @param taskData - Task data from form\n   */\n  onFormSubmit(taskData: Partial<Task>): void {\n    this.loading = true;\n    this.cdr.markForCheck();\n    \n    this.taskService.createTask(taskData)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (createdTask) => {\n          this.loading = false;\n          this.notificationService.success('Task created successfully');\n          this.router.navigate(['/tasks', createdTask.id]);\n        },\n        error: (err) => {\n          this.loading = false;\n          this.notificationService.error('Failed to create task');\n          console.error('Error creating task:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Handle form cancellation\n   */\n  onFormCancel(): void {\n    this.router.navigate(['/tasks']);\n  }\n}\n", "<!-- Task create page container -->\n<div class=\"task-create-page-container\">\n  <!-- Page header -->\n  <div class=\"page-header\">\n    <h1>Create New Task</h1>\n    <button mat-button (click)=\"onFormCancel()\" class=\"back-button\">\n      <mat-icon>arrow_back</mat-icon>\n      Back to Tasks\n    </button>\n  </div>\n  \n  <!-- Task form component -->\n  <app-task-form\n    [users]=\"users\"\n    [loading]=\"loading\"\n    (formSubmit)=\"onFormSubmit($event)\"\n    (formCancel)=\"onFormCancel()\">\n  </app-task-form>\n</div>\n"], "mappings": "AAMA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;AAa1C,OAAM,MAAOC,uBAAuB;EAgBlC;;;;;;;;EAQAC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,GAAsB;IAJtB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA5Bb;;;IAGA,KAAAC,KAAK,GAAW,EAAE;IAElB;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIX,OAAO,EAAQ;EAgBnC;EAEH;;;;EAIAY,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;IACpB,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;EAC1B;EAEA;;;EAGAH,SAASA,CAAA;IACP,IAAI,CAACR,WAAW,CAACY,QAAQ,EAAE,CACxBC,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTJ,IAAI,EAAGN,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACD,GAAG,CAACY,YAAY,EAAE;MACzB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAAChB,mBAAmB,CAACe,KAAK,CAAC,sBAAsB,CAAC;MACxD;KACD,CAAC;EACN;EAEA;;;;EAIAG,YAAYA,CAACC,QAAuB;IAClC,IAAI,CAACf,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,GAAG,CAACY,YAAY,EAAE;IAEvB,IAAI,CAAChB,WAAW,CAACsB,UAAU,CAACD,QAAQ,CAAC,CAClCP,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTJ,IAAI,EAAGY,WAAW,IAAI;QACpB,IAAI,CAACjB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACJ,mBAAmB,CAACsB,OAAO,CAAC,2BAA2B,CAAC;QAC7D,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,QAAQ,EAAEF,WAAW,CAACG,EAAE,CAAC,CAAC;MAClD,CAAC;MACDT,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAACZ,OAAO,GAAG,KAAK;QACpB,IAAI,CAACJ,mBAAmB,CAACe,KAAK,CAAC,uBAAuB,CAAC;QACvDE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAACd,GAAG,CAACY,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAW,YAAYA,CAAA;IACV,IAAI,CAACxB,MAAM,CAACsB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAjGW3B,uBAAuB,EAAA8B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAU,iBAAA;IAAA;EAAA;;;YAAvBxC,uBAAuB;MAAAyC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBpCjB,EAAA,CAAAmB,cAAA,aAAwC;UAGhCnB,EAAA,CAAAoB,MAAA,sBAAe;UAAApB,EAAA,CAAAqB,YAAA,EAAK;UACxBrB,EAAA,CAAAmB,cAAA,gBAAgE;UAA7CnB,EAAA,CAAAsB,UAAA,mBAAAC,yDAAA;YAAA,OAASL,GAAA,CAAAnB,YAAA,EAAc;UAAA,EAAC;UACzCC,EAAA,CAAAmB,cAAA,eAAU;UAAAnB,EAAA,CAAAoB,MAAA,iBAAU;UAAApB,EAAA,CAAAqB,YAAA,EAAW;UAC/BrB,EAAA,CAAAoB,MAAA,sBACF;UAAApB,EAAA,CAAAqB,YAAA,EAAS;UAIXrB,EAAA,CAAAmB,cAAA,uBAIgC;UAD9BnB,EAAA,CAAAsB,UAAA,wBAAAE,qEAAAC,MAAA;YAAA,OAAcP,GAAA,CAAA1B,YAAA,CAAAiC,MAAA,CAAoB;UAAA,EAAC,wBAAAC,qEAAA;YAAA,OACrBR,GAAA,CAAAnB,YAAA,EAAc;UAAA,EADO;UAErCC,EAAA,CAAAqB,YAAA,EAAgB;;;UAJdrB,EAAA,CAAA2B,SAAA,GAAe;UAAf3B,EAAA,CAAA4B,UAAA,UAAAV,GAAA,CAAAzC,KAAA,CAAe,YAAAyC,GAAA,CAAAxC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}