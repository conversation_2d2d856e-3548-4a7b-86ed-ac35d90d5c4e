{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\n/**\n * Core module for the application\n * Contains singleton services, universal components, and other features\n * that are used throughout the application and should be loaded only once\n */\nimport { NgModule, Optional, SkipSelf } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HttpClientModule } from '@angular/common/http';\nimport { RouterModule } from '@angular/router';\n// Import SharedModule for Angular Material components\nimport { SharedModule } from '../shared/shared.module';\n// Guards\nimport { AuthGuard } from './guards/auth.guard';\n// Services\nimport { AuthService } from './services/auth.service';\nimport { TaskService } from './services/task.service';\nimport { UserService } from './services/user.service';\nimport { NotificationService } from './services/notification.service';\n// Components\nimport { HeaderComponent } from './components/header/header.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport { SidenavComponent } from './components/sidenav/sidenav.component';\n// Interceptors are provided in app.module.ts\n/**\n * Prevents importing CoreModule in any module other than AppModule\n * @param parentModule - The parent module\n */\nexport function throwIfAlreadyLoaded(parentModule, moduleName) {\n  if (parentModule) {\n    throw new Error(`${moduleName} has already been loaded. Import Core modules in the AppModule only.`);\n  }\n}\nexport let CoreModule = class CoreModule {\n  /**\n   * Constructor to prevent reimporting of the CoreModule\n   * @param parentModule - The parent module\n   */\n  constructor(parentModule) {\n    throwIfAlreadyLoaded(parentModule, 'CoreModule');\n  }\n};\nCoreModule = __decorate([NgModule({\n  declarations: [HeaderComponent, FooterComponent, SidenavComponent],\n  imports: [CommonModule, HttpClientModule, RouterModule, SharedModule],\n  exports: [HeaderComponent, FooterComponent, SidenavComponent],\n  providers: [AuthGuard, AuthService, TaskService, UserService, NotificationService]\n}), __param(0, Optional()), __param(0, SkipSelf())], CoreModule);", "map": {"version": 3, "names": ["NgModule", "Optional", "SkipSelf", "CommonModule", "HttpClientModule", "RouterModule", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "AuthService", "TaskService", "UserService", "NotificationService", "HeaderComponent", "FooterComponent", "SidenavComponent", "throwIfAlreadyLoaded", "parentModule", "moduleName", "Error", "CoreModule", "constructor", "__decorate", "declarations", "imports", "exports", "providers", "__param"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\core.module.ts"], "sourcesContent": ["/**\n * Core module for the application\n * Contains singleton services, universal components, and other features\n * that are used throughout the application and should be loaded only once\n */\nimport { NgModule, Optional, SkipSelf } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { RouterModule } from '@angular/router';\n\n// Import SharedModule for Angular Material components\nimport { SharedModule } from '../shared/shared.module';\n\n// Guards\nimport { AuthGuard } from './guards/auth.guard';\n\n// Services\nimport { AuthService } from './services/auth.service';\nimport { TaskService } from './services/task.service';\nimport { UserService } from './services/user.service';\nimport { NotificationService } from './services/notification.service';\n\n// Components\nimport { HeaderComponent } from './components/header/header.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport { SidenavComponent } from './components/sidenav/sidenav.component';\n\n// Interceptors are provided in app.module.ts\n\n/**\n * Prevents importing CoreModule in any module other than AppModule\n * @param parentModule - The parent module\n */\nexport function throwIfAlreadyLoaded(parentModule: any, moduleName: string): void {\n  if (parentModule) {\n    throw new Error(\n      `${moduleName} has already been loaded. Import Core modules in the AppModule only.`\n    );\n  }\n}\n\n@NgModule({\n  declarations: [\n    HeaderComponent,\n    FooterComponent,\n    SidenavComponent\n  ],\n  imports: [\n    CommonModule,\n    HttpClientModule,\n    RouterModule,\n    SharedModule\n  ],\n  exports: [\n    HeaderComponent,\n    FooterComponent,\n    SidenavComponent\n  ],\n  providers: [\n    AuthGuard,\n    AuthService,\n    TaskService,\n    UserService,\n    NotificationService\n  ]\n})\nexport class CoreModule {\n  /**\n   * Constructor to prevent reimporting of the CoreModule\n   * @param parentModule - The parent module\n   */\n  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {\n    throwIfAlreadyLoaded(parentModule, 'CoreModule');\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAAA;;;;;AAKA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAA2B,sBAAsB;AAC1E,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,YAAY,QAAQ,yBAAyB;AAEtD;AACA,SAASC,SAAS,QAAQ,qBAAqB;AAE/C;AACA,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,iCAAiC;AAErE;AACA,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,gBAAgB,QAAQ,wCAAwC;AAEzE;AAEA;;;;AAIA,OAAM,SAAUC,oBAAoBA,CAACC,YAAiB,EAAEC,UAAkB;EACxE,IAAID,YAAY,EAAE;IAChB,MAAM,IAAIE,KAAK,CACb,GAAGD,UAAU,sEAAsE,CACpF;;AAEL;AA2BO,WAAME,UAAU,GAAhB,MAAMA,UAAU;EACrB;;;;EAIAC,YAAoCJ,YAAwB;IAC1DD,oBAAoB,CAACC,YAAY,EAAE,YAAY,CAAC;EAClD;CACD;AARYG,UAAU,GAAAE,UAAA,EAzBtBrB,QAAQ,CAAC;EACRsB,YAAY,EAAE,CACZV,eAAe,EACfC,eAAe,EACfC,gBAAgB,CACjB;EACDS,OAAO,EAAE,CACPpB,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,CACb;EACDkB,OAAO,EAAE,CACPZ,eAAe,EACfC,eAAe,EACfC,gBAAgB,CACjB;EACDW,SAAS,EAAE,CACTlB,SAAS,EACTC,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,mBAAmB;CAEtB,CAAC,EAMae,OAAA,IAAAzB,QAAQ,EAAE,GAAEyB,OAAA,IAAAxB,QAAQ,EAAE,E,EALxBiB,UAAU,CAQtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}