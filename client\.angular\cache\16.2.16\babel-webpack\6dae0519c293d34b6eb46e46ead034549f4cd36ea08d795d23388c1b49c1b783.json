{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/table\";\nfunction RecentTasksComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"assignment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No tasks available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RecentTasksComponent_table_2_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 15);\n    i0.ɵɵtext(1, \"Task\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RecentTasksComponent_table_2_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r15 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(task_r15.title);\n  }\n}\nfunction RecentTasksComponent_table_2_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 15);\n    i0.ɵɵtext(1, \"Priority\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RecentTasksComponent_table_2_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 16)(1, \"span\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r16 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r6.getPriorityClass(task_r16.priority));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, task_r16.priority), \" \");\n  }\n}\nfunction RecentTasksComponent_table_2_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 15);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RecentTasksComponent_table_2_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 16)(1, \"span\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r17 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.getStatusClass(task_r17.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.formatStatus(task_r17.status), \" \");\n  }\n}\nfunction RecentTasksComponent_table_2_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 15);\n    i0.ɵɵtext(1, \"Updated\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RecentTasksComponent_table_2_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r18 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, task_r18.updatedAt, \"short\"), \" \");\n  }\n}\nfunction RecentTasksComponent_table_2_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 15);\n  }\n}\nfunction RecentTasksComponent_table_2_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 16)(1, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function RecentTasksComponent_table_2_td_15_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const task_r19 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.viewTask(task_r19._id));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RecentTasksComponent_table_2_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 20);\n  }\n}\nfunction RecentTasksComponent_table_2_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 21);\n    i0.ɵɵlistener(\"click\", function RecentTasksComponent_table_2_tr_17_Template_tr_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r24);\n      const row_r22 = restoredCtx.$implicit;\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.viewTask(row_r22._id));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RecentTasksComponent_table_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 5);\n    i0.ɵɵelementContainerStart(1, 6);\n    i0.ɵɵtemplate(2, RecentTasksComponent_table_2_th_2_Template, 2, 0, \"th\", 7);\n    i0.ɵɵtemplate(3, RecentTasksComponent_table_2_td_3_Template, 2, 1, \"td\", 8);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(4, 9);\n    i0.ɵɵtemplate(5, RecentTasksComponent_table_2_th_5_Template, 2, 0, \"th\", 7);\n    i0.ɵɵtemplate(6, RecentTasksComponent_table_2_td_6_Template, 4, 4, \"td\", 8);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(7, 10);\n    i0.ɵɵtemplate(8, RecentTasksComponent_table_2_th_8_Template, 2, 0, \"th\", 7);\n    i0.ɵɵtemplate(9, RecentTasksComponent_table_2_td_9_Template, 3, 2, \"td\", 8);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(10, 11);\n    i0.ɵɵtemplate(11, RecentTasksComponent_table_2_th_11_Template, 2, 0, \"th\", 7);\n    i0.ɵɵtemplate(12, RecentTasksComponent_table_2_td_12_Template, 3, 4, \"td\", 8);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(13, 12);\n    i0.ɵɵtemplate(14, RecentTasksComponent_table_2_th_14_Template, 1, 0, \"th\", 7);\n    i0.ɵɵtemplate(15, RecentTasksComponent_table_2_td_15_Template, 4, 0, \"td\", 8);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(16, RecentTasksComponent_table_2_tr_16_Template, 1, 0, \"tr\", 13);\n    i0.ɵɵtemplate(17, RecentTasksComponent_table_2_tr_17_Template, 1, 0, \"tr\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"dataSource\", ctx_r1.tasks);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r1.displayedColumns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r1.displayedColumns);\n  }\n}\nfunction RecentTasksComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"a\", 23);\n    i0.ɵɵtext(2, \" View All Tasks \");\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"arrow_forward\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class RecentTasksComponent {\n  /**\n   * Constructor with dependency injection\n   * @param router - Router for navigation\n   */\n  constructor(router) {\n    this.router = router;\n    /**\n     * List of recent tasks\n     */\n    this.tasks = [];\n    /**\n     * Columns to display in the task table\n     */\n    this.displayedColumns = ['title', 'priority', 'status', 'updatedAt', 'actions'];\n  }\n  /**\n   * Navigate to task detail page\n   * @param taskId - ID of the task to view\n   */\n  viewTask(taskId) {\n    this.router.navigate(['/tasks', taskId]);\n  }\n  /**\n   * Get CSS class for task priority\n   * @param priority - Task priority\n   * @returns CSS class name\n   */\n  getPriorityClass(priority) {\n    switch (priority) {\n      case 'high':\n        return 'priority-high';\n      case 'medium':\n        return 'priority-medium';\n      case 'low':\n        return 'priority-low';\n      default:\n        return '';\n    }\n  }\n  /**\n   * Get CSS class for task status\n   * @param status - Task status\n   * @returns CSS class name\n   */\n  getStatusClass(status) {\n    switch (status) {\n      case 'todo':\n        return 'status-todo';\n      case 'in_progress':\n        return 'status-in-progress';\n      case 'done':\n        return 'status-done';\n      default:\n        return '';\n    }\n  }\n  /**\n   * Format task status for display\n   * @param status - Task status\n   * @returns Formatted status string\n   */\n  formatStatus(status) {\n    switch (status) {\n      case 'todo':\n        return 'To Do';\n      case 'in_progress':\n        return 'In Progress';\n      case 'done':\n        return 'Done';\n      default:\n        return status;\n    }\n  }\n  static {\n    this.ɵfac = function RecentTasksComponent_Factory(t) {\n      return new (t || RecentTasksComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RecentTasksComponent,\n      selectors: [[\"app-recent-tasks\"]],\n      inputs: {\n        tasks: \"tasks\"\n      },\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"recent-tasks-container\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"mat-table\", \"\", \"class\", \"tasks-table\", 3, \"dataSource\", 4, \"ngIf\"], [\"class\", \"view-all-container\", 4, \"ngIf\"], [1, \"empty-state\"], [\"mat-table\", \"\", 1, \"tasks-table\", 3, \"dataSource\"], [\"matColumnDef\", \"title\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"priority\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"updatedAt\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", \"class\", \"task-row\", 3, \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"priority-badge\", 3, \"ngClass\"], [1, \"status-badge\", 3, \"ngClass\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"aria-label\", \"View task details\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\", 1, \"task-row\", 3, \"click\"], [1, \"view-all-container\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/tasks\"]],\n      template: function RecentTasksComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, RecentTasksComponent_div_1_Template, 5, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, RecentTasksComponent_table_2_Template, 18, 3, \"table\", 2);\n          i0.ɵɵtemplate(3, RecentTasksComponent_div_3_Template, 5, 0, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tasks.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tasks.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tasks.length > 0);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf, i1.RouterLink, i3.MatAnchor, i3.MatIconButton, i4.MatIcon, i5.MatTable, i5.MatHeaderCellDef, i5.MatHeaderRowDef, i5.MatColumnDef, i5.MatCellDef, i5.MatRowDef, i5.MatHeaderCell, i5.MatCell, i5.MatHeaderRow, i5.MatRow, i2.TitleCasePipe, i2.DatePipe],\n      styles: [\"\\n\\n\\n\\n\\n\\n.recent-tasks-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow-x: auto;\\n}\\n\\n\\n\\n.tasks-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.tasks-table[_ngcontent-%COMP%]   th.mat-header-cell[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n.tasks-table[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.tasks-table[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n\\n\\n\\n.priority-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  text-align: center;\\n  min-width: 60px;\\n}\\n\\n\\n\\n.priority-high[_ngcontent-%COMP%] {\\n  background-color: rgba(244, 67, 54, 0.1);\\n  color: #f44336;\\n}\\n\\n.priority-medium[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 152, 0, 0.1);\\n  color: #ff9800;\\n}\\n\\n.priority-low[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.1);\\n  color: #4caf50;\\n}\\n\\n\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  text-align: center;\\n  min-width: 80px;\\n}\\n\\n\\n\\n.status-todo[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 152, 0, 0.1);\\n  color: #ff9800;\\n}\\n\\n.status-in-progress[_ngcontent-%COMP%] {\\n  background-color: rgba(33, 150, 243, 0.1);\\n  color: #2196f3;\\n}\\n\\n.status-done[_ngcontent-%COMP%] {\\n  background-color: rgba(76, 175, 80, 0.1);\\n  color: #4caf50;\\n}\\n\\n\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 32px 16px;\\n  color: #9e9e9e;\\n}\\n.empty-state[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  margin-bottom: 16px;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n\\n\\n.view-all-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  margin-top: 16px;\\n}\\n.view-all-container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.view-all-container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 4px;\\n}\\n\\n\\n\\n@media (max-width: 600px) {\\n  .tasks-table[_ngcontent-%COMP%]   .mat-header-row[_ngcontent-%COMP%], .tasks-table[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n  }\\n  .tasks-table[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%], .tasks-table[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%] {\\n    padding: 8px 4px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "task_r15", "title", "ɵɵproperty", "ctx_r6", "getPriorityClass", "task_r16", "priority", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ctx_r8", "getStatusClass", "task_r17", "status", "formatStatus", "ɵɵpipeBind2", "task_r18", "updatedAt", "ɵɵelement", "ɵɵlistener", "RecentTasksComponent_table_2_td_15_Template_button_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r21", "task_r19", "$implicit", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "viewTask", "_id", "RecentTasksComponent_table_2_tr_17_Template_tr_click_0_listener", "_r24", "row_r22", "ctx_r23", "ɵɵelementContainerStart", "ɵɵtemplate", "RecentTasksComponent_table_2_th_2_Template", "RecentTasksComponent_table_2_td_3_Template", "ɵɵelementContainerEnd", "RecentTasksComponent_table_2_th_5_Template", "RecentTasksComponent_table_2_td_6_Template", "RecentTasksComponent_table_2_th_8_Template", "RecentTasksComponent_table_2_td_9_Template", "RecentTasksComponent_table_2_th_11_Template", "RecentTasksComponent_table_2_td_12_Template", "RecentTasksComponent_table_2_th_14_Template", "RecentTasksComponent_table_2_td_15_Template", "RecentTasksComponent_table_2_tr_16_Template", "RecentTasksComponent_table_2_tr_17_Template", "ctx_r1", "tasks", "displayedColumns", "RecentTasksComponent", "constructor", "router", "taskId", "navigate", "ɵɵdirectiveInject", "i1", "Router", "selectors", "inputs", "decls", "vars", "consts", "template", "RecentTasksComponent_Template", "rf", "ctx", "RecentTasksComponent_div_1_Template", "RecentTasksComponent_table_2_Template", "RecentTasksComponent_div_3_Template", "length"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\recent-tasks\\recent-tasks.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\dashboard\\components\\recent-tasks\\recent-tasks.component.html"], "sourcesContent": ["/**\n * Recent Tasks Component\n * Displays a list of recently updated tasks\n */\nimport { Component, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Task } from '../../../../core/models/task.model';\n\n@Component({\n  selector: 'app-recent-tasks',\n  templateUrl: './recent-tasks.component.html',\n  styleUrls: ['./recent-tasks.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class RecentTasksComponent {\n  /**\n   * List of recent tasks\n   */\n  @Input() tasks: Task[] = [];\n  \n  /**\n   * Columns to display in the task table\n   */\n  displayedColumns: string[] = ['title', 'priority', 'status', 'updatedAt', 'actions'];\n  \n  /**\n   * Constructor with dependency injection\n   * @param router - Router for navigation\n   */\n  constructor(private router: Router) {}\n  \n  /**\n   * Navigate to task detail page\n   * @param taskId - ID of the task to view\n   */\n  viewTask(taskId: string): void {\n    this.router.navigate(['/tasks', taskId]);\n  }\n  \n  /**\n   * Get CSS class for task priority\n   * @param priority - Task priority\n   * @returns CSS class name\n   */\n  getPriorityClass(priority: string): string {\n    switch (priority) {\n      case 'high':\n        return 'priority-high';\n      case 'medium':\n        return 'priority-medium';\n      case 'low':\n        return 'priority-low';\n      default:\n        return '';\n    }\n  }\n  \n  /**\n   * Get CSS class for task status\n   * @param status - Task status\n   * @returns CSS class name\n   */\n  getStatusClass(status: string): string {\n    switch (status) {\n      case 'todo':\n        return 'status-todo';\n      case 'in_progress':\n        return 'status-in-progress';\n      case 'done':\n        return 'status-done';\n      default:\n        return '';\n    }\n  }\n  \n  /**\n   * Format task status for display\n   * @param status - Task status\n   * @returns Formatted status string\n   */\n  formatStatus(status: string): string {\n    switch (status) {\n      case 'todo':\n        return 'To Do';\n      case 'in_progress':\n        return 'In Progress';\n      case 'done':\n        return 'Done';\n      default:\n        return status;\n    }\n  }\n}\n", "<!-- Recent tasks table -->\n<div class=\"recent-tasks-container\">\n  <!-- Empty state message -->\n  <div *ngIf=\"tasks.length === 0\" class=\"empty-state\">\n    <mat-icon>assignment</mat-icon>\n    <p>No tasks available</p>\n  </div>\n\n  <!-- Tasks table -->\n  <table mat-table [dataSource]=\"tasks\" class=\"tasks-table\" *ngIf=\"tasks.length > 0\">\n    <!-- Title Column -->\n    <ng-container matColumnDef=\"title\">\n      <th mat-header-cell *matHeaderCellDef>Task</th>\n      <td mat-cell *matCellDef=\"let task\">{{ task.title }}</td>\n    </ng-container>\n\n    <!-- Priority Column -->\n    <ng-container matColumnDef=\"priority\">\n      <th mat-header-cell *matHeaderCellDef>Priority</th>\n      <td mat-cell *matCellDef=\"let task\">\n        <span class=\"priority-badge\" [ngClass]=\"getPriorityClass(task.priority)\">\n          {{ task.priority | titlecase }}\n        </span>\n      </td>\n    </ng-container>\n\n    <!-- Status Column -->\n    <ng-container matColumnDef=\"status\">\n      <th mat-header-cell *matHeaderCellDef>Status</th>\n      <td mat-cell *matCellDef=\"let task\">\n        <span class=\"status-badge\" [ngClass]=\"getStatusClass(task.status)\">\n          {{ formatStatus(task.status) }}\n        </span>\n      </td>\n    </ng-container>\n\n    <!-- Updated Date Column -->\n    <ng-container matColumnDef=\"updatedAt\">\n      <th mat-header-cell *matHeaderCellDef>Updated</th>\n      <td mat-cell *matCellDef=\"let task\">\n        {{ task.updatedAt | date:'short' }}\n      </td>\n    </ng-container>\n\n    <!-- Actions Column -->\n    <ng-container matColumnDef=\"actions\">\n      <th mat-header-cell *matHeaderCellDef></th>\n      <td mat-cell *matCellDef=\"let task\">\n        <button \n          mat-icon-button \n          color=\"primary\" \n          (click)=\"viewTask(task._id)\"\n          aria-label=\"View task details\">\n          <mat-icon>visibility</mat-icon>\n        </button>\n      </td>\n    </ng-container>\n\n    <!-- Table Header and Rows -->\n    <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n    <tr \n      mat-row \n      *matRowDef=\"let row; columns: displayedColumns;\"\n      (click)=\"viewTask(row._id)\"\n      class=\"task-row\">\n    </tr>\n  </table>\n\n  <!-- View all tasks link -->\n  <div class=\"view-all-container\" *ngIf=\"tasks.length > 0\">\n    <a mat-button color=\"primary\" routerLink=\"/tasks\">\n      View All Tasks\n      <mat-icon>arrow_forward</mat-icon>\n    </a>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICGEA,EAAA,CAAAC,cAAA,aAAoD;IACxCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAOvBH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC/CH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAArBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAC,KAAA,CAAgB;;;;;IAKpDP,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACnDH,EAAA,CAAAC,cAAA,aAAoC;IAEhCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFsBH,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAQ,UAAA,YAAAC,MAAA,CAAAC,gBAAA,CAAAC,QAAA,CAAAC,QAAA,EAA2C;IACtEZ,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,OAAAH,QAAA,CAAAC,QAAA,OACF;;;;;IAMFZ,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACjDH,EAAA,CAAAC,cAAA,aAAoC;IAEhCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFoBH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAQ,UAAA,YAAAO,MAAA,CAAAC,cAAA,CAAAC,QAAA,CAAAC,MAAA,EAAuC;IAChElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAa,kBAAA,MAAAE,MAAA,CAAAI,YAAA,CAAAF,QAAA,CAAAC,MAAA,OACF;;;;;IAMFlB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAClDH,EAAA,CAAAC,cAAA,aAAoC;IAClCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IADHH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAoB,WAAA,OAAAC,QAAA,CAAAC,SAAA,gBACF;;;;;IAKAtB,EAAA,CAAAuB,SAAA,aAA2C;;;;;;IAC3CvB,EAAA,CAAAC,cAAA,aAAoC;IAIhCD,EAAA,CAAAwB,UAAA,mBAAAC,oEAAA;MAAA,MAAAC,WAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAF,OAAA,CAAAG,QAAA,CAAAL,QAAA,CAAAM,GAAA,CAAkB;IAAA,EAAC;IAE5BnC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAMrCH,EAAA,CAAAuB,SAAA,aAA4D;;;;;;IAC5DvB,EAAA,CAAAC,cAAA,aAImB;IADjBD,EAAA,CAAAwB,UAAA,mBAAAY,gEAAA;MAAA,MAAAV,WAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAU,IAAA;MAAA,MAAAC,OAAA,GAAAZ,WAAA,CAAAI,SAAA;MAAA,MAAAS,OAAA,GAAAvC,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAM,OAAA,CAAAL,QAAA,CAAAI,OAAA,CAAAH,GAAA,CAAiB;IAAA,EAAC;IAE7BnC,EAAA,CAAAG,YAAA,EAAK;;;;;IAxDPH,EAAA,CAAAC,cAAA,eAAmF;IAEjFD,EAAA,CAAAwC,uBAAA,MAAmC;IACjCxC,EAAA,CAAAyC,UAAA,IAAAC,0CAAA,gBAA+C;IAC/C1C,EAAA,CAAAyC,UAAA,IAAAE,0CAAA,gBAAyD;IAC3D3C,EAAA,CAAA4C,qBAAA,EAAe;IAGf5C,EAAA,CAAAwC,uBAAA,MAAsC;IACpCxC,EAAA,CAAAyC,UAAA,IAAAI,0CAAA,gBAAmD;IACnD7C,EAAA,CAAAyC,UAAA,IAAAK,0CAAA,gBAIK;IACP9C,EAAA,CAAA4C,qBAAA,EAAe;IAGf5C,EAAA,CAAAwC,uBAAA,OAAoC;IAClCxC,EAAA,CAAAyC,UAAA,IAAAM,0CAAA,gBAAiD;IACjD/C,EAAA,CAAAyC,UAAA,IAAAO,0CAAA,gBAIK;IACPhD,EAAA,CAAA4C,qBAAA,EAAe;IAGf5C,EAAA,CAAAwC,uBAAA,QAAuC;IACrCxC,EAAA,CAAAyC,UAAA,KAAAQ,2CAAA,gBAAkD;IAClDjD,EAAA,CAAAyC,UAAA,KAAAS,2CAAA,gBAEK;IACPlD,EAAA,CAAA4C,qBAAA,EAAe;IAGf5C,EAAA,CAAAwC,uBAAA,QAAqC;IACnCxC,EAAA,CAAAyC,UAAA,KAAAU,2CAAA,gBAA2C;IAC3CnD,EAAA,CAAAyC,UAAA,KAAAW,2CAAA,gBAQK;IACPpD,EAAA,CAAA4C,qBAAA,EAAe;IAGf5C,EAAA,CAAAyC,UAAA,KAAAY,2CAAA,iBAA4D;IAC5DrD,EAAA,CAAAyC,UAAA,KAAAa,2CAAA,iBAKK;IACPtD,EAAA,CAAAG,YAAA,EAAQ;;;;IAzDSH,EAAA,CAAAQ,UAAA,eAAA+C,MAAA,CAAAC,KAAA,CAAoB;IAkDfxD,EAAA,CAAAI,SAAA,IAAiC;IAAjCJ,EAAA,CAAAQ,UAAA,oBAAA+C,MAAA,CAAAE,gBAAA,CAAiC;IAG9BzD,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAQ,UAAA,qBAAA+C,MAAA,CAAAE,gBAAA,CAA0B;;;;;IAOnDzD,EAAA,CAAAC,cAAA,cAAyD;IAErDD,EAAA,CAAAE,MAAA,uBACA;IAAAF,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;AD1DxC,OAAM,MAAOuD,oBAAoB;EAW/B;;;;EAIAC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAd1B;;;IAGS,KAAAJ,KAAK,GAAW,EAAE;IAE3B;;;IAGA,KAAAC,gBAAgB,GAAa,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;EAM/C;EAErC;;;;EAIAvB,QAAQA,CAAC2B,MAAc;IACrB,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,QAAQ,EAAED,MAAM,CAAC,CAAC;EAC1C;EAEA;;;;;EAKAnD,gBAAgBA,CAACE,QAAgB;IAC/B,QAAQA,QAAQ;MACd,KAAK,MAAM;QACT,OAAO,eAAe;MACxB,KAAK,QAAQ;QACX,OAAO,iBAAiB;MAC1B,KAAK,KAAK;QACR,OAAO,cAAc;MACvB;QACE,OAAO,EAAE;;EAEf;EAEA;;;;;EAKAI,cAAcA,CAACE,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,aAAa;MACtB,KAAK,aAAa;QAChB,OAAO,oBAAoB;MAC7B,KAAK,MAAM;QACT,OAAO,aAAa;MACtB;QACE,OAAO,EAAE;;EAEf;EAEA;;;;;EAKAC,YAAYA,CAACD,MAAc;IACzB,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,OAAO;MAChB,KAAK,aAAa;QAChB,OAAO,aAAa;MACtB,KAAK,MAAM;QACT,OAAO,MAAM;MACf;QACE,OAAOA,MAAM;;EAEnB;;;uBA7EWwC,oBAAoB,EAAA1D,EAAA,CAAA+D,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBP,oBAAoB;MAAAQ,SAAA;MAAAC,MAAA;QAAAX,KAAA;MAAA;MAAAY,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbjCzE,EAAA,CAAAC,cAAA,aAAoC;UAElCD,EAAA,CAAAyC,UAAA,IAAAkC,mCAAA,iBAGM;UAGN3E,EAAA,CAAAyC,UAAA,IAAAmC,qCAAA,oBAyDQ;UAGR5E,EAAA,CAAAyC,UAAA,IAAAoC,mCAAA,iBAKM;UACR7E,EAAA,CAAAG,YAAA,EAAM;;;UAxEEH,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAQ,UAAA,SAAAkE,GAAA,CAAAlB,KAAA,CAAAsB,MAAA,OAAwB;UAM6B9E,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAQ,UAAA,SAAAkE,GAAA,CAAAlB,KAAA,CAAAsB,MAAA,KAAsB;UA4DhD9E,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAQ,UAAA,SAAAkE,GAAA,CAAAlB,KAAA,CAAAsB,MAAA,KAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}