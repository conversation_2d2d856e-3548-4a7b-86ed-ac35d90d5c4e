/**
 * Swagger documentation setup
 * Configures and initializes Swagger UI for API documentation
 */
import { Application } from 'express';
import swaggerJsDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

/**
 * Sets up Swagger documentation for the API
 * @param app - Express application instance
 */
const swaggerSetup = (app: Application): void => {
  // Swagger definition options
  const swaggerOptions = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Task Management API',
        version: '1.0.0',
        description: 'API documentation for Task Management Application',
        contact: {
          name: 'API Support',
          url: 'https://github.com/Mustaffa96/task-management-application',
        },
        license: {
          name: 'MIT',
          url: 'https://opensource.org/licenses/MIT',
        },
      },
      servers: [
        {
          url: `http://localhost:${process.env.PORT || 5000}`,
          description: 'Development server',
        },
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
      security: [
        {
          bearerAuth: [],
        },
      ],
    },
    // Path to the API docs
    apis: ['./src/infrastructure/routes/*.ts', './src/domain/entities/*.ts'],
  };

  // Initialize swagger docs
  const swaggerDocs = swaggerJsDoc(swaggerOptions);

  // Setup swagger UI route
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
  }));

  console.log(`Swagger docs available at /api-docs`);
};

export default swaggerSetup;
