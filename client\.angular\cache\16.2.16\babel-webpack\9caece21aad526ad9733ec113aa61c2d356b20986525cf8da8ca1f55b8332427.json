{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Login Page Component\n * Page for user authentication\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nexport let LoginPageComponent = class LoginPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, notificationService, router, cdr) {\n    this.authService = authService;\n    this.notificationService = notificationService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed login attempt\n     */\n    this.error = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit() {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Handle login form submission\n   * @param credentials - User credentials\n   */\n  onFormSubmit(credentials) {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    this.authService.login(credentials.email, credentials.password).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.loading = false;\n        this.notificationService.success('Login successful');\n        this.router.navigate(['/dashboard']);\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.message || 'Invalid email or password';\n        console.error('Login error:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Navigate to forgot password page\n   */\n  onForgotPassword() {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n  /**\n   * Navigate to register page\n   */\n  onRegister() {\n    this.router.navigate(['/auth/register']);\n  }\n};\nLoginPageComponent = __decorate([Component({\n  selector: 'app-login-page',\n  templateUrl: './login-page.component.html',\n  styleUrls: ['./login-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], LoginPageComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "Subject", "takeUntil", "LoginPageComponent", "constructor", "authService", "notificationService", "router", "cdr", "loading", "error", "destroy$", "ngOnInit", "isAuthenticated", "navigate", "ngOnDestroy", "next", "complete", "onFormSubmit", "credentials", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "login", "email", "password", "pipe", "subscribe", "success", "err", "message", "console", "onForgotPassword", "onRegister", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\login-page\\login-page.component.ts"], "sourcesContent": ["/**\n * Login Page Component\n * Page for user authentication\n */\nimport { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-login-page',\n  templateUrl: './login-page.component.html',\n  styleUrls: ['./login-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class LoginPageComponent implements OnInit, OnDestroy {\n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message from failed login attempt\n   */\n  error: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private notificationService: NotificationService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit(): void {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Handle login form submission\n   * @param credentials - User credentials\n   */\n  onFormSubmit(credentials: { email: string; password: string }): void {\n    this.loading = true;\n    this.error = null;\n    this.cdr.markForCheck();\n    \n    this.authService.login(credentials.email, credentials.password)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.loading = false;\n          this.notificationService.success('Login successful');\n          this.router.navigate(['/dashboard']);\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = err.message || 'Invalid email or password';\n          console.error('Login error:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Navigate to forgot password page\n   */\n  onForgotPassword(): void {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n\n  /**\n   * Navigate to register page\n   */\n  onRegister(): void {\n    this.router.navigate(['/auth/register']);\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AAExG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAUnC,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAgB7B;;;;;;;EAOAC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA1Bb;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIV,OAAO,EAAQ;EAcnC;EAEH;;;;EAIAW,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACP,WAAW,CAACQ,eAAe,EAAE,EAAE;MACtC,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;;EAIAC,YAAYA,CAACC,WAAgD;IAC3D,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACF,GAAG,CAACY,YAAY,EAAE;IAEvB,IAAI,CAACf,WAAW,CAACgB,KAAK,CAACF,WAAW,CAACG,KAAK,EAAEH,WAAW,CAACI,QAAQ,CAAC,CAC5DC,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACS,QAAQ,CAAC,CAAC,CAC9Bc,SAAS,CAAC;MACTT,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACP,OAAO,GAAG,KAAK;QACpB,IAAI,CAACH,mBAAmB,CAACoB,OAAO,CAAC,kBAAkB,CAAC;QACpD,IAAI,CAACnB,MAAM,CAACO,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MACtC,CAAC;MACDJ,KAAK,EAAGiB,GAAG,IAAI;QACb,IAAI,CAAClB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGiB,GAAG,CAACC,OAAO,IAAI,2BAA2B;QACvDC,OAAO,CAACnB,KAAK,CAAC,cAAc,EAAEiB,GAAG,CAAC;QAClC,IAAI,CAACnB,GAAG,CAACY,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAU,gBAAgBA,CAAA;IACd,IAAI,CAACvB,MAAM,CAACO,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAEA;;;EAGAiB,UAAUA,CAAA;IACR,IAAI,CAACxB,MAAM,CAACO,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;CACD;AAzFYX,kBAAkB,GAAA6B,UAAA,EAN9BjC,SAAS,CAAC;EACTkC,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,6BAA6B,CAAC;EAC1CC,eAAe,EAAEpC,uBAAuB,CAACqC;CAC1C,CAAC,C,EACWlC,kBAAkB,CAyF9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}