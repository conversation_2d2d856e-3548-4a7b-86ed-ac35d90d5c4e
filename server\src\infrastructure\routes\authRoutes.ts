/**
 * Authentication Routes
 * Defines API endpoints for authentication operations
 */
import { Router } from 'express';
import {
  register,
  login,
  getMe,
} from '../controllers/authController';
import { protect } from '../middleware/authMiddleware';

// Create router instance
const router = Router();

// Define public routes
router.post('/register', register);
router.post('/login', login);

// Define protected routes
router.get('/me', protect, getMe);

export default router;
