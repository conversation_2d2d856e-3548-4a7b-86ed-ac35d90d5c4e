{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"../../../../core/services/notification.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"../../components/reset-password-form/reset-password-form.component\";\nexport class ResetPasswordPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param route - Activated route for getting route parameters\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, notificationService, route, router, cdr) {\n    this.authService = authService;\n    this.notificationService = notificationService;\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed request\n     */\n    this.error = null;\n    /**\n     * Success message after successful request\n     */\n    this.success = null;\n    /**\n     * Reset token from URL\n     */\n    this.token = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Gets token from URL and checks if user is already authenticated\n   */\n  ngOnInit() {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n      return;\n    }\n    // Get token from query params\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.token = params['token'] || null;\n      this.cdr.markForCheck();\n    });\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Handle reset password form submission\n   * @param data - Form data with password and token\n   */\n  onFormSubmit(data) {\n    this.loading = true;\n    this.error = null;\n    this.success = null;\n    this.cdr.markForCheck();\n    this.authService.resetPassword(data.token, data.password).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.loading = false;\n        this.success = 'Your password has been successfully reset.';\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.message || 'Failed to reset password. The link may have expired.';\n        console.error('Reset password error:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Navigate to login page\n   */\n  onLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  static {\n    this.ɵfac = function ResetPasswordPageComponent_Factory(t) {\n      return new (t || ResetPasswordPageComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResetPasswordPageComponent,\n      selectors: [[\"app-reset-password-page\"]],\n      decls: 11,\n      vars: 4,\n      consts: [[1, \"reset-password-page-container\"], [1, \"auth-container\"], [1, \"auth-header\"], [1, \"logo\"], [1, \"app-title\"], [1, \"app-subtitle\"], [3, \"loading\", \"error\", \"success\", \"token\", \"formSubmit\", \"login\"]],\n      template: function ResetPasswordPageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"task_alt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"h1\", 4);\n          i0.ɵɵtext(7, \"Task Manager\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 5);\n          i0.ɵɵtext(9, \"Create a new password\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"app-reset-password-form\", 6);\n          i0.ɵɵlistener(\"formSubmit\", function ResetPasswordPageComponent_Template_app_reset_password_form_formSubmit_10_listener($event) {\n            return ctx.onFormSubmit($event);\n          })(\"login\", function ResetPasswordPageComponent_Template_app_reset_password_form_login_10_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"loading\", ctx.loading)(\"error\", ctx.error)(\"success\", ctx.success)(\"token\", ctx.token);\n        }\n      },\n      dependencies: [i4.MatIcon, i5.ResetPasswordFormComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.reset-password-page-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  background-color: #f5f5f5;\\n  padding: 24px;\\n}\\n\\n\\n\\n.auth-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n\\n\\n.auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n\\n\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 16px;\\n}\\n.logo[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: var(--primary-color);\\n}\\n\\n\\n\\n.app-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 500;\\n  margin: 0 0 8px;\\n  color: #333;\\n}\\n\\n\\n\\n.app-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .reset-password-page-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    background-color: #fff;\\n  }\\n  .auth-header[_ngcontent-%COMP%] {\\n    margin-bottom: 24px;\\n  }\\n  .app-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYXV0aC9wYWdlcy9yZXNldC1wYXNzd29yZC1wYWdlL3Jlc2V0LXBhc3N3b3JkLXBhZ2UuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7O0VBQUE7QUFJQSxpREFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLHlCQUFBO0VBQ0EsYUFBQTtBQUFGOztBQUdBLDZCQUFBO0FBQ0E7RUFDRSxXQUFBO0VBQ0EsZ0JBQUE7QUFBRjs7QUFHQSwrQkFBQTtBQUNBO0VBQ0Usa0JBQUE7RUFDQSxtQkFBQTtBQUFGOztBQUdBLGlCQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQUFGO0FBRUU7RUFDRSxlQUFBO0VBQ0EsWUFBQTtFQUNBLFdBQUE7RUFDQSwyQkFBQTtBQUFKOztBQUlBLGNBQUE7QUFDQTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0FBREY7O0FBSUEsaUJBQUE7QUFDQTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsU0FBQTtBQURGOztBQUlBLDJCQUFBO0FBQ0E7RUFDRTtJQUNFLGFBQUE7SUFDQSxzQkFBQTtFQURGO0VBSUE7SUFDRSxtQkFBQTtFQUZGO0VBS0E7SUFDRSxpQkFBQTtFQUhGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJlc2V0IHBhc3N3b3JkIHBhZ2UgY29tcG9uZW50IHN0eWxlc1xuICovXG5cbi8qIENvbnRhaW5lciBmb3IgdGhlIGVudGlyZSByZXNldCBwYXNzd29yZCBwYWdlICovXG4ucmVzZXQtcGFzc3dvcmQtcGFnZS1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgbWluLWhlaWdodDogMTAwdmg7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gIHBhZGRpbmc6IDI0cHg7XG59XG5cbi8qIEF1dGhlbnRpY2F0aW9uIGNvbnRhaW5lciAqL1xuLmF1dGgtY29udGFpbmVyIHtcbiAgd2lkdGg6IDEwMCU7XG4gIG1heC13aWR0aDogNDAwcHg7XG59XG5cbi8qIEhlYWRlciB3aXRoIGxvZ28gYW5kIHRpdGxlICovXG4uYXV0aC1oZWFkZXIge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDMycHg7XG59XG5cbi8qIExvZ28gc3R5bGluZyAqL1xuLmxvZ28ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgXG4gIG1hdC1pY29uIHtcbiAgICBmb250LXNpemU6IDQ4cHg7XG4gICAgaGVpZ2h0OiA0OHB4O1xuICAgIHdpZHRoOiA0OHB4O1xuICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgfVxufVxuXG4vKiBBcHAgdGl0bGUgKi9cbi5hcHAtdGl0bGUge1xuICBmb250LXNpemU6IDJyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIG1hcmdpbjogMCAwIDhweDtcbiAgY29sb3I6ICMzMzM7XG59XG5cbi8qIEFwcCBzdWJ0aXRsZSAqL1xuLmFwcC1zdWJ0aXRsZSB7XG4gIGZvbnQtc2l6ZTogMXJlbTtcbiAgY29sb3I6ICM2NjY7XG4gIG1hcmdpbjogMDtcbn1cblxuLyogUmVzcG9uc2l2ZSBhZGp1c3RtZW50cyAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gIC5yZXNldC1wYXNzd29yZC1wYWdlLWNvbnRhaW5lciB7XG4gICAgcGFkZGluZzogMTZweDtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xuICB9XG4gIFxuICAuYXV0aC1oZWFkZXIge1xuICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gIH1cbiAgXG4gIC5hcHAtdGl0bGUge1xuICAgIGZvbnQtc2l6ZTogMS44cmVtO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "ResetPasswordPageComponent", "constructor", "authService", "notificationService", "route", "router", "cdr", "loading", "error", "success", "token", "destroy$", "ngOnInit", "isAuthenticated", "navigate", "queryParams", "pipe", "subscribe", "params", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "next", "complete", "onFormSubmit", "data", "resetPassword", "password", "err", "message", "console", "onLogin", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "NotificationService", "i3", "ActivatedRoute", "Router", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "ResetPasswordPageComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ResetPasswordPageComponent_Template_app_reset_password_form_formSubmit_10_listener", "$event", "ResetPasswordPageComponent_Template_app_reset_password_form_login_10_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\reset-password-page\\reset-password-page.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\reset-password-page\\reset-password-page.component.html"], "sourcesContent": ["/**\n * Reset Password Page Component\n * Page for resetting password with token\n */\nimport { Component, OnInit, OnD<PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-reset-password-page',\n  templateUrl: './reset-password-page.component.html',\n  styleUrls: ['./reset-password-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ResetPasswordPageComponent implements OnInit, OnDestroy {\n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message from failed request\n   */\n  error: string | null = null;\n  \n  /**\n   * Success message after successful request\n   */\n  success: string | null = null;\n  \n  /**\n   * Reset token from URL\n   */\n  token: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param route - Activated route for getting route parameters\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private notificationService: NotificationService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Gets token from URL and checks if user is already authenticated\n   */\n  ngOnInit(): void {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n      return;\n    }\n    \n    // Get token from query params\n    this.route.queryParams\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(params => {\n        this.token = params['token'] || null;\n        this.cdr.markForCheck();\n      });\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Handle reset password form submission\n   * @param data - Form data with password and token\n   */\n  onFormSubmit(data: { password: string; token: string }): void {\n    this.loading = true;\n    this.error = null;\n    this.success = null;\n    this.cdr.markForCheck();\n    \n    this.authService.resetPassword(data.token, data.password)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.loading = false;\n          this.success = 'Your password has been successfully reset.';\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = err.message || 'Failed to reset password. The link may have expired.';\n          console.error('Reset password error:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Navigate to login page\n   */\n  onLogin(): void {\n    this.router.navigate(['/auth/login']);\n  }\n}\n", "<!-- Reset password page container -->\n<div class=\"reset-password-page-container\">\n  <div class=\"auth-container\">\n    <!-- App logo and title -->\n    <div class=\"auth-header\">\n      <div class=\"logo\">\n        <mat-icon>task_alt</mat-icon>\n      </div>\n      <h1 class=\"app-title\">Task Manager</h1>\n      <p class=\"app-subtitle\">Create a new password</p>\n    </div>\n    \n    <!-- Reset password form component -->\n    <app-reset-password-form\n      [loading]=\"loading\"\n      [error]=\"error\"\n      [success]=\"success\"\n      [token]=\"token\"\n      (formSubmit)=\"onFormSubmit($event)\"\n      (login)=\"onLogin()\">\n    </app-reset-password-form>\n  </div>\n</div>\n"], "mappings": "AAMA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;AAU1C,OAAM,MAAOC,0BAA0B;EA0BrC;;;;;;;;EAQAC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,KAAqB,EACrBC,MAAc,EACdC,GAAsB;IAJtB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IAtCb;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGA,KAAAC,OAAO,GAAkB,IAAI;IAE7B;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIb,OAAO,EAAQ;EAgBnC;EAEH;;;;EAIAc,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACV,WAAW,CAACW,eAAe,EAAE,EAAE;MACtC,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MACpC;;IAGF;IACA,IAAI,CAACV,KAAK,CAACW,WAAW,CACnBC,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACY,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAACC,MAAM,IAAG;MAClB,IAAI,CAACR,KAAK,GAAGQ,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI;MACpC,IAAI,CAACZ,GAAG,CAACa,YAAY,EAAE;IACzB,CAAC,CAAC;EACN;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACT,QAAQ,CAACU,IAAI,EAAE;IACpB,IAAI,CAACV,QAAQ,CAACW,QAAQ,EAAE;EAC1B;EAEA;;;;EAIAC,YAAYA,CAACC,IAAyC;IACpD,IAAI,CAACjB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,GAAG,CAACa,YAAY,EAAE;IAEvB,IAAI,CAACjB,WAAW,CAACuB,aAAa,CAACD,IAAI,CAACd,KAAK,EAAEc,IAAI,CAACE,QAAQ,CAAC,CACtDV,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACY,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAAC;MACTI,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACd,OAAO,GAAG,KAAK;QACpB,IAAI,CAACE,OAAO,GAAG,4CAA4C;QAC3D,IAAI,CAACH,GAAG,CAACa,YAAY,EAAE;MACzB,CAAC;MACDX,KAAK,EAAGmB,GAAG,IAAI;QACb,IAAI,CAACpB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGmB,GAAG,CAACC,OAAO,IAAI,sDAAsD;QAClFC,OAAO,CAACrB,KAAK,CAAC,uBAAuB,EAAEmB,GAAG,CAAC;QAC3C,IAAI,CAACrB,GAAG,CAACa,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAW,OAAOA,CAAA;IACL,IAAI,CAACzB,MAAM,CAACS,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;;;uBAvGWd,0BAA0B,EAAA+B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAE,MAAA,GAAAR,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAS,iBAAA;IAAA;EAAA;;;YAA1BxC,0BAA0B;MAAAyC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBvChB,EAAA,CAAAkB,cAAA,aAA2C;UAKzBlB,EAAA,CAAAmB,MAAA,eAAQ;UAAAnB,EAAA,CAAAoB,YAAA,EAAW;UAE/BpB,EAAA,CAAAkB,cAAA,YAAsB;UAAAlB,EAAA,CAAAmB,MAAA,mBAAY;UAAAnB,EAAA,CAAAoB,YAAA,EAAK;UACvCpB,EAAA,CAAAkB,cAAA,WAAwB;UAAAlB,EAAA,CAAAmB,MAAA,4BAAqB;UAAAnB,EAAA,CAAAoB,YAAA,EAAI;UAInDpB,EAAA,CAAAkB,cAAA,kCAMsB;UADpBlB,EAAA,CAAAqB,UAAA,wBAAAC,mFAAAC,MAAA;YAAA,OAAcN,GAAA,CAAAzB,YAAA,CAAA+B,MAAA,CAAoB;UAAA,EAAC,mBAAAC,8EAAA;YAAA,OAC1BP,GAAA,CAAAlB,OAAA,EAAS;UAAA,EADiB;UAErCC,EAAA,CAAAoB,YAAA,EAA0B;;;UANxBpB,EAAA,CAAAyB,SAAA,IAAmB;UAAnBzB,EAAA,CAAA0B,UAAA,YAAAT,GAAA,CAAAzC,OAAA,CAAmB,UAAAyC,GAAA,CAAAxC,KAAA,aAAAwC,GAAA,CAAAvC,OAAA,WAAAuC,GAAA,CAAAtC,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}