/**
 * Delete Task Use Case
 * Handles business logic for deleting a task
 */
import { ITaskRepository } from '../../repositories/ITaskRepository';
import { AppError } from '../../common/AppError';

/**
 * Use case for deleting a task
 * Implements business rules and validation for task deletion
 */
export class DeleteTaskUseCase {
  /**
   * Constructor for DeleteTaskUseCase
   * @param taskRepository - Repository for task data access
   */
  constructor(private taskRepository: ITaskRepository) {}

  /**
   * Executes the use case to delete a task
   * @param taskId - ID of the task to delete
   * @returns Promise resolving to boolean indicating success
   * @throws AppError if task not found or deletion fails
   */
  async execute(taskId: string): Promise<boolean> {
    try {
      // Validate task ID
      if (!taskId) {
        throw new AppError('Task ID is required', 400);
      }

      // Delete task
      const deleted = await this.taskRepository.delete(taskId);
      
      // Check if task existed
      if (!deleted) {
        throw new AppError(`Task with ID ${taskId} not found`, 404);
      }
      
      return true;
    } catch (error) {
      // Rethrow AppErrors, wrap other errors
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Error deleting task: ${(error as Error).message}`, 500);
    }
  }
}
