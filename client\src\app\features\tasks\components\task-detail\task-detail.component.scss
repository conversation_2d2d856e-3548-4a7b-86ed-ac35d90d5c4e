/**
 * Task detail component styles
 */

/* Container for the entire task detail */
.task-detail-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

/* Loading state container */
.task-detail-loading {
  padding: 32px;
  display: flex;
  justify-content: center;
}

/* Header with title and actions */
.task-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

/* Task title */
.task-title {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 500;
  color: #333;
  flex: 1;
}

/* Task actions */
.task-actions {
  display: flex;
  gap: 8px;
}

/* Task metadata section */
.task-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}

/* Individual metadata item */
.metadata-item {
  display: flex;
  flex-direction: column;
  min-width: 150px;
  
  mat-form-field {
    width: 100%;
  }
}

/* Metadata label */
.metadata-label {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 4px;
}

/* Priority badge */
.priority-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  text-transform: uppercase;
  font-weight: 500;
  display: inline-block;
  
  &.priority-high {
    background-color: #ffebee;
    color: #c62828;
  }
  
  &.priority-medium {
    background-color: #fff8e1;
    color: #ff8f00;
  }
  
  &.priority-low {
    background-color: #e8f5e9;
    color: #2e7d32;
  }
}

/* Due date display */
.due-date {
  display: flex;
  align-items: center;
  font-size: 0.95rem;
  
  &.overdue {
    color: #d32f2f;
  }
  
  mat-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
    margin-right: 4px;
  }
}

/* Task section */
.task-section {
  margin-bottom: 24px;
  
  h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 12px;
    color: #444;
  }
}

/* Task description */
.task-description {
  line-height: 1.6;
  color: #333;
  white-space: pre-line;
}

/* Task tags */
.task-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* Individual tag */
.task-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
}

/* Task info section */
.task-info {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #666;
}

/* Info item */
.info-item {
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

/* Info label */
.info-label {
  font-weight: 500;
  margin-right: 8px;
}

/* Status select styling */
:host ::ng-deep {
  .status-todo .mat-mdc-select-value {
    color: #616161;
  }
  
  .status-in_progress .mat-mdc-select-value {
    color: #1976d2;
  }
  
  .status-review .mat-mdc-select-value {
    color: #e65100;
  }
  
  .status-done .mat-mdc-select-value {
    color: #2e7d32;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .task-detail-header {
    flex-direction: column;
    
    .task-actions {
      margin-top: 16px;
      align-self: flex-end;
    }
  }
  
  .task-metadata {
    flex-direction: column;
    gap: 8px;
  }
}
