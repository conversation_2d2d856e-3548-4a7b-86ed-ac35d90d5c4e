/**
 * Task item component styles
 */

/* Task item container */
.task-item {
  display: flex;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f9f9f9;
    cursor: pointer;
  }
  
  /* Status-based background tints */
  &.status-todo {
    border-left: 4px solid #9e9e9e;
  }
  
  &.status-in_progress {
    border-left: 4px solid #2196f3;
  }
  
  &.status-review {
    border-left: 4px solid #ff9800;
  }
  
  &.status-done {
    border-left: 4px solid #4caf50;
    background-color: #f9f9f9;
    
    .task-title, .task-description {
      color: #757575;
    }
  }
}

/* Column styling */
.task-column {
  padding: 12px 16px;
  display: flex;
  align-items: center;
}

/* Status column */
.task-status-column {
  width: 120px;
  flex-shrink: 0;
}

/* Status indicator */
.status-indicator {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  
  mat-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
    margin-left: 2px;
  }
  
  &.status-todo {
    background-color: #eeeeee;
    color: #616161;
  }
  
  &.status-in_progress {
    background-color: #e3f2fd;
    color: #1976d2;
  }
  
  &.status-review {
    background-color: #fff3e0;
    color: #e65100;
  }
  
  &.status-done {
    background-color: #e8f5e9;
    color: #2e7d32;
  }
}

/* Status dropdown menu */
.status-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 120px;
  margin-top: 4px;
}

/* Status option in dropdown */
.status-option {
  padding: 8px 12px;
  cursor: pointer;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &.status-todo {
    border-left: 4px solid #9e9e9e;
  }
  
  &.status-in_progress {
    border-left: 4px solid #2196f3;
  }
  
  &.status-review {
    border-left: 4px solid #ff9800;
  }
  
  &.status-done {
    border-left: 4px solid #4caf50;
  }
}

/* Title column */
.task-title-column {
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
}

/* Task title */
.task-title {
  font-weight: 500;
  margin-bottom: 4px;
}

/* Task description */
.task-description {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 8px;
}

/* Task tags */
.task-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* Individual tag */
.task-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
}

/* Priority column */
.task-priority-column {
  width: 100px;
  flex-shrink: 0;
}

/* Priority badge */
.priority-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  text-transform: uppercase;
  font-weight: 500;
  
  &.priority-high {
    background-color: #ffebee;
    color: #c62828;
  }
  
  &.priority-medium {
    background-color: #fff8e1;
    color: #ff8f00;
  }
  
  &.priority-low {
    background-color: #e8f5e9;
    color: #2e7d32;
  }
}

/* Due date column */
.task-date-column {
  width: 120px;
  flex-shrink: 0;
}

/* Due date display */
.due-date {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  
  &.overdue {
    color: #d32f2f;
  }
  
  mat-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
    margin-right: 4px;
  }
}

/* Actions column */
.task-actions-column {
  width: 100px;
  flex-shrink: 0;
  justify-content: flex-end;
}

/* Task actions */
.task-actions {
  display: flex;
  
  button {
    opacity: 0.6;
    
    &:hover {
      opacity: 1;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .task-status-column {
    width: 80px;
  }
  
  .task-priority-column,
  .task-date-column {
    display: none;
  }
  
  .task-actions-column {
    width: 80px;
  }
}
