/**
 * Task Item Component
 * Displays a single task item in the task list
 */
import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { Task } from '../../../../core/models/task.model';

@Component({
  selector: 'app-task-item',
  templateUrl: './task-item.component.html',
  styleUrls: ['./task-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskItemComponent {
  /**
   * Input task to display
   */
  @Input() task!: Task;
  
  /**
   * Event emitted when task is selected
   */
  @Output() select = new EventEmitter<void>();
  
  /**
   * Event emitted when task is deleted
   */
  @Output() delete = new EventEmitter<string>();
  
  /**
   * Event emitted when task status is changed
   */
  @Output() statusChange = new EventEmitter<string>();
  
  /**
   * Available task statuses
   */
  statuses = [
    { value: 'todo', label: 'To Do' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'review', label: 'Review' },
    { value: 'done', label: 'Done' }
  ];
  
  /**
   * Status menu is open
   */
  statusMenuOpen = false;

  /**
   * Handle task selection
   * @param event - Mouse event
   */
  onSelect(event: MouseEvent): void {
    // Prevent event bubbling if clicking on actions
    if ((event.target as HTMLElement).closest('.task-actions')) {
      return;
    }
    this.select.emit();
  }

  /**
   * Handle task deletion
   * @param event - Mouse event
   */
  onDelete(event: MouseEvent): void {
    event.stopPropagation();
    this.delete.emit(this.task.id);
  }

  /**
   * Handle status change
   * @param event - Mouse event
   * @param status - New status
   */
  onStatusChange(event: MouseEvent, status: string): void {
    event.stopPropagation();
    this.statusChange.emit(status);
    this.statusMenuOpen = false;
  }

  /**
   * Toggle status menu
   * @param event - Mouse event
   */
  toggleStatusMenu(event: MouseEvent): void {
    event.stopPropagation();
    this.statusMenuOpen = !this.statusMenuOpen;
  }

  /**
   * Get CSS class for priority
   * @returns CSS class name
   */
  getPriorityClass(): string {
    return `priority-${this.task.priority}`;
  }

  /**
   * Get CSS class for status
   * @returns CSS class name
   */
  getStatusClass(): string {
    return `status-${this.task.status}`;
  }

  /**
   * Get formatted status label
   * @returns Status label
   */
  getStatusLabel(): string {
    const status = this.statuses.find(s => s.value === this.task.status);
    return status ? status.label : this.task.status;
  }

  /**
   * Check if task is overdue
   * @returns True if task is overdue
   */
  isOverdue(): boolean {
    if (!this.task.dueDate || this.task.status === 'done') {
      return false;
    }
    
    const dueDate = new Date(this.task.dueDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return dueDate < today;
  }
}
