{"name": "task-management-client", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "analyze": "ng build --stats-json && webpack-bundle-analyzer dist/task-management-client/stats.json", "postinstall": "ngcc --properties es2020 browser module main"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "rxjs": "~7.8.0", "tslib": "^2.6.2", "web-vitals": "^5.1.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.0", "@angular-eslint/builder": "16.1.0", "@angular-eslint/eslint-plugin": "16.1.0", "@angular-eslint/eslint-plugin-template": "16.1.0", "@angular-eslint/schematics": "16.1.0", "@angular-eslint/template-parser": "16.1.0", "@angular/cli": "~16.2.0", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "eslint": "^8.40.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3", "webpack-bundle-analyzer": "^4.9.0"}}