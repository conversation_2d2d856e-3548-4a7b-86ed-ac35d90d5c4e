/**
 * Task entity definition
 * Core business object representing a task in the system
 */
import mongoose, { Document, Schema } from 'mongoose';

/**
 * @swagger
 * components:
 *   schemas:
 *     Task:
 *       type: object
 *       required:
 *         - title
 *         - status
 *       properties:
 *         id:
 *           type: string
 *           description: Auto-generated unique identifier
 *         title:
 *           type: string
 *           description: Task title
 *         description:
 *           type: string
 *           description: Detailed task description
 *         status:
 *           type: string
 *           enum: [todo, in-progress, done]
 *           description: Current status of the task
 *         assigneeId:
 *           type: string
 *           description: ID of the user assigned to the task
 *         dueDate:
 *           type: string
 *           format: date-time
 *           description: Task due date and time
 *         priority:
 *           type: string
 *           enum: [low, medium, high]
 *           description: Task priority level
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Task creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Task last update timestamp
 */

// Task status enum
export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in-progress',
  DONE = 'done',
}

// Task priority enum
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

// Task interface definition
export interface ITask {
  title: string;
  description?: string;
  status: TaskStatus;
  assigneeId?: mongoose.Types.ObjectId;
  dueDate?: Date;
  priority: TaskPriority;
}

// Task document interface for Mongoose
export interface ITaskDocument extends ITask, Document {
  createdAt: Date;
  updatedAt: Date;
}

// Task schema definition
const TaskSchema: Schema = new Schema(
  {
    title: {
      type: String,
      required: [true, 'Task title is required'],
      trim: true,
      maxlength: [100, 'Task title cannot exceed 100 characters'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Task description cannot exceed 500 characters'],
    },
    status: {
      type: String,
      enum: Object.values(TaskStatus),
      default: TaskStatus.TODO,
      required: [true, 'Task status is required'],
    },
    assigneeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    dueDate: {
      type: Date,
    },
    priority: {
      type: String,
      enum: Object.values(TaskPriority),
      default: TaskPriority.MEDIUM,
      required: [true, 'Task priority is required'],
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
    toJSON: {
      transform: (_, ret) => {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Create and export the Task model
export const Task = mongoose.model<ITaskDocument>('Task', TaskSchema);
