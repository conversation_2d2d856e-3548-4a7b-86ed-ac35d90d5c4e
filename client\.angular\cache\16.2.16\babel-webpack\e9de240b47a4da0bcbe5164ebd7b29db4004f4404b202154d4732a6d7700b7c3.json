{"ast": null, "code": "/**\n * Task Item Component\n * Displays a single task item in the task list\n */\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"../../../../shared/pipes/truncate.pipe\";\nfunction TaskItemComponent_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function TaskItemComponent_div_6_div_1_Template_div_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const status_r4 = restoredCtx.$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onStatusChange($event, status_r4.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + status_r4.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", status_r4.label, \" \");\n  }\n}\nfunction TaskItemComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, TaskItemComponent_div_6_div_1_Template, 2, 2, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.statuses);\n  }\n}\nfunction TaskItemComponent_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r8 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(tag_r8);\n  }\n}\nfunction TaskItemComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, TaskItemComponent_div_13_span_1_Template, 2, 1, \"span\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.task.tags);\n  }\n}\nfunction TaskItemComponent_mat_icon_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"warning\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"overdue\": a0\n  };\n};\nconst _c1 = function (a1) {\n  return [\"/tasks\", a1, \"edit\"];\n};\nexport class TaskItemComponent {\n  constructor() {\n    /**\n     * Event emitted when task is selected\n     */\n    this.select = new EventEmitter();\n    /**\n     * Event emitted when task is deleted\n     */\n    this.delete = new EventEmitter();\n    /**\n     * Event emitted when task status is changed\n     */\n    this.statusChange = new EventEmitter();\n    /**\n     * Available task statuses\n     */\n    this.statuses = [{\n      value: 'todo',\n      label: 'To Do'\n    }, {\n      value: 'in_progress',\n      label: 'In Progress'\n    }, {\n      value: 'review',\n      label: 'Review'\n    }, {\n      value: 'done',\n      label: 'Done'\n    }];\n    /**\n     * Status menu is open\n     */\n    this.statusMenuOpen = false;\n  }\n  /**\n   * Handle task selection\n   * @param event - Mouse event\n   */\n  onSelect(event) {\n    // Prevent event bubbling if clicking on actions\n    if (event.target.closest('.task-actions')) {\n      return;\n    }\n    this.select.emit();\n  }\n  /**\n   * Handle task deletion\n   * @param event - Mouse event\n   */\n  onDelete(event) {\n    event.stopPropagation();\n    this.delete.emit(this.task.id);\n  }\n  /**\n   * Handle status change\n   * @param event - Mouse event\n   * @param status - New status\n   */\n  onStatusChange(event, status) {\n    event.stopPropagation();\n    this.statusChange.emit(status);\n    this.statusMenuOpen = false;\n  }\n  /**\n   * Toggle status menu\n   * @param event - Mouse event\n   */\n  toggleStatusMenu(event) {\n    event.stopPropagation();\n    this.statusMenuOpen = !this.statusMenuOpen;\n  }\n  /**\n   * Get CSS class for priority\n   * @returns CSS class name\n   */\n  getPriorityClass() {\n    return `priority-${this.task.priority}`;\n  }\n  /**\n   * Get CSS class for status\n   * @returns CSS class name\n   */\n  getStatusClass() {\n    return `status-${this.task.status}`;\n  }\n  /**\n   * Get formatted status label\n   * @returns Status label\n   */\n  getStatusLabel() {\n    const status = this.statuses.find(s => s.value === this.task.status);\n    return status ? status.label : this.task.status;\n  }\n  /**\n   * Check if task is overdue\n   * @returns True if task is overdue\n   */\n  isOverdue() {\n    if (!this.task.dueDate || this.task.status === 'done') {\n      return false;\n    }\n    const dueDate = new Date(this.task.dueDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    return dueDate < today;\n  }\n  static {\n    this.ɵfac = function TaskItemComponent_Factory(t) {\n      return new (t || TaskItemComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TaskItemComponent,\n      selectors: [[\"app-task-item\"]],\n      inputs: {\n        task: \"task\"\n      },\n      outputs: {\n        select: \"select\",\n        delete: \"delete\",\n        statusChange: \"statusChange\"\n      },\n      decls: 30,\n      vars: 24,\n      consts: [[1, \"task-item\", 3, \"ngClass\", \"click\"], [1, \"task-column\", \"task-status-column\"], [1, \"status-indicator\", 3, \"ngClass\", \"click\"], [\"class\", \"status-menu\", 4, \"ngIf\"], [1, \"task-column\", \"task-title-column\"], [1, \"task-title\"], [1, \"task-description\"], [\"class\", \"task-tags\", 4, \"ngIf\"], [1, \"task-column\", \"task-priority-column\"], [1, \"priority-badge\", 3, \"ngClass\"], [1, \"task-column\", \"task-date-column\"], [1, \"due-date\", 3, \"ngClass\"], [4, \"ngIf\"], [1, \"task-column\", \"task-actions-column\"], [1, \"task-actions\"], [\"mat-icon-button\", \"\", 3, \"routerLink\", \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"status-menu\"], [\"class\", \"status-option\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"status-option\", 3, \"ngClass\", \"click\"], [1, \"task-tags\"], [\"class\", \"task-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"task-tag\"]],\n      template: function TaskItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function TaskItemComponent_Template_div_click_0_listener($event) {\n            return ctx.onSelect($event);\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function TaskItemComponent_Template_div_click_2_listener($event) {\n            return ctx.toggleStatusMenu($event);\n          });\n          i0.ɵɵtext(3);\n          i0.ɵɵelementStart(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"arrow_drop_down\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, TaskItemComponent_div_6_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"div\", 5);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 6);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"truncate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, TaskItemComponent_div_13_Template, 2, 1, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 8)(15, \"div\", 9);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 10)(18, \"div\", 11);\n          i0.ɵɵtemplate(19, TaskItemComponent_mat_icon_19_Template, 2, 0, \"mat-icon\", 12);\n          i0.ɵɵtext(20);\n          i0.ɵɵpipe(21, \"date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 13)(23, \"div\", 14)(24, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function TaskItemComponent_Template_button_click_24_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(25, \"mat-icon\");\n          i0.ɵɵtext(26, \"edit\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function TaskItemComponent_Template_button_click_27_listener($event) {\n            return ctx.onDelete($event);\n          });\n          i0.ɵɵelementStart(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"delete\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusLabel(), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.statusMenuOpen);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.task.title);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(12, 13, ctx.task.description, 100, true));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.task.tags && ctx.task.tags.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.getPriorityClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.task.priority, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(20, _c0, ctx.isOverdue()));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOverdue());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(21, 17, ctx.task.dueDate, \"MMM d\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(22, _c1, ctx.task.id));\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.RouterLink, i3.MatIconButton, i4.MatIcon, i1.DatePipe, i5.TruncatePipe],\n      styles: [\"\\n\\n\\n\\n\\n\\n.task-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-bottom: 1px solid #eee;\\n  transition: background-color 0.2s ease;\\n  \\n\\n}\\n.task-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f9f9f9;\\n  cursor: pointer;\\n}\\n.task-item.status-todo[_ngcontent-%COMP%] {\\n  border-left: 4px solid #9e9e9e;\\n}\\n.task-item.status-in_progress[_ngcontent-%COMP%] {\\n  border-left: 4px solid #2196f3;\\n}\\n.task-item.status-review[_ngcontent-%COMP%] {\\n  border-left: 4px solid #ff9800;\\n}\\n.task-item.status-done[_ngcontent-%COMP%] {\\n  border-left: 4px solid #4caf50;\\n  background-color: #f9f9f9;\\n}\\n.task-item.status-done[_ngcontent-%COMP%]   .task-title[_ngcontent-%COMP%], .task-item.status-done[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%] {\\n  color: #757575;\\n}\\n\\n\\n\\n.task-column[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n\\n.task-status-column[_ngcontent-%COMP%] {\\n  width: 120px;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-size: 0.85rem;\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  position: relative;\\n}\\n.status-indicator[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  height: 18px;\\n  width: 18px;\\n  margin-left: 2px;\\n}\\n.status-indicator.status-todo[_ngcontent-%COMP%] {\\n  background-color: #eeeeee;\\n  color: #616161;\\n}\\n.status-indicator.status-in_progress[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n}\\n.status-indicator.status-review[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n}\\n.status-indicator.status-done[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  color: #2e7d32;\\n}\\n\\n\\n\\n.status-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  z-index: 10;\\n  background-color: white;\\n  border-radius: 4px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  width: 120px;\\n  margin-top: 4px;\\n}\\n\\n\\n\\n.status-option[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  cursor: pointer;\\n}\\n.status-option[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n.status-option.status-todo[_ngcontent-%COMP%] {\\n  border-left: 4px solid #9e9e9e;\\n}\\n.status-option.status-in_progress[_ngcontent-%COMP%] {\\n  border-left: 4px solid #2196f3;\\n}\\n.status-option.status-review[_ngcontent-%COMP%] {\\n  border-left: 4px solid #ff9800;\\n}\\n.status-option.status-done[_ngcontent-%COMP%] {\\n  border-left: 4px solid #4caf50;\\n}\\n\\n\\n\\n.task-title-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n\\n\\n\\n.task-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n\\n\\n\\n.task-description[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #666;\\n  margin-bottom: 8px;\\n}\\n\\n\\n\\n.task-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 4px;\\n}\\n\\n\\n\\n.task-tag[_ngcontent-%COMP%] {\\n  background-color: #f0f0f0;\\n  color: #666;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n}\\n\\n\\n\\n.task-priority-column[_ngcontent-%COMP%] {\\n  width: 100px;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.priority-badge[_ngcontent-%COMP%] {\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  text-transform: uppercase;\\n  font-weight: 500;\\n}\\n.priority-badge.priority-high[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n}\\n.priority-badge.priority-medium[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #ff8f00;\\n}\\n.priority-badge.priority-low[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  color: #2e7d32;\\n}\\n\\n\\n\\n.task-date-column[_ngcontent-%COMP%] {\\n  width: 120px;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.due-date[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.85rem;\\n}\\n.due-date.overdue[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n.due-date[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  height: 16px;\\n  width: 16px;\\n  margin-right: 4px;\\n}\\n\\n\\n\\n.task-actions-column[_ngcontent-%COMP%] {\\n  width: 100px;\\n  flex-shrink: 0;\\n  justify-content: flex-end;\\n}\\n\\n\\n\\n.task-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.task-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n.task-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .task-status-column[_ngcontent-%COMP%] {\\n    width: 80px;\\n  }\\n  .task-priority-column[_ngcontent-%COMP%], .task-date-column[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .task-actions-column[_ngcontent-%COMP%] {\\n    width: 80px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵlistener", "TaskItemComponent_div_6_div_1_Template_div_click_0_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r6", "status_r4", "$implicit", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "onStatusChange", "value", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtemplate", "TaskItemComponent_div_6_div_1_Template", "ctx_r0", "statuses", "ɵɵtextInterpolate", "tag_r8", "TaskItemComponent_div_13_span_1_Template", "ctx_r1", "task", "tags", "TaskItemComponent", "constructor", "select", "delete", "statusChange", "statusMenuOpen", "onSelect", "event", "target", "closest", "emit", "onDelete", "stopPropagation", "id", "status", "toggleStatusMenu", "getPriorityClass", "priority", "getStatusClass", "getStatusLabel", "find", "s", "isOverdue", "dueDate", "Date", "today", "setHours", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "TaskItemComponent_Template", "rf", "ctx", "TaskItemComponent_Template_div_click_0_listener", "TaskItemComponent_Template_div_click_2_listener", "TaskItemComponent_div_6_Template", "TaskItemComponent_div_13_Template", "TaskItemComponent_mat_icon_19_Template", "TaskItemComponent_Template_button_click_24_listener", "TaskItemComponent_Template_button_click_27_listener", "title", "ɵɵpipeBind3", "description", "length", "ɵɵpureFunction1", "_c0", "ɵɵpipeBind2", "_c1"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\components\\task-item\\task-item.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\tasks\\components\\task-item\\task-item.component.html"], "sourcesContent": ["/**\n * Task Item Component\n * Displays a single task item in the task list\n */\nimport { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';\nimport { Task } from '../../../../core/models/task.model';\n\n@Component({\n  selector: 'app-task-item',\n  templateUrl: './task-item.component.html',\n  styleUrls: ['./task-item.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class TaskItemComponent {\n  /**\n   * Input task to display\n   */\n  @Input() task!: Task;\n  \n  /**\n   * Event emitted when task is selected\n   */\n  @Output() select = new EventEmitter<void>();\n  \n  /**\n   * Event emitted when task is deleted\n   */\n  @Output() delete = new EventEmitter<string>();\n  \n  /**\n   * Event emitted when task status is changed\n   */\n  @Output() statusChange = new EventEmitter<string>();\n  \n  /**\n   * Available task statuses\n   */\n  statuses = [\n    { value: 'todo', label: 'To Do' },\n    { value: 'in_progress', label: 'In Progress' },\n    { value: 'review', label: 'Review' },\n    { value: 'done', label: 'Done' }\n  ];\n  \n  /**\n   * Status menu is open\n   */\n  statusMenuOpen = false;\n\n  /**\n   * Handle task selection\n   * @param event - Mouse event\n   */\n  onSelect(event: MouseEvent): void {\n    // Prevent event bubbling if clicking on actions\n    if ((event.target as HTMLElement).closest('.task-actions')) {\n      return;\n    }\n    this.select.emit();\n  }\n\n  /**\n   * Handle task deletion\n   * @param event - Mouse event\n   */\n  onDelete(event: MouseEvent): void {\n    event.stopPropagation();\n    this.delete.emit(this.task.id);\n  }\n\n  /**\n   * Handle status change\n   * @param event - Mouse event\n   * @param status - New status\n   */\n  onStatusChange(event: MouseEvent, status: string): void {\n    event.stopPropagation();\n    this.statusChange.emit(status);\n    this.statusMenuOpen = false;\n  }\n\n  /**\n   * Toggle status menu\n   * @param event - Mouse event\n   */\n  toggleStatusMenu(event: MouseEvent): void {\n    event.stopPropagation();\n    this.statusMenuOpen = !this.statusMenuOpen;\n  }\n\n  /**\n   * Get CSS class for priority\n   * @returns CSS class name\n   */\n  getPriorityClass(): string {\n    return `priority-${this.task.priority}`;\n  }\n\n  /**\n   * Get CSS class for status\n   * @returns CSS class name\n   */\n  getStatusClass(): string {\n    return `status-${this.task.status}`;\n  }\n\n  /**\n   * Get formatted status label\n   * @returns Status label\n   */\n  getStatusLabel(): string {\n    const status = this.statuses.find(s => s.value === this.task.status);\n    return status ? status.label : this.task.status;\n  }\n\n  /**\n   * Check if task is overdue\n   * @returns True if task is overdue\n   */\n  isOverdue(): boolean {\n    if (!this.task.dueDate || this.task.status === 'done') {\n      return false;\n    }\n    \n    const dueDate = new Date(this.task.dueDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    \n    return dueDate < today;\n  }\n}\n", "<!-- Task item container -->\n<div class=\"task-item\" (click)=\"onSelect($event)\" [ngClass]=\"getStatusClass()\">\n  <!-- Status column -->\n  <div class=\"task-column task-status-column\">\n    <div class=\"status-indicator\" [ngClass]=\"getStatusClass()\" (click)=\"toggleStatusMenu($event)\">\n      {{ getStatusLabel() }}\n      <mat-icon>arrow_drop_down</mat-icon>\n      \n      <!-- Status dropdown menu -->\n      <div class=\"status-menu\" *ngIf=\"statusMenuOpen\">\n        <div \n          *ngFor=\"let status of statuses\" \n          class=\"status-option\" \n          [ngClass]=\"'status-' + status.value\"\n          (click)=\"onStatusChange($event, status.value)\">\n          {{ status.label }}\n        </div>\n      </div>\n    </div>\n  </div>\n  \n  <!-- Title column -->\n  <div class=\"task-column task-title-column\">\n    <div class=\"task-title\">{{ task.title }}</div>\n    <div class=\"task-description\">{{ task.description | truncate:100:true }}</div>\n    \n    <!-- Tags -->\n    <div class=\"task-tags\" *ngIf=\"task.tags && task.tags.length > 0\">\n      <span class=\"task-tag\" *ngFor=\"let tag of task.tags\">{{ tag }}</span>\n    </div>\n  </div>\n  \n  <!-- Priority column -->\n  <div class=\"task-column task-priority-column\">\n    <div class=\"priority-badge\" [ngClass]=\"getPriorityClass()\">\n      {{ task.priority }}\n    </div>\n  </div>\n  \n  <!-- Due date column -->\n  <div class=\"task-column task-date-column\">\n    <div class=\"due-date\" [ngClass]=\"{'overdue': isOverdue()}\">\n      <mat-icon *ngIf=\"isOverdue()\">warning</mat-icon>\n      {{ task.dueDate | date:'MMM d' }}\n    </div>\n  </div>\n  \n  <!-- Actions column -->\n  <div class=\"task-column task-actions-column\">\n    <div class=\"task-actions\">\n      <button mat-icon-button [routerLink]=\"['/tasks', task.id, 'edit']\" (click)=\"$event.stopPropagation()\">\n        <mat-icon>edit</mat-icon>\n      </button>\n      <button mat-icon-button color=\"warn\" (click)=\"onDelete($event)\">\n        <mat-icon>delete</mat-icon>\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA;;;;AAIA,SAAmCA,YAAY,QAAiC,eAAe;;;;;;;;;;ICMvFC,EAAA,CAAAC,cAAA,cAIiD;IAA/CD,EAAA,CAAAE,UAAA,mBAAAC,4DAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAAT,MAAA,EAAAI,SAAA,CAAAM,KAAA,CAAoC;IAAA,EAAC;IAC9Cd,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAM;;;;IAHJhB,EAAA,CAAAiB,UAAA,wBAAAT,SAAA,CAAAM,KAAA,CAAoC;IAEpCd,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAmB,kBAAA,MAAAX,SAAA,CAAAY,KAAA,MACF;;;;;IAPFpB,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAqB,UAAA,IAAAC,sCAAA,kBAMM;IACRtB,EAAA,CAAAgB,YAAA,EAAM;;;;IANiBhB,EAAA,CAAAkB,SAAA,GAAW;IAAXlB,EAAA,CAAAiB,UAAA,YAAAM,MAAA,CAAAC,QAAA,CAAW;;;;;IAiBlCxB,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAe,MAAA,GAAS;IAAAf,EAAA,CAAAgB,YAAA,EAAO;;;;IAAhBhB,EAAA,CAAAkB,SAAA,GAAS;IAATlB,EAAA,CAAAyB,iBAAA,CAAAC,MAAA,CAAS;;;;;IADhE1B,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAqB,UAAA,IAAAM,wCAAA,mBAAqE;IACvE3B,EAAA,CAAAgB,YAAA,EAAM;;;;IADmChB,EAAA,CAAAkB,SAAA,GAAY;IAAZlB,EAAA,CAAAiB,UAAA,YAAAW,MAAA,CAAAC,IAAA,CAAAC,IAAA,CAAY;;;;;IAcnD9B,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAe,MAAA,cAAO;IAAAf,EAAA,CAAAgB,YAAA,EAAW;;;;;;;;;;;AD7BtD,OAAM,MAAOe,iBAAiB;EAN9BC,YAAA;IAYE;;;IAGU,KAAAC,MAAM,GAAG,IAAIlC,YAAY,EAAQ;IAE3C;;;IAGU,KAAAmC,MAAM,GAAG,IAAInC,YAAY,EAAU;IAE7C;;;IAGU,KAAAoC,YAAY,GAAG,IAAIpC,YAAY,EAAU;IAEnD;;;IAGA,KAAAyB,QAAQ,GAAG,CACT;MAAEV,KAAK,EAAE,MAAM;MAAEM,KAAK,EAAE;IAAO,CAAE,EACjC;MAAEN,KAAK,EAAE,aAAa;MAAEM,KAAK,EAAE;IAAa,CAAE,EAC9C;MAAEN,KAAK,EAAE,QAAQ;MAAEM,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEN,KAAK,EAAE,MAAM;MAAEM,KAAK,EAAE;IAAM,CAAE,CACjC;IAED;;;IAGA,KAAAgB,cAAc,GAAG,KAAK;;EAEtB;;;;EAIAC,QAAQA,CAACC,KAAiB;IACxB;IACA,IAAKA,KAAK,CAACC,MAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,EAAE;MAC1D;;IAEF,IAAI,CAACP,MAAM,CAACQ,IAAI,EAAE;EACpB;EAEA;;;;EAIAC,QAAQA,CAACJ,KAAiB;IACxBA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACT,MAAM,CAACO,IAAI,CAAC,IAAI,CAACZ,IAAI,CAACe,EAAE,CAAC;EAChC;EAEA;;;;;EAKA/B,cAAcA,CAACyB,KAAiB,EAAEO,MAAc;IAC9CP,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACR,YAAY,CAACM,IAAI,CAACI,MAAM,CAAC;IAC9B,IAAI,CAACT,cAAc,GAAG,KAAK;EAC7B;EAEA;;;;EAIAU,gBAAgBA,CAACR,KAAiB;IAChCA,KAAK,CAACK,eAAe,EAAE;IACvB,IAAI,CAACP,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA;;;;EAIAW,gBAAgBA,CAAA;IACd,OAAO,YAAY,IAAI,CAAClB,IAAI,CAACmB,QAAQ,EAAE;EACzC;EAEA;;;;EAIAC,cAAcA,CAAA;IACZ,OAAO,UAAU,IAAI,CAACpB,IAAI,CAACgB,MAAM,EAAE;EACrC;EAEA;;;;EAIAK,cAAcA,CAAA;IACZ,MAAML,MAAM,GAAG,IAAI,CAACrB,QAAQ,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,KAAK,KAAK,IAAI,CAACe,IAAI,CAACgB,MAAM,CAAC;IACpE,OAAOA,MAAM,GAAGA,MAAM,CAACzB,KAAK,GAAG,IAAI,CAACS,IAAI,CAACgB,MAAM;EACjD;EAEA;;;;EAIAQ,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACxB,IAAI,CAACyB,OAAO,IAAI,IAAI,CAACzB,IAAI,CAACgB,MAAM,KAAK,MAAM,EAAE;MACrD,OAAO,KAAK;;IAGd,MAAMS,OAAO,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAC1B,IAAI,CAACyB,OAAO,CAAC;IAC3C,MAAME,KAAK,GAAG,IAAID,IAAI,EAAE;IACxBC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,OAAOH,OAAO,GAAGE,KAAK;EACxB;;;uBApHWzB,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAA2B,SAAA;MAAAC,MAAA;QAAA9B,IAAA;MAAA;MAAA+B,OAAA;QAAA3B,MAAA;QAAAC,MAAA;QAAAC,YAAA;MAAA;MAAA0B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ9BlE,EAAA,CAAAC,cAAA,aAA+E;UAAxDD,EAAA,CAAAE,UAAA,mBAAAkE,gDAAAhE,MAAA;YAAA,OAAS+D,GAAA,CAAA9B,QAAA,CAAAjC,MAAA,CAAgB;UAAA,EAAC;UAE/CJ,EAAA,CAAAC,cAAA,aAA4C;UACiBD,EAAA,CAAAE,UAAA,mBAAAmE,gDAAAjE,MAAA;YAAA,OAAS+D,GAAA,CAAArB,gBAAA,CAAA1C,MAAA,CAAwB;UAAA,EAAC;UAC3FJ,EAAA,CAAAe,MAAA,GACA;UAAAf,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAe,MAAA,sBAAe;UAAAf,EAAA,CAAAgB,YAAA,EAAW;UAGpChB,EAAA,CAAAqB,UAAA,IAAAiD,gCAAA,iBAQM;UACRtE,EAAA,CAAAgB,YAAA,EAAM;UAIRhB,EAAA,CAAAC,cAAA,aAA2C;UACjBD,EAAA,CAAAe,MAAA,GAAgB;UAAAf,EAAA,CAAAgB,YAAA,EAAM;UAC9ChB,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAe,MAAA,IAA0C;;UAAAf,EAAA,CAAAgB,YAAA,EAAM;UAG9EhB,EAAA,CAAAqB,UAAA,KAAAkD,iCAAA,iBAEM;UACRvE,EAAA,CAAAgB,YAAA,EAAM;UAGNhB,EAAA,CAAAC,cAAA,cAA8C;UAE1CD,EAAA,CAAAe,MAAA,IACF;UAAAf,EAAA,CAAAgB,YAAA,EAAM;UAIRhB,EAAA,CAAAC,cAAA,eAA0C;UAEtCD,EAAA,CAAAqB,UAAA,KAAAmD,sCAAA,uBAAgD;UAChDxE,EAAA,CAAAe,MAAA,IACF;;UAAAf,EAAA,CAAAgB,YAAA,EAAM;UAIRhB,EAAA,CAAAC,cAAA,eAA6C;UAE0BD,EAAA,CAAAE,UAAA,mBAAAuE,oDAAArE,MAAA;YAAA,OAASA,MAAA,CAAAuC,eAAA,EAAwB;UAAA,EAAC;UACnG3C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAe,MAAA,YAAI;UAAAf,EAAA,CAAAgB,YAAA,EAAW;UAE3BhB,EAAA,CAAAC,cAAA,kBAAgE;UAA3BD,EAAA,CAAAE,UAAA,mBAAAwE,oDAAAtE,MAAA;YAAA,OAAS+D,GAAA,CAAAzB,QAAA,CAAAtC,MAAA,CAAgB;UAAA,EAAC;UAC7DJ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAe,MAAA,cAAM;UAAAf,EAAA,CAAAgB,YAAA,EAAW;;;UArDehB,EAAA,CAAAiB,UAAA,YAAAkD,GAAA,CAAAlB,cAAA,GAA4B;UAG5CjD,EAAA,CAAAkB,SAAA,GAA4B;UAA5BlB,EAAA,CAAAiB,UAAA,YAAAkD,GAAA,CAAAlB,cAAA,GAA4B;UACxDjD,EAAA,CAAAkB,SAAA,GACA;UADAlB,EAAA,CAAAmB,kBAAA,MAAAgD,GAAA,CAAAjB,cAAA,QACA;UAG0BlD,EAAA,CAAAkB,SAAA,GAAoB;UAApBlB,EAAA,CAAAiB,UAAA,SAAAkD,GAAA,CAAA/B,cAAA,CAAoB;UAcxBpC,EAAA,CAAAkB,SAAA,GAAgB;UAAhBlB,EAAA,CAAAyB,iBAAA,CAAA0C,GAAA,CAAAtC,IAAA,CAAA8C,KAAA,CAAgB;UACV3E,EAAA,CAAAkB,SAAA,GAA0C;UAA1ClB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA4E,WAAA,SAAAT,GAAA,CAAAtC,IAAA,CAAAgD,WAAA,aAA0C;UAGhD7E,EAAA,CAAAkB,SAAA,GAAuC;UAAvClB,EAAA,CAAAiB,UAAA,SAAAkD,GAAA,CAAAtC,IAAA,CAAAC,IAAA,IAAAqC,GAAA,CAAAtC,IAAA,CAAAC,IAAA,CAAAgD,MAAA,KAAuC;UAOnC9E,EAAA,CAAAkB,SAAA,GAA8B;UAA9BlB,EAAA,CAAAiB,UAAA,YAAAkD,GAAA,CAAApB,gBAAA,GAA8B;UACxD/C,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAgD,GAAA,CAAAtC,IAAA,CAAAmB,QAAA,MACF;UAKsBhD,EAAA,CAAAkB,SAAA,GAAoC;UAApClB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAA+E,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAAd,SAAA,IAAoC;UAC7CrD,EAAA,CAAAkB,SAAA,GAAiB;UAAjBlB,EAAA,CAAAiB,UAAA,SAAAkD,GAAA,CAAAd,SAAA,GAAiB;UAC5BrD,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAiF,WAAA,SAAAd,GAAA,CAAAtC,IAAA,CAAAyB,OAAA,gBACF;UAM0BtD,EAAA,CAAAkB,SAAA,GAA0C;UAA1ClB,EAAA,CAAAiB,UAAA,eAAAjB,EAAA,CAAA+E,eAAA,KAAAG,GAAA,EAAAf,GAAA,CAAAtC,IAAA,CAAAe,EAAA,EAA0C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}