<!-- Task detail container -->
<div class="task-detail-container">
  <!-- Loading state -->
  <div *ngIf="loading" class="task-detail-loading">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Task content when loaded -->
  <ng-container *ngIf="!loading && task">
    <!-- Header with title and actions -->
    <div class="task-detail-header">
      <h1 class="task-title">{{ task.title }}</h1>
      
      <div class="task-actions">
        <button mat-icon-button (click)="onEdit()" aria-label="Edit task">
          <mat-icon>edit</mat-icon>
        </button>
        <button mat-icon-button color="warn" (click)="onDelete()" aria-label="Delete task">
          <mat-icon>delete</mat-icon>
        </button>
      </div>
    </div>

    <!-- Task metadata -->
    <div class="task-metadata">
      <!-- Status -->
      <div class="metadata-item">
        <span class="metadata-label">Status</span>
        <mat-form-field appearance="outline">
          <mat-select 
            [value]="task.status" 
            (selectionChange)="onStatusChange($event.value)"
            [ngClass]="getStatusClass()">
            <mat-option *ngFor="let status of statuses" [value]="status.value">
              {{ status.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      
      <!-- Priority -->
      <div class="metadata-item">
        <span class="metadata-label">Priority</span>
        <div class="priority-badge" [ngClass]="getPriorityClass()">
          {{ task.priority }}
        </div>
      </div>
      
      <!-- Due date -->
      <div class="metadata-item">
        <span class="metadata-label">Due Date</span>
        <div class="due-date" [ngClass]="{'overdue': isOverdue()}">
          <mat-icon *ngIf="isOverdue()">warning</mat-icon>
          {{ task.dueDate | date:'mediumDate' || 'No due date' }}
        </div>
      </div>
      
      <!-- Assignee -->
      <div class="metadata-item">
        <span class="metadata-label">Assignee</span>
        <mat-form-field appearance="outline">
          <mat-select 
            [value]="task.assigneeId" 
            (selectionChange)="onAssigneeChange($event.value)">
            <mat-option [value]="''">Unassigned</mat-option>
            <mat-option *ngFor="let user of users" [value]="user.id">
              {{ user.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

    <!-- Task description -->
    <div class="task-section">
      <h2>Description</h2>
      <div class="task-description">
        {{ task.description || 'No description provided.' }}
      </div>
    </div>

    <!-- Task tags -->
    <div class="task-section" *ngIf="task.tags && task.tags.length > 0">
      <h2>Tags</h2>
      <div class="task-tags">
        <span class="task-tag" *ngFor="let tag of task.tags">{{ tag }}</span>
      </div>
    </div>

    <!-- Task creation info -->
    <div class="task-section task-info">
      <div class="info-item">
        <span class="info-label">Created by:</span>
        <span>{{ getCreatorName() }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Created:</span>
        <span>{{ task.createdAt | date:'medium' }}</span>
      </div>
      <div class="info-item" *ngIf="task.updatedAt">
        <span class="info-label">Last updated:</span>
        <span>{{ task.updatedAt | date:'medium' }}</span>
      </div>
    </div>
  </ng-container>
</div>
