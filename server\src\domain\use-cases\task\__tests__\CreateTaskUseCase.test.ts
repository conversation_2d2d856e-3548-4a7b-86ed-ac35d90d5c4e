/**
 * Tests for CreateTaskUseCase
 * Verifies the business logic for creating tasks
 */
import { CreateTaskUseCase } from '../CreateTaskUseCase';
import { ITaskRepository } from '../../../repositories/ITaskRepository';
import { ITask, TaskStatus, TaskPriority } from '../../../entities/Task';
import { AppError } from '../../../common/AppError';

// Mock task repository
const mockTaskRepository: jest.Mocked<ITaskRepository> = {
  create: jest.fn(),
  findById: jest.fn(),
  findAll: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  findByAssignee: jest.fn(),
};

// Mock task data
const mockTask: ITask = {
  title: 'Test Task',
  description: 'Test Description',
  status: TaskStatus.TODO,
  priority: TaskPriority.MEDIUM,
};

// Mock task document (returned from repository)
const mockTaskDocument = {
  id: '123456789012',
  ...mockTask,
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('CreateTaskUseCase', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Test successful task creation
  it('should create a task successfully', async () => {
    // Arrange
    mockTaskRepository.create.mockResolvedValue(mockTaskDocument as any);
    const createTaskUseCase = new CreateTaskUseCase(mockTaskRepository);

    // Act
    const result = await createTaskUseCase.execute(mockTask);

    // Assert
    expect(mockTaskRepository.create).toHaveBeenCalledWith(mockTask);
    expect(result).toEqual(mockTaskDocument);
  });

  // Test validation for missing title
  it('should throw an error if title is missing', async () => {
    // Arrange
    const createTaskUseCase = new CreateTaskUseCase(mockTaskRepository);
    const invalidTask = { ...mockTask, title: '' };

    // Act & Assert
    await expect(createTaskUseCase.execute(invalidTask)).rejects.toThrow(
      new AppError('Task title is required', 400)
    );
    expect(mockTaskRepository.create).not.toHaveBeenCalled();
  });

  // Test validation for title length
  it('should throw an error if title exceeds maximum length', async () => {
    // Arrange
    const createTaskUseCase = new CreateTaskUseCase(mockTaskRepository);
    const longTitle = 'a'.repeat(101); // 101 characters
    const invalidTask = { ...mockTask, title: longTitle };

    // Act & Assert
    await expect(createTaskUseCase.execute(invalidTask)).rejects.toThrow(
      new AppError('Task title cannot exceed 100 characters', 400)
    );
    expect(mockTaskRepository.create).not.toHaveBeenCalled();
  });

  // Test validation for description length
  it('should throw an error if description exceeds maximum length', async () => {
    // Arrange
    const createTaskUseCase = new CreateTaskUseCase(mockTaskRepository);
    const longDescription = 'a'.repeat(501); // 501 characters
    const invalidTask = { ...mockTask, description: longDescription };

    // Act & Assert
    await expect(createTaskUseCase.execute(invalidTask)).rejects.toThrow(
      new AppError('Task description cannot exceed 500 characters', 400)
    );
    expect(mockTaskRepository.create).not.toHaveBeenCalled();
  });

  // Test repository error handling
  it('should handle repository errors', async () => {
    // Arrange
    const errorMessage = 'Database error';
    mockTaskRepository.create.mockRejectedValue(new Error(errorMessage));
    const createTaskUseCase = new CreateTaskUseCase(mockTaskRepository);

    // Act & Assert
    await expect(createTaskUseCase.execute(mockTask)).rejects.toThrow(
      new AppError(`Failed to create task: ${errorMessage}`, 500)
    );
  });
});
