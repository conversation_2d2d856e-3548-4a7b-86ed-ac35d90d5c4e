{"ast": null, "code": "import { Version } from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nconst VERSION = new Version('16.2.14');\nexport { VERSION };", "map": {"version": 3, "names": ["Version", "VERSION"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/@angular+cdk@16.2.14_@angul_8ae5d31fdbd8afc62caac07e0981178d/node_modules/@angular/cdk/fesm2022/cdk.mjs"], "sourcesContent": ["import { Version } from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nconst VERSION = new Version('16.2.14');\n\nexport { VERSION };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;;AAEvC;AACA,MAAMC,OAAO,GAAG,IAAID,OAAO,CAAC,SAAS,CAAC;AAEtC,SAASC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}