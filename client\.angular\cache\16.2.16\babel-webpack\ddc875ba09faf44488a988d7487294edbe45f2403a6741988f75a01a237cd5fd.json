{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Profile page module\n * Contains the main component for displaying and editing user profile\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../../../shared/shared.module';\nimport { ProfilePageComponent } from './profile-page.component';\n// Define routes for the profile page\nconst routes = [{\n  path: '',\n  component: ProfilePageComponent\n}];\nexport let ProfilePageModule = class ProfilePageModule {};\nProfilePageModule = __decorate([NgModule({\n  declarations: [ProfilePageComponent],\n  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n})], ProfilePageModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "SharedModule", "ProfilePageComponent", "routes", "path", "component", "ProfilePageModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\profile\\pages\\profile-page\\profile-page.module.ts"], "sourcesContent": ["/**\n * Profile page module\n * Contains the main component for displaying and editing user profile\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../../../shared/shared.module';\nimport { ProfilePageComponent } from './profile-page.component';\n\n// Define routes for the profile page\nconst routes: Routes = [\n  {\n    path: '',\n    component: ProfilePageComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    ProfilePageComponent\n  ],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class ProfilePageModule { }\n"], "mappings": ";AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,oBAAoB,QAAQ,0BAA0B;AAE/D;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAYM,WAAMI,iBAAiB,GAAvB,MAAMA,iBAAiB,GAAI;AAArBA,iBAAiB,GAAAC,UAAA,EAV7BT,QAAQ,CAAC;EACRU,YAAY,EAAE,CACZN,oBAAoB,CACrB;EACDO,OAAO,EAAE,CACPV,YAAY,EACZE,YAAY,EACZD,YAAY,CAACU,QAAQ,CAACP,MAAM,CAAC;CAEhC,CAAC,C,EACWG,iBAAiB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}