/**
 * Sidenav Component
 * Side navigation menu for the application
 */
import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { AuthService } from '../../services/auth.service';
import { User } from '../../models/user.model';

@Component({
  selector: 'app-sidenav',
  templateUrl: './sidenav.component.html',
  styleUrls: ['./sidenav.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SidenavComponent implements OnInit {
  /**
   * Current authenticated user
   */
  currentUser: User | null = null;
  
  /**
   * Flag to control sidenav expanded state
   */
  isExpanded = true;
  
  /**
   * Current active route
   */
  activeRoute = '';

  /**
   * Navigation menu items
   */
  navItems = [
    { 
      label: 'Dashboard', 
      icon: 'dashboard', 
      route: '/dashboard',
      requiresAdmin: false
    },
    { 
      label: 'My Tasks', 
      icon: 'assignment', 
      route: '/tasks',
      requiresAdmin: false
    },
    { 
      label: 'Create Task', 
      icon: 'add_task', 
      route: '/tasks/create',
      requiresAdmin: false
    },
    { 
      label: 'Team', 
      icon: 'people', 
      route: '/team',
      requiresAdmin: false
    },
    { 
      label: 'Reports', 
      icon: 'bar_chart', 
      route: '/reports',
      requiresAdmin: true
    },
    { 
      label: 'Settings', 
      icon: 'settings', 
      route: '/settings',
      requiresAdmin: true
    }
  ];

  /**
   * Constructor with dependency injection
   * @param authService - Authentication service
   * @param router - Router for navigation
   * @param cdr - Change detector reference for OnPush strategy
   */
  constructor(
    private authService: AuthService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Sets up subscriptions to current user and route changes
   */
  ngOnInit(): void {
    // Subscribe to current user changes
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.cdr.markForCheck();
    });
    
    // Track active route
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.activeRoute = event.urlAfterRedirects;
      this.cdr.markForCheck();
    });
  }

  /**
   * Toggle the expanded state of the sidenav
   */
  toggleSidenav(): void {
    this.isExpanded = !this.isExpanded;
    this.cdr.markForCheck();
  }

  /**
   * Check if a route is currently active
   * @param route - Route to check
   * @returns Boolean indicating if route is active
   */
  isActive(route: string): boolean {
    return this.activeRoute.startsWith(route);
  }

  /**
   * Check if the current user has admin privileges
   * @returns Boolean indicating if user is admin
   */
  isAdmin(): boolean {
    return this.currentUser?.role === 'admin';
  }
}
