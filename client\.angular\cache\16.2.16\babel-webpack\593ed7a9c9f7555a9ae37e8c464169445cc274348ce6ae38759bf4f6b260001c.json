{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * Login Form Component\n * Handles user authentication with email and password\n */\nimport { Component, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { Validators } from '@angular/forms';\nexport let LoginFormComponent = class LoginFormComponent {\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(fb) {\n    this.fb = fb;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed login attempt\n     */\n    this.error = null;\n    /**\n     * Event emitted when form is submitted\n     */\n    this.formSubmit = new EventEmitter();\n    /**\n     * Event emitted when forgot password link is clicked\n     */\n    this.forgotPassword = new EventEmitter();\n    /**\n     * Event emitted when register link is clicked\n     */\n    this.register = new EventEmitter();\n    /**\n     * Flag to toggle password visibility\n     */\n    this.hidePassword = true;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the login form\n   */\n  ngOnInit() {\n    this.initForm();\n  }\n  /**\n   * Initialize login form with validation\n   */\n  initForm() {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      rememberMe: [false]\n    });\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.loginForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.loginForm.markAllAsTouched();\n      return;\n    }\n    const {\n      email,\n      password\n    } = this.loginForm.value;\n    this.formSubmit.emit({\n      email,\n      password\n    });\n  }\n  /**\n   * Handle forgot password link click\n   */\n  onForgotPassword() {\n    this.forgotPassword.emit();\n  }\n  /**\n   * Handle register link click\n   */\n  onRegister() {\n    this.register.emit();\n  }\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName, errorName) {\n    const control = this.loginForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility() {\n    this.hidePassword = !this.hidePassword;\n  }\n};\n__decorate([Input()], LoginFormComponent.prototype, \"loading\", void 0);\n__decorate([Input()], LoginFormComponent.prototype, \"error\", void 0);\n__decorate([Output()], LoginFormComponent.prototype, \"formSubmit\", void 0);\n__decorate([Output()], LoginFormComponent.prototype, \"forgotPassword\", void 0);\n__decorate([Output()], LoginFormComponent.prototype, \"register\", void 0);\nLoginFormComponent = __decorate([Component({\n  selector: 'app-login-form',\n  templateUrl: './login-form.component.html',\n  styleUrls: ['./login-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], LoginFormComponent);", "map": {"version": 3, "names": ["Component", "Output", "EventEmitter", "Input", "ChangeDetectionStrategy", "Validators", "LoginFormComponent", "constructor", "fb", "loading", "error", "formSubmit", "forgotPassword", "register", "hidePassword", "ngOnInit", "initForm", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "rememberMe", "onSubmit", "invalid", "mark<PERSON>llAsTouched", "value", "emit", "onForgotPassword", "onRegister", "<PERSON><PERSON><PERSON><PERSON>", "controlName", "errorName", "control", "get", "touched", "togglePasswordVisibility", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\components\\login-form\\login-form.component.ts"], "sourcesContent": ["/**\n * Login Form Component\n * Handles user authentication with email and password\n */\nimport { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\n\n@Component({\n  selector: 'app-login-form',\n  templateUrl: './login-form.component.html',\n  styleUrls: ['./login-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class LoginFormComponent implements OnInit {\n  /**\n   * Loading state indicator\n   */\n  @Input() loading = false;\n  \n  /**\n   * Error message from failed login attempt\n   */\n  @Input() error: string | null = null;\n  \n  /**\n   * Event emitted when form is submitted\n   */\n  @Output() formSubmit = new EventEmitter<{ email: string; password: string }>();\n  \n  /**\n   * Event emitted when forgot password link is clicked\n   */\n  @Output() forgotPassword = new EventEmitter<void>();\n  \n  /**\n   * Event emitted when register link is clicked\n   */\n  @Output() register = new EventEmitter<void>();\n  \n  /**\n   * Login form group\n   */\n  loginForm!: FormGroup;\n  \n  /**\n   * Flag to toggle password visibility\n   */\n  hidePassword = true;\n\n  /**\n   * Constructor with dependency injection\n   * @param fb - FormBuilder for reactive forms\n   */\n  constructor(private fb: FormBuilder) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Initializes the login form\n   */\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  /**\n   * Initialize login form with validation\n   */\n  private initForm(): void {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      rememberMe: [false]\n    });\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.loginForm.invalid || this.loading) {\n      // Mark all fields as touched to trigger validation messages\n      this.loginForm.markAllAsTouched();\n      return;\n    }\n    \n    const { email, password } = this.loginForm.value;\n    this.formSubmit.emit({ email, password });\n  }\n\n  /**\n   * Handle forgot password link click\n   */\n  onForgotPassword(): void {\n    this.forgotPassword.emit();\n  }\n\n  /**\n   * Handle register link click\n   */\n  onRegister(): void {\n    this.register.emit();\n  }\n\n  /**\n   * Check if form control has error\n   * @param controlName - Name of form control\n   * @param errorName - Name of error\n   * @returns True if control has error\n   */\n  hasError(controlName: string, errorName: string): boolean {\n    const control = this.loginForm.get(controlName);\n    return !!(control && control.touched && control.hasError(errorName));\n  }\n\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility(): void {\n    this.hidePassword = !this.hidePassword;\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;;;AAIA,SAASA,SAAS,EAAUC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,uBAAuB,QAAQ,eAAe;AACvG,SAAiCC,UAAU,QAAQ,gBAAgB;AAQ5D,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAoC7B;;;;EAIAC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAvCtB;;;IAGS,KAAAC,OAAO,GAAG,KAAK;IAExB;;;IAGS,KAAAC,KAAK,GAAkB,IAAI;IAEpC;;;IAGU,KAAAC,UAAU,GAAG,IAAIT,YAAY,EAAuC;IAE9E;;;IAGU,KAAAU,cAAc,GAAG,IAAIV,YAAY,EAAQ;IAEnD;;;IAGU,KAAAW,QAAQ,GAAG,IAAIX,YAAY,EAAQ;IAO7C;;;IAGA,KAAAY,YAAY,GAAG,IAAI;EAMmB;EAEtC;;;;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEA;;;EAGQA,QAAQA,CAAA;IACd,IAAI,CAACC,SAAS,GAAG,IAAI,CAACT,EAAE,CAACU,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACd,UAAU,CAACe,QAAQ,EAAEf,UAAU,CAACc,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAACe,QAAQ,EAAEf,UAAU,CAACiB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACP,SAAS,CAACQ,OAAO,IAAI,IAAI,CAAChB,OAAO,EAAE;MAC1C;MACA,IAAI,CAACQ,SAAS,CAACS,gBAAgB,EAAE;MACjC;;IAGF,MAAM;MAAEP,KAAK;MAAEE;IAAQ,CAAE,GAAG,IAAI,CAACJ,SAAS,CAACU,KAAK;IAChD,IAAI,CAAChB,UAAU,CAACiB,IAAI,CAAC;MAAET,KAAK;MAAEE;IAAQ,CAAE,CAAC;EAC3C;EAEA;;;EAGAQ,gBAAgBA,CAAA;IACd,IAAI,CAACjB,cAAc,CAACgB,IAAI,EAAE;EAC5B;EAEA;;;EAGAE,UAAUA,CAAA;IACR,IAAI,CAACjB,QAAQ,CAACe,IAAI,EAAE;EACtB;EAEA;;;;;;EAMAG,QAAQA,CAACC,WAAmB,EAAEC,SAAiB;IAC7C,MAAMC,OAAO,GAAG,IAAI,CAACjB,SAAS,CAACkB,GAAG,CAACH,WAAW,CAAC;IAC/C,OAAO,CAAC,EAAEE,OAAO,IAAIA,OAAO,CAACE,OAAO,IAAIF,OAAO,CAACH,QAAQ,CAACE,SAAS,CAAC,CAAC;EACtE;EAEA;;;EAGAI,wBAAwBA,CAAA;IACtB,IAAI,CAACvB,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;CACD;AAtGUwB,UAAA,EAARnC,KAAK,EAAE,C,kDAAiB;AAKhBmC,UAAA,EAARnC,KAAK,EAAE,C,gDAA6B;AAK3BmC,UAAA,EAATrC,MAAM,EAAE,C,qDAAsE;AAKrEqC,UAAA,EAATrC,MAAM,EAAE,C,yDAA2C;AAK1CqC,UAAA,EAATrC,MAAM,EAAE,C,mDAAqC;AAxBnCK,kBAAkB,GAAAgC,UAAA,EAN9BtC,SAAS,CAAC;EACTuC,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,6BAA6B,CAAC;EAC1CC,eAAe,EAAEtC,uBAAuB,CAACuC;CAC1C,CAAC,C,EACWrC,kBAAkB,CA0G9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}