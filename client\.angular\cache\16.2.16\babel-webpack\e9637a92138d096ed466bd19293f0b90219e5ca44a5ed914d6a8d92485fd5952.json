{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Click Outside Directive\n * Detects clicks outside of an element and emits an event\n */\nimport { Directive, EventEmitter, HostListener, Output } from '@angular/core';\nexport let ClickOutsideDirective = class ClickOutsideDirective {\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    /**\n     * Event emitted when a click occurs outside the element\n     */\n    this.appClickOutside = new EventEmitter();\n  }\n  /**\n   * Listen for document click events\n   * @param event - Mouse event\n   */\n  onClick(event) {\n    // Check if the click was outside the element\n    const clickedInside = this.elementRef.nativeElement.contains(event.target);\n    if (!clickedInside) {\n      this.appClickOutside.emit();\n    }\n  }\n};\n__decorate([Output()], ClickOutsideDirective.prototype, \"appClickOutside\", void 0);\n__decorate([HostListener('document:click', ['$event'])], ClickOutsideDirective.prototype, \"onClick\", null);\nClickOutsideDirective = __decorate([Directive({\n  selector: '[appClickOutside]'\n})], ClickOutsideDirective);", "map": {"version": 3, "names": ["Directive", "EventEmitter", "HostListener", "Output", "ClickOutsideDirective", "constructor", "elementRef", "appClickOutside", "onClick", "event", "clickedInside", "nativeElement", "contains", "target", "emit", "__decorate", "selector"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\directives\\click-outside.directive.ts"], "sourcesContent": ["/**\n * Click Outside Directive\n * Detects clicks outside of an element and emits an event\n */\nimport { Directive, ElementRef, EventEmitter, HostListener, Output } from '@angular/core';\n\n@Directive({\n  selector: '[appClickOutside]'\n})\nexport class ClickOutsideDirective {\n  /**\n   * Event emitted when a click occurs outside the element\n   */\n  @Output() appClickOutside = new EventEmitter<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param elementRef - Reference to the host element\n   */\n  constructor(private elementRef: ElementRef) {}\n\n  /**\n   * Listen for document click events\n   * @param event - Mouse event\n   */\n  @HostListener('document:click', ['$event'])\n  onClick(event: MouseEvent): void {\n    // Check if the click was outside the element\n    const clickedInside = this.elementRef.nativeElement.contains(event.target);\n    \n    if (!clickedInside) {\n      this.appClickOutside.emit();\n    }\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAcC,YAAY,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AAKlF,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAMhC;;;;EAIAC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAT9B;;;IAGU,KAAAC,eAAe,GAAG,IAAIN,YAAY,EAAQ;EAMP;EAE7C;;;;EAKAO,OAAOA,CAACC,KAAiB;IACvB;IACA,MAAMC,aAAa,GAAG,IAAI,CAACJ,UAAU,CAACK,aAAa,CAACC,QAAQ,CAACH,KAAK,CAACI,MAAM,CAAC;IAE1E,IAAI,CAACH,aAAa,EAAE;MAClB,IAAI,CAACH,eAAe,CAACO,IAAI,EAAE;;EAE/B;CACD;AArBWC,UAAA,EAATZ,MAAM,EAAE,C,6DAA4C;AAarDY,UAAA,EADCb,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,mDAQ1C;AAxBUE,qBAAqB,GAAAW,UAAA,EAHjCf,SAAS,CAAC;EACTgB,QAAQ,EAAE;CACX,CAAC,C,EACWZ,qBAAqB,CAyBjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}