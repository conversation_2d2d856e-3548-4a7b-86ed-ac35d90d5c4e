/**
 * Task Service
 * Handles API communication for task-related operations
 */
import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Task } from '../models/task.model';
import { TaskFilter } from '../models/task-filter.model';
import { ApiResponse } from '../models/api-response.model';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class TaskService {
  /**
   * API endpoint path
   */
  private endpoint = 'tasks';

  /**
   * Constructor with dependency injection
   * @param apiService - Base API service for HTTP requests
   */
  constructor(private apiService: ApiService) {}

  /**
   * Get all tasks with optional filtering
   * @param filter - Optional filter parameters
   * @returns Observable with array of tasks
   */
  getTasks(filter?: TaskFilter): Observable<Task[]> {
    // Build query parameters from filter
    let params = new HttpParams();
    
    if (filter) {
      if (filter.status) {
        params = params.set('status', filter.status);
      }
      
      if (filter.priority) {
        params = params.set('priority', filter.priority);
      }
      
      if (filter.assigneeId) {
        params = params.set('assigneeId', filter.assigneeId);
      }
    }
    
    // Make API request
    return this.apiService.get<ApiResponse<Task[]>>(this.endpoint, params)
      .pipe(
        map(response => response.data)
      );
  }

  /**
   * Get a task by ID
   * @param id - Task ID
   * @returns Observable with task data
   */
  getTask(id: string): Observable<Task> {
    return this.apiService.get<ApiResponse<Task>>(`${this.endpoint}/${id}`)
      .pipe(
        map(response => response.data)
      );
  }
  
  /**
   * Get task by ID (alias for getTask for compatibility)
   * @param id - Task ID
   * @returns Observable with task data
   */
  getTaskById(id: string): Observable<Task> {
    return this.getTask(id);
  }

  /**
   * Create a new task
   * @param task - Task data
   * @returns Observable with created task
   */
  createTask(task: Partial<Task>): Observable<Task> {
    return this.apiService.post<ApiResponse<Task>>(this.endpoint, task)
      .pipe(
        map(response => response.data)
      );
  }

  /**
   * Update an existing task
   * @param id - Task ID
   * @param task - Updated task data
   * @returns Observable with updated task
   */
  updateTask(id: string, task: Partial<Task>): Observable<Task> {
    return this.apiService.put<ApiResponse<Task>>(`${this.endpoint}/${id}`, task)
      .pipe(
        map(response => response.data)
      );
  }

  /**
   * Delete a task
   * @param id - Task ID
   * @returns Observable with success status
   */
  deleteTask(id: string): Observable<boolean> {
    return this.apiService.delete<ApiResponse<Record<string, never>>>(`${this.endpoint}/${id}`)
      .pipe(
        map(response => response.success)
      );
  }

  /**
   * Get tasks assigned to a specific user
   * @param userId - User ID
   * @returns Observable with array of tasks
   */
  getTasksByAssignee(userId: string): Observable<Task[]> {
    const params = new HttpParams().set('assigneeId', userId);
    
    return this.apiService.get<ApiResponse<Task[]>>(this.endpoint, params)
      .pipe(
        map(response => response.data)
      );
  }
}
