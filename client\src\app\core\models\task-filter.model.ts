/**
 * Task Filter model interface
 * Used for filtering tasks in API requests
 */
export interface TaskFilter {
  /**
   * Filter by task status
   */
  status?: 'todo' | 'in_progress' | 'review' | 'done';
  
  /**
   * Filter by task priority
   */
  priority?: 'low' | 'medium' | 'high';
  
  /**
   * Filter by assignee ID
   */
  assigneeId?: string;
  
  /**
   * Filter by tags (comma-separated)
   */
  tags?: string;
  
  /**
   * Filter by due date range (start)
   */
  dueDateStart?: Date;
  
  /**
   * Filter by due date range (end)
   */
  dueDateEnd?: Date;
}
