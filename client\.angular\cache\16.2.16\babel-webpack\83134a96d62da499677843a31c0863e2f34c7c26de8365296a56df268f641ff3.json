{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TimeAgoPipe {\n  /**\n   * Transform method to convert date to relative time\n   * @param value - Input date (string, Date, or number)\n   * @returns Relative time string\n   */\n  transform(value) {\n    if (!value) {\n      return '';\n    }\n    // Convert input to Date object\n    const date = value instanceof Date ? value : new Date(value);\n    // Get current time\n    const now = new Date();\n    // Calculate time difference in seconds\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    // Define time intervals in seconds\n    const intervals = {\n      year: 31536000,\n      month: 2592000,\n      week: 604800,\n      day: 86400,\n      hour: 3600,\n      minute: 60,\n      second: 1\n    };\n    // Format based on time difference\n    if (diffInSeconds < 5) {\n      return 'just now';\n    } else if (diffInSeconds < intervals.minute) {\n      return `${diffInSeconds} seconds ago`;\n    } else if (diffInSeconds < intervals.hour) {\n      const minutes = Math.floor(diffInSeconds / intervals.minute);\n      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;\n    } else if (diffInSeconds < intervals.day) {\n      const hours = Math.floor(diffInSeconds / intervals.hour);\n      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;\n    } else if (diffInSeconds < intervals.week) {\n      const days = Math.floor(diffInSeconds / intervals.day);\n      return `${days} ${days === 1 ? 'day' : 'days'} ago`;\n    } else if (diffInSeconds < intervals.month) {\n      const weeks = Math.floor(diffInSeconds / intervals.week);\n      return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;\n    } else if (diffInSeconds < intervals.year) {\n      const months = Math.floor(diffInSeconds / intervals.month);\n      return `${months} ${months === 1 ? 'month' : 'months'} ago`;\n    } else {\n      const years = Math.floor(diffInSeconds / intervals.year);\n      return `${years} ${years === 1 ? 'year' : 'years'} ago`;\n    }\n  }\n  static {\n    this.ɵfac = function TimeAgoPipe_Factory(t) {\n      return new (t || TimeAgoPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"timeAgo\",\n      type: TimeAgoPipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["TimeAgoPipe", "transform", "value", "date", "Date", "now", "diffInSeconds", "Math", "floor", "getTime", "intervals", "year", "month", "week", "day", "hour", "minute", "second", "minutes", "hours", "days", "weeks", "months", "years", "pure"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\pipes\\time-ago.pipe.ts"], "sourcesContent": ["/**\n * Time Ago Pipe\n * Converts timestamps to relative time format (e.g., \"5 minutes ago\")\n */\nimport { <PERSON><PERSON> } from '@angular/core';\n\n@Pipe({\n  name: 'timeAgo'\n})\nexport class TimeAgoPipe {\n  /**\n   * Transform method to convert date to relative time\n   * @param value - Input date (string, Date, or number)\n   * @returns Relative time string\n   */\n  transform(value: string | Date | number): string {\n    if (!value) {\n      return '';\n    }\n\n    // Convert input to Date object\n    const date = value instanceof Date ? value : new Date(value);\n    \n    // Get current time\n    const now = new Date();\n    \n    // Calculate time difference in seconds\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    \n    // Define time intervals in seconds\n    const intervals = {\n      year: 31536000,\n      month: 2592000,\n      week: 604800,\n      day: 86400,\n      hour: 3600,\n      minute: 60,\n      second: 1\n    };\n    \n    // Format based on time difference\n    if (diffInSeconds < 5) {\n      return 'just now';\n    } else if (diffInSeconds < intervals.minute) {\n      return `${diffInSeconds} seconds ago`;\n    } else if (diffInSeconds < intervals.hour) {\n      const minutes = Math.floor(diffInSeconds / intervals.minute);\n      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;\n    } else if (diffInSeconds < intervals.day) {\n      const hours = Math.floor(diffInSeconds / intervals.hour);\n      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;\n    } else if (diffInSeconds < intervals.week) {\n      const days = Math.floor(diffInSeconds / intervals.day);\n      return `${days} ${days === 1 ? 'day' : 'days'} ago`;\n    } else if (diffInSeconds < intervals.month) {\n      const weeks = Math.floor(diffInSeconds / intervals.week);\n      return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;\n    } else if (diffInSeconds < intervals.year) {\n      const months = Math.floor(diffInSeconds / intervals.month);\n      return `${months} ${months === 1 ? 'month' : 'months'} ago`;\n    } else {\n      const years = Math.floor(diffInSeconds / intervals.year);\n      return `${years} ${years === 1 ? 'year' : 'years'} ago`;\n    }\n  }\n}\n"], "mappings": ";AASA,OAAM,MAAOA,WAAW;EACtB;;;;;EAKAC,SAASA,CAACC,KAA6B;IACrC,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,EAAE;;IAGX;IACA,MAAMC,IAAI,GAAGD,KAAK,YAAYE,IAAI,GAAGF,KAAK,GAAG,IAAIE,IAAI,CAACF,KAAK,CAAC;IAE5D;IACA,MAAMG,GAAG,GAAG,IAAID,IAAI,EAAE;IAEtB;IACA,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACI,OAAO,EAAE,GAAGN,IAAI,CAACM,OAAO,EAAE,IAAI,IAAI,CAAC;IAEzE;IACA,MAAMC,SAAS,GAAG;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;KACT;IAED;IACA,IAAIX,aAAa,GAAG,CAAC,EAAE;MACrB,OAAO,UAAU;KAClB,MAAM,IAAIA,aAAa,GAAGI,SAAS,CAACM,MAAM,EAAE;MAC3C,OAAO,GAAGV,aAAa,cAAc;KACtC,MAAM,IAAIA,aAAa,GAAGI,SAAS,CAACK,IAAI,EAAE;MACzC,MAAMG,OAAO,GAAGX,IAAI,CAACC,KAAK,CAACF,aAAa,GAAGI,SAAS,CAACM,MAAM,CAAC;MAC5D,OAAO,GAAGE,OAAO,IAAIA,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,MAAM;KAChE,MAAM,IAAIZ,aAAa,GAAGI,SAAS,CAACI,GAAG,EAAE;MACxC,MAAMK,KAAK,GAAGZ,IAAI,CAACC,KAAK,CAACF,aAAa,GAAGI,SAAS,CAACK,IAAI,CAAC;MACxD,OAAO,GAAGI,KAAK,IAAIA,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,MAAM;KACxD,MAAM,IAAIb,aAAa,GAAGI,SAAS,CAACG,IAAI,EAAE;MACzC,MAAMO,IAAI,GAAGb,IAAI,CAACC,KAAK,CAACF,aAAa,GAAGI,SAAS,CAACI,GAAG,CAAC;MACtD,OAAO,GAAGM,IAAI,IAAIA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,MAAM;KACpD,MAAM,IAAId,aAAa,GAAGI,SAAS,CAACE,KAAK,EAAE;MAC1C,MAAMS,KAAK,GAAGd,IAAI,CAACC,KAAK,CAACF,aAAa,GAAGI,SAAS,CAACG,IAAI,CAAC;MACxD,OAAO,GAAGQ,KAAK,IAAIA,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,MAAM;KACxD,MAAM,IAAIf,aAAa,GAAGI,SAAS,CAACC,IAAI,EAAE;MACzC,MAAMW,MAAM,GAAGf,IAAI,CAACC,KAAK,CAACF,aAAa,GAAGI,SAAS,CAACE,KAAK,CAAC;MAC1D,OAAO,GAAGU,MAAM,IAAIA,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,QAAQ,MAAM;KAC5D,MAAM;MACL,MAAMC,KAAK,GAAGhB,IAAI,CAACC,KAAK,CAACF,aAAa,GAAGI,SAAS,CAACC,IAAI,CAAC;MACxD,OAAO,GAAGY,KAAK,IAAIA,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,MAAM;;EAE3D;;;uBAvDWvB,WAAW;IAAA;EAAA;;;;YAAXA,WAAW;MAAAwB,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}