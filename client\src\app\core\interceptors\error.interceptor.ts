/**
 * Error Interceptor
 * Handles HTTP errors globally and provides consistent error handling
 */
import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { NotificationService } from '../services/notification.service';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  /**
   * Constructor with dependency injection
   * @param authService - Authentication service
   * @param notificationService - Notification service for displaying errors
   * @param router - Router for navigation
   */
  constructor(
    private authService: AuthService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  /**
   * Intercepts HTTP responses and handles errors
   * @param request - The outgoing HTTP request
   * @param next - The next interceptor in the chain
   * @returns Observable of HTTP event
   */
  intercept(request: HttpRequest<unknown>, next: HttpHand<PERSON>): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        let errorMessage = 'An unknown error occurred';
        
        // Handle different types of errors
        if (error.error instanceof ErrorEvent) {
          // Client-side error
          errorMessage = `Error: ${error.error.message}`;
        } else {
          // Server-side error
          switch (error.status) {
            case 400:
              // Bad request
              errorMessage = error.error?.message || 'Bad request';
              break;
              
            case 401:
              // Unauthorized
              errorMessage = 'You are not authorized to access this resource';
              // Logout user if token is invalid
              this.authService.logout();
              this.router.navigate(['/auth/login']);
              break;
              
            case 403:
              // Forbidden
              errorMessage = 'You do not have permission to access this resource';
              break;
              
            case 404:
              // Not found
              errorMessage = 'Resource not found';
              break;
              
            case 500:
              // Server error
              errorMessage = 'Server error. Please try again later';
              break;
              
            default:
              // Other errors
              errorMessage = error.error?.message || `Error ${error.status}: ${error.statusText}`;
              break;
          }
        }
        
        // Show error notification
        this.notificationService.error(errorMessage);
        
        // Pass the error along
        return throwError(() => new Error(errorMessage));
      })
    );
  }
}
