/**
 * Dashboard Module
 * Contains components for the dashboard feature
 */
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { DashboardPageComponent } from './pages/dashboard-page/dashboard-page.component';
import { TaskSummaryComponent } from './components/task-summary/task-summary.component';
import { RecentTasksComponent } from './components/recent-tasks/recent-tasks.component';
import { TaskStatusChartComponent } from './components/task-status-chart/task-status-chart.component';
import { TaskPriorityChartComponent } from './components/task-priority-chart/task-priority-chart.component';

// Dashboard routes
const routes: Routes = [
  {
    path: '',
    component: DashboardPageComponent
  }
];

@NgModule({
  declarations: [
    DashboardPageComponent,
    TaskSummaryComponent,
    RecentTasksComponent,
    TaskStatusChartComponent,
    TaskPriorityChartComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes)
  ]
})
export class DashboardModule { }
