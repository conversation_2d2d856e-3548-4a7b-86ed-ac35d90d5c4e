<!-- Side navigation menu -->
<div class="sidenav" [class.expanded]="isExpanded">
  <!-- Toggle button for expanding/collapsing sidenav -->
  <div class="sidenav-toggle">
    <button mat-icon-button (click)="toggleSidenav()" aria-label="Toggle sidenav">
      <mat-icon>{{ isExpanded ? 'chevron_left' : 'chevron_right' }}</mat-icon>
    </button>
  </div>

  <!-- Navigation items -->
  <nav class="sidenav-nav">
    <ul class="nav-list">
      <!-- Loop through navigation items -->
      <li *ngFor="let item of navItems">
        <!-- Only show admin items to admin users -->
        <a *ngIf="!item.requiresAdmin || isAdmin()"
           [routerLink]="item.route"
           [class.active]="isActive(item.route)"
           class="nav-item"
           [attr.title]="item.label">
          <mat-icon class="nav-icon">{{ item.icon }}</mat-icon>
          <span class="nav-label" *ngIf="isExpanded">{{ item.label }}</span>
        </a>
      </li>
    </ul>
  </nav>

  <!-- User profile section at bottom -->
  <div class="sidenav-footer">
    <a [routerLink]="['/profile']" class="profile-link">
      <div class="user-avatar" [style.backgroundImage]="currentUser?.avatarUrl ? 'url(' + currentUser?.avatarUrl + ')' : ''">
        <span *ngIf="!currentUser?.avatarUrl">{{ currentUser?.name?.charAt(0) }}</span>
      </div>
      <div class="user-info" *ngIf="isExpanded">
        <span class="user-name">{{ currentUser?.name }}</span>
        <span class="user-role">{{ currentUser?.role }}</span>
      </div>
    </a>
  </div>
</div>
