{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../../../shared/shared.module';\nimport { NotFoundPageComponent } from './not-found-page.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n// Define routes for the not-found page\nconst routes = [{\n  path: '',\n  component: NotFoundPageComponent\n}];\nexport class NotFoundPageModule {\n  static {\n    this.ɵfac = function NotFoundPageModule_Factory(t) {\n      return new (t || NotFoundPageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: NotFoundPageModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(NotFoundPageModule, {\n    declarations: [NotFoundPageComponent],\n    imports: [CommonModule, SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "SharedModule", "NotFoundPageComponent", "routes", "path", "component", "NotFoundPageModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\not-found\\pages\\not-found-page\\not-found-page.module.ts"], "sourcesContent": ["/**\n * Not Found Page module\n * Contains the component for displaying 404 error page\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SharedModule } from '../../../../shared/shared.module';\nimport { NotFoundPageComponent } from './not-found-page.component';\n\n// Define routes for the not-found page\nconst routes: Routes = [\n  {\n    path: '',\n    component: NotFoundPageComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    NotFoundPageComponent\n  ],\n  imports: [\n    CommonModule,\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class NotFoundPageModule { }\n"], "mappings": "AAKA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,qBAAqB,QAAQ,4BAA4B;;;AAElE;AACA,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAYD,OAAM,MAAOI,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAL3BP,YAAY,EACZE,YAAY,EACZD,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC;IAAA;EAAA;;;2EAGpBG,kBAAkB;IAAAE,YAAA,GAR3BN,qBAAqB;IAAAO,OAAA,GAGrBV,YAAY,EACZE,YAAY,EAAAS,EAAA,CAAAV,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}