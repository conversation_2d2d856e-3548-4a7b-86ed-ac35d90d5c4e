<!-- Task filter container -->
<div class="task-filter-container">
  <!-- Filter form -->
  <form [formGroup]="filterForm" class="filter-form">
    <!-- Basic filters row -->
    <div class="filter-row">
      <!-- Search input -->
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search tasks</mat-label>
        <input matInput formControlName="search" placeholder="Search by title or description">
        <mat-icon matPrefix>search</mat-icon>
        <button 
          *ngIf="filterForm.get('search')?.value" 
          matSuffix 
          mat-icon-button 
          aria-label="Clear" 
          (click)="filterForm.get('search')?.setValue('')">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>
      
      <!-- Status filter -->
      <mat-form-field appearance="outline">
        <mat-label>Status</mat-label>
        <mat-select formControlName="status">
          <mat-option *ngFor="let status of statuses" [value]="status.value">
            {{ status.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      
      <!-- Priority filter -->
      <mat-form-field appearance="outline">
        <mat-label>Priority</mat-label>
        <mat-select formControlName="priority">
          <mat-option *ngFor="let priority of priorities" [value]="priority.value">
            {{ priority.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      
      <!-- Filter actions -->
      <div class="filter-actions">
        <button 
          mat-button 
          type="button" 
          color="primary" 
          (click)="toggleAdvancedFilters()">
          {{ showAdvancedFilters ? 'Hide Advanced' : 'Advanced Filters' }}
          <mat-icon>{{ showAdvancedFilters ? 'expand_less' : 'expand_more' }}</mat-icon>
        </button>
        
        <button 
          mat-button 
          type="button" 
          (click)="clearFilters()">
          Clear All
        </button>
      </div>
    </div>
    
    <!-- Advanced filters section -->
    <div class="advanced-filters" *ngIf="showAdvancedFilters">
      <div class="filter-row">
        <!-- Assignee filter -->
        <mat-form-field appearance="outline">
          <mat-label>Assignee</mat-label>
          <input matInput formControlName="assigneeId" placeholder="Assignee ID">
        </mat-form-field>
        
        <!-- Tags filter -->
        <mat-form-field appearance="outline">
          <mat-label>Tags</mat-label>
          <mat-chip-grid #chipGrid aria-label="Tag selection">
            <mat-chip-row
              *ngFor="let tag of filterForm.get('tags')?.value || []"
              (removed)="filterForm.get('tags')?.setValue(filterForm.get('tags')?.value.filter(t => t !== tag))">
              {{tag}}
              <button matChipRemove aria-label="remove {{tag}}">
                <mat-icon>cancel</mat-icon>
              </button>
            </mat-chip-row>
            <input
              placeholder="Add tag..."
              [matChipInputFor]="chipGrid"
              (matChipInputTokenEnd)="filterForm.get('tags')?.setValue([...(filterForm.get('tags')?.value || []), $event.value]); $event.chipInput.clear()">
          </mat-chip-grid>
        </mat-form-field>
      </div>
      
      <div class="filter-row">
        <!-- Due date range -->
        <mat-form-field appearance="outline">
          <mat-label>Due date from</mat-label>
          <input matInput [matDatepicker]="fromPicker" formControlName="dueDateFrom">
          <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
          <mat-datepicker #fromPicker></mat-datepicker>
        </mat-form-field>
        
        <mat-form-field appearance="outline">
          <mat-label>Due date to</mat-label>
          <input matInput [matDatepicker]="toPicker" formControlName="dueDateTo">
          <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
          <mat-datepicker #toPicker></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
  </form>
</div>
