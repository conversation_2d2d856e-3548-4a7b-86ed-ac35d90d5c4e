{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"../../../../core/services/notification.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"../../components/forgot-password-form/forgot-password-form.component\";\nexport class ForgotPasswordPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, notificationService, router, cdr) {\n    this.authService = authService;\n    this.notificationService = notificationService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed request\n     */\n    this.error = null;\n    /**\n     * Success message after successful request\n     */\n    this.success = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit() {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Handle forgot password form submission\n   * @param data - Form data with email\n   */\n  onFormSubmit(data) {\n    this.loading = true;\n    this.error = null;\n    this.success = null;\n    this.cdr.markForCheck();\n    this.authService.forgotPassword(data.email).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.loading = false;\n        this.success = 'Password reset instructions have been sent to your email.';\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.message || 'Failed to send password reset email. Please try again.';\n        console.error('Forgot password error:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Navigate to login page\n   */\n  onLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  static {\n    this.ɵfac = function ForgotPasswordPageComponent_Factory(t) {\n      return new (t || ForgotPasswordPageComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotPasswordPageComponent,\n      selectors: [[\"app-forgot-password-page\"]],\n      decls: 11,\n      vars: 3,\n      consts: [[1, \"forgot-password-page-container\"], [1, \"auth-container\"], [1, \"auth-header\"], [1, \"logo\"], [1, \"app-title\"], [1, \"app-subtitle\"], [3, \"loading\", \"error\", \"success\", \"formSubmit\", \"login\"]],\n      template: function ForgotPasswordPageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"task_alt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"h1\", 4);\n          i0.ɵɵtext(7, \"Task Manager\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 5);\n          i0.ɵɵtext(9, \"Reset your password\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"app-forgot-password-form\", 6);\n          i0.ɵɵlistener(\"formSubmit\", function ForgotPasswordPageComponent_Template_app_forgot_password_form_formSubmit_10_listener($event) {\n            return ctx.onFormSubmit($event);\n          })(\"login\", function ForgotPasswordPageComponent_Template_app_forgot_password_form_login_10_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"loading\", ctx.loading)(\"error\", ctx.error)(\"success\", ctx.success);\n        }\n      },\n      dependencies: [i4.MatIcon, i5.ForgotPasswordFormComponent],\n      styles: [\"\\n\\n\\n\\n\\n\\n.forgot-password-page-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  background-color: #f5f5f5;\\n  padding: 24px;\\n}\\n\\n\\n\\n.auth-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n\\n\\n.auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n\\n\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 16px;\\n}\\n.logo[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: var(--primary-color);\\n}\\n\\n\\n\\n.app-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 500;\\n  margin: 0 0 8px;\\n  color: #333;\\n}\\n\\n\\n\\n.app-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .forgot-password-page-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    background-color: #fff;\\n  }\\n  .auth-header[_ngcontent-%COMP%] {\\n    margin-bottom: 24px;\\n  }\\n  .app-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "ForgotPasswordPageComponent", "constructor", "authService", "notificationService", "router", "cdr", "loading", "error", "success", "destroy$", "ngOnInit", "isAuthenticated", "navigate", "ngOnDestroy", "next", "complete", "onFormSubmit", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgotPassword", "email", "pipe", "subscribe", "err", "message", "console", "onLogin", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "NotificationService", "i3", "Router", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "ForgotPasswordPageComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ForgotPasswordPageComponent_Template_app_forgot_password_form_formSubmit_10_listener", "$event", "ForgotPasswordPageComponent_Template_app_forgot_password_form_login_10_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\forgot-password-page\\forgot-password-page.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\forgot-password-page\\forgot-password-page.component.html"], "sourcesContent": ["/**\n * Forgot Password Page Component\n * Page for requesting password reset\n */\nimport { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-forgot-password-page',\n  templateUrl: './forgot-password-page.component.html',\n  styleUrls: ['./forgot-password-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ForgotPasswordPageComponent implements OnInit, OnD<PERSON>roy {\n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message from failed request\n   */\n  error: string | null = null;\n  \n  /**\n   * Success message after successful request\n   */\n  success: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private notificationService: NotificationService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit(): void {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Handle forgot password form submission\n   * @param data - Form data with email\n   */\n  onFormSubmit(data: { email: string }): void {\n    this.loading = true;\n    this.error = null;\n    this.success = null;\n    this.cdr.markForCheck();\n    \n    this.authService.forgotPassword(data.email)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.loading = false;\n          this.success = 'Password reset instructions have been sent to your email.';\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = err.message || 'Failed to send password reset email. Please try again.';\n          console.error('Forgot password error:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Navigate to login page\n   */\n  onLogin(): void {\n    this.router.navigate(['/auth/login']);\n  }\n}\n", "<!-- Forgot password page container -->\n<div class=\"forgot-password-page-container\">\n  <div class=\"auth-container\">\n    <!-- App logo and title -->\n    <div class=\"auth-header\">\n      <div class=\"logo\">\n        <mat-icon>task_alt</mat-icon>\n      </div>\n      <h1 class=\"app-title\">Task Manager</h1>\n      <p class=\"app-subtitle\">Reset your password</p>\n    </div>\n    \n    <!-- Forgot password form component -->\n    <app-forgot-password-form\n      [loading]=\"loading\"\n      [error]=\"error\"\n      [success]=\"success\"\n      (formSubmit)=\"onFormSubmit($event)\"\n      (login)=\"onLogin()\">\n    </app-forgot-password-form>\n  </div>\n</div>\n"], "mappings": "AAMA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;AAU1C,OAAM,MAAOC,2BAA2B;EAqBtC;;;;;;;EAOAC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA/Bb;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGA,KAAAC,OAAO,GAAkB,IAAI;IAE7B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIX,OAAO,EAAQ;EAcnC;EAEH;;;;EAIAY,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACR,WAAW,CAACS,eAAe,EAAE,EAAE;MACtC,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;;EAIAC,YAAYA,CAACC,IAAuB;IAClC,IAAI,CAACX,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,GAAG,CAACa,YAAY,EAAE;IAEvB,IAAI,CAAChB,WAAW,CAACiB,cAAc,CAACF,IAAI,CAACG,KAAK,CAAC,CACxCC,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAC9Ba,SAAS,CAAC;MACTR,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACR,OAAO,GAAG,KAAK;QACpB,IAAI,CAACE,OAAO,GAAG,2DAA2D;QAC1E,IAAI,CAACH,GAAG,CAACa,YAAY,EAAE;MACzB,CAAC;MACDX,KAAK,EAAGgB,GAAG,IAAI;QACb,IAAI,CAACjB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGgB,GAAG,CAACC,OAAO,IAAI,wDAAwD;QACpFC,OAAO,CAAClB,KAAK,CAAC,wBAAwB,EAAEgB,GAAG,CAAC;QAC5C,IAAI,CAAClB,GAAG,CAACa,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAQ,OAAOA,CAAA;IACL,IAAI,CAACtB,MAAM,CAACQ,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;;;uBAvFWZ,2BAA2B,EAAA2B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAQ,iBAAA;IAAA;EAAA;;;YAA3BnC,2BAA2B;MAAAoC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBxCf,EAAA,CAAAiB,cAAA,aAA4C;UAK1BjB,EAAA,CAAAkB,MAAA,eAAQ;UAAAlB,EAAA,CAAAmB,YAAA,EAAW;UAE/BnB,EAAA,CAAAiB,cAAA,YAAsB;UAAAjB,EAAA,CAAAkB,MAAA,mBAAY;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACvCnB,EAAA,CAAAiB,cAAA,WAAwB;UAAAjB,EAAA,CAAAkB,MAAA,0BAAmB;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAIjDnB,EAAA,CAAAiB,cAAA,mCAKsB;UADpBjB,EAAA,CAAAoB,UAAA,wBAAAC,qFAAAC,MAAA;YAAA,OAAcN,GAAA,CAAA3B,YAAA,CAAAiC,MAAA,CAAoB;UAAA,EAAC,mBAAAC,gFAAA;YAAA,OAC1BP,GAAA,CAAAjB,OAAA,EAAS;UAAA,EADiB;UAErCC,EAAA,CAAAmB,YAAA,EAA2B;;;UALzBnB,EAAA,CAAAwB,SAAA,IAAmB;UAAnBxB,EAAA,CAAAyB,UAAA,YAAAT,GAAA,CAAArC,OAAA,CAAmB,UAAAqC,GAAA,CAAApC,KAAA,aAAAoC,GAAA,CAAAnC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}