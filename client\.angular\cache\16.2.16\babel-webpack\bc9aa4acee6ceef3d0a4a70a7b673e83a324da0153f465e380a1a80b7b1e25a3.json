{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, Input, Directive, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"], [\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"], [\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"], [\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"], [\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]], \"*\"];\nconst _c2 = [\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\", \"*\"];\nconst _c3 = [[[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]], [[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], \"*\"];\nconst _c4 = [\"[mat-card-avatar], [matCardAvatar]\", \"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"*\"];\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n  constructor(config) {\n    this.appearance = config?.appearance || 'raised';\n  }\n  static {\n    this.ɵfac = function MatCard_Factory(t) {\n      return new (t || MatCard)(i0.ɵɵdirectiveInject(MAT_CARD_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCard,\n      selectors: [[\"mat-card\"]],\n      hostAttrs: [1, \"mat-mdc-card\", \"mdc-card\"],\n      hostVars: 4,\n      hostBindings: function MatCard_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-card-outlined\", ctx.appearance === \"outlined\")(\"mdc-card--outlined\", ctx.appearance === \"outlined\");\n        }\n      },\n      inputs: {\n        appearance: \"appearance\"\n      },\n      exportAs: [\"matCard\"],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatCard_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation);--mdc-elevated-card-container-shape:4px;--mdc-outlined-card-container-shape:4px;--mdc-outlined-card-outline-width:1px}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCard, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card',\n      host: {\n        'class': 'mat-mdc-card mdc-card',\n        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n        '[class.mdc-card--outlined]': 'appearance === \"outlined\"'\n      },\n      exportAs: 'matCard',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content></ng-content>\\n\",\n      styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation);--mdc-elevated-card-container-shape:4px;--mdc-outlined-card-container-shape:4px;--mdc-outlined-card-outline-width:1px}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_CARD_CONFIG]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {\n  static {\n    this.ɵfac = function MatCardTitle_Factory(t) {\n      return new (t || MatCardTitle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardTitle,\n      selectors: [[\"mat-card-title\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"matCardTitle\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-title\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n      host: {\n        'class': 'mat-mdc-card-title'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {\n  static {\n    this.ɵfac = function MatCardTitleGroup_Factory(t) {\n      return new (t || MatCardTitleGroup)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCardTitleGroup,\n      selectors: [[\"mat-card-title-group\"]],\n      hostAttrs: [1, \"mat-mdc-card-title-group\"],\n      ngContentSelectors: _c2,\n      decls: 4,\n      vars: 0,\n      template: function MatCardTitleGroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitleGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-title-group',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-title-group'\n      },\n      template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {\n  static {\n    this.ɵfac = function MatCardContent_Factory(t) {\n      return new (t || MatCardContent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardContent,\n      selectors: [[\"mat-card-content\"]],\n      hostAttrs: [1, \"mat-mdc-card-content\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardContent, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-content',\n      host: {\n        'class': 'mat-mdc-card-content'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {\n  static {\n    this.ɵfac = function MatCardSubtitle_Factory(t) {\n      return new (t || MatCardSubtitle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardSubtitle,\n      selectors: [[\"mat-card-subtitle\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-subtitle\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSubtitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n      host: {\n        'class': 'mat-mdc-card-subtitle'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n  constructor() {\n    // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n    // as to not conflict with the native `align` attribute.\n    /** Position of the actions inside the card. */\n    this.align = 'start';\n  }\n  static {\n    this.ɵfac = function MatCardActions_Factory(t) {\n      return new (t || MatCardActions)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardActions,\n      selectors: [[\"mat-card-actions\"]],\n      hostAttrs: [1, \"mat-mdc-card-actions\", \"mdc-card__actions\"],\n      hostVars: 2,\n      hostBindings: function MatCardActions_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-card-actions-align-end\", ctx.align === \"end\");\n        }\n      },\n      inputs: {\n        align: \"align\"\n      },\n      exportAs: [\"matCardActions\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardActions, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-actions',\n      exportAs: 'matCardActions',\n      host: {\n        'class': 'mat-mdc-card-actions mdc-card__actions',\n        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {\n  static {\n    this.ɵfac = function MatCardHeader_Factory(t) {\n      return new (t || MatCardHeader)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCardHeader,\n      selectors: [[\"mat-card-header\"]],\n      hostAttrs: [1, \"mat-mdc-card-header\"],\n      ngContentSelectors: _c4,\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"mat-mdc-card-header-text\"]],\n      template: function MatCardHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(3, 2);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-header'\n      },\n      template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {\n  static {\n    this.ɵfac = function MatCardFooter_Factory(t) {\n      return new (t || MatCardFooter)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardFooter,\n      selectors: [[\"mat-card-footer\"]],\n      hostAttrs: [1, \"mat-mdc-card-footer\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardFooter, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-footer',\n      host: {\n        'class': 'mat-mdc-card-footer'\n      }\n    }]\n  }], null, null);\n})();\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {\n  static {\n    this.ɵfac = function MatCardImage_Factory(t) {\n      return new (t || MatCardImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardImage,\n      selectors: [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-image\", \"mdc-card__media\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-image], [matCardImage]',\n      host: {\n        'class': 'mat-mdc-card-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {\n  static {\n    this.ɵfac = function MatCardSmImage_Factory(t) {\n      return new (t || MatCardSmImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardSmImage,\n      selectors: [[\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-sm-image\", \"mdc-card__media\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSmImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-sm-image], [matCardImageSmall]',\n      host: {\n        'class': 'mat-mdc-card-sm-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {\n  static {\n    this.ɵfac = function MatCardMdImage_Factory(t) {\n      return new (t || MatCardMdImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardMdImage,\n      selectors: [[\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-md-image\", \"mdc-card__media\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardMdImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-md-image], [matCardImageMedium]',\n      host: {\n        'class': 'mat-mdc-card-md-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {\n  static {\n    this.ɵfac = function MatCardLgImage_Factory(t) {\n      return new (t || MatCardLgImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardLgImage,\n      selectors: [[\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-lg-image\", \"mdc-card__media\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardLgImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-lg-image], [matCardImageLarge]',\n      host: {\n        'class': 'mat-mdc-card-lg-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {\n  static {\n    this.ɵfac = function MatCardXlImage_Factory(t) {\n      return new (t || MatCardXlImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardXlImage,\n      selectors: [[\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-xl-image\", \"mdc-card__media\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardXlImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-xl-image], [matCardImageXLarge]',\n      host: {\n        'class': 'mat-mdc-card-xl-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {\n  static {\n    this.ɵfac = function MatCardAvatar_Factory(t) {\n      return new (t || MatCardAvatar)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardAvatar,\n      selectors: [[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-avatar\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardAvatar, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-avatar], [matCardAvatar]',\n      host: {\n        'class': 'mat-mdc-card-avatar'\n      }\n    }]\n  }], null, null);\n})();\nconst CARD_DIRECTIVES = [MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage];\nclass MatCardModule {\n  static {\n    this.ɵfac = function MatCardModule_Factory(t) {\n      return new (t || MatCardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatCardModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CommonModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule],\n      exports: [CARD_DIRECTIVES, MatCommonModule],\n      declarations: CARD_DIRECTIVES\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Inject", "Optional", "Input", "Directive", "NgModule", "CommonModule", "MatCommonModule", "_c0", "_c1", "_c2", "_c3", "_c4", "MAT_CARD_CONFIG", "MatCard", "constructor", "config", "appearance", "ɵfac", "MatCard_Factory", "t", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatCard_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "exportAs", "ngContentSelectors", "decls", "vars", "template", "MatCard_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "undefined", "decorators", "MatCardTitle", "MatCardTitle_Factory", "ɵdir", "ɵɵdefineDirective", "MatCardTitleGroup", "MatCardTitleGroup_Factory", "MatCardTitleGroup_Template", "ɵɵelementStart", "ɵɵelementEnd", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardContent_Factory", "MatCardSubtitle", "MatCardSubtitle_Factory", "MatCardActions", "align", "MatCardActions_Factory", "MatCardActions_HostBindings", "MatCardHeader", "MatCardHeader_Factory", "consts", "MatCardHeader_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatCardFooter_Factory", "MatCardImage", "MatCardImage_Factory", "MatCardSmImage", "MatCardSmImage_Factory", "MatCardMdImage", "MatCardMdImage_Factory", "MatCardLgImage", "MatCardLgImage_Factory", "MatCardXlImage", "MatCardXlImage_Factory", "MatCardAvatar", "MatCardAvatar_Factory", "CARD_DIRECTIVES", "MatCardModule", "MatCardModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/GitHub/task-management-application/client/node_modules/.pnpm/@angular+material@16.2.14_4056c724f738b156ccd72c3e8383c8cb/node_modules/@angular/material/fesm2022/card.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, Input, Directive, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n    constructor(config) {\n        this.appearance = config?.appearance || 'raised';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCard, deps: [{ token: MAT_CARD_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCard, selector: \"mat-card\", inputs: { appearance: \"appearance\" }, host: { properties: { \"class.mat-mdc-card-outlined\": \"appearance === \\\"outlined\\\"\", \"class.mdc-card--outlined\": \"appearance === \\\"outlined\\\"\" }, classAttribute: \"mat-mdc-card mdc-card\" }, exportAs: [\"matCard\"], ngImport: i0, template: \"<ng-content></ng-content>\\n\", styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation);--mdc-elevated-card-container-shape:4px;--mdc-outlined-card-container-shape:4px;--mdc-outlined-card-outline-width:1px}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCard, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card', host: {\n                        'class': 'mat-mdc-card mdc-card',\n                        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n                        '[class.mdc-card--outlined]': 'appearance === \"outlined\"',\n                    }, exportAs: 'matCard', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-content></ng-content>\\n\", styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation);--mdc-elevated-card-container-shape:4px;--mdc-outlined-card-container-shape:4px;--mdc-outlined-card-outline-width:1px}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_CARD_CONFIG]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { appearance: [{\n                type: Input\n            }] } });\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardTitle, selector: \"mat-card-title, [mat-card-title], [matCardTitle]\", host: { classAttribute: \"mat-mdc-card-title\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n                    host: { 'class': 'mat-mdc-card-title' },\n                }]\n        }] });\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardTitleGroup, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardTitleGroup, selector: \"mat-card-title-group\", host: { classAttribute: \"mat-mdc-card-title-group\" }, ngImport: i0, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardTitleGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-title-group', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-title-group' }, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardContent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardContent, selector: \"mat-card-content\", host: { classAttribute: \"mat-mdc-card-content\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-content',\n                    host: { 'class': 'mat-mdc-card-content' },\n                }]\n        }] });\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardSubtitle, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardSubtitle, selector: \"mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]\", host: { classAttribute: \"mat-mdc-card-subtitle\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardSubtitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n                    host: { 'class': 'mat-mdc-card-subtitle' },\n                }]\n        }] });\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n    constructor() {\n        // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n        // as to not conflict with the native `align` attribute.\n        /** Position of the actions inside the card. */\n        this.align = 'start';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardActions, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardActions, selector: \"mat-card-actions\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-mdc-card-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-mdc-card-actions mdc-card__actions\" }, exportAs: [\"matCardActions\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-actions',\n                    exportAs: 'matCardActions',\n                    host: {\n                        'class': 'mat-mdc-card-actions mdc-card__actions',\n                        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardHeader, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardHeader, selector: \"mat-card-header\", host: { classAttribute: \"mat-mdc-card-header\" }, ngImport: i0, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-header' }, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardFooter, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardFooter, selector: \"mat-card-footer\", host: { classAttribute: \"mat-mdc-card-footer\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardFooter, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-footer',\n                    host: { 'class': 'mat-mdc-card-footer' },\n                }]\n        }] });\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardImage, selector: \"[mat-card-image], [matCardImage]\", host: { classAttribute: \"mat-mdc-card-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-image], [matCardImage]',\n                    host: { 'class': 'mat-mdc-card-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardSmImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardSmImage, selector: \"[mat-card-sm-image], [matCardImageSmall]\", host: { classAttribute: \"mat-mdc-card-sm-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardSmImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-sm-image], [matCardImageSmall]',\n                    host: { 'class': 'mat-mdc-card-sm-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardMdImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardMdImage, selector: \"[mat-card-md-image], [matCardImageMedium]\", host: { classAttribute: \"mat-mdc-card-md-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardMdImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-md-image], [matCardImageMedium]',\n                    host: { 'class': 'mat-mdc-card-md-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardLgImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardLgImage, selector: \"[mat-card-lg-image], [matCardImageLarge]\", host: { classAttribute: \"mat-mdc-card-lg-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardLgImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-lg-image], [matCardImageLarge]',\n                    host: { 'class': 'mat-mdc-card-lg-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardXlImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardXlImage, selector: \"[mat-card-xl-image], [matCardImageXLarge]\", host: { classAttribute: \"mat-mdc-card-xl-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardXlImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-xl-image], [matCardImageXLarge]',\n                    host: { 'class': 'mat-mdc-card-xl-image mdc-card__media' },\n                }]\n        }] });\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCardAvatar, selector: \"[mat-card-avatar], [matCardAvatar]\", host: { classAttribute: \"mat-mdc-card-avatar\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-avatar], [matCardAvatar]',\n                    host: { 'class': 'mat-mdc-card-avatar' },\n                }]\n        }] });\n\nconst CARD_DIRECTIVES = [\n    MatCard,\n    MatCardActions,\n    MatCardAvatar,\n    MatCardContent,\n    MatCardFooter,\n    MatCardHeader,\n    MatCardImage,\n    MatCardLgImage,\n    MatCardMdImage,\n    MatCardSmImage,\n    MatCardSubtitle,\n    MatCardTitle,\n    MatCardTitleGroup,\n    MatCardXlImage,\n];\nclass MatCardModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardModule, declarations: [MatCard,\n            MatCardActions,\n            MatCardAvatar,\n            MatCardContent,\n            MatCardFooter,\n            MatCardHeader,\n            MatCardImage,\n            MatCardLgImage,\n            MatCardMdImage,\n            MatCardSmImage,\n            MatCardSubtitle,\n            MatCardTitle,\n            MatCardTitleGroup,\n            MatCardXlImage], imports: [MatCommonModule, CommonModule], exports: [MatCard,\n            MatCardActions,\n            MatCardAvatar,\n            MatCardContent,\n            MatCardFooter,\n            MatCardHeader,\n            MatCardImage,\n            MatCardLgImage,\n            MatCardMdImage,\n            MatCardSmImage,\n            MatCardSubtitle,\n            MatCardTitle,\n            MatCardTitleGroup,\n            MatCardXlImage, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardModule, imports: [MatCommonModule, CommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCardModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CommonModule],\n                    exports: [CARD_DIRECTIVES, MatCommonModule],\n                    declarations: CARD_DIRECTIVES,\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACnJ,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,eAAe,GAAG,IAAIhB,cAAc,CAAC,iBAAiB,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiB,OAAO,CAAC;EACVC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,UAAU,GAAGD,MAAM,EAAEC,UAAU,IAAI,QAAQ;EACpD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFN,OAAO,EAAjBlB,EAAE,CAAAyB,iBAAA,CAAiCR,eAAe;IAAA,CAA4D;EAAE;EAChN;IAAS,IAAI,CAACS,IAAI,kBAD8E1B,EAAE,CAAA2B,iBAAA;MAAAC,IAAA,EACJV,OAAO;MAAAW,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADLlC,EAAE,CAAAoC,WAAA,0BAAAD,GAAA,CAAAd,UAAA,uCAAAc,GAAA,CAAAd,UAAA;QAAA;MAAA;MAAAgB,MAAA;QAAAhB,UAAA;MAAA;MAAAiB,QAAA;MAAAC,kBAAA,EAAA3B,GAAA;MAAA4B,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iBAAAT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlC,EAAE,CAAA4C,eAAA;UAAF5C,EAAE,CAAA6C,YAAA,EACqU,CAAC;QAAA;MAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAk4L;EAAE;AAChzM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGjD,EAAE,CAAAkD,iBAAA,CAGXhC,OAAO,EAAc,CAAC;IACrGU,IAAI,EAAE1B,SAAS;IACfiD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEC,IAAI,EAAE;QACzB,OAAO,EAAE,uBAAuB;QAChC,+BAA+B,EAAE,2BAA2B;QAC5D,4BAA4B,EAAE;MAClC,CAAC;MAAEf,QAAQ,EAAE,SAAS;MAAES,aAAa,EAAE5C,iBAAiB,CAACmD,IAAI;MAAEN,eAAe,EAAE5C,uBAAuB,CAACmD,MAAM;MAAEb,QAAQ,EAAE,6BAA6B;MAAEI,MAAM,EAAE,CAAC,kxLAAkxL;IAAE,CAAC;EACn8L,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElB,IAAI,EAAE4B,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9D7B,IAAI,EAAEvB,MAAM;QACZ8C,IAAI,EAAE,CAAClC,eAAe;MAC1B,CAAC,EAAE;QACCW,IAAI,EAAEtB;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEe,UAAU,EAAE,CAAC;MACzCO,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmD,YAAY,CAAC;EACf;IAAS,IAAI,CAACpC,IAAI,YAAAqC,qBAAAnC,CAAA;MAAA,YAAAA,CAAA,IAAwFkC,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBA5B8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EA4BJ8B,YAAY;MAAA7B,SAAA;MAAAC,SAAA;IAAA,EAA+H;EAAE;AAC/O;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KA9BoGjD,EAAE,CAAAkD,iBAAA,CA8BXQ,YAAY,EAAc,CAAC;IAC1G9B,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,kDAAiD;MAC5DC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqB;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMS,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAACxC,IAAI,YAAAyC,0BAAAvC,CAAA;MAAA,YAAAA,CAAA,IAAwFsC,iBAAiB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACpC,IAAI,kBA5C8E1B,EAAE,CAAA2B,iBAAA;MAAAC,IAAA,EA4CJkC,iBAAiB;MAAAjC,SAAA;MAAAC,SAAA;MAAAS,kBAAA,EAAAzB,GAAA;MAAA0B,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAsB,2BAAA9B,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5CflC,EAAE,CAAA4C,eAAA,CAAA/B,GAAA;UAAFb,EAAE,CAAAiE,cAAA,SA4CoI,CAAC;UA5CvIjE,EAAE,CAAA6C,YAAA,EA4C6S,CAAC;UA5ChT7C,EAAE,CAAAkE,YAAA,CA4CqT,CAAC;UA5CxTlE,EAAE,CAAA6C,YAAA,KA4C0nB,CAAC;UA5C7nB7C,EAAE,CAAA6C,YAAA,KA4CqpB,CAAC;QAAA;MAAA;MAAAE,aAAA;MAAAC,eAAA;IAAA,EAAoG;EAAE;AACl2B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9CoGjD,EAAE,CAAAkD,iBAAA,CA8CXY,iBAAiB,EAAc,CAAC;IAC/GlC,IAAI,EAAE1B,SAAS;IACfiD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAsB;MAAEL,aAAa,EAAE5C,iBAAiB,CAACmD,IAAI;MAAEN,eAAe,EAAE5C,uBAAuB,CAACmD,MAAM;MAAEF,IAAI,EAAE;QAAE,OAAO,EAAE;MAA2B,CAAC;MAAEX,QAAQ,EAAE;IAA2hB,CAAC;EAC5tB,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyB,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC7C,IAAI,YAAA8C,uBAAA5C,CAAA;MAAA,YAAAA,CAAA,IAAwF2C,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACP,IAAI,kBA3D8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EA2DJuC,cAAc;MAAAtC,SAAA;MAAAC,SAAA;IAAA,EAAiG;EAAE;AACnN;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KA7DoGjD,EAAE,CAAAkD,iBAAA,CA6DXiB,cAAc,EAAc,CAAC;IAC5GvC,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAuB;IAC5C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,eAAe,CAAC;EAClB;IAAS,IAAI,CAAC/C,IAAI,YAAAgD,wBAAA9C,CAAA;MAAA,YAAAA,CAAA,IAAwF6C,eAAe;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACT,IAAI,kBA7E8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EA6EJyC,eAAe;MAAAxC,SAAA;MAAAC,SAAA;IAAA,EAA2I;EAAE;AAC9P;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KA/EoGjD,EAAE,CAAAkD,iBAAA,CA+EXmB,eAAe,EAAc,CAAC;IAC7GzC,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,2DAA0D;MACrEC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwB;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,cAAc,CAAC;EACjBpD,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACA,IAAI,CAACqD,KAAK,GAAG,OAAO;EACxB;EACA;IAAS,IAAI,CAAClD,IAAI,YAAAmD,uBAAAjD,CAAA;MAAA,YAAAA,CAAA,IAAwF+C,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACX,IAAI,kBArG8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EAqGJ2C,cAAc;MAAA1C,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA0C,4BAAAxC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArGZlC,EAAE,CAAAoC,WAAA,mCAAAD,GAAA,CAAAqC,KAAA;QAAA;MAAA;MAAAnC,MAAA;QAAAmC,KAAA;MAAA;MAAAlC,QAAA;IAAA,EAqGoQ;EAAE;AAC5W;AACA;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KAvGoGjD,EAAE,CAAAkD,iBAAA,CAuGXqB,cAAc,EAAc,CAAC;IAC5G3C,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5Bd,QAAQ,EAAE,gBAAgB;MAC1Be,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,wCAAwC,EAAE;MAC9C;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEmB,KAAK,EAAE,CAAC;MACtB5C,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoE,aAAa,CAAC;EAChB;IAAS,IAAI,CAACrD,IAAI,YAAAsD,sBAAApD,CAAA;MAAA,YAAAA,CAAA,IAAwFmD,aAAa;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACjD,IAAI,kBA9H8E1B,EAAE,CAAA2B,iBAAA;MAAAC,IAAA,EA8HJ+C,aAAa;MAAA9C,SAAA;MAAAC,SAAA;MAAAS,kBAAA,EAAAvB,GAAA;MAAAwB,KAAA;MAAAC,IAAA;MAAAoC,MAAA;MAAAnC,QAAA,WAAAoC,uBAAA5C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9HXlC,EAAE,CAAA4C,eAAA,CAAA7B,GAAA;UAAFf,EAAE,CAAA6C,YAAA,EA8HwL,CAAC;UA9H3L7C,EAAE,CAAAiE,cAAA,YA8HkO,CAAC;UA9HrOjE,EAAE,CAAA6C,YAAA,KA8H2Y,CAAC;UA9H9Y7C,EAAE,CAAAkE,YAAA,CA8HmZ,CAAC;UA9HtZlE,EAAE,CAAA6C,YAAA,KA8H8a,CAAC;QAAA;MAAA;MAAAE,aAAA;MAAAC,eAAA;IAAA,EAAoG;EAAE;AAC3nB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhIoGjD,EAAE,CAAAkD,iBAAA,CAgIXyB,aAAa,EAAc,CAAC;IAC3G/C,IAAI,EAAE1B,SAAS;IACfiD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEL,aAAa,EAAE5C,iBAAiB,CAACmD,IAAI;MAAEN,eAAe,EAAE5C,uBAAuB,CAACmD,MAAM;MAAEF,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB,CAAC;MAAEX,QAAQ,EAAE;IAAkU,CAAC;EACzf,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqC,aAAa,CAAC;EAChB;IAAS,IAAI,CAACzD,IAAI,YAAA0D,sBAAAxD,CAAA;MAAA,YAAAA,CAAA,IAAwFuD,aAAa;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACnB,IAAI,kBA7I8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EA6IJmD,aAAa;MAAAlD,SAAA;MAAAC,SAAA;IAAA,EAA+F;EAAE;AAChN;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KA/IoGjD,EAAE,CAAAkD,iBAAA,CA+IX6B,aAAa,EAAc,CAAC;IAC3GnD,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4B,YAAY,CAAC;EACf;IAAS,IAAI,CAAC3D,IAAI,YAAA4D,qBAAA1D,CAAA;MAAA,YAAAA,CAAA,IAAwFyD,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACrB,IAAI,kBAnK8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EAmKJqD,YAAY;MAAApD,SAAA;MAAAC,SAAA;IAAA,EAA+H;EAAE;AAC/O;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KArKoGjD,EAAE,CAAAkD,iBAAA,CAqKX+B,YAAY,EAAc,CAAC;IAC1GrD,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqC;IAC1D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM8B,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC7D,IAAI,YAAA8D,uBAAA5D,CAAA;MAAA,YAAAA,CAAA,IAAwF2D,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACvB,IAAI,kBA/K8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EA+KJuD,cAAc;MAAAtD,SAAA;MAAAC,SAAA;IAAA,EAA0I;EAAE;AAC5P;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KAjLoGjD,EAAE,CAAAkD,iBAAA,CAiLXiC,cAAc,EAAc,CAAC;IAC5GvD,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMgC,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC/D,IAAI,YAAAgE,uBAAA9D,CAAA;MAAA,YAAAA,CAAA,IAAwF6D,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACzB,IAAI,kBA3L8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EA2LJyD,cAAc;MAAAxD,SAAA;MAAAC,SAAA;IAAA,EAA2I;EAAE;AAC7P;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KA7LoGjD,EAAE,CAAAkD,iBAAA,CA6LXmC,cAAc,EAAc,CAAC;IAC5GzD,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMkC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACjE,IAAI,YAAAkE,uBAAAhE,CAAA;MAAA,YAAAA,CAAA,IAAwF+D,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAC3B,IAAI,kBAvM8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EAuMJ2D,cAAc;MAAA1D,SAAA;MAAAC,SAAA;IAAA,EAA0I;EAAE;AAC5P;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KAzMoGjD,EAAE,CAAAkD,iBAAA,CAyMXqC,cAAc,EAAc,CAAC;IAC5G3D,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMoC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACnE,IAAI,YAAAoE,uBAAAlE,CAAA;MAAA,YAAAA,CAAA,IAAwFiE,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAC7B,IAAI,kBAnN8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EAmNJ6D,cAAc;MAAA5D,SAAA;MAAAC,SAAA;IAAA,EAA2I;EAAE;AAC7P;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KArNoGjD,EAAE,CAAAkD,iBAAA,CAqNXuC,cAAc,EAAc,CAAC;IAC5G7D,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsC,aAAa,CAAC;EAChB;IAAS,IAAI,CAACrE,IAAI,YAAAsE,sBAAApE,CAAA;MAAA,YAAAA,CAAA,IAAwFmE,aAAa;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAAC/B,IAAI,kBAvO8E5D,EAAE,CAAA6D,iBAAA;MAAAjC,IAAA,EAuOJ+D,aAAa;MAAA9D,SAAA;MAAAC,SAAA;IAAA,EAAkH;EAAE;AACnO;AACA;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KAzOoGjD,EAAE,CAAAkD,iBAAA,CAyOXyC,aAAa,EAAc,CAAC;IAC3G/D,IAAI,EAAEpB,SAAS;IACf2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMwC,eAAe,GAAG,CACpB3E,OAAO,EACPqD,cAAc,EACdoB,aAAa,EACbxB,cAAc,EACdY,aAAa,EACbJ,aAAa,EACbM,YAAY,EACZM,cAAc,EACdF,cAAc,EACdF,cAAc,EACdd,eAAe,EACfX,YAAY,EACZI,iBAAiB,EACjB2B,cAAc,CACjB;AACD,MAAMK,aAAa,CAAC;EAChB;IAAS,IAAI,CAACxE,IAAI,YAAAyE,sBAAAvE,CAAA;MAAA,YAAAA,CAAA,IAAwFsE,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAnQ8EhG,EAAE,CAAAiG,gBAAA;MAAArE,IAAA,EAmQSkE;IAAa,EA0B7E;EAAE;EAC7C;IAAS,IAAI,CAACI,IAAI,kBA9R8ElG,EAAE,CAAAmG,gBAAA;MAAAC,OAAA,GA8RkCzF,eAAe,EAAED,YAAY,EAAEC,eAAe;IAAA,EAAI;EAAE;AAC5L;AACA;EAAA,QAAAsC,SAAA,oBAAAA,SAAA,KAhSoGjD,EAAE,CAAAkD,iBAAA,CAgSX4C,aAAa,EAAc,CAAC;IAC3GlE,IAAI,EAAEnB,QAAQ;IACd0C,IAAI,EAAE,CAAC;MACCiD,OAAO,EAAE,CAACzF,eAAe,EAAED,YAAY,CAAC;MACxC2F,OAAO,EAAE,CAACR,eAAe,EAAElF,eAAe,CAAC;MAC3C2F,YAAY,EAAET;IAClB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS5E,eAAe,EAAEC,OAAO,EAAEqD,cAAc,EAAEoB,aAAa,EAAExB,cAAc,EAAEY,aAAa,EAAEJ,aAAa,EAAEM,YAAY,EAAEM,cAAc,EAAEF,cAAc,EAAES,aAAa,EAAEX,cAAc,EAAEd,eAAe,EAAEX,YAAY,EAAEI,iBAAiB,EAAE2B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}