/**
 * Profile page component
 * Displays and allows editing of user profile information
 */
import { Component } from '@angular/core';
import { Valida<PERSON> } from '@angular/forms';

/**
 * Type definitions to fix namespace errors
 */
import type { OnInit } from '@angular/core';
import type { FormBuilder, FormGroup } from '@angular/forms';
import { UserService } from '../../../../core/services/user.service';
import { AuthService } from '../../../../core/services/auth.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { User } from '../../../../core/models/user.model';

@Component({
  selector: 'app-profile-page',
  templateUrl: './profile-page.component.html',
  styleUrls: ['./profile-page.component.scss']
})
export class ProfilePageComponent implements OnInit {
  // Current user profile data
  currentUser: User | null = null;
  
  // Form for editing profile
  profileForm: FormGroup;
  
  // Loading state
  isLoading = false;
  
  // Edit mode toggle
  isEditMode = false;

  /**
   * Constructor
   * @param fb - FormBuilder for creating reactive forms
   * @param userService - Service for user-related operations
   * @param authService - Service for authentication operations
   * @param notificationService - Service for displaying notifications
   */
  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private authService: AuthService,
    private notificationService: NotificationService
  ) {
    // Initialize the form
    this.profileForm = this.fb.group({
      name: ['', [Validators.required]],
      email: [{value: '', disabled: true}],
      avatarUrl: ['']
    });
  }

  /**
   * Lifecycle hook that is called after component initialization
   */
  ngOnInit(): void {
    // Load the current user profile
    this.loadUserProfile();
  }

  /**
   * Loads the current user profile data
   */
  loadUserProfile(): void {
    this.isLoading = true;
    
    this.authService.currentUser$.subscribe(
      (user: User | null) => {
        this.currentUser = user;
        
        if (user) {
          // Populate the form with user data
          this.profileForm.patchValue({
            name: user.name,
            email: user.email,
            avatarUrl: user.avatarUrl || ''
          });
        }
        
        this.isLoading = false;
      },
      (error: any) => {
        this.notificationService.error('Failed to load profile');
        this.isLoading = false;
      }
    );
  }

  /**
   * Toggles edit mode for the profile form
   */
  toggleEditMode(): void {
    this.isEditMode = !this.isEditMode;
    
    if (!this.isEditMode) {
      // Reset form to original values when canceling edit
      this.profileForm.patchValue({
        name: this.currentUser?.name || '',
        avatarUrl: this.currentUser?.avatarUrl || ''
      });
    }
  }

  /**
   * Submits the profile update form
   */
  updateProfile(): void {
    if (this.profileForm.invalid) {
      return;
    }
    
    this.isLoading = true;
    
    const updatedProfile = {
      name: this.profileForm.get('name')?.value,
      avatarUrl: this.profileForm.get('avatarUrl')?.value
    };
    
    if (this.currentUser) {
      this.userService.updateUser(this.currentUser.id, updatedProfile).subscribe(
        (response: any) => {
          this.notificationService.success('Profile updated successfully');
          this.isLoading = false;
          this.isEditMode = false;
          
          // Update the current user in auth service
          // Reload the current user data
          this.loadUserProfile();
        },
        (error: any) => {
          this.notificationService.error('Failed to update profile');
          this.isLoading = false;
        }
      );
    }
  }
}
