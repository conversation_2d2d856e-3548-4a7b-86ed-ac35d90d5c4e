/**
 * Task create page component styles
 */

/* Container for the entire task create page */
.task-create-page-container {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Page header with title and back button */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 500;
    color: #333;
  }
}

/* Back button styling */
.back-button {
  display: flex;
  align-items: center;
  
  mat-icon {
    margin-right: 8px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .task-create-page-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    h1 {
      font-size: 1.5rem;
    }
  }
}
