{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Shared module for the application\n * Contains components, directives, and pipes that are used across multiple feature modules\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material Modules\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n// Shared Components\nimport { LoadingSpinnerComponent } from './components/loading-spinner/loading-spinner.component';\nimport { ConfirmDialogComponent } from './components/confirm-dialog/confirm-dialog.component';\nimport { ErrorMessageComponent } from './components/error-message/error-message.component';\n// Shared Directives\nimport { ClickOutsideDirective } from './directives/click-outside.directive';\nimport { LazyLoadDirective } from './directives/lazy-load.directive';\n// Shared Pipes\nimport { TruncatePipe } from './pipes/truncate.pipe';\nimport { TimeAgoPipe } from './pipes/time-ago.pipe';\n/**\n * Array of Angular Material modules to be imported and exported\n */\nconst materialModules = [MatButtonModule, MatCardModule, MatCheckboxModule, MatChipsModule, MatDatepickerModule, MatDialogModule, MatFormFieldModule, MatIconModule, MatInputModule, MatListModule, MatMenuModule, MatNativeDateModule, MatProgressBarModule, MatProgressSpinnerModule, MatSelectModule, MatSidenavModule, MatSnackBarModule, MatTableModule, MatTabsModule, MatToolbarModule, MatTooltipModule];\n/**\n * Array of shared components to be declared and exported\n */\nconst sharedComponents = [LoadingSpinnerComponent, ConfirmDialogComponent, ErrorMessageComponent];\n/**\n * Array of shared directives to be declared and exported\n */\nconst sharedDirectives = [ClickOutsideDirective, LazyLoadDirective];\n/**\n * Array of shared pipes to be declared and exported\n */\nconst sharedPipes = [TruncatePipe, TimeAgoPipe];\nexport let SharedModule = class SharedModule {};\nSharedModule = __decorate([NgModule({\n  declarations: [...sharedComponents, ...sharedDirectives, ...sharedPipes],\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, ...materialModules],\n  exports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, ...materialModules, ...sharedComponents, ...sharedDirectives, ...sharedPipes]\n})], SharedModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "FormsModule", "ReactiveFormsModule", "RouterModule", "MatButtonModule", "MatCardModule", "MatCheckboxModule", "MatChipsModule", "MatDatepickerModule", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatListModule", "MatMenuModule", "MatNativeDateModule", "MatProgressBarModule", "MatProgressSpinnerModule", "MatSelectModule", "MatSidenavModule", "MatSnackBarModule", "MatTableModule", "MatTabsModule", "MatToolbarModule", "MatTooltipModule", "LoadingSpinnerComponent", "ConfirmDialogComponent", "ErrorMessageComponent", "ClickOutsideDirective", "LazyLoadDirective", "TruncatePipe", "TimeAgoPipe", "materialModules", "sharedComponents", "sharedDirectives", "sharedPipes", "SharedModule", "__decorate", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["/**\n * Shared module for the application\n * Contains components, directives, and pipes that are used across multiple feature modules\n */\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\n// Angular Material Modules\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n\n// Shared Components\nimport { LoadingSpinnerComponent } from './components/loading-spinner/loading-spinner.component';\nimport { ConfirmDialogComponent } from './components/confirm-dialog/confirm-dialog.component';\nimport { ErrorMessageComponent } from './components/error-message/error-message.component';\n\n// Shared Directives\nimport { ClickOutsideDirective } from './directives/click-outside.directive';\nimport { LazyLoadDirective } from './directives/lazy-load.directive';\n\n// Shared Pipes\nimport { TruncatePipe } from './pipes/truncate.pipe';\nimport { TimeAgoPipe } from './pipes/time-ago.pipe';\n\n/**\n * Array of Angular Material modules to be imported and exported\n */\nconst materialModules = [\n  MatButtonModule,\n  MatCardModule,\n  MatCheckboxModule,\n  MatChipsModule,\n  MatDatepickerModule,\n  MatDialogModule,\n  MatFormFieldModule,\n  MatIconModule,\n  MatInputModule,\n  MatListModule,\n  MatMenuModule,\n  MatNativeDateModule,\n  MatProgressBarModule,\n  MatProgressSpinnerModule,\n  MatSelectModule,\n  MatSidenavModule,\n  MatSnackBarModule,\n  MatTableModule,\n  MatTabsModule,\n  MatToolbarModule,\n  MatTooltipModule\n];\n\n/**\n * Array of shared components to be declared and exported\n */\nconst sharedComponents = [\n  LoadingSpinnerComponent,\n  ConfirmDialogComponent,\n  ErrorMessageComponent\n];\n\n/**\n * Array of shared directives to be declared and exported\n */\nconst sharedDirectives = [\n  ClickOutsideDirective,\n  LazyLoadDirective\n];\n\n/**\n * Array of shared pipes to be declared and exported\n */\nconst sharedPipes = [\n  TruncatePipe,\n  TimeAgoPipe\n];\n\n@NgModule({\n  declarations: [\n    ...sharedComponents,\n    ...sharedDirectives,\n    ...sharedPipes\n  ],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    RouterModule,\n    ...materialModules\n  ],\n  exports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    RouterModule,\n    ...materialModules,\n    ...sharedComponents,\n    ...sharedDirectives,\n    ...sharedPipes\n  ]\n})\nexport class SharedModule { }\n"], "mappings": ";AAAA;;;;AAIA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,qBAAqB,QAAQ,oDAAoD;AAE1F;AACA,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,iBAAiB,QAAQ,kCAAkC;AAEpE;AACA,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,WAAW,QAAQ,uBAAuB;AAEnD;;;AAGA,MAAMC,eAAe,GAAG,CACtB5B,eAAe,EACfC,aAAa,EACbC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,eAAe,EACfC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,aAAa,EACbC,mBAAmB,EACnBC,oBAAoB,EACpBC,wBAAwB,EACxBC,eAAe,EACfC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB,CACjB;AAED;;;AAGA,MAAMS,gBAAgB,GAAG,CACvBR,uBAAuB,EACvBC,sBAAsB,EACtBC,qBAAqB,CACtB;AAED;;;AAGA,MAAMO,gBAAgB,GAAG,CACvBN,qBAAqB,EACrBC,iBAAiB,CAClB;AAED;;;AAGA,MAAMM,WAAW,GAAG,CAClBL,YAAY,EACZC,WAAW,CACZ;AA0BM,WAAMK,YAAY,GAAlB,MAAMA,YAAY,GAAI;AAAhBA,YAAY,GAAAC,UAAA,EAxBxBtC,QAAQ,CAAC;EACRuC,YAAY,EAAE,CACZ,GAAGL,gBAAgB,EACnB,GAAGC,gBAAgB,EACnB,GAAGC,WAAW,CACf;EACDI,OAAO,EAAE,CACPvC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZ,GAAG6B,eAAe,CACnB;EACDQ,OAAO,EAAE,CACPxC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZ,GAAG6B,eAAe,EAClB,GAAGC,gBAAgB,EACnB,GAAGC,gBAAgB,EACnB,GAAGC,WAAW;CAEjB,CAAC,C,EACWC,YAAY,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}