/**
 * Login Form Component
 * Handles user authentication with email and password
 */
import { Component, OnInit, Output, EventEmitter, Input, ChangeDetectionStrategy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-login-form',
  templateUrl: './login-form.component.html',
  styleUrls: ['./login-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoginFormComponent implements OnInit {
  /**
   * Loading state indicator
   */
  @Input() loading = false;
  
  /**
   * Error message from failed login attempt
   */
  @Input() error: string | null = null;
  
  /**
   * Event emitted when form is submitted
   */
  @Output() formSubmit = new EventEmitter<{ email: string; password: string }>();
  
  /**
   * Event emitted when forgot password link is clicked
   */
  @Output() forgotPassword = new EventEmitter<void>();
  
  /**
   * Event emitted when register link is clicked
   */
  @Output() register = new EventEmitter<void>();
  
  /**
   * Login form group
   */
  loginForm!: FormGroup;
  
  /**
   * Flag to toggle password visibility
   */
  hidePassword = true;

  /**
   * Constructor with dependency injection
   * @param fb - FormBuilder for reactive forms
   */
  constructor(private fb: FormBuilder) {}

  /**
   * Lifecycle hook that is called after component initialization
   * Initializes the login form
   */
  ngOnInit(): void {
    this.initForm();
  }

  /**
   * Initialize login form with validation
   */
  private initForm(): void {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      rememberMe: [false]
    });
  }

  /**
   * Handle form submission
   */
  onSubmit(): void {
    if (this.loginForm.invalid || this.loading) {
      // Mark all fields as touched to trigger validation messages
      this.loginForm.markAllAsTouched();
      return;
    }
    
    const { email, password } = this.loginForm.value;
    this.formSubmit.emit({ email, password });
  }

  /**
   * Handle forgot password link click
   */
  onForgotPassword(): void {
    this.forgotPassword.emit();
  }

  /**
   * Handle register link click
   */
  onRegister(): void {
    this.register.emit();
  }

  /**
   * Check if form control has error
   * @param controlName - Name of form control
   * @param errorName - Name of error
   * @returns True if control has error
   */
  hasError(controlName: string, errorName: string): boolean {
    const control = this.loginForm.get(controlName);
    return !!(control && control.touched && control.hasError(errorName));
  }

  /**
   * Toggle password visibility
   */
  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }
}
