{"ast": null, "code": "import { __decorate } from \"tslib\";\n/**\n * Forgot Password Page Component\n * Page for requesting password reset\n */\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nexport let ForgotPasswordPageComponent = class ForgotPasswordPageComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, notificationService, router, cdr) {\n    this.authService = authService;\n    this.notificationService = notificationService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Loading state indicator\n     */\n    this.loading = false;\n    /**\n     * Error message from failed request\n     */\n    this.error = null;\n    /**\n     * Success message after successful request\n     */\n    this.success = null;\n    /**\n     * Subject for unsubscribing from observables\n     */\n    this.destroy$ = new Subject();\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit() {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Handle forgot password form submission\n   * @param data - Form data with email\n   */\n  onFormSubmit(data) {\n    this.loading = true;\n    this.error = null;\n    this.success = null;\n    this.cdr.markForCheck();\n    this.authService.forgotPassword(data.email).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.loading = false;\n        this.success = 'Password reset instructions have been sent to your email.';\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.message || 'Failed to send password reset email. Please try again.';\n        console.error('Forgot password error:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Navigate to login page\n   */\n  onLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n};\nForgotPasswordPageComponent = __decorate([Component({\n  selector: 'app-forgot-password-page',\n  templateUrl: './forgot-password-page.component.html',\n  styleUrls: ['./forgot-password-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], ForgotPasswordPageComponent);", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "Subject", "takeUntil", "ForgotPasswordPageComponent", "constructor", "authService", "notificationService", "router", "cdr", "loading", "error", "success", "destroy$", "ngOnInit", "isAuthenticated", "navigate", "ngOnDestroy", "next", "complete", "onFormSubmit", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgotPassword", "email", "pipe", "subscribe", "err", "message", "console", "onLogin", "__decorate", "selector", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\features\\auth\\pages\\forgot-password-page\\forgot-password-page.component.ts"], "sourcesContent": ["/**\n * Forgot Password Page Component\n * Page for requesting password reset\n */\nimport { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-forgot-password-page',\n  templateUrl: './forgot-password-page.component.html',\n  styleUrls: ['./forgot-password-page.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class ForgotPasswordPageComponent implements OnInit, OnD<PERSON>roy {\n  /**\n   * Loading state indicator\n   */\n  loading = false;\n  \n  /**\n   * Error message from failed request\n   */\n  error: string | null = null;\n  \n  /**\n   * Success message after successful request\n   */\n  success: string | null = null;\n  \n  /**\n   * Subject for unsubscribing from observables\n   */\n  private destroy$ = new Subject<void>();\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param notificationService - Notification service for displaying messages\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private notificationService: NotificationService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Checks if user is already authenticated\n   */\n  ngOnInit(): void {\n    // Redirect to dashboard if already logged in\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  /**\n   * Lifecycle hook that is called before component destruction\n   * Unsubscribes from observables\n   */\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Handle forgot password form submission\n   * @param data - Form data with email\n   */\n  onFormSubmit(data: { email: string }): void {\n    this.loading = true;\n    this.error = null;\n    this.success = null;\n    this.cdr.markForCheck();\n    \n    this.authService.forgotPassword(data.email)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.loading = false;\n          this.success = 'Password reset instructions have been sent to your email.';\n          this.cdr.markForCheck();\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = err.message || 'Failed to send password reset email. Please try again.';\n          console.error('Forgot password error:', err);\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  /**\n   * Navigate to login page\n   */\n  onLogin(): void {\n    this.router.navigate(['/auth/login']);\n  }\n}\n"], "mappings": ";AAAA;;;;AAIA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AAExG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAUnC,WAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAqBtC;;;;;;;EAOAC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,GAAsB;IAHtB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA/Bb;;;IAGA,KAAAC,OAAO,GAAG,KAAK;IAEf;;;IAGA,KAAAC,KAAK,GAAkB,IAAI;IAE3B;;;IAGA,KAAAC,OAAO,GAAkB,IAAI;IAE7B;;;IAGQ,KAAAC,QAAQ,GAAG,IAAIX,OAAO,EAAQ;EAcnC;EAEH;;;;EAIAY,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACR,WAAW,CAACS,eAAe,EAAE,EAAE;MACtC,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;;;;EAIAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEA;;;;EAIAC,YAAYA,CAACC,IAAuB;IAClC,IAAI,CAACX,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,GAAG,CAACa,YAAY,EAAE;IAEvB,IAAI,CAAChB,WAAW,CAACiB,cAAc,CAACF,IAAI,CAACG,KAAK,CAAC,CACxCC,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAC9Ba,SAAS,CAAC;MACTR,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACR,OAAO,GAAG,KAAK;QACpB,IAAI,CAACE,OAAO,GAAG,2DAA2D;QAC1E,IAAI,CAACH,GAAG,CAACa,YAAY,EAAE;MACzB,CAAC;MACDX,KAAK,EAAGgB,GAAG,IAAI;QACb,IAAI,CAACjB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGgB,GAAG,CAACC,OAAO,IAAI,wDAAwD;QACpFC,OAAO,CAAClB,KAAK,CAAC,wBAAwB,EAAEgB,GAAG,CAAC;QAC5C,IAAI,CAAClB,GAAG,CAACa,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEA;;;EAGAQ,OAAOA,CAAA;IACL,IAAI,CAACtB,MAAM,CAACQ,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;CACD;AAxFYZ,2BAA2B,GAAA2B,UAAA,EANvC/B,SAAS,CAAC;EACTgC,QAAQ,EAAE,0BAA0B;EACpCC,WAAW,EAAE,uCAAuC;EACpDC,SAAS,EAAE,CAAC,uCAAuC,CAAC;EACpDC,eAAe,EAAElC,uBAAuB,CAACmC;CAC1C,CAAC,C,EACWhC,2BAA2B,CAwFvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}