<!-- Reset password form container -->
<div class="reset-password-form-container">
  <!-- Error message -->
  <app-error-message 
    *ngIf="error" 
    [message]="error">
  </app-error-message>

  <!-- Success message -->
  <div *ngIf="success" class="success-message">
    <mat-icon>check_circle</mat-icon>
    <span>{{ success }}</span>
  </div>

  <!-- Token error message -->
  <app-error-message 
    *ngIf="!token && !success" 
    [message]="'Invalid or expired password reset link. Please request a new one.'">
  </app-error-message>

  <!-- Reset password form -->
  <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()" class="reset-password-form" *ngIf="token && !success">
    <!-- Password field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>New Password</mat-label>
      <input 
        matInput 
        [type]="hidePassword ? 'password' : 'text'" 
        formControlName="password"
        placeholder="Create a new password"
        autocomplete="new-password">
      <mat-icon matPrefix>lock</mat-icon>
      <button 
        mat-icon-button 
        matSuffix 
        type="button"
        (click)="togglePasswordVisibility()" 
        [attr.aria-label]="'Hide password'" 
        [attr.aria-pressed]="hidePassword">
        <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
      </button>
      <mat-error *ngIf="hasError('password', 'required')">
        Password is required
      </mat-error>
      <mat-error *ngIf="hasError('password', 'minlength')">
        Password must be at least 8 characters
      </mat-error>
      <mat-error *ngIf="hasError('password', 'passwordStrength')">
        Password must include uppercase, lowercase, number, and special character
      </mat-error>
    </mat-form-field>

    <!-- Confirm Password field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Confirm Password</mat-label>
      <input 
        matInput 
        [type]="hideConfirmPassword ? 'password' : 'text'" 
        formControlName="confirmPassword"
        placeholder="Confirm your new password"
        autocomplete="new-password">
      <mat-icon matPrefix>lock</mat-icon>
      <button 
        mat-icon-button 
        matSuffix 
        type="button"
        (click)="toggleConfirmPasswordVisibility()" 
        [attr.aria-label]="'Hide confirm password'" 
        [attr.aria-pressed]="hideConfirmPassword">
        <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
      </button>
      <mat-error *ngIf="hasError('confirmPassword', 'required')">
        Please confirm your password
      </mat-error>
      <mat-error *ngIf="hasFormError('passwordMismatch')">
        Passwords do not match
      </mat-error>
    </mat-form-field>

    <!-- Submit button -->
    <button 
      mat-raised-button 
      color="primary" 
      type="submit" 
      class="submit-button"
      [disabled]="loading">
      <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
      <span *ngIf="!loading">Reset Password</span>
    </button>
  </form>

  <!-- Back to login button -->
  <button 
    *ngIf="success || !token"
    mat-raised-button 
    color="primary" 
    (click)="onLogin()" 
    class="back-to-login-button">
    Back to Login
  </button>
</div>
