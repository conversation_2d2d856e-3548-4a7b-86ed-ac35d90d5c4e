{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction HeaderComponent_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser == null ? null : ctx_r0.currentUser.name == null ? null : ctx_r0.currentUser.name.charAt(0));\n  }\n}\nexport class HeaderComponent {\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(authService, router, cdr) {\n    this.authService = authService;\n    this.router = router;\n    this.cdr = cdr;\n    /**\n     * Current authenticated user\n     */\n    this.currentUser = null;\n    /**\n     * Flag to show/hide the user menu\n     */\n    this.isUserMenuOpen = false;\n  }\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up subscription to current user\n   */\n  ngOnInit() {\n    // Subscribe to current user changes\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.cdr.markForCheck();\n    });\n  }\n  /**\n   * Toggle the user menu dropdown\n   */\n  toggleUserMenu() {\n    this.isUserMenuOpen = !this.isUserMenuOpen;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Close the user menu dropdown\n   */\n  closeUserMenu() {\n    this.isUserMenuOpen = false;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Navigate to user profile page\n   */\n  goToProfile() {\n    this.closeUserMenu();\n    this.router.navigate(['/profile']);\n  }\n  /**\n   * Log out the current user\n   */\n  logout() {\n    this.closeUserMenu();\n    this.authService.logout();\n  }\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      decls: 44,\n      vars: 6,\n      consts: [[\"color\", \"primary\", 1, \"header\"], [1, \"header-brand\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Toggle sidenav\", 1, \"menu-button\"], [\"routerLink\", \"/\", 1, \"brand-link\"], [1, \"brand-title\"], [1, \"spacer\"], [1, \"nav-links\"], [\"mat-button\", \"\", \"routerLink\", \"/tasks\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/tasks/create\", \"routerLinkActive\", \"active\"], [1, \"user-menu\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Show notifications\"], [1, \"user-dropdown\", 3, \"appClickOutside\"], [\"mat-button\", \"\", 1, \"user-button\", 3, \"matMenuTriggerFor\", \"click\"], [1, \"user-avatar\"], [4, \"ngIf\"], [1, \"user-name\"], [\"userMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"click\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\", 0)(1, \"div\", 1)(2, \"button\", 2)(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"menu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"a\", 3)(6, \"span\", 4);\n          i0.ɵɵtext(7, \"Task Management\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(8, \"span\", 5);\n          i0.ɵɵelementStart(9, \"nav\", 6)(10, \"a\", 7)(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"task\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\");\n          i0.ɵɵtext(14, \"Tasks\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"a\", 8)(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"add_task\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19, \"New Task\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 9)(21, \"button\", 10)(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"notifications\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 11);\n          i0.ɵɵlistener(\"appClickOutside\", function HeaderComponent_Template_div_appClickOutside_24_listener() {\n            return ctx.closeUserMenu();\n          });\n          i0.ɵɵelementStart(25, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_25_listener() {\n            return ctx.toggleUserMenu();\n          });\n          i0.ɵɵelementStart(26, \"div\", 13);\n          i0.ɵɵtemplate(27, HeaderComponent_span_27_Template, 2, 1, \"span\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"span\", 15);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-icon\");\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"mat-menu\", null, 16)(34, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_34_listener() {\n            return ctx.goToProfile();\n          });\n          i0.ɵɵelementStart(35, \"mat-icon\");\n          i0.ɵɵtext(36, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\");\n          i0.ɵɵtext(38, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_39_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelementStart(40, \"mat-icon\");\n          i0.ɵɵtext(41, \"exit_to_app\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\");\n          i0.ɵɵtext(43, \"Logout\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(33);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", _r1);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"background-image\", (ctx.currentUser == null ? null : ctx.currentUser.avatarUrl) ? \"url(\" + (ctx.currentUser == null ? null : ctx.currentUser.avatarUrl) + \")\" : \"\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.currentUser == null ? null : ctx.currentUser.avatarUrl));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.currentUser == null ? null : ctx.currentUser.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isUserMenuOpen ? \"arrow_drop_up\" : \"arrow_drop_down\");\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive],\n      styles: [\"\\n\\n\\n\\n\\n\\n.header[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  height: 64px;\\n  padding: 0 16px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.header-brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.header-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n}\\n.header-brand[_ngcontent-%COMP%]   .brand-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 500;\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n\\n\\n.nav-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.nav-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0 4px;\\n}\\n.nav-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n}\\n.nav-links[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n}\\n\\n\\n\\n.user-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 16px;\\n}\\n\\n\\n\\n.user-dropdown[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.user-dropdown[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0 8px;\\n  height: 36px;\\n  border-radius: 18px;\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.user-dropdown[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n.user-dropdown[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: #fff;\\n  color: #3f51b5;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 500;\\n  background-size: cover;\\n  background-position: center;\\n}\\n.user-dropdown[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n  max-width: 120px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .nav-links[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .user-name[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .header-brand[_ngcontent-%COMP%]   .brand-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "currentUser", "name", "char<PERSON>t", "HeaderComponent", "constructor", "authService", "router", "cdr", "isUserMenuOpen", "ngOnInit", "currentUser$", "subscribe", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toggleUserMenu", "closeUserMenu", "goToProfile", "navigate", "logout", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "HeaderComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "HeaderComponent_Template_div_appClickOutside_24_listener", "HeaderComponent_Template_button_click_25_listener", "ɵɵtemplate", "HeaderComponent_span_27_Template", "HeaderComponent_Template_button_click_34_listener", "HeaderComponent_Template_button_click_39_listener", "ɵɵproperty", "_r1", "ɵɵstyleProp", "avatarUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\components\\header\\header.component.ts", "C:\\Users\\<USER>\\Documents\\GitHub\\task-management-application\\client\\src\\app\\core\\components\\header\\header.component.html"], "sourcesContent": ["/**\n * Header Component\n * Main navigation header for the application\n */\nimport { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { User } from '../../models/user.model';\n\n@Component({\n  selector: 'app-header',\n  templateUrl: './header.component.html',\n  styleUrls: ['./header.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class HeaderComponent implements OnInit {\n  /**\n   * Current authenticated user\n   */\n  currentUser: User | null = null;\n  \n  /**\n   * Flag to show/hide the user menu\n   */\n  isUserMenuOpen = false;\n\n  /**\n   * Constructor with dependency injection\n   * @param authService - Authentication service\n   * @param router - Router for navigation\n   * @param cdr - Change detector reference for OnPush strategy\n   */\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  /**\n   * Lifecycle hook that is called after component initialization\n   * Sets up subscription to current user\n   */\n  ngOnInit(): void {\n    // Subscribe to current user changes\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      this.cdr.markForCheck();\n    });\n  }\n\n  /**\n   * Toggle the user menu dropdown\n   */\n  toggleUserMenu(): void {\n    this.isUserMenuOpen = !this.isUserMenuOpen;\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Close the user menu dropdown\n   */\n  closeUserMenu(): void {\n    this.isUserMenuOpen = false;\n    this.cdr.markForCheck();\n  }\n\n  /**\n   * Navigate to user profile page\n   */\n  goToProfile(): void {\n    this.closeUserMenu();\n    this.router.navigate(['/profile']);\n  }\n\n  /**\n   * Log out the current user\n   */\n  logout(): void {\n    this.closeUserMenu();\n    this.authService.logout();\n  }\n}\n", "<!-- Main application header -->\n<mat-toolbar color=\"primary\" class=\"header\">\n  <!-- Application logo and title -->\n  <div class=\"header-brand\">\n    <button mat-icon-button aria-label=\"Toggle sidenav\" class=\"menu-button\">\n      <mat-icon>menu</mat-icon>\n    </button>\n    <a routerLink=\"/\" class=\"brand-link\">\n      <span class=\"brand-title\">Task Management</span>\n    </a>\n  </div>\n\n  <!-- Spacer to push content to opposite sides -->\n  <span class=\"spacer\"></span>\n\n  <!-- Navigation links -->\n  <nav class=\"nav-links\">\n    <a mat-button routerLink=\"/tasks\" routerLinkActive=\"active\">\n      <mat-icon>task</mat-icon>\n      <span>Tasks</span>\n    </a>\n    <a mat-button routerLink=\"/tasks/create\" routerLinkActive=\"active\">\n      <mat-icon>add_task</mat-icon>\n      <span>New Task</span>\n    </a>\n  </nav>\n\n  <!-- User profile menu -->\n  <div class=\"user-menu\">\n    <!-- Notification bell -->\n    <button mat-icon-button aria-label=\"Show notifications\">\n      <mat-icon>notifications</mat-icon>\n    </button>\n    \n    <!-- User avatar and dropdown -->\n    <div class=\"user-dropdown\" (appClickOutside)=\"closeUserMenu()\">\n      <button mat-button [matMenuTriggerFor]=\"userMenu\" class=\"user-button\" (click)=\"toggleUserMenu()\">\n        <div class=\"user-avatar\" [style.backgroundImage]=\"currentUser?.avatarUrl ? 'url(' + currentUser?.avatarUrl + ')' : ''\">\n          <span *ngIf=\"!currentUser?.avatarUrl\">{{ currentUser?.name?.charAt(0) }}</span>\n        </div>\n        <span class=\"user-name\">{{ currentUser?.name }}</span>\n        <mat-icon>{{ isUserMenuOpen ? 'arrow_drop_up' : 'arrow_drop_down' }}</mat-icon>\n      </button>\n      \n      <!-- User dropdown menu -->\n      <mat-menu #userMenu=\"matMenu\">\n        <button mat-menu-item (click)=\"goToProfile()\">\n          <mat-icon>person</mat-icon>\n          <span>Profile</span>\n        </button>\n        <button mat-menu-item (click)=\"logout()\">\n          <mat-icon>exit_to_app</mat-icon>\n          <span>Logout</span>\n        </button>\n      </mat-menu>\n    </div>\n  </div>\n</mat-toolbar>\n"], "mappings": ";;;;;;ICsCUA,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzCH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,IAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,IAAA,CAAAC,MAAA,IAAkC;;;ADvBlF,OAAM,MAAOC,eAAe;EAW1B;;;;;;EAMAC,YACUC,WAAwB,EACxBC,MAAc,EACdC,GAAsB;IAFtB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IAnBb;;;IAGA,KAAAP,WAAW,GAAgB,IAAI;IAE/B;;;IAGA,KAAAQ,cAAc,GAAG,KAAK;EAYnB;EAEH;;;;EAIAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,WAAW,CAACK,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACZ,WAAW,GAAGY,IAAI;MACvB,IAAI,CAACL,GAAG,CAACM,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAACN,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,CAACD,GAAG,CAACM,YAAY,EAAE;EACzB;EAEA;;;EAGAE,aAAaA,CAAA;IACX,IAAI,CAACP,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACD,GAAG,CAACM,YAAY,EAAE;EACzB;EAEA;;;EAGAG,WAAWA,CAAA;IACT,IAAI,CAACD,aAAa,EAAE;IACpB,IAAI,CAACT,MAAM,CAACW,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAI,CAACH,aAAa,EAAE;IACpB,IAAI,CAACV,WAAW,CAACa,MAAM,EAAE;EAC3B;;;uBAjEWf,eAAe,EAAAV,EAAA,CAAA0B,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5B,EAAA,CAAA0B,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA9B,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAA+B,iBAAA;IAAA;EAAA;;;YAAfrB,eAAe;MAAAsB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd5BtC,EAAA,CAAAC,cAAA,qBAA4C;UAI5BD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE3BH,EAAA,CAAAC,cAAA,WAAqC;UACTD,EAAA,CAAAE,MAAA,sBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKpDH,EAAA,CAAAwC,SAAA,cAA4B;UAG5BxC,EAAA,CAAAC,cAAA,aAAuB;UAETD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEpBH,EAAA,CAAAC,cAAA,YAAmE;UACvDD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKzBH,EAAA,CAAAC,cAAA,cAAuB;UAGTD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAIpCH,EAAA,CAAAC,cAAA,eAA+D;UAApCD,EAAA,CAAAyC,UAAA,6BAAAC,yDAAA;YAAA,OAAmBH,GAAA,CAAAjB,aAAA,EAAe;UAAA,EAAC;UAC5DtB,EAAA,CAAAC,cAAA,kBAAiG;UAA3BD,EAAA,CAAAyC,UAAA,mBAAAE,kDAAA;YAAA,OAASJ,GAAA,CAAAlB,cAAA,EAAgB;UAAA,EAAC;UAC9FrB,EAAA,CAAAC,cAAA,eAAuH;UACrHD,EAAA,CAAA4C,UAAA,KAAAC,gCAAA,mBAA+E;UACjF7C,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,IAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtDH,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAIjFH,EAAA,CAAAC,cAAA,0BAA8B;UACND,EAAA,CAAAyC,UAAA,mBAAAK,kDAAA;YAAA,OAASP,GAAA,CAAAhB,WAAA,EAAa;UAAA,EAAC;UAC3CvB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEtBH,EAAA,CAAAC,cAAA,kBAAyC;UAAnBD,EAAA,CAAAyC,UAAA,mBAAAM,kDAAA;YAAA,OAASR,GAAA,CAAAd,MAAA,EAAQ;UAAA,EAAC;UACtCzB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;;UAhBJH,EAAA,CAAAI,SAAA,IAA8B;UAA9BJ,EAAA,CAAAgD,UAAA,sBAAAC,GAAA,CAA8B;UACtBjD,EAAA,CAAAI,SAAA,GAA6F;UAA7FJ,EAAA,CAAAkD,WAAA,sBAAAX,GAAA,CAAAhC,WAAA,kBAAAgC,GAAA,CAAAhC,WAAA,CAAA4C,SAAA,cAAAZ,GAAA,CAAAhC,WAAA,kBAAAgC,GAAA,CAAAhC,WAAA,CAAA4C,SAAA,aAA6F;UAC7GnD,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAgD,UAAA,WAAAT,GAAA,CAAAhC,WAAA,kBAAAgC,GAAA,CAAAhC,WAAA,CAAA4C,SAAA,EAA6B;UAEdnD,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAK,iBAAA,CAAAkC,GAAA,CAAAhC,WAAA,kBAAAgC,GAAA,CAAAhC,WAAA,CAAAC,IAAA,CAAuB;UACrCR,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAAK,iBAAA,CAAAkC,GAAA,CAAAxB,cAAA,uCAA0D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}